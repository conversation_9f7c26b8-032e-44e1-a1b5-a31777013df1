﻿/// <summary>
/// TaskClient
/// </summary>
/// <remarks>
/// 2021/7/24 10:37:57: 创建. 王正勇 <br/>
/// 任务模块Client
/// </remarks>
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using game.proto;
using game.scene;
using GLib;
using GLib.Common;
namespace GLib.Client
{
    /// <summary>
    /// TaskClient
    /// </summary>
    public class TaskClient : ITaskClient, IMessageHandler
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName
        {
            get;
            set;
        }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState
        {
            get;
            set;
        }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress
        {
            get;
            set;
        }
        INetManager pNetManager;
        public bool Create()
        {
            pNetManager = GlobalGame.Instance.NetManager;
            GlobalGame.Instance.StartCoroutineEx(waitNetManager());
            return true;
        }
        IEnumerator waitNetManager()
        {
            while (pNetManager == null)
            {
                pNetManager = GlobalGame.Instance.NetManager;
                yield return null;
            }
            pNetManager.RegisterMessageHandler(MSG_MODULEID.Task, this);
        }
        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }

        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {
            package.Pop(out int nLen);
            package.ReadByteBuffer(out byte[] szValue, nLen);
            switch (head.wKeyAction)
            {
                //接受任务
                case (int)TaskAction.TaskInstanceListUpdateSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_INSTANCE_LIST_UPDTE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //更新任务数据
                case (int)TaskAction.UpdateTrackingMessageSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_UPDATE_TRACK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //执行一段脚本
                case (int)TaskAction.ClientInvokeScriptSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_EXECUTE_SCRIPT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //启动结算
                case (int)TaskAction.TaskWindUpResultSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_DETAILED_MESSAGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //任务VM运行超时
                case (int)TaskAction.VmrunTimeoutSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_VM_RUN_TIME_OUT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //任务运行结束
                case (int)TaskAction.TaskFinishedSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_OVER_INFORM, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //子任务完成
                case (int)TaskAction.TaskChildFinishedSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_CHILD_FINISHED, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //任务追踪目标数量
                case (int)TaskAction.GetTaskAimNumSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.INFORM_TASK_AIM_NUM, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                    //任务失败消息
                case (int)TaskAction.TaskFailSc:
                    GHelp.FireExecute((ushort)ViewLogicDef.TASK_FAIL_INFO, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    break;
                default:
                    GHelp.ToLaDataByFunction("OnTaskMessage", head, szValue);
                    break;
            }

        }

        public void Release()
        {
        }

        public void Update()
        {
        }
    }
}
