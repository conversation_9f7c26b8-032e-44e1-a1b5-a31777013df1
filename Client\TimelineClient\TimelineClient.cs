﻿/// <summary>
/// TimelineClient
/// </summary>
/// <remarks>
/// 2021.12.15: 创建. 王康阳 <br/>
/// timeline模块Client<br/>
/// </remarks>
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class TimelineClient : ITimelineClient
    {
        public GameObject m_mainTimeline;

        public TimelineClient()
        {
        }

        ~TimelineClient()
        {

        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName
        {
            get;
            set;
        }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState
        {
            get;
            set;
        }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress
        {
            get;
            set;
        }

        public bool Create()
        {
            return true;
        }

        public GameObject GetMainTimeline()
        {
            return m_mainTimeline;
        }

        public void SetMainTimeline(GameObject obj)
        {
            m_mainTimeline = obj;
        }

        public void DestroyMainTimeline(bool isStopScript = false)
        {
            if(m_mainTimeline != null)
            {
                opcode_timelineDef def = new opcode_timelineDef();
                def.Obj = m_mainTimeline;
                def.IsStopScript = isStopScript;
                GHelp.FireExecute((int)EntityLogicDef.ENTITY_TIMELINE_DESTORY, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, def);
                //UnityEngine.Object.Destroy(m_mainTimeline);
                m_mainTimeline = null;
            }
        }

        public void Release()
        {

        }

        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }

        public void Update()
        {

        }
    }

}
