﻿/// <summary>
/// ScreenShot
/// </summary>
/// <remarks>
/// 2021.8.30: 创建. 谌安 <br/>
/// 截屏的工具类<br/>
/// </remarks>
using System;
using UnityEngine;
using System.IO;
using System.Collections;
using GLib;

namespace GLib.Common
{
    internal class WaitForFileWriteEnd : CustomYieldInstruction
    {
        private bool m_bWait = true;
        public bool isError;
        public string errorString;
        
        public AndroidJavaObject obAndroid = null;
        public override bool keepWaiting
        {
            get
            {
                return m_bWait;
            } 
        }

        public WaitForFileWriteEnd (string filePath, byte[] data) {
            asynWriteScreenShotPicture(filePath, data);
        }

        private class AnsyWriteStateClass
        {
            public FileStream writer;
        }

        private void OnException(Exception e)
        {
            m_bWait = false;
            isError = true;
            errorString = e.Message;
            TRACE.ErrorLn(e.Message);
        }

        private void OnSucess()
        {
            m_bWait = false;
            isError = false;
            errorString = null;
        }
        private void asynWriteScreenShotPicture(string filePath, byte[] data)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                TRACE.ErrorLn("Screen Shot Error!" + "File Path Null or Empty");
                return;
            }
            try
            {
                FileStream write = new FileStream(@filePath, FileMode.Create, FileAccess.Write);
                AnsyWriteStateClass writeState = new AnsyWriteStateClass();
                writeState.writer = write;
                write.BeginWrite(data, 0, data.Length, new AsyncCallback(OnFileWriteEnd), writeState);
                
            }
            catch(Exception e) {
                OnException(e);
            }
        }
        
        private void OnFileWriteEnd(IAsyncResult res) {
            if (res != null) {
                AnsyWriteStateClass stateData = res.AsyncState as AnsyWriteStateClass;
                if (null != stateData) {
                    FileStream writer = stateData.writer;
                    if (null == writer) {
                        OnException(new Exception("System Error! file Call back fun lose state"));
                        return;
                    }
                    using (writer) {
                        try
                        { 
                            writer.EndWrite(res);
                            writer.Close();
                            OnSucess();
                        }
                        catch (Exception e) {
                            OnException(e);
                        }
                    }
                }
            }
        }
    }


    //截屏的图片目前只支持PNG
    public class CCaptureScreen:ICaptureScreen{

#if UNITY_ANDROID
        private readonly string cashPath =  "/sdcard/DCIM/Growlib/";
#else
        private readonly string cashPath = Application.persistentDataPath + "/Growlib/";
#endif
        public AndroidJavaObject obAndroid = null;
        private float m_fProgress;
        public  bool isPNGPicture(string FilePath)
        {
            string[] splits = FilePath.Split('.');
            int lenSplits = splits.Length;
            if (lenSplits >= 2)
            {
                if (splits[lenSplits - 1].ToLower() == "png")//检查是否为png格式文件
                {
                    return true;
                }
            }
            return false;
        }

        public string GenCaptureName() {
            CheckCashPath();
            string fileName = cashPath+ DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss");
            
            fileName += ".png";
            
            return fileName;
        }

      
        //bApply  是否同步到显存 如果只是要保存文件最好推荐设置成false 这样性能会高点
        //如果需要作为贴图或者UI使用 必须选择true
        private Texture2D _captureScreenToTexture(Rect screenRect, bool bApply = false) {
            Texture2D tex = new Texture2D((int)screenRect.width, (int)screenRect.height, TextureFormat.RGB24, false);
            tex.ReadPixels(screenRect, 0, 0);
            if (bApply)
            {
                tex.Apply();
            }
            return tex;
        }


        public void CaptureScreenToTexture2D(Rect screenRect, Camera renderCamera, ICaptureScreenCallback captureEndCallback)
        {
            if (null == renderCamera)
            {
                TRACE.WarningLn("截屏的相机为空");
                if (null != captureEndCallback) {
                    captureEndCallback.OnCaptureScreenFailed("Capture Camera is null");
                }
                return;
            }
            else if( !renderCamera.isActiveAndEnabled){
                TRACE.WarningLn("截屏相机没有被激活");
                if (null != captureEndCallback)
                {
                    captureEndCallback.OnCaptureScreenFailed("Capture Camera is not active");
                }
                return;
            }
            CameraCapture cap = renderCamera.GetComponent<CameraCapture>();
            if (null == cap)
            {
                cap = renderCamera.gameObject.AddComponent<CameraCapture>();
            }
            else{
                if (cap.IsCapturing) {
                    if (null != captureEndCallback)
                    {
                        GameObject.Destroy(cap);
                        captureEndCallback.OnCaptureScreenFailed(Api.NTR("已有截屏操作正在进行"));
                    }
                }
            }
            cap.capEnd = OnCapEnd;
            cap.StartCapture(screenRect,captureEndCallback);
        }


        private void OnCapEnd(CameraCapture cap, Texture2D tex, ICaptureScreenCallback callBack) {
            if (null != callBack) {
                callBack.OnCaptureSuccess("", tex);
            }
            GameObject.Destroy(cap);
        }
        
        public void CaptureScreenToTexture2D(Rect screenRect, ICaptureScreenCallback captureEndCallback) {
            GlobalGame.Instance.StartCoroutineEx(CorCaptureScreenToTexture2D(screenRect, captureEndCallback));
        }
        public void CaptureScreenToLocal(Rect screenRect, string fileName, ICaptureScreenCallback callback) {
            GlobalGame.Instance.StartCoroutineEx(CorCaptureScreenLocal(screenRect, fileName, callback));
        }
        private void  setCallbackData(bool bSuccess ,string content ,Texture2D tex,ICaptureScreenCallback captureEndCallback) {
            if (null != captureEndCallback)
            {
                if (bSuccess)
                {
                    if (RuntimePlatform.Android == Application.platform) {
                        obAndroid.CallStatic<int>("addImageToGallery", content);
                    }
                    captureEndCallback.OnCaptureSuccess(content, tex);
                }
                else {
                    captureEndCallback.OnCaptureScreenFailed(content);
                }
            }
        }

        private IEnumerator CorCaptureScreenToTexture2D(Rect screenRect, ICaptureScreenCallback captureEndCallback) {
            yield return new WaitForEndOfFrame();
            Texture2D tex = _captureScreenToTexture(screenRect, true);
            captureEndCallback.OnCaptureSuccess("",tex);
        }
        private IEnumerator CorCaptureScreenLocal(Rect screenRect, string fileName, ICaptureScreenCallback captureEndCallback) {
            //此处必须写在协程里面 否则报错ReadPixels was called to read pixels from system frame buffer, while not inside 
            yield return new WaitForEndOfFrame();

            //截屏图片格式 目前支持PNG图片
            if (string.IsNullOrEmpty(fileName) || !isPNGPicture(fileName))
            {
                TRACE.ErrorLn("file name Invalide!" + "file name is null or not a png file");
                setCallbackData(false, "file name Invalide!" + "file name is null or not a png file", null, captureEndCallback);
                yield break;
            }
            Texture2D tex = _captureScreenToTexture(screenRect, false);
            byte[] data = tex.EncodeToPNG();
            CheckCashPath();
            WaitForFileWriteEnd  screenShoot = new WaitForFileWriteEnd (fileName, data);
            yield return screenShoot;


            if (screenShoot.isError)
            {

                setCallbackData(false, screenShoot.errorString, tex,captureEndCallback);
                yield break;
            }
            setCallbackData(true, fileName, tex,captureEndCallback);
        }
        private  void  CheckCashPath() {
           if(!Directory.Exists(@cashPath)){
                Directory.CreateDirectory(@cashPath);
            }
        }

        private void onCaptureEnd(CameraCapture cap, Texture2D tex)
        {
            GameObject.Destroy(cap);
        }


        //清理截屏缓存
        public void ClearScreenShotLocalCash()
        {
            CheckCashPath();
            try
            {
                DirectoryInfo dir = new DirectoryInfo(cashPath);
                FileSystemInfo[] fileinfo = dir.GetFileSystemInfos();
                for (int i = 0; i < fileinfo.Length; i++)
                {
                    if (fileinfo[i] is FileInfo)
                    {
                        File.Delete(fileinfo[i].FullName);
                    }
                }
            }
            catch (Exception e)
            {
                TRACE.ErrorLn("Clear ScreenShot Cash Error:" + e.Message);
            }
        }

        //IModule 接口相关
        #region IMODULE  
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
       public float Progress {
            get {
                return m_fProgress;
            }
            set {
                m_fProgress = value;
            }
        }

        /// <summary>
        /// 模块创建
        /// 如果是同步模块，Create成功就表示加载成功。
        /// 如果是异步模块, Create成功不表示加载成功，必须通过模块的ModuleLoadState属性来判断。
        /// </summary>
        /// <returns></returns>
        public bool Create(){
            ClearScreenShotLocalCash();
            if (RuntimePlatform.Android == Application.platform)
            {
                AndroidJavaClass ac = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                obAndroid = ac.GetStatic<AndroidJavaObject>("currentActivity");
            }
            return true;
        }

        /// <summary>
        /// 模块释放
        /// </summary>
        public void Release() {
            
        }

        ////////////////模块驱动基础接口//////////////////////

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update() {
        }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate() {
        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate(){
        }
        #endregion

    }
}
