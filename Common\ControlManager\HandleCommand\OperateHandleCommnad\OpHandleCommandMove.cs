﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandMove : IHandleCommand
    {
        private GameObject m_target;
        private List<Vector3> m_pathList;
        private Vector3 m_orgRotate;
        private Vector3 m_targetRotate;
        private Quaternion m_targetQua;
        private Vector3 m_orgScale;
        private Vector3 m_orgPoint;
        private Vector3 m_pTargetPos;
        protected Vector3 m_ptTarget;      // 调整过后的目标点
        private bool m_bFirstCheck = true;//第一次检查是否达到终点
        private bool m_isLocal = false;
                                          // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;   //0.001f
        private float moveSpeed = 1.0f;
        private float rotateSpeed = 10f;

        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;

        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;
        private float startTime;
        public COpHandleCommandMove(SOpHandleCommand_Move data)
        {
            m_target = data.target;
            runInstance = data.runInstance;
            m_pathList = new List<Vector3>();
            if (data.pathList != null)
            {
                m_pathList.AddRange(data.pathList);
            }
            else
            {
                m_pathList.Add(data.ptTargetTile);
            }
            m_orgRotate = data.orgRotate;
            m_targetRotate = data.targetRotate;
            m_targetQua = Quaternion.Euler(m_targetRotate);
            m_orgScale = data.orgScale;
            m_orgPoint = data.orgPoint;
            m_isLocal = data.isLocal;        
            m_ptTarget = m_pathList[0];
            moveSpeed = data.speed;
            // 后续也给开放出来给设置
            rotateSpeed = moveSpeed * 10;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpMove;
        }

        public void OnPause()
        {
            
        }

        public void release()
        {
            
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                startTime = Time.time;
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
            }
            if (m_target == null)
            {
                m_isEnd = true;
                return false;
            }
            if (!m_isPlay)
            {//开始时重新设置默认坐标
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                if (m_orgPoint.x > -9999)
                {
                    if (m_isLocal)
                    {
                        m_target.transform.localPosition = m_orgPoint;
                    }
                    else
                    {
                        m_target.transform.position = m_orgPoint;
                    }
                }
                if (m_orgRotate.x > -9999)
                {
                    m_target.transform.localEulerAngles = m_orgRotate;
                }
                if (m_orgScale.x > -9999)
                {
                    m_target.transform.localScale = m_orgScale;
                }
            }
            Vector3 ptSource = m_target.transform.position;
            if (m_isLocal)
            {
                ptSource = m_target.transform.localPosition;
            }

            Vector3 TempSource = ptSource;
            Vector3 TempTarget = m_ptTarget;

            //if (m_bFirstCheck)
            {
                float dis = Vector3.Distance(TempSource, TempTarget) ;
                // 不用寻路就已经达到目标点
                if (dis <= MOVE_STATE_MIN_DISTANCE)
                {
                    if (m_isLocal)
                    {
                        m_target.transform.localPosition = m_pathList[0];
                    }
                    else
                    {
                        m_target.transform.position = m_pathList[0];
                    }
                    m_pathList.RemoveAt(0);
                    if (m_pathList.Count <= 0)
                    {
                        SetFinalRotate();
                        return true;
                    }
                    else
                    {
                        m_ptTarget = m_pathList[0];
                    }
                }
                //m_bFirstCheck = false;
            }
            Vector3 pos = Vector3.zero;
            //移动
            pos = Vector3.MoveTowards(ptSource, m_ptTarget, Time.deltaTime * moveSpeed);
            //pos.x = ptSource.x + Time.deltaTime * moveSpeed * (m_ptTarget.x-ptSource.x);
            //pos.y = ptSource.y + Time.deltaTime * moveSpeed * (m_ptTarget.y - ptSource.y);
            //pos.z = ptSource.z + Time.deltaTime * moveSpeed * (m_ptTarget.z - ptSource.z);
            ///防止移动位置不能满足MOVE_STATE_MIN_DISTANCE，超过3s直接定位
            float curTime = Time.time - startTime;
            if (curTime > 3)
            {
                pos = m_ptTarget;
                SetPos(pos);
                SetFinalRotate();
                return true;
            }
            SetPos(pos);
            DoRotate();
            return false;
        }

        void SetPos(Vector3 pos)
        {
            if (m_isLocal)
            {
                m_target.transform.localPosition = pos;
            }
            else
            {
                m_target.transform.position = pos;
            }
        }

        void DoRotate()
        {
            if (m_targetRotate != null && m_targetRotate.x > -9999)
            {
                if(m_isLocal)
                    m_target.transform.localRotation = Quaternion.Slerp(m_target.transform.localRotation, m_targetQua, Time.deltaTime * rotateSpeed);
                else
                    m_target.transform.rotation = Quaternion.Slerp(m_target.transform.rotation, m_targetQua, Time.deltaTime * rotateSpeed);
            }
        }
        void SetFinalRotate()
        {
            if (m_targetRotate != null && m_targetRotate.x > -9999)
            {
                if (m_isLocal)
                    m_target.transform.localRotation = m_targetQua;
                else
                    m_target.transform.rotation = m_targetQua;
            }
        }
               
        public void update()
        {
        }
    }
}
