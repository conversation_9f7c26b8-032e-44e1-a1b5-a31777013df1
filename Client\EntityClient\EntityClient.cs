﻿/// <summary>
/// EntityClient
/// </summary>
/// <remarks>
/// 2021.3.15: 创建. 谌安 <br/>
/// 实体客户端 <br/>
/// </remarks>
/// 
using System;
using System.Collections.Generic;
using UnityEngine;
using GLib.Common;
using game.proto;
using Google.Protobuf;
using static Game.Entity.Entity_CreateEntity.Types;

namespace GLib.Client.Entity
{
    public class CEntityClient : IEntityClient, IEventExecuteSink
    {
        /// <summary>
		/// 逻辑实体对象池最大尺寸定义
		/// </summary>
		public enum EmLogicEntityPoolMaxSize
        {
            Puppet = 50,        // 非主角玩家
            Monster = 50,       // 怪物
            Box = 50,           // 宝箱
        }

        public class ObjectPool<T>
        {
            List<T> m_objectList = null;
            int m_nCapacity = 0;

            public ObjectPool(int nCapacity = 10)
            {
                m_objectList = new List<T>();
                m_nCapacity = nCapacity;
            }

            public void SetCapacity(int nCapacity)
            {
                if (nCapacity < m_objectList.Count)
                {
                    int nDelCount = m_objectList.Count - nCapacity;
                    for (int i = 0; i < nDelCount; i++)
                    {
                        m_objectList.RemoveAt(0);
                    }
                }

                m_nCapacity = nCapacity;
            }

            public int GetCount()
            {
                return m_objectList.Count;
            }

            public int GetCapacity()
            {
                return m_nCapacity;
            }

            public void Push(T obj)
            {
                if (obj == null)
                    return;
                if (m_objectList.Count < m_nCapacity)
                    m_objectList.Add(obj);
            }

            public T Pop()
            {
                int nCount = m_objectList.Count;
                if (nCount == 0)
                    return default(T);
                T obj = m_objectList[nCount - 1];
                m_objectList.RemoveAt(nCount - 1);
                return obj;
            }

            public void Clear()
            {
                m_objectList.Clear();
            }
        }


        CEntityClient g_pEntityClient = null;

        // 当前场景ID
        uint m_dwZoneID;

        // 当前地图ID
        uint m_dwMapID;

        // 实体世界
        CEntityWorld m_EntityWorld;

        // 客户端主角
        CHero m_pHero;

        // MSG_MODULEID_ENTITYPROP
        public CEntityPropMessageHandler m_EntityPropMessageHandler;

        // MSG_MODULEID_ENTITYACTION
       // CEntityActionMessageHandler m_EntityActionMessageHandler;

        // 场景服务器时间
        int m_nCurZoneServerTime;

        // 第一次ticket值
        int m_nFirstTicketValue;

        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        private static UInt32 m_nPDBID = 0;             // 登录玩家

        // 非主角玩家对象池
        private ObjectPool<CPuppet> m_PuppetPools;
        // 怪物对象池
        private ObjectPool<CMonster> m_MonsterPools;
        // 宝箱对象池
        private ObjectPool<CBox> m_BoxPools;

        /** 
        @param   
        @param   
        @return  
        */
        public CEntityClient()
        {
            // 当前场景ID
            m_dwZoneID = DGlobalGame.INVALID_ZONEID;

            // 当前地图ID
            m_dwMapID = DGlobalGame.INVALID_MAPID;

            // 客户端主角
            m_pHero = null;

            m_PuppetPools = new ObjectPool<CPuppet>();
            m_MonsterPools = new ObjectPool<CMonster>();
            m_BoxPools = new ObjectPool<CBox>();
        }
        public void SendTest()
        {
            /*SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Gateway;
            head.wKeyModule = (int)MSG_MODULEID.Gateway;
            head.wKeyAction = (int)GatewayMessageCodes.PingRequest;
            m_EntityPropMessageHandler.OnMessage(head, null);*/

            BuildEntity(1, null, 0, 1);
        }
        /** 
        @param   
        @param   
        @return  
        */
        public bool Create()
        {

            m_EntityWorld = new CEntityWorld();
            m_EntityWorld.Init();

            m_EntityPropMessageHandler = new CEntityPropMessageHandler();
            // 场景服务器时间
            m_nCurZoneServerTime = 0;

            // 第一次ticket值
            m_nFirstTicketValue = 0;

            // 取得事件引擎
            IEventEngine pEventEngine = GlobalGame.Instance.EventEngine;
            if (pEventEngine == null)
            {
                return false;
            }

            INetManager pNetManager = GlobalGame.Instance.NetManager;
            if (pNetManager == null)
            {
                return false;
            }
            
            // 订阅 MSG_MODULEID_ENTITYPROP
            pNetManager.RegisterMessageHandler(MSG_MODULEID.Entity, m_EntityPropMessageHandler);

            // 订阅 MSG_MODULEID_ENTITYACTION
            //  pNetManager.RegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_ACTION, m_EntityActionMessageHandler);

            // 订阅 EVENT_SYSTEM_DESTORYZONE
            pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SYSTEM_DESTORYZONE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EntityClient::Create");

            //pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAME_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EntityClient::Create");

            // 设置非主角玩家对象池最大大小
            m_PuppetPools.SetCapacity((int)EmLogicEntityPoolMaxSize.Puppet);
            // 设置怪物对象池最大大小
            m_MonsterPools.SetCapacity((int)EmLogicEntityPoolMaxSize.Monster);
            // 设置宝箱对象池最大大小
            m_BoxPools.SetCapacity((int)EmLogicEntityPoolMaxSize.Box);
            return true;
        }

        /** 释放
		@param   
		@param   
		@return  
		*/
        public void Release()
        {
            INetManager pNetManager = GlobalGame.Instance.NetManager;
            if (pNetManager != null)
            {
              //  pNetManager.UnRegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_PROP);
             //   pNetManager.UnRegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_ACTION);
            }

            // 取消事件引擎
            IEventEngine pEventEngine = GlobalGame.Instance.EventEngine;
            if (pEventEngine != null)
            {
                // 订阅 EVENT_SYSTEM_DESTORYZONE
                pEventEngine.UnSubscibe(this, DGlobalEvent.EVENT_SYSTEM_DESTORYZONE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);

                // EVENT_EXIT_GAME_STATE
                //pEventEngine.UnSubscibe(this, DGlobalEvent.EVENT_EXIT_GAME_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            }

            m_PuppetPools.Clear();
            m_MonsterPools.Clear();
            m_BoxPools.Clear();

            // 实体世界
            m_EntityWorld.Close(true);
            m_EntityWorld = null;

            // 当前场景ID
            m_dwZoneID = DGlobalGame.INVALID_ZONEID;

            // 当前地图ID
            m_dwMapID = DGlobalGame.INVALID_MAPID;

            if (m_pHero != null)
            {
                m_pHero.Release();
                // 释放主角
                m_pHero = null;
            }
        }

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update() { }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate() { }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate() { }

        /** 设置当前的场景ID
        @param   
        @param   
        @return  
        */
        public void SetZoneID(uint dwZoneID)
        {
            m_dwZoneID = dwZoneID;
        }

        /** 取得当前的场景ID
        @param   
        @param   
        @return  
        */
        public uint GetZoneID()
        {
            return m_dwZoneID;
        }

        /** 设置当前的地图ID
        @param   
        @param   
        @return  
        */
        public void SetMapID(uint dwMapID)
        {
            m_dwMapID = dwMapID;
        }

        /** 取得当前的地图ID
        @param   
        @param   
        @return  
        */
        public uint GetMapID()
        {
            return m_dwMapID;
        }

        /** 取得客户端主角
        @param   
        @param   
        @return  
        */
        public IPerson GetHero()
        {
            return m_pHero;
        }

        /** 通过UID取得实体
        @param   
        @param   dwGOClass ：是为了防止外部直接用IEntity转成相应的实体接口，对服务器稳定的威胁
        @                    （tEntity_Class_Person， tEntity_Class_Monster，tEntity_Class_Equipment，tEntity_Class_Leechdom）
        @return  
        */
        public IEntity Get(Int64 uid, uint dwClass)
        {
            return m_EntityWorld.Get(uid, dwClass);
        }

        /** 取得某一类客户端实体
       @param   EntityArray ：输出实体的数组，如果为空，表示返回实体数量
       @param   nSize       ：输入＝EntityArray的大小，输出＝实体数量
       @param   dwClass     ：实体类型
       @return  
       */
        public bool Get(ref Int64[] uidEntityArray, ref int nSize, uint dwClass)
        {
            return m_EntityWorld.Get(ref uidEntityArray, ref nSize, dwClass);
        }

        /** 通过PDBID取得人物实体
        @param   
        @param   
        @return  
        */
        public IEntity Get(uint dwPDBID)
        {
            return m_EntityWorld.Get(dwPDBID);
        }

        /** 设置客户端主角
        @param   
        @param   
        @return  
        */
        public void SetHero(CHero pHero)
        {
            m_pHero = pHero;
        }

        /** 增加实体
        @param   
        @param 
        @return  
        */
        public bool Add(IEntity pEntity)
        {
            return m_EntityWorld.Add(pEntity);
        }

        /** 删除实体
        @param   
        @param   
        @return  
        */
        public void Remove(IEntity pEntity)
        {
            m_EntityWorld.Remove(pEntity);
        }

        /** 通过UID取得实体
        @param   
        @param	
        @return  
        */
        public IEntity Get(Int64 uid)
        {
            return m_EntityWorld.Get(uid);
        }

        /** 通过UID中的序列号取得实体
        @param   
        @param   
        @return  
        */
        public IEntity GetBySNO(uint dwSNO)
        {
            return m_EntityWorld.GetBySNO(dwSNO);
        }

        /** 通过名称取得实体列表
        @param   
        @param   
        @return  
        */
        public List<IEntity> GetByName(string name)
        {
            return m_EntityWorld.GetByName(name);
        }
        

        /** 向客户端内实体广播消息
        @param   
        @param   dwClass：实体类型，支持tEntity_Class_Person|tEntity_Class_Monster
        @return  
        */
        public void BroadcastMessage(uint dwMsgID, CPacketRecv pMsg, uint dwClass)
        {
            m_EntityWorld.BroadcastMessage(dwMsgID, pMsg, dwClass);
        }

        /// <summary>
        /// 设置登录玩家的dwPBDBID
        /// </summary>
        /// <param name="dwPBDBID"></param>
        public void SetHeroPBDBID(uint dwPBDBID)
        {
            // 不同的角色登录
            //TRACE.ErrorLn("==SetHeroPBDBID: old: " + m_nPDBID + ",  dwPBDBID=" + dwPBDBID );
            /*if (m_nPDBID > 0 && m_nPDBID != dwPBDBID)
            {
                // 发送事件 todo
                SEventLoginChange_C eventLogin = new SEventLoginChange_C();
                eventLogin.dwOldPBDBID = m_nPDBID;
                eventLogin.dwPBDBID = dwPBDBID;
                GameHelp.FireExecute((ushort)DGlobalEvent.EVENT_HERO_LOGIN_CHANGE,
                            (byte)EMSOURCE_TYPE.SOURCE_TYPE_PERSON, 0, eventLogin);
            }*/
            m_nPDBID = dwPBDBID;
        }

        public IEntity BuildEntity(uint dwEntityClass, IMessage pszContext)
        {
            IEntity pNewEntity = null;
            //因场景重置会存在一些不需要删除的物体，所以服务器会发一些重复数据，这个判断存在重复跳过
            EntityInfo other = pszContext as EntityInfo;
            if ((GHelp.GetEntityClient()).Get(Api.GuidCInt(other.Guid), dwEntityClass)!=null)
            {
                return null;
            }

            switch (dwEntityClass)
            {
                // 人物
                case (uint)EMtEntity_Class.tEntity_Class_Person:
                    pNewEntity = BuildPuppet(pszContext, 0);
                    break;
                // 怪物
                case (uint)EMtEntity_Class.tEntity_Class_Monster:
                case (uint)EMtEntity_Class.tEntity_Class_Trap:
                    pNewEntity = BuildMonster(pszContext, 0);
                    break;
                //载具
                case (uint)EMtEntity_Class.tEntity_Class_Tank:
                    pNewEntity = BuildTank(pszContext, 0);
                    break;
                //宝箱
                case (uint)EMtEntity_Class.tEntity_Class_Box:
                case (uint)EMtEntity_Class.tEntity_IsClass_Goods:
                    pNewEntity = BuildBox(pszContext, 0);
                    break;
                //传送门
                case (uint)EMtEntity_Class.tEntity_Class_Mast:
                    pNewEntity = BuildMast(pszContext, 0);
                    break;
                default:
                    return null;
            }

            byte bSrcType;
            // 发送事件
            SEventEntityCreateEntity_C eventcreateentity;
            eventcreateentity.uidEntity = pNewEntity.GetUID();
            bSrcType = pNewEntity.GetEventSourceType();
            long uid = pNewEntity.GetUID();
            GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_CREATEENTITY,
                                        bSrcType,
                                            (uint)UID_DATA.ANALYZEUID_SERIALNO(uid),
                                        eventcreateentity);
            return pNewEntity;
        }

        /** 通过默认数据构造实体
        @param   
        @param   
        @return  
        */
        public IEntity BuildEntity(uint dwEntityClass, CPacketRecv pszContext, int nLen, int nIsHero)
        {

            if (dwEntityClass < 0 || dwEntityClass >= (uint)EMtEntity_Class.tEntity_Class_Max)
            {
                TRACE.ErrorLn("通过默认数据构造实体失败！EntityClass = " + dwEntityClass);
                return null;
            }

            IEntity pNewEntity = null;

            switch (dwEntityClass)
            {
                // 人物
                case (uint)EMtEntity_Class.tEntity_Class_Person:
                    if (nIsHero == 1)
                    {
                        pNewEntity = BuildHero(pszContext, nLen);
                    }
                    break;
                default:
                    return null;
            }



            /* if (pNewEntity != null)
             {
                 if (dwEntityClass == (uint)EMtEntity_Class.tEntity_Class_Person)
                 {
                     if (nIsHero == 1)
                     {
                         // 发送事件,创建客户端主角
                         SEventPersonCreateHero_C eventcreatehero;
                         eventcreatehero.uidHero = pNewEntity.GetUID();
                         bSrcType = pNewEntity.GetEventSourceType();
                         uid = pNewEntity.GetUID();
                         GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_PERSON_CREATEHERO,
                             bSrcType,
                                 (uint)uid,
                             eventcreatehero);
                     }
                     else
                     {
                         // 发送事件，创建非客户端主角
                         SEventPersonCreateNotHero_C eventcreatenothero;
                         eventcreatenothero.uidNotHero = pNewEntity.GetUID();
                         bSrcType = pNewEntity.GetEventSourceType();
                         uid = pNewEntity.GetUID();
                         GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_PERSON_CREATENOTHERO,
                             bSrcType,
                             (uint)UID_DATA.ANALYZEUID_SERIALNO(uid),
                             eventcreatenothero);

                     }

                     // 发送事件，创建人物
                     SEventPersonCreate_C eventcreateperson;
                     eventcreateperson.uid = pNewEntity.GetUID();
                     bSrcType = pNewEntity.GetEventSourceType();
                     uid = pNewEntity.GetUID();
                     GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_PERSON_CREATE,
                         bSrcType,
                         (uint)UID_DATA.ANALYZEUID_SERIALNO(uid),
                         eventcreateperson);
                 }

                 

             }*/
            byte bSrcType;
            // 发送事件
            SEventEntityCreateEntity_C eventcreateentity;
            eventcreateentity.uidEntity = pNewEntity.GetUID();
            bSrcType = pNewEntity.GetEventSourceType();
            long uid = pNewEntity.GetUID();
            GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_CREATEENTITY,
                                        bSrcType,
                                            (uint)UID_DATA.ANALYZEUID_SERIALNO(uid),
                                        eventcreateentity);
            return pNewEntity;
        }

        /** 创建自已客户端
        @param   
        @param   
        @return  
        */
        IEntity BuildHero(CPacketRecv pszContext, int nLen)
        {
            // 释放以前存在的主角	
            if (m_pHero != null)
            {
                //英雄数据一直保留，当下一次英雄创建的时候，移除EntityWord数据,重新添加
                Remove(m_pHero);
                m_pHero.Release();
                m_pHero = null;
            }


            // 重新构建新主角
            CHero pHero = new CHero();
            pHero.Init();

            // 填入属性
            if (!pHero.BatchUpdateProp(pszContext, nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 填入属性");

                pHero.Release();
                pHero = null;

                return null;
            }

            // 创建
            if (!pHero.Create(pHero.GetUID()))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建");

                pHero.Release();
                pHero = null;

                return null;
            }

            // 创建公用部件
            CCreatureCommonPart pCommonPart = new CCreatureCommonPart();
            if (!pCommonPart.Create(pHero, null))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建公用部件");
                pCommonPart.Release();
                pCommonPart = null;
                pHero.Release();
                pHero = null;

                return null;
            }
            pHero.AddEntityPart(pCommonPart);

            return pHero;
        }

        /** 创建别的客户端
       @param   
       @param   
       @return  
       */
        IEntity BuildPuppet(IMessage pszContext, int nLen)
        {
            // 从缓存中取对象
            CPuppet pPuppet = (CPuppet)GetEntityFromRecyclePool(EMtEntity_Class.tEntity_Class_Person);
            if (pPuppet == null)
            {
                pPuppet = new CPuppet();
            }
            else
            {
                return BuildPuppet(pszContext, nLen, pPuppet);
            }

            pPuppet.Init();

            // 填入属性
#if OpenDebugInfo_Profiler
	        Api.PP_BY_NAME_START(Api._NGT("Puppet 填入属性"));
#endif
            if (!pPuppet.BatchUpdateProp(pszContext, nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Puppet实体失败！-- 填入属性");

                if (pPuppet != null)
                {
                    pPuppet.Release();
                    pPuppet = null;
                }
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

            //创建
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START(Api._NGT("Puppet 创建"));
#endif
            if (!pPuppet.Create(pPuppet.GetUID()))
            {
                TRACE.ErrorLn("通过默认数据构造Puppet实体失败！-- 创建" + pPuppet.GetName());
                pPuppet.Release();
                pPuppet = null;
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

            // 创建公用部件
            CCreatureCommonPart pCommonPart = new CCreatureCommonPart();
            if (!pCommonPart.Create(pPuppet, null))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建公用部件");
                pCommonPart.Release();
                pCommonPart = null;
                pPuppet.Release();
                pPuppet = null;

                return null;
            }
            pPuppet.AddEntityPart(pCommonPart);

            return pPuppet;
        }

        /** 创建别的客户端
      @param   
      @param   
      @return  
      */
        IEntity BuildPuppet(IMessage pszContext, int nLen, CPuppet pPuppet)
        {
            if (pPuppet == null)
            {
                TRACE.ErrorLn("EntityClient::BuildPuppet pPuppet == null");
                return null;
            }

            //TRACE.ErrorLn("BuildPuppetEx");
            pPuppet.Init();

            // 填入属性
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START(Api._NGT("Puppet 填入属性"));
#endif
            if (!pPuppet.BatchUpdateProp(pszContext,nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Puppet实体失败！-- 填入属性");

                if (pPuppet != null)
                {
                    pPuppet.Release();
                }
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

            //创建
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START(Api._NGT("Puppet 创建"));
#endif
            if (!pPuppet.Create(pPuppet.GetUID()))
            {
                TRACE.ErrorLn("通过默认数据构造Puppet实体失败！-- 创建" + pPuppet.GetName());
                pPuppet.Release();
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif
            // 创建公用部件
            IEntityPart pCommonPart = pPuppet.GetEntityPart((uint)EMENTITYPART.ENTITYPART_CREATURE_COMMON);
            if (!pCommonPart.Create(pPuppet, null))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建公用部件");
                pCommonPart.Release();
                pCommonPart = null;
                pPuppet.Release();
                pPuppet = null;

                return null;
            }
            return pPuppet;
        }

        /** 创建怪物
        @param   
        @param   
        @return  
        */
        IEntity BuildMonster(IMessage pszContext, int nLen)
        {
               CMonster pMonster = (CMonster)GetEntityFromRecyclePool(EMtEntity_Class.tEntity_Class_Monster);
               if (pMonster == null)
               {
                   pMonster = new CMonster();
               }
               else
               {
                   return BuildMonster(pszContext, nLen, pMonster);
               }
               pMonster.Init();
   #if OpenDebugInfo_Profiler
               Api.PP_BY_NAME_START(Api._NGT("Monster 填入属性"));
   #endif
               // 填入属性
               if (!pMonster.BatchUpdateProp(pszContext, nLen))
               {
                   TRACE.ErrorLn("通过默认数据构造Monster实体失败！-- 填入属性");

                   pMonster.Release();
                   pMonster = null;
   #if OpenDebugInfo_Profiler
                   Api.PP_BY_NAME_STOP();
   #endif
                   return null;
               }
#if OpenDebugInfo_Profiler
               Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
               Api.PP_BY_NAME_START("Monster Create");
#endif

            // 创建
            if (!pMonster.Create())
               {
                   TRACE.ErrorLn("通过默认数据构造Monster实体失败！-- 创建");

                   pMonster.Release();
                   pMonster = null;
   #if OpenDebugInfo_Profiler
                   Api.PP_BY_NAME_STOP();
   #endif

                   return null;
               }
#if OpenDebugInfo_Profiler
               Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
               Api.PP_BY_NAME_START(Api._NGT("Monster 创建公用部件"));
#endif
            // 创建公用部件
            CCreatureCommonPart pCommonPart = new CCreatureCommonPart();
            if (!pCommonPart.Create(pMonster, null))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建公用部件");
                pCommonPart.Release();
                pCommonPart = null;
                pMonster.Release();
                pMonster = null;

                return null;
            }
            pMonster.AddEntityPart(pCommonPart);

            CBuffPart buffPart = new CBuffPart();
            if (!buffPart.Create(pMonster, null))
            {
                buffPart.Release();
                buffPart = null;
                pMonster.Release();
                pMonster = null;
                return null;
            }
            pMonster.AddEntityPart(buffPart);
#if OpenDebugInfo_Profiler
               Api.PP_BY_NAME_STOP();
#endif
            return pMonster; 
        }

        /** 创建怪物
		@param   
		@param   
		@return  
		*/
        IEntity BuildMonster(IMessage pszContext, int nLen, CMonster pMonster)
        {
            //TRACE.ErrorLn("BuildMonsterEx");
            if (pMonster == null)
            {
                TRACE.ErrorLn("EntityClient::BuildMonster pMonster == null");
                return null;
            }
            pMonster.Init();

#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START(Api._NGT("Monster 填入属性"));
#endif
            // 填入属性
            if (!pMonster.BatchUpdateProp(pszContext, nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Monster实体失败！-- 填入属性");

                pMonster.Release();
                pMonster = null;
#if OpenDebugInfo_Profiler
				Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START("Monster Create");
#endif
            // 创建
            if (!pMonster.Create())
            {
                TRACE.ErrorLn("通过默认数据构造Monster实体失败！-- 创建");

                pMonster.Release();
                pMonster = null;
#if OpenDebugInfo_Profiler
				Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START(Api._NGT("Monster 创建公用部件"));
#endif
            // 创建公用部件
            IEntityPart pCommonPart = pMonster.GetEntityPart((uint)EMENTITYPART.ENTITYPART_CREATURE_COMMON);
            if (pCommonPart == null || !pCommonPart.Create(pMonster, null))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建公用部件");
                pCommonPart.Release();
                pCommonPart = null;
                pMonster.Release();
                pMonster = null;

                return null;
            }

            IEntityPart buffPart = pMonster.GetEntityPart((uint)EMENTITYPART.ENTITYPART_ENTITY_BUFF);
            if (buffPart == null ||  !buffPart.Create(pMonster, null))
            {
                buffPart.Release();
                buffPart = null;
                pMonster.Release();
                pMonster = null;
                return null;
            }
            pMonster.AddEntityPart(buffPart);
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif
            pMonster.ShowEntityName();
            return pMonster;
        }
        /** 创建宝箱
      @param   
      @param   
      @return  
      */
        IEntity BuildBox(IMessage pszContext, int nLen)
        {
            // 从缓存中取对象
            CBox pBox = (CBox)GetEntityFromRecyclePool(EMtEntity_Class.tEntity_Class_Box);
            if (pBox == null)
            {
                pBox = new CBox();
            }
            else
            {
                return BuildBox(pszContext, nLen, pBox);
            }
            pBox.Init();

            // 填入属性
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START(Api._NGT("Box 填入属性"));
#endif
            // 填入属性
            if (!pBox.BatchUpdateProp(pszContext, nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Box实体失败！-- 填入属性");
                pBox.Release();
                pBox = null;

#if OpenDebugInfo_Profiler
				Api.PP_BY_NAME_STOP();
#endif
                return null;

            }
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START("Box Create");
#endif
            // 创建
            if (!pBox.Create())
            {
                TRACE.ErrorLn("通过默认数据构造Box实体失败！-- 创建");
                pBox.Release();
                pBox = null;

#if OpenDebugInfo_Profiler
				Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif

            return pBox;
        }

        /** 创建宝箱
        @param   
        @param   
        @return  
        */
        IEntity BuildBox(IMessage pszContext, int nLen, CBox pBox)
        {
            //TRACE.ErrorLn("BuildBoxEx");
            if (pBox == null)
            {
                TRACE.ErrorLn("EntityClient::BuildBox pMonster == null");
                return null;
            }
            pBox.Init();

            // 填入属性
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START(Api._NGT("Box 填入属性"));
#endif

            // 填入属性
            if (!pBox.BatchUpdateProp(pszContext, nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Box实体失败！-- 填入属性");
                pBox.Release();
                pBox = null;

#if OpenDebugInfo_Profiler
				Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif


#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_START("Box Create");
#endif
            // 创建
            if (!pBox.Create())
            {
                TRACE.ErrorLn("通过默认数据构造Box实体失败！-- 创建");
                pBox.Release();
                pBox = null;
#if OpenDebugInfo_Profiler
				Api.PP_BY_NAME_STOP();
#endif
                return null;
            }
#if OpenDebugInfo_Profiler
			Api.PP_BY_NAME_STOP();
#endif
            return pBox;
        }

        /** 创建旗杆
       @param   
       @param   
       @return  
       */
        IEntity BuildMast(IMessage pszContext, int nLen)
        {
            CMast pMast = new CMast();
            pMast.Init();

            // 填入属性
            if (!pMast.BatchUpdateProp(pszContext,nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Mast实体失败！-- 填入属性");
                pMast.Release();
                pMast = null;

                return null;
            }

            // 创建
            if (!pMast.Create())
            {
                TRACE.ErrorLn("通过默认数据构造Mast实体失败！-- 创建");
                pMast.Release();
                pMast = null;

                return null;
            }

            return pMast;
        }

        /** 创建载具
      @param   
      @param   
      @return  
      */
        IEntity BuildTank(IMessage pszContext, int nLen)
        {
            CTank pTank = new CTank();
            pTank.Init();

            // 填入属性
            if (!pTank.BatchUpdateProp(pszContext, nLen))
            {
                TRACE.ErrorLn("通过默认数据构造Tank实体失败！-- 填入属性");
                pTank.Release();
                pTank = null;

                return null;
            }

            //找到相同的tank将旧的删除,因为服务器一些被重启，或者未能正常发销毁事件过来，需先将载具移除
            IEntity entity =  Get(pTank.GetUID());
            if (entity != null)
            {
                (entity as ITank).Release();
            }

            // 创建
            if (!pTank.Create())
            {
                TRACE.ErrorLn("通过默认数据构造Tank实体失败！-- 创建");
                pTank.Release();
                pTank = null;

                return null;
            }

            pTank.CheckRobotMaster();

            // 创建公用部件
            /*CCreatureCommonPart pCommonPart = new CCreatureCommonPart();
            if (!pCommonPart.Create(pTank, null))
            {
                TRACE.ErrorLn("通过默认数据构造Tank实体失败！-- 创建公用部件");
                pCommonPart.Release();
                pCommonPart = null;
                pTank.Release();
                pTank = null;

                return null;
            }
            pTank.AddEntityPart(pCommonPart);*/

            // 创建冷却部件
            /*IFreezeClient pFreezeClient = GHelp.GetFreezeClient();
            IFreezePart pFreezePart = pFreezeClient.CreatePart();
            if (pFreezePart == null || !pFreezePart.Create(pTank, null))
            {
                TRACE.ErrorLn("通过默认数据构造Hero实体失败！-- 创建冷却部件");

                if (pFreezePart != null)
                {
                    pFreezePart.Release();
                    pFreezePart = null;
                }

                pTank.Release();
                pTank = null;

                return null;
            }
            pTank.AddEntityPart(pFreezePart);*/

            return pTank;
        }


        public void OnExecute(UInt16 wEventID, byte bSrcType, uint dwSrcID, object pszContext)
        {
            //if (wEventID == (UInt16)DGlobalEvent.EVENT_SYSTEM_DESTORYZONE || wEventID == (UInt16)DGlobalEvent.EVENT_EXIT_GAME_STATE)
            if (wEventID == (UInt16)DGlobalEvent.EVENT_SYSTEM_DESTORYZONE)
            {
                DestoryCurScene();
            }
        }

        /** 关闭当前场景
        @param   
        @param   
        @return  
        */
        public void DestoryCurScene(bool bCleanMapInfo = true)
        {
            // 清实体列表
            m_EntityWorld.Close(false);

            if (bCleanMapInfo)
            {
                // 当前场景ID
                m_dwZoneID = DGlobalGame.INVALID_ZONEID;

                // 当前地图ID
                m_dwMapID = DGlobalGame.INVALID_MAPID;
            }
        }

        /** 取服务器时间与当地时间的差距
        @param   
        @param   
        @return  
        */
        public int GetServerTimeDiff()
        {
            // 服务器时间
            int nLocalTime = (int)Api.GetCurZoneTimeSec();

            int nServerTimeDiff = m_nCurZoneServerTime + (Api.GetTickCount() - m_nFirstTicketValue) / 1000;
            nServerTimeDiff = nServerTimeDiff - nLocalTime;

            return nServerTimeDiff;

        }

        /** 取场景服务器时间
        @param   
        @param   
        @return  
        */
        public int GetZoneServerTime()
        {
            int nValue = m_nCurZoneServerTime + (Api.GetTickCount() - m_nFirstTicketValue) / 1000;
            return nValue;
        }
      
        /** 设置时间
        @param   
        @param   
        @return  
        */
        public void SetZoneServerTime(int nZoneServerTime)
        {
            // 场景服务器时间
            m_nCurZoneServerTime = nZoneServerTime;

            // 第一次ticket值
            m_nFirstTicketValue = Api.GetTickCount();
        }

        /** 取得客户端实体世界
        @param   
        @param   
        @return  
        */
        public IEntityWorld GetEntityWorld()
        {
            return m_EntityWorld;
        }

        /**
        设置优化开关
        */
        public void SetOption(bool isOpen)
        {
        }

        /** 接受实体显示层发出的事件
         */
        public bool onEntityEvent(UInt32 entityID, UInt32 eventID, int nParam, string strParam, object ptrParam)
        {
            IEntity pEntity = GetBySNO(entityID);
            if (pEntity == null)
            {
                return false;
            }

            pEntity.sendCommand(eventID, nParam, strParam, ptrParam);

            return true;
        }

        public void ObserveMonsterHold(Int64 uidMonster, bool bIsObserve)
        {

        }

      
        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {

        }

        

        /// <summary>
        /// 回收实体，根据不同的类型处理是否回收
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        public void RecycleEntity(IEntity entity)
        {
            if (entity == null)
                return;
            if (entity.GetEntityClass().IsPerson())     // 非主角玩家
            {
                IPerson pPerson = entity as IPerson;
                if (!pPerson.IsHero())
                {
                    m_PuppetPools.Push((CPuppet)entity);
                    //TRACE.ErrorLn("回收CPuppet");
                }
            }
            else if (entity.GetEntityClass().IsMonster())// 怪物
            {
                m_MonsterPools.Push((CMonster)entity);
                //TRACE.ErrorLn("回收Monster");
            }
            else
            {

            }
        }

        /// <summary>
        /// 从回收池获取实体
        /// </summary>
        /// <param name="entityClass">实体类型</param>
        /// <returns></returns>
        public IEntity GetEntityFromRecyclePool(EMtEntity_Class entityClass)
        {
            if (entityClass == EMtEntity_Class.tEntity_Class_Person)
            {
                return m_PuppetPools.Pop();
            }
            else if (entityClass == EMtEntity_Class.tEntity_Class_Monster)
            {
                return m_MonsterPools.Pop();
            }
            else if (entityClass == EMtEntity_Class.tEntity_Class_Box)
            {
                return  m_BoxPools.Pop();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
		/// 重连成功，清除所有场景中数据，等待服务器重建
		/// </summary>
		public void ReconnectedClearAll()
        {
            List<IEntity> tmp = m_EntityWorld.GetAll();
            //特殊处理，客户端的主角实体数据层不删除，因为从场景退回主界面服务器不会重新发送主角信息
            int arrayIdx = 0;
            while (tmp.Count > arrayIdx)
            {
                IEntity e = tmp[arrayIdx];
                DestroyEntity(e);

                if (e.GetEntityType() == EMEntityType.typeHero)
                {
                    arrayIdx = 1;
                }
            };

        }

        public void DestroyEntity(IEntity entity)
        {
            IEntity pEntity = entity;
            if (pEntity == null)
            {
                return;
            }

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("DestroyEntity.Release()");
#endif
            //目前只有怪物，非主角玩家才才支持Restore
            if (pEntity.GetEntityClass().IsMonster())
            {
                pEntity.Restore();
            }
            else if (pEntity.GetEntityClass().IsPerson())
            {
                IPerson person = pEntity as IPerson;
                if (person.IsHero())
                {
                    pEntity.Release();
                }
                else
                {
                    pEntity.Restore();
                }
            }
            else
            {
                pEntity.Release();
            }

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

            pEntity = null;
        }
    }
}
