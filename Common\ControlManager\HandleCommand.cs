﻿/// <summary>
/// CHandleCommandFactory
/// </summary>
/// <remarks>
/// 2021.4.21: 创建. 谌安 <br/>
/// 操作命令对像接口<br/>
/// </remarks>

namespace GLib.Common
{
    public class CHandleCommandFactory : IHandleCommandFactory
    {

        /**
      @name            : 靠近目标命令
      @return          : 返回靠近目标命令对象指针
      */
        public IHandleCommand CreateCommand_Close(SHandleCommand_Close data)
        {
            return new CHandleCommandClose(data);
        }

        public IHandleCommand CreateCommand_WaitTime(SHandleCommand_WaitTime data)
        {
            return new CHandleCommandWaitTime(data);
        }
        
        /// <summary>
        /// 播放指定动画动作
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public IHandleCommand CreateCommand_PlayAnimated(SHandleCommand_PlayAnimated param)
        {
            return new HandleCommandPlayAnimated(param);
        }
        
        /// <summary>
        /// 设置可见状态
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public IHandleCommand CreateCommand_EntityShow(SHandleCommand_EntityShow param)
        {
            return new HandleCommandEntityShow(param);
        }
        
        /// <summary>
        /// 播放音频
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public IHandleCommand CreateCommand_PlaySound(SHandleCommand_PlaySound param)
        {
            return new HandleCommandPlaySound(param);
        }


        //--------------------------operate-------------------------
        public IHandleCommand CreateCommand_Op_Move(SOpHandleCommand_Move param)
        {
            return new COpHandleCommandMove(param);
        }

        public IHandleCommand CreateCommand_Op_Rotate(SOpHandleCommand_Rotate param)
        {
            return new COpHandleCommandRotate(param);
        }

        public IHandleCommand CreateCommand_Op_Animation(SOpHandleCommand_Animation param)
        {
            return new COpHandleCommandAnimation(param);
        }

        public IHandleCommand CreateCommand_Op_Callback(SOpHandleCommand_Callback param)
        {
            return new COpHandleCommandCallback(param);
        }

        public IHandleCommand CreateCommand_Op_Answer(SOpHandleCommand_Answer param)
        {
            return new COpHandleCommandAnswer(param);
        }

        public IHandleCommand CreateCommand_Op_ShowAndHide(SOpHandleCommand_ShowAndHide param)
        {
            return new COpHandleCommandShowAndHide(param);
        }

        public IHandleCommand CreateCommand_Op_Teleport(SOpHandleCommand_Teleport param)
        {
            return new COpHandleCommandTeleport(param);
        }

        public IHandleCommand CreateCommand_Op_StateChange(SOpHandleCommand_StateChange param)
        {
            return new COpHandleCommandStateChange(param);
        }

        public IHandleCommand CreateCommand_Op_ShaderChange(SOpHandleCommand_ShaderChange param)
        {
            return new COpHandleCommandShaderChange(param);
        }


        public IHandleCommand CreateCommand_Op_CreateEntity(SOpHandleCommand_Entity param)
        {
            return new COpHandleCommandCreateEntity(param);
        }

        public IHandleCommand CreateCommand_Op_EventSend(SOpHandleCommand_EventSend param)
        {
            return new COpHandleCommandEventSend(param);
        }

        public IHandleCommand CreateCommand_Op_ClassChange(SOpHandleCommand_ClassChange param)
        {
            return new COpHandleCommandClassChange(param);
        }

        public IHandleCommand CreateCommand_Op_HeroMove(SOpHandleCommand_HeroMove param)
        {
            return new COpHandleCommandHeroMove(param);
        }

        public IHandleCommand CreateCommand_Op_DoctorDialogue(SOpHandleCommand_DoctorDialogue param)
        {
            return new COpHandleCommandDoctorDialogue(param);
        }
        public IHandleCommand CreateCommand_Op_HightLight(SOpHandleCommand_HightLight param)
        {
            return new COpHandleCommandHightLight(param);
        }
        public IHandleCommand CreateCommand_Op_WaitTime(SOpHandleCommand_WaitTime param)
        {
            return new COpHandleCommandWaitTime(param);
        }
        public IHandleCommand CreateCommand_Op_PlaySound(SOpHandleCommand_PlaySound param)
        {
            return new COpHandleCommandPlaySound(param);
        }

        public IHandleCommand CreateCommand_Op_MoveAndShaderChange(SOpHandleCommand_MoveAndShaderChange param)
        {
            return new COpHandleCommandMoveAndShaderChange(param);
        }

        public IHandleCommand CreateCommand_Op_Slider(SOpHandleCommand_Slider param)
        {
            return new COpHandleCommandSlider(param);
        }
        public IHandleCommand CreateCommand_Op_OperateUI(SOpHandleCommand_OperateUI param)
        {
            return new COpHandleCommandOperateUI(param);
        }
        public IHandleCommand CreateCommand_Op_ShowTips(SOpHandleCommand_ShowTips param)
        {
            return new COpHandleCommandShowTips(param);
        }
        public IHandleCommand CreateCommand_Op_ModelClick(SOpHandleCommand_ModelClick param)
        {
            return new COpHandleCommandModelClick(param);
        }

        public IHandleCommand CreateCommand_Op_TweenMove(SOpHandleCommand_TweenMove param)
        {
            return new COpHandleCommandTweenMove(param);
        }
    }
}