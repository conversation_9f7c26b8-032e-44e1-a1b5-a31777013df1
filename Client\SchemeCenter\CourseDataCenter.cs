﻿/// <summary>
/// CCourseDataCenter
/// </summary>
/// <remarks>
/// 2022/12/8 14:21:33: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CourseDataCenter : ISchemeNode, ICourseDate
    {
        public const string Task_Info = "CourseData";
        public Dictionary<int, CourseData> m_TaskInfoById;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        public CourseDataCenter()
        {
            m_TaskInfoById = new Dictionary<int, CourseData>();
        }
        ~CourseDataCenter()
        {
        }
        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }
        public bool LoadScheme()
        {
            string strPath = Task_Info;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;

                CourseData courseData = new CourseData();

                courseData.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                //courseData.CourseID = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.CourseName = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.CourseIntroduction = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.CourseDifficulty = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.CourseImage = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.CaseSelectScene = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.CaseOperateScene = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.CaseModule = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.PreoperativeModule = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.ModelSelectModule = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.OperateModule = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.ModelRecycleModule = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.PostoperativeModule = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.IsOpen = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.CaseNum = pCSVReader.GetInt(nRow, tmp_col++, 0);
                courseData.LogoName = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.HospitalName = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.SmallOperationName = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.Summary = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.ExaminationSite = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.SmallOperationImage = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.TrainScnenID = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.ExaminationScnenID = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.HomepageImage = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.AssessmentTask = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.HasFenZhi = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.ZongJieTitle = pCSVReader.GetString(nRow, tmp_col++, "");
                courseData.ShuHouZongJie =pCSVReader.GetString(nRow, tmp_col++, "").Split(';');
                courseData.OperateTime = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                courseData.YiXueSuYang = pCSVReader.GetString(nRow, tmp_col++, "").Split(';');

                m_TaskInfoById.Add(courseData.Id, courseData);
            }
            return true;
        }
        public void Release()
        {
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
        }

        public CourseData GetCourseDateByID(int tID)
        {
            CourseData info = null;

            m_TaskInfoById.TryGetValue(tID, out info);

            return info;
        }

        public List<CourseData> GetAllCourseDate()
        {
            List<CourseData> temDate = new List<CourseData>();
            foreach (var item in m_TaskInfoById.Values)
            {
                temDate.Add(item);
            }
            return temDate;
        }

        public int GetCourseDateByName(string courseName)
        {
            int courseID = 0;
            foreach (var item in m_TaskInfoById.Values)
            {
                if (item.CourseName==courseName)
                {
                    courseID = item.Id;
                }
            }
            return courseID;
        }
       public bool IsHasFenZhi(int id,int trainSceneID)
        {
            foreach (var item in m_TaskInfoById)
            {
                if (item.Key == id)
                {
                    for (int j = 0; j < item.Value.TrainScnenID.Length; j++)
                    {
                        if (item.Value.TrainScnenID[j] == trainSceneID.ToString())
                        {
                            if (item.Value.HasFenZhi.Length > 0)
                            {
                                if (!string.IsNullOrEmpty(item.Value.HasFenZhi))
                                {
                                    string[] fenzhis = item.Value.HasFenZhi.Split(';');
                                    if (fenzhis[j] == "1")
                                    {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }
    }
}