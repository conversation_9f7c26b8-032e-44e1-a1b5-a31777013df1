﻿/// <summary>
/// HandleCommandPlaySound
/// </summary>
/// <remarks>
/// 2021.11.22: 创建. 王康阳 <br/>
/// 播放音效命令<br/>
/// </remarks>
using System;
using GLib;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
	public class HandleCommandPlaySound : IHandleCommand
	{
		int m_soundId;
		bool m_isPlay;
		bool m_isEnd = false; // 是否不正确的执行完指令

		public HandleCommandPlaySound(SHandleCommand_PlaySound data)
		{
			m_soundId = data.SoundId;
			m_isPlay = data.IsPlay;
		}

		public virtual EHandleCommandType GetTypeEX()
		{ 
			return EHandleCommandType.PlaySound;
		}

		public virtual CommandsType GetCommandType()
		{
			return CommandsType.Default;
		}
		public void release()
		{
			m_soundId = 0;
		}

		public bool run()
		{
			// 超时的话就直接强制结束指令
            if (m_isPlay)
			{
				GHelp.PauseMusic(m_soundId, AudioTagClass.OPCodeAudio);
				GHelp.PlayMusic(m_soundId, AudioTagClass.AudioSound,AudioTagClass.OPCodeAudio);
            }
            else
            {
				GHelp.PauseMusic(m_soundId, AudioTagClass.OPCodeAudio);
			}
			return true;
		}

		public void update()
		{

		}
		public bool finish()
		{
			return m_isEnd;
		}

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}
	}
}
