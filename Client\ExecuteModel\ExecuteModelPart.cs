﻿/// <summary>
/// CAssemblingClient
/// </summary>
/// <remarks>
/// 2021.4.26: 创建. 秦勉 <br/>
/// 执行模块Client<br/>
/// </remarks>
using ENTITY_ID = System.UInt32;
using game.proto;
using game.scene;
using GLib.Common;
using System.Collections.Generic;
using UnityEngine;
using System;
using game.common;
using System.Linq;

namespace GLib.Client
{
    public class ExecuteModelPart : IEventExecuteSink
    {
        public void Create()
        {
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EXECUTE_RECIVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_EXECUTEMODEL, 0, "收到执行命令");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EXECUTE_FAIL, (byte)EMSOURCE_TYPE.SOURCE_TYPE_EXECUTEMODEL, 0, "执行行为失败");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EXECUTE_SUCCESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_EXECUTEMODEL, 0, "执行行为成功");
        }



        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            //((CPacketRecv)pContext).Pop(out int nLen);
            //((CPacketRecv)pContext).ReadByteBuffer(out byte[] szValue, nLen);
            //SCommonAction sCommonAction = SCommonAction.Parser.ParseFrom(szValue);

            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EXECUTE_SUCCESS:
                    {
                    }
                    break;
                case (ushort)DGlobalEvent.EXECUTE_FAIL:
                    {
                        //VMOPCode_CS sCommonAction = FailedSCommonAction();
                        //sCommonAction.HatId = pContext.ToString();
                        //GHelp.GetControlManager().GetHandleMulitpleCommandManager().ExecuteCommandFail(sCommonAction.HatId);
                        //SendMessage(sCommonAction,false);
                    }
                    break;
                case (ushort)DGlobalEvent.EXECUTE_RECIVE_OPCODE:
                    {
                    }
                    break;
                case (ushort)DGlobalEvent.EXECUTE_RECIVE_CELLOPCODE:
                    {
                    }
                    break;
            }
        }



        public void update()
        {
            // executeModelBase.update();
        }

        public void Release()
        {
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EXECUTE_RECIVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EXECUTE_FAIL, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EXECUTE_SUCCESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
        }

        /// <summary>
        /// 服务端传来的值转Vector3
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static Vector3 Parse(string str)
        {
            //str = str.Replace("(", " ").Replace(")", " "); //将字符串中"("和")"替换为" "
            string[] s = str.Split(';');
            return new Vector3(float.Parse(s[0]), float.Parse(s[1]), float.Parse(s[2]));
        }

        private float DirToRotation(string Direction)
        {
            float rotation = -9999;
            if (!string.IsNullOrEmpty(Direction) && Direction != "None")
            {
                MoveDirection dir = (MoveDirection)Enum.Parse(typeof(MoveDirection), Direction);
                switch (dir)
                {
                    case MoveDirection.Right:
                        {
                            rotation = 90;
                        }
                        break;
                    case MoveDirection.Bottom:
                        {
                            rotation = 180;
                        }
                        break;
                    case MoveDirection.Left:
                        {
                            rotation = 270;
                        }
                        break;
                    case MoveDirection.Top:
                        {
                            rotation = 0;
                        }
                        break;
                    default: break;
                }
            }
            return rotation;
        }

        private float DirToRotation(string Direction, string DirectionValue)
        {
            TurnDirection dir = (TurnDirection)Enum.Parse(typeof(TurnDirection), Direction);
            float rotation = 0;
            switch (dir)
            {
                case TurnDirection.Unknow:
                    {
                        rotation = 0;
                    }
                    break;
                case TurnDirection.Left:
                    {
                        rotation = -90;
                    }
                    break;
                case TurnDirection.Right:
                    {
                        rotation = 90;
                    }
                    break;
                case TurnDirection.Backward:
                    {
                        rotation = 180;
                    }
                    break;
                case TurnDirection.ToAngle:
                    {
                        rotation = System.Convert.ToSingle(DirectionValue);
                    }
                    break;
                case TurnDirection.East:
                    {
                        rotation = 0;
                    }
                    break;
                case TurnDirection.South:
                    {
                        rotation = 270;
                    }
                    break;
                case TurnDirection.West:
                    {
                        rotation = 90;
                    }
                    break;
                case TurnDirection.North:
                    {
                        rotation = 180;
                    }
                    break;
                default: break;
            }
            return rotation;
        }
    }
}
