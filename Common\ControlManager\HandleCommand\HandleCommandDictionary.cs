﻿/// <summary>
/// CAssemblingClient
/// </summary>
/// <remarks>
/// 2021.5.7: 创建. 秦勉 <br/>
/// 执行模块Client<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public class CHandleCommandDictionary
    {
        Dictionary<string, LinkedList<IHandleCommand>> m_vCommands;
        List<string> m_vCommandKey;  // 指令的key
        List<string> EndList = new List<string>();

        List<string> m_dels = new List<string>();

        List<string> m_executeAlls = new List<string>();
        public CHandleCommandDictionary()
        {
            m_vCommands = new Dictionary<string, LinkedList<IHandleCommand>>();
            m_vCommandKey = new List<string>();
        }

        public LinkedList<IHandleCommand> TryGetCommandListByID(string hatId)
        {
            if(!m_vCommands.TryGetValue(hatId, out LinkedList<IHandleCommand> m_Commands))
            {
                m_Commands = new LinkedList<IHandleCommand>();
                m_vCommands.Add(hatId, m_Commands);
                m_vCommandKey.Add(hatId);
            }
            return m_Commands;
        }

        public IHandleCommand GetHead(string hatId)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            if (m_Commands.Count > 0)
                return m_Commands.First.Value;
            return null;
        }

        public IHandleCommand GetTail(string hatId)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            if (m_Commands.Count > 0)
                return m_Commands.Last.Value;
            return null;
        }

        public virtual EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.Max;
        }

        public virtual CommandsType GetCommandType()
        {
            return CommandsType.Default;
        }

        /// <summary>
        /// 备份命令到list
        /// </summary>
        /// <returns></returns>
        public List<IHandleCommand> BackupToList(string hatId)
        {
            if (m_vCommands == null || m_vCommands.Count == 0)
            {
                return null;
            }
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            if (m_Commands == null || m_Commands.Count == 0)
            {
                return null;
            }
            List<IHandleCommand> listCmd = new List<IHandleCommand>();
            while (m_Commands.Count > 0)
            {
                IHandleCommand pCommand = m_Commands.First.Value;
                listCmd.Add(pCommand);
                m_Commands.Remove(pCommand);
                pCommand.OnPause();
            }

            return listCmd;
        }

        //是否立即执行下一次循环，主要使用在
        private bool bExecuteNow = false;
        public void update()
        {
            bExecuteNow = false;
            m_dels.Clear();
            foreach (string hatId in m_vCommandKey)
            {
                LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
                if (m_Commands.Count <= 0)
                {
                    m_dels.Add(hatId);
                    //TRACE.ErrorLn("hatId:" + hatId);
                    GHelp.FireExecute(DGlobalEvent.EVENT_COMMANDHANDLE_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, hatId);
                    continue;
                }

                //foreach (var item in m_vCommands)
                //{
                //    foreach (var item1 in item.Value)
                //    {
                //        TRACE.ErrorLn("pCom.run():" + (int)item1.GetTypeEX());
                //    }
                //}

                if(m_executeAlls.Contains(hatId))
                {//特殊处理，跳步情况下所有指令能执行完的一次性执行 完。如果碰到类似动画需要等待时间按原来的情况处理
                    do
                    {
                        IHandleCommand pCom = m_Commands.First.Value;
                        if (pCom != null)
                        {
                            if (pCom.run())
                            {
                                if (m_Commands.Count <= 0)
                                {
                                    break;
                                }

                                if (m_Commands.First.Value == pCom)
                                {
                                    pCom.release();
                                    m_Commands.RemoveFirst();  // pCommand可能已经被销毁
                                }
                            }
                            else
                            {
                                pCom.update();
                                if (pCom.finish())
                                {
                                    pCom.release();
                                    m_Commands.RemoveFirst();  // pCommand可能已经被销毁
                                }
                                else
                                {
                                    break;
                                }
                            }
                        }
                        else
                        {
                            m_Commands.RemoveFirst();
                        }
                        } while (m_Commands.Count > 0) ;
                    }
                else
                    { 

                        IHandleCommand pCommand = m_Commands.First.Value;
                    if (pCommand != null)
                    {
                        if (pCommand.run())
                        {
                            if (m_Commands.Count <= 0)
                            {
                                continue;
                            }

                            if (m_Commands.First.Value == pCommand)
                            {
                                pCommand.release();
                                m_Commands.RemoveFirst();  // pCommand可能已经被销毁
                                //bExecuteNow = true;
                                //TRACE.ErrorLn("进入下一次计算:" + Api.GetTickCount());
                            }
                        }
                        else
                        {
                            if (m_Commands.Count <= 0 || m_Commands.First.Value != pCommand)
                                continue;

                            pCommand.update();

                            // 是否强行结束指令
                            if (pCommand.finish())
                            {
                                pCommand.release();
                                m_Commands.RemoveFirst();  // pCommand可能已经被销毁
                                //bExecuteNow = true;
                            }
                        }
                    }
                }
            }
            if (m_dels.Count > 0)
            {
                foreach (string key in m_dels)
                {
                    m_vCommandKey.Remove(key);
                    RemoveCommandByHatID(key);
                }
                m_dels.Clear();
            }

            if (bExecuteNow)
            {
                update();
            }
        }

        private void RemoveCommandByHatID(string hatID)
        {
            if (m_vCommands != null && m_vCommands.Count > 0)
            {
                m_vCommands.Remove(hatID);
            }
            m_executeAlls.Remove(hatID);
        }

        public void clearAll()
        {
            List<string> removelist = new List<string>();
            foreach (string hatId in m_vCommands.Keys)
            {
                clearAll(hatId);
                removelist.Add(hatId);
            }
            foreach(string id in removelist)
            {
                m_vCommands.Remove(id);
                m_vCommandKey.Remove(id);
            }
            m_executeAlls.Clear();
        }

        public void clearAll(string hatId)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            while (m_Commands.Count > 0)
            {
                IHandleCommand pCommand = m_Commands.First.Value;
                m_Commands.Remove(pCommand);
                if (pCommand != null)
                {
                    pCommand.release();
                }
            }
            m_executeAlls.Remove(hatId);
            //TRACE.ErrorLn("hatId:" + hatId);
            GHelp.FireExecute(DGlobalEvent.EVENT_COMMANDHANDLE_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, hatId);
        }

        public void AddAllExecute(string hatId)
        {
            m_executeAlls.Add(hatId);
        }

        public void pushBack(string hatId, IHandleCommand pCommand)
        {
            // 如果传入的指令是实体属性类别的，则删除旧的指令，用新的指令覆盖
           /* if(pCommand.GetCommandType() == CommandsType.EntityProperty)
            {
                foreach (string keyHatId in m_vCommands.Keys)
                {
                    LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(keyHatId);
                    if (m_Commands.Count <= 0)
                    {
                        continue;
                    }
                    // 存入待删除指令
                    LinkedList<IHandleCommand> deleteList = new LinkedList<IHandleCommand>();
                    foreach (IHandleCommand com in m_Commands)
                    {
                        if (com.GetCommandType() == CommandsType.EntityProperty)
                        {
                            deleteList.AddLast(com);
                        }
                    }
                    foreach (IHandleCommand com in deleteList)
                    {
                        m_Commands.Remove(com);
                    }
                    deleteList.Clear();
                }
            }*/

            LinkedList<IHandleCommand> m_Command = TryGetCommandListByID(hatId);
            m_Command.AddLast(pCommand);
        }

        public void pushFront(string hatId, IHandleCommand pCommand)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            m_Commands.AddFirst(pCommand);
        }

        public void clearBefore(string hatId, IHandleCommand pCmd)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            while (m_Commands.Count > 0)
            {
                IHandleCommand pCommand = m_Commands.First.Value;
                if (pCmd == pCommand)
                {
                    break;
                }
                m_Commands.Remove(pCommand);
                pCommand.release();
            }
        }

        public void clearAfter(string hatId, IHandleCommand pCmd)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            if(m_Commands.Count==0)
            {
                return;
            }
            LinkedListNode<IHandleCommand> currentNode = m_Commands.Last;

            LinkedListNode<IHandleCommand> tmpNode;
            while (currentNode.Previous != null && currentNode.Previous.Value.Equals(pCmd))
            {
                tmpNode = currentNode;
                currentNode = currentNode.Previous;
                tmpNode.Value.release();
                m_Commands.Remove(tmpNode);
            }

        }

        public void RemoveCommand(string hatId)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            if (m_Commands.Count <= 0)
            {
                return;
            }
            IHandleCommand pCommand = m_Commands.First();
            pCommand.release();
            m_Commands.Remove(pCommand);  // pCommand可能已经被销毁
            m_executeAlls.Remove(hatId);
        }

        // 移除某项指令
        public void RemoveCommand(string hatId, IHandleCommand pCommand)
        {
            LinkedList<IHandleCommand> m_Commands = TryGetCommandListByID(hatId);
            if (pCommand == null || m_Commands.Count <= 0)
            {
                return;
            }
            pCommand.release();
            m_Commands.Remove(pCommand);  // pCommand可能已经被销毁
        }

        public void printInfo(int time)
        {
            TRACE.WarningLn("HandleCommandPursuit更新超时 200 毫秒" + time);
        }

        public void release()
        {

        }

        public bool run()
        {
            return true;
        }

        /// <summary>
        /// 暂停,命令被备份时，会调用
        /// </summary>
        public void OnPause()
        {

        }

    }


}
