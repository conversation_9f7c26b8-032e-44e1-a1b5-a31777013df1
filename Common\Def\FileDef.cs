﻿/// <summary>
/// FileDef
/// </summary>
/// <remarks>
/// 2021/9/9 21:48:20: 创建. 王正勇 <br/>
/// 文件数据类
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    [Serializable]
    public class FileResDef
    {
        public FileInfo fileInfo;
        public FileDownDef downloadInfo;
    }
    /// <summary>
    /// 文件保存回调
    /// </summary>
    [Serializable]
    public class FileInfo
    {
        public string id;
        public string creatorId;
        public string lastModifierId;
        public string fileName;
    }
    /// <summary>
    /// 文件下载回调
    /// </summary>
    [Serializable]
    public class FileDownDef
    {
        /// <summary>
        /// 下载模式
        /// </summary>
        public string downloadMethod;
        /// <summary>
        /// 下载路径
        /// </summary>
        public string downloadUrl;
        /// <summary>
        /// 预览路径
        /// </summary>
        public string previewUrl;
        /// <summary>
        /// 文件名
        /// </summary>
        public string expectedFileName;
        public string token;

    }
    public enum FileType
    {
        Img_JPEG,
        Img_JPG,
        Img_Png,
        Audio_Mp3,
        Audio_Wav,
        Audio_Mpga,
        Video_Mp4
    }
}
