﻿/// <summary>
/// ApparatusSelectCenter
/// </summary>
/// <remarks>
/// 2022.12.28: 创建. 谌安 <br/>
/// 器械选择<br/>
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class ApparatusSelectCenter : ISchemeNode, IApparatusSelectCenter
    {
        private const string APPARATUS_INFO = "ApparatusSelect";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, List<ApparatusSelectDef>> m_apparatusByID;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        private Action m_curLoadFinishCallback;

        public ApparatusSelectCenter()
        {
            m_apparatusByID = new Dictionary<int, List<ApparatusSelectDef>>();
        }

        ~ApparatusSelectCenter()
        {
        }

        public bool Create()
        {
          
            return true;
        }

        public bool LoadScheme(int courseID, Action action)
        {
            m_curLoadFinishCallback = action;
            if (!m_apparatusByID.ContainsKey(courseID))
            {
                m_apparatusByID.Add(courseID, null);
                string strPath = "CourseCsv/"+courseID+"/"+APPARATUS_INFO;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent,courseID);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                if (m_curLoadFinishCallback != null)
                {
                    m_curLoadFinishCallback();
                }
            }
            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            List<ApparatusSelectDef> curDef = null;
            m_apparatusByID.TryGetValue((int)pData, out curDef);
            if(curDef == null)
            {
                curDef = new List<ApparatusSelectDef>();
                m_apparatusByID[(int)pData] = curDef;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                ApparatusSelectDef map = new ApparatusSelectDef();

                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.ModelID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.IsRight = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.Overdue = pCSVReader.GetInt(nRow, tmp_col++, 0);
                String tmpInfo = "";

                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SelectViewPos = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp =  tmpInfo.Split(';');
                    map.SelectViewPos = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }

                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SelectViewRotate = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.SelectViewRotate = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }

                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SelectViewScale = Vector3.one;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.SelectViewScale = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }

                map.ParentIdx = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.IconID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.RecycleID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.QualityGuaranteePeriod = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.ModelName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.ToolId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.DirtyIconID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.MovePos = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.MovePos = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }
                map.Remarks = pCSVReader.GetString(nRow, tmp_col++, "");
                map.NotTween = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.TypeData = pCSVReader.GetString(nRow, tmp_col++, "");
                map.TypeTargetIdx = pCSVReader.GetInt(nRow, tmp_col++, 0);
                curDef.Add(map);
            }

            if (m_curLoadFinishCallback != null)
            {
                m_curLoadFinishCallback();
            }
            return true;
        }

        public void Release()
        {
            m_apparatusByID.Clear();
            m_apparatusByID = null;
        }

        /// <summary>
        /// 获得器械选择数据
        /// </summary>
        /// <param name="courseID"></param>
        /// <returns></returns>
        public List<ApparatusSelectDef> GetApparatusSelectInfos(int courseID)
        {
            List<ApparatusSelectDef> curDef = null;
            m_apparatusByID.TryGetValue(courseID, out curDef);
            return curDef;
        }
    }
}
