﻿using game.common;
using game.scene;
using Game.Entity;
using Google.Protobuf;
using GLib.Common;
using System;
using System.Collections.Generic;
using static Game.Entity.Entity_CreateEntity.Types;

namespace GLib.Client.Entity
{
    public class CPuppet : CPerson
    {

		// 数值属性
		int[] m_nNumProp;
		//实体ID
		int m_entitiyID = 0;
		/** 
			   @param   
			   @param   
			   @return  
			   */
		public CPuppet()
		{
		}

		public override void Init()
		{
			base.Init();
			m_nNumProp = new int[(int)eEntityProp.EEntityMax];
			// 数值属性	
			for (int i = 0; i < (int)eEntityProp.EEntityMax; i++)
			{
				m_nNumProp[i] = 0;
			}

		}

		public override void Restore()
		{
			base.Restore();
			GlobalGame.Instance.EntityClient.RecycleEntity(this);
		}

		/** 设置数值属性
        @param   nValue ：属性值
        @param   
        @return  
        */
		public override bool SetNumProp(uint dwPropID, int nValue)
		{
			if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
			{
				return false;
			}

			m_nNumProp[dwPropID] = nValue;
			return true;
		}

		/** 取得数值属性
        @param   
        @param   
        @return  
        */
		public override int GetNumProp(uint dwPropID)
		{
			if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
			{
				return 0;
			}
			return m_nNumProp[dwPropID];
		}

		/** 设置字符属性
        @param   pszValue ：属性值
        @param   
        @return  
        */
		public override bool SetStrProp(uint dwPropID, string pszValue)
		{
			int _value = 0;
			GHelp.ConvertEntityPropInt((eEntityProp)dwPropID, pszValue, ref _value);
			m_nNumProp[dwPropID] = _value;
			return true;
		}

		/** 批量更新属性
        @param   
        @param   
        @return  
        */
		public override bool BatchUpdateProp(IMessage pszProp,int nLen)
		{
			if (pszProp != null)
			{
				EntityInfo other = pszProp as EntityInfo;
				m_uid =Api.GuidCInt(other.Guid);
				m_guid = other.Guid;
				m_Position = new UnityEngine.Vector3(other.Position.X, other.Position.Y, other.Position.Z);
				m_szName = other.Name;
				m_entitiyType = (EMEntityType)other.EntityType;
				m_entitiyID = other.ConfigId;

				foreach (ProItem item in other.Props)
				{
					SetStrProp((uint)item.PropType, item.PropValue);
				}
			}
			return true;
		}

		/** 批量更新属性
        @param   
        @param   
        @return  
        */
		public override bool BatchUpdateProp(CPacketRecv pszProp, int nLen)
		{
			if (pszProp != null)
			{
			}
			return true;
		}

		public override bool GetBasicViewInfo(ref EntityViewItem item)
		{
			base.GetBasicViewInfo(ref item);
			item.EntityType = (byte)EMEntityType.typeActor;
			item.byIsHero = 0;
			//item.prefabPath = "Prefab/Character/Player/PCW001/p_c_w_001";
			item.nSkinID = 1001;
			item.fMoveSpeed = GetMoveSpeed();
			return true;
		}
	}
}
