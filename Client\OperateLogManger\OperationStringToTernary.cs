﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GLib.Common;
namespace GLib.Client
{
    public static class OperationStringToTernary 
    {
      
      public  static int StringToTernary(OperateConditionInfo tem, string temDate, OperationLogInfo info)
        {
            ConditionDateType conditionDate= (ConditionDateType)int.Parse(info.ConditionDateType);
            string tems = tem.Condiotion;
            string[] temStrs = tems.Split('?');
            temStrs[1] = temStrs[1].Replace("(", "");
            temStrs[1] = temStrs[1].Replace(")", "");
            string[] bools = temStrs[1].Split(':');
            switch (conditionDate)
            {
                case ConditionDateType.None:
                    break;
                case ConditionDateType.Bool:
                    bool isdata;
                    try
                    {
                        isdata = Convert.ToBoolean(temDate); Convert.ToBoolean(temDate);
                    }
                    catch (Exception)
                    {

                        isdata = false;
                    }
                   
                   
                    return isdata ? int.Parse(bools[0]) : int.Parse (bools[1]);
                case ConditionDateType.Int:
                    if (info == null)
                    {
                        return 0;
                    }
                    int isSure = 0;
                    int dateNumber = int.Parse(temDate);
                    int condNum = int.Parse((string)tem.ConditionDate);
                    switch (GetIntSymbol(temStrs[0]))
                    {
                        case 2:
                            isSure = dateNumber < condNum ? int.Parse(bools[0]) : int.Parse( bools[1]);
                            break;
                        case 4:
                            isSure = dateNumber == condNum ? int.Parse(bools[0]) : int.Parse(bools[1]);
                            break;
                        case 6:
                            isSure = dateNumber <= condNum ? int.Parse(bools[0]) : int.Parse(bools[1]);
                            break;
                        case 8:
                            isSure = dateNumber > condNum ? int.Parse(bools[0]) : int.Parse(bools[1]);
                            break;
                        case 12:
                            isSure = dateNumber >= condNum ? int.Parse(bools[0]) : int.Parse(bools[1]);
                            break;
                    }
                    return isSure;
                case ConditionDateType.String:
                    if (info == null)
                    {
                        return 0;
                    }
                    string dateStr = (string)temDate;
                    return dateStr.Contains((string)tem.ConditionDate) ? int.Parse(bools[0]) : int.Parse(bools[1]);
            }
            return 0;
        }
        public  static bool StringToBool(string tem)
        {
            int index = -1;
            int.TryParse(tem, out index);
            if (index == -1)
            {
                TRACE.ErrorLn("日志条件表格三目运算配置错误" + tem);
                return false;
            }
            return index == 1 ? true : false;
        }
        public static int GetIntSymbol(string tem)
        {
            int index = 0;
            if (tem.Contains("<"))
            {
                index += 2;
            }
            if (tem.Contains("="))
            {
                index += 4;
            }
            if (tem.Contains(">"))
            {
                index += 8;
            }
            return index;
        }

    }
}
