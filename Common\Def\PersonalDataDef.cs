﻿using System;
using System.Collections.Generic;

namespace GLib.Common
{
    public enum PersonalDataMenuType
    {
        /// <summary>
        /// 功能入口
        /// </summary>
        FunctionEnter = 1,
        /// <summary>
        /// web界面
        /// </summary>
        Web = 2,
    }

    public enum PersonalDataMenuFunctionType
    {
        /// <summary>
        /// 意见反馈
        /// </summary>
        YiJianFanKui = 0,
        /// <summary>
        /// 账号安全
        /// </summary>
        ZhangHaoAnQuan = 1,
        /// <summary>
        /// 帮助中心
        /// </summary>
        BangZhuZhongXing = 2,

        /// <summary>
        /// 系统设置
        /// </summary>
        SystemSettings=3,
    }

    /// <summary>
    /// 主要用于区别传参
    /// 看服务器那边需要什么参数
    /// </summary>
    public enum PersonalDataMenuWebType
    {
        /// <summary>
        /// 平台参数
        /// </summary>
        Platform = 0,
        /// <summary>
        /// token参数
        /// </summary>
        Token = 1,
    }

    public enum PersonalSendType
    {
        All,
        /// <summary>
        /// 根据年份获取训练次数
        /// </summary>
        GetTrainByYear,
        /// <summary>
        /// 获取个人信息
        /// </summary>
        GetPersonalInfo,
    }

    public class PersonalInfo
    {
        public string StudentName { get; set; }         // 姓名
        public string StudentId { get; set; }              // 学生ID
        public string Gendar { get; set; }              // 性别
        public string City { get; set; }                // 城市
        public string SchoolName { get; set; }          // 学校名称
        public string Grade { get; set; }               // 年级
        public string Major { get; set; }               // 专业
        public string Class { get; set; }               // 班级
        /// <summary>
        /// 出生日期显示 格式："yyyy-MM-DD"
        /// </summary>
        public string BirthDay { get; set; }
        public string Phone { get; set; }               // 电话
        public string Email { get; set; }               // 邮箱
        /// <summary>
        /// 服务器头像base64数据
        /// </summary>
        public byte[] HeadIconData { get; set; } // 头像数据
    }

    [Serializable]
    public class S2C_GetPersonalToken
    {
        public S2C_TokenDetail data;
        public string message;
        public string status;
    }
    [Serializable]
    public class S2C_TokenDetail
    {
        public string user;
        public string token;
    }
    [Serializable]
    public class S2C_GetTrainData
    {
        public List<List<object>> data;
        public string message;
        public string status;
    }
    [Serializable]
    public class S2C_ChangeUserInfos
    {
        public S2C_PersonalInfo_Detail data;
        public string message;
        public string status;
    }
    [Serializable]
    public class S2C_GetMockRank
    {
        public List<S2C_MockRankItem> data;
        public string message;
        public string status;
    }
    [Serializable]
    public class S2C_MockRankItem
    {
        public int rank;
        public double score;
        public string projectName;
        public string name;
        public string dateInfo;
    }

    [Serializable]
    public class S2C_GetNotice
    {
        public List<S2C_NoticeItem> data;
        public string message;
        public string status;
    }
    [Serializable]
    public class S2C_NoticeItem
    {
        public int id;
        public string name;
        public string content;
        public bool hasPic;
    }

    [Serializable]
    public class S2C_GetPersonalInfo
    {
        public S2C_PersonalInfo_Detail data;
        public string message;
        public string status;
    }

    [Serializable]
    public class S2C_PersonalInfo_Detail
    {
        public int id;
        public string name;
        public string createTime;
        public string updateTime;
        public string username;
        public string phoneNumber;
        public int dataScope;
        public S2C_PersonalInfo_Detail_Dept dept;
        public string school;
        public string email;
        public string profession;
        public string city;
        public string birthday;
        public string gender;
        public string sclass;
    }

    public class S2C_PersonalInfo_Detail_Dept
    {
        public int id;
        public string name;
        public string createTime;
        public string updateTime;
        public int createBy;
        public int updateBy;
        public int parentID;
    }

    [Serializable]
    public class MessageData{
        public List<MessageDataBody> data;
        public string message;
        public string status;
    }

    [Serializable]
    public class MessageDataBody
    {
        public int id;
        public string name;
        public string createTime;
        public string updateTime;
        public int createBy;
        public int updateBy;
        public string content;
        public int? question;
        public bool replied;
        public string filename;
        public string creator;
    }

    [Serializable]
    public class AddQuesData {
        public string name;
        public string content;
        public string file;
    }
}
