﻿/// <summary>
/// BuildBlockDef
/// </summary>
/// <remarks>
/// 2021.4.7 <br/>
/// 积木块数据类型 <br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    [Serializable]
    public class FlayoutUpack
    {
        /// <summary>
        /// 积木块信息
        /// </summary>
        public string blockly;
        /// <summary>
        /// 积木块分类数据
        /// </summary>
        public string workspaceLayout;
        /// <summary>
        /// 积木块message消息
        /// </summary>
        public string blocklyLocales;
        /// <summary>
        /// 图片资源列表
        /// </summary>
        public string[] resoruceFiles;
        /// <summary>
        /// 资源布局
        /// </summary>
        public string blocklyField;
    }
    /// <summary>
    /// 参数类型
    /// </summary>
    public enum ParameterType
    {
        /// <summary>
        /// 下拉列表
        /// </summary>
        DropDown,
        /// <summary>
        /// 输入框
        /// </summary>
        InputField,
        /// <summary>
        /// 可内嵌积木块的输入框
        /// </summary>
        BuildBlockInput,
        /// <summary>
        /// 文本
        /// </summary>
        Text,
        /// <summary>
        /// 语句输入块
        /// </summary>
        Statement,
        /// <summary>
        /// 虚输入块
        /// </summary>
        Dummy
    }
    /// <summary>
    /// 基础参数基类
    /// </summary>
    public class BaseParameter
    {
        /// <summary>
        /// 参数类型
        /// </summary>
        public string type;
        /// <summary>
        /// 是否内嵌
        /// </summary>
        public bool IsDrop;
    }
    /// <summary>
    /// 积木块值
    /// </summary>
    public class BlockValue
    {

        /// <summary>
        /// 值控件名称
        /// </summary>
        public string ValueName;
        /// <summary>
        /// 内置类型
        /// </summary>
        public string ShadowType;
        public string ShadowId;
        /// <summary>
        /// 值类型
        /// </summary>
        public string FieldName;
        /// <summary>
        /// 使用的变量ID
        /// </summary>
        public string FieldId;
        /// <summary>
        /// 使用的变量类型
        /// </summary>
        public string FieldVariable;
        /// <summary>
        /// 值
        /// </summary>
        public object Values;
        public BlockValue ChildBlockValue;
        public List<ProgramBlock> listProgramBlock;
    }
    /// <summary>
    /// 积木块数据
    /// </summary>
    [Serializable]
    public class BlockData
    {
        /// <summary>
        /// 积木的opcode
        /// </summary>
        public string type;
        /// <summary>
        /// 显示消息，中间以空格或；隔开。
        /// </summary>
        public List<MessageArgs> listMessage;
        /// <summary>
        /// 积木块归属类型
        /// </summary>
        public string category;
        /// <summary>
        /// 积木块扩展
        /// </summary>
        public string[] extensions;


    }
    /// <summary>
    /// 显示消息和变量
    /// </summary>
    public class MessageArgs
    {
        /// <summary>
        /// 显示消息
        /// </summary>
        public string message;
        /// <summary>
        /// 积木块参数列表
        /// </summary>
        public List<BaseParameter> listBaseParameter;
    }
    /// <summary>
    /// 积木块
    /// </summary>
    public class BlockCenter
    {
        /// <summary>
        /// 字段类型
        /// </summary>
        public string FieldType;
        /// <summary>
        /// 积木块标识ID
        /// </summary>
        public string ID;
        /// <summary>
        /// 使用次数
        /// </summary>
        public int usedCountLimit;
        /// <summary>
        /// 是否是动态积木块
        /// </summary>
        public bool isDynamicComponent;
        /// <summary>
        /// 是否隐藏积木块
        /// </summary>
        public bool isHideBlcok;
        public bool isUnableDelBlock;
        /// <summary>
        /// 是否资源积木块
        /// </summary>
        public bool isResourceBlock;
        public float x;
        public float y;
        /// <summary>
        /// 积木块值
        /// </summary>
        public List<BlockValue> listValue;
    }
    /// <summary>
    /// 积木块目录
    /// </summary>
    public class BuildBlockMeun
    {
        public string BuildBlockID;
        /// <summary>
        /// 积木块数量
        /// </summary>
        public List<BlockCenter> listBlock;
        /// <summary>
        /// 颜色
        /// </summary>
        public List<string> listColour;
        /// <summary>
        /// 积木块类型名称
        /// </summary>
        public string Name;
        /// <summary>
        /// 是否隐藏
        /// </summary>
        public bool Hidden;
        /// <summary>
        /// 
        /// </summary>
        public string custom;
        /// <summary>
        /// 图标路径
        /// </summary>
        public string iconUrl;
    }
    public enum BlockMeunType
    {
        None,
        BlockMeun,
        Variable,

    }
    /// <summary>
    /// 列表框
    /// </summary>
    public class Options
    {
        public string name;
        public string src;
        public string value;
        public float? width;
        public float? height;
    }
    /// <summary>
    /// 下拉列表框
    /// </summary>
    public class DropdownParameter : BaseParameter
    {
        public string name;
        public List<Options> listOptions;

        public void Set(DropdownParameter param)
        {
            name = param.name;
            if (param.listOptions != null)
            {
                listOptions = new List<Options>();
                listOptions.AddRange(param.listOptions);
            }
            else
            {
                listOptions = null;
            }

            type = param.type;
            IsDrop = param.IsDrop;
        }
    }
    public class EntityDropdownParameter : BaseParameter
    {
        public string name;
        public string showtype;
        public List<EntityParams> param;

        public void Set(EntityDropdownParameter entityParam)
        {
            name = entityParam.name;
            showtype = entityParam.showtype;
            if (entityParam.param != null)
            {
                param = new List<EntityParams>();
                param.AddRange(entityParam.param);
            }
            else
            {
                param = null;
            }

            type = entityParam.type;
            IsDrop = entityParam.IsDrop;
        }
    }
    public class VariableDropdownParameter : BaseParameter
    {
        public string name;
        public void Set(VariableDropdownParameter variableParam)
        {
            name = variableParam.name;
            type = variableParam.type;
            IsDrop = variableParam.IsDrop;
        }
    }
    public class EntityParams
    {
        /// <summary>
        /// 实体类型
        /// </summary>
        public EMEntityType EntityType;
        /// <summary>
        /// 实体阵营
        /// </summary>
        public int EntityCamp;
        /// <summary>
        /// 实体状态
        /// </summary>
        public string EntityState;
        /// <summary>
        /// 有效范围
        /// </summary>
        public float ValidScope;
        /// <summary>
        /// 需要获取的实体ID，以，隔开。
        /// </summary>
        public string EntityName;
    }
    /// <summary>
    /// 图片参数
    /// </summary>
    public class ImageParameter : BaseParameter
    {
        public string src;
        public float? width;
        public float? height;
        public string alt;

        public void Set(ImageParameter param)
        {
            src = param.src;
            width = param.width;
            height = param.height;
            alt = param.alt;

            type = param.type;
            IsDrop = param.IsDrop;
        }
    }
    /// <summary>
    /// 文字输入框参数
    /// </summary>
    public class TextParameter : BaseParameter
    {
        public string text;

        public void Set(TextParameter param)
        {
            text = param.text;

            type = param.type;
            IsDrop = param.IsDrop;
        }
    }
    /// <summary>
    /// 输入框参数
    /// </summary>
    public class InputParameter : BaseParameter
    {
        public string name;
        public string DefaultValues;
    }
    public class StatementParameter : BaseParameter
    {
        /// <summary>
        /// 控件名称
        /// </summary>
        public string name;
        /// <summary>
        /// 空间类型
        /// </summary>
        public string check;

        public void Set(StatementParameter param)
        {
            name = param.name;
            check = param.check;

            type = param.type;
            IsDrop = param.IsDrop;
        }
    }
    public class NumberParameter : BaseParameter
    {
        /// <summary>
        /// 控件名称
        /// </summary>
        public string name;
        /// <summary>
        /// 默认值
        /// </summary>
        public float? value;
        /// <summary>
        /// 最小值
        /// </summary>
        public float? min;
        /// <summary>
        /// 最大值
        /// </summary>
        public float? max;
        /// <summary>
        /// 精度
        /// </summary>
        public float? precision;

        public void Set(NumberParameter param)
        {
            name = param.name;
            value = param.value;
            min = param.min;
            max = param.max;
            precision = param.precision;

            type = param.type;
            IsDrop = param.IsDrop;
        }
    }
    /// <summary>
    /// 定义积木块类型数据
    /// </summary>
    public class BlockMessageDef
    {
        /// <summary>
        /// 积木块消息Key
        /// </summary>
        public string BMKey;
        /// <summary>
        /// 积木块消息数据
        /// </summary>
        public string BMValue;
    }
    /// <summary>
    /// 编程区积木块xml数据
    /// </summary>
    public class BlockProgramme
    {
        /// <summary>
        /// 编程积木块配置
        /// </summary>
        public ProgrameOtherConfig programeOtherConfig;
        /// <summary>
        /// 变量积木块
        /// </summary>
        public List<Variable> listVariable;
        /// <summary>
        /// 分屏数据
        /// </summary>
        public MultiScreen multiScreen;
        /// <summary>
        /// 动态资源列表
        /// </summary>
        public List<ResourceBlocks> listResourceBlocks;
        /// <summary>
        /// 积木块使用次数
        /// </summary>
        public List<BlockUsedStatic> listBlockUsedStatic;
        /// <summary>
        /// 当前Pin的积木块帽子ID
        /// </summary>
        public string pinBlock;
        /// <summary>
        /// 编程区的积木块
        /// </summary>
        public List<ProgramBlock> listProgramBlock;
    }
    /// <summary>
    /// 编程xml中的变量
    /// </summary>
    public class Variable
    {
        public string type;
        public string id;
        public string name;
        public string variatetype;
        public bool islocal;
        public bool iscloud;
        public string value;
    }
    /// <summary>
    /// 分配管理
    /// </summary>
    public class MultiScreen
    {
        public int count;
        public int checkScreen;
        public List<BlockScreen> listScreen;
    }
    /// <summary>
    /// 分屏信息
    /// </summary>
    public class BlockScreen
    {
        public int index;
        public string name;
    }
    public class ResourceBlocks
    {
        /// <summary>
        /// 字段类型
        /// </summary>
        public string Type;
        /// <summary>
        /// 积木块标识ID
        /// </summary>
        public string ID;
        public int Index;
        /// <summary>
        /// 积木块值
        /// </summary>
        public List<BlockValue> listValue;
    }
    public class BlockUsedStatic
    {
        public string blockType;
        public int usedNumber;
    }
    public class ProgramBlock
    {
        /// <summary>
        /// 积木块信息
        /// </summary>
        public BlockCenter blockCenter;
        /// <summary>
        /// 内循环积木块
        /// </summary>
        public List<BlockStatement> listBlockStatement;
        /// <summary>
        /// 外部积木块
        /// </summary>
        public List<ProgramBlock> listNextProgramBlock;

    }
    /// <summary>
    /// 内循环积木块
    /// </summary>
    public class BlockStatement
    {
        /// <summary>
        /// 内联积木块名称
        /// </summary>
        public string name;
        /// <summary>
        /// 积木块数据
        /// </summary>
        public List<ProgramBlock> listPargramBlock;
    }
    public class ScriptTargetXml : ErrorSerial
    {
        public string id;
        public string workspaceXml;
    }
    public class ScriptTaget : ErrorSerial
    {
        public string id;
        public string workspaceXml;
    }
    public class SaveProgramXmlSerial : ErrorSerial
    {
        /// <summary>
        /// 输出码
        /// </summary>
        public int code;
        /// <summary>
        /// 消息
        /// </summary>
        public string message;
        /// <summary>
        /// 内容
        /// </summary>
        public SaveProgramXml result;
    }
    [Serializable]
    public class SaveProgramXml
    {
        public string id;
        public string creatorId;
        public int sceneType;
        public string sceneId;
        public string taskId;
        public string ramUserId;
        public string workspaceXml;
    }

    /// <summary>
    /// 下拉列表关联值
    /// </summary>
    public class DropKeyValue
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int SerialNumber;
        /// <summary>
        /// 名称
        /// </summary>
        public string Name;
        /// <summary>
        /// 图片路径
        /// </summary>
        public string ImageSrc;
        /// <summary>
        /// 值
        /// </summary>
        public string Value;
        /// <summary>
        /// 用户变量ID
        /// </summary>
        public string VariableId;
        /// <summary>
        /// 用户变量类型
        /// </summary>
        public string VariableType;
    }
    [Serializable]
    public class BlocklyFieldAppearance
    {
        /// <summary>
        /// 字段类型
        /// </summary>
        public string FieldType { get; set; }

        /// <summary>
        /// 字体名称
        /// </summary>
        public string FontName { get; set; }

        /// <summary>
        /// 字体大小
        /// </summary>
        public float? FontSize { get; set; }

        /// <summary>
        /// 字体颜色
        /// </summary>
        public string FontColour { get; set; }
        /// <summary>
        /// 宽
        /// </summary>
        public float? Width { get; set; }
        /// <summary>
        /// 高度
        /// </summary>
        public float? Height { get; set; }

        /// <summary>
        /// 上边距
        /// </summary>
        public float? TopMargin { get; set; }

        /// <summary>
        /// 下边距
        /// </summary>
        public float? BottomMargin { get; set; }

        /// <summary>
        /// 左边距
        /// </summary>
        public float? LeftMargin { get; set; }

        /// <summary>
        /// 右边距
        /// </summary>
        public float? RightMargin { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string Tag { get; set; }
    }

    /// <summary>
    /// 编程区组坐标
    /// </summary>
    public class PABGroupPosition
    {
        /// <summary>
        ///当前积木块ID
        /// </summary>
        public uint blockId;
        /// <summary>
        /// 上半部分区域
        /// </summary>
        public Rect rectUpperPos;
        /// <summary>
        /// 上半部分区域
        /// </summary>
        public Rect rectNextPos;
        /// <summary>
        /// 全部区域
        /// </summary>
        public Rect rectWholePos;
        /// <summary>
        /// 积木块组内的所有积木块
        /// </summary>
        public List<PADPosition> listPADPosition;
    }

    /// <summary>
    /// 编程区摆放坐标
    /// </summary>
    public class PADPosition
    {
        public uint blockId;
        public int iSubscript;
        public Rect rectUpperPos;
        public Rect rectNextPos;
    }
    public struct PAGroupParentNode
    {
        /// <summary>
        /// 积木块归属父节点
        /// </summary>
        public Transform parentTran;
        /// <summary>
        /// 父积木块ID
        /// </summary>
        public uint parentBlockId;
        /// <summary>
        /// 子积木块ID
        /// </summary>
        public uint childBlockId;
        /// <summary>
        /// 摆放位置索引
        /// </summary>
        public int locationIndex;
        /// <summary>
        /// 索引
        /// </summary>
        public int indexeId;
        public bool loopEmbedded;
        public void Reset()
        {
            parentTran = null;
            parentBlockId = 0;
            childBlockId = 0;
            locationIndex = 0;
            loopEmbedded = false;
        }
    }
    public struct PAGroupVariable
    {
        /// <summary>
        /// 积木块归属父节点
        /// </summary>
        public Transform orgBockTransform;
        /// <summary>
        /// 摆放位置索引
        /// </summary>
        public int locationIndex;
        public void Reset()
        {
            orgBockTransform = null;
            locationIndex = 0;
        }
    }
    public class VariablePutArea
    {
        public uint subCompoentId;
        public Rect rectPos;
    }
    public struct ReplaceVariable
    {
        public GameObject m_VariableGame;
        public int m_ISubscript;
        public void Reset()
        {
            m_VariableGame = null;
            m_ISubscript = 0;
        }
    }
    public class BlockLayoutIco
    {
        public int Id;
        public string LayoutId;
        public string IconUrl;
        public string Describe;
    }
    public class ProgrameOtherConfig
    {
        public ProgramePos m_ProgramePos;
    }
    public class ProgramePos
    {
        /// <summary>
        /// X轴位置
        /// </summary>
        public float xPos;
        /// <summary>
        /// Y轴位置
        /// </summary>
        public float yPos;
    }
}
