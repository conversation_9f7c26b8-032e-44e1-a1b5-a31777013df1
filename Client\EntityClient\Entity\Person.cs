﻿/// <summary>
/// Person
/// </summary>
/// <remarks>
/// 2021.3.15: 创建. 谌安 <br/>
/// 人物 <br/>
/// </remarks>
using game.common;
using Google.Protobuf;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client.Entity
{
    public class CPerson : IPerson
    {
        // UID
        protected Int64 m_uid;
        //GUID
        protected string m_guid;
        //实体工厂配置ID（服务器刷怪表ID）
        int m_entityFactoryConfigID = 0;
        // 玩家名字
        protected string m_szName;

        // 实体类型
        protected CEntityClass m_EntityClass;

        // 实体视图ID
        protected UInt32 m_nEntityViewID;

        // 实体部件
        protected IEntityPart[] m_pEntityPart;

        // 信号槽
        public CMessageSlot m_MessageSlot;

        // 实体技能部件
        Dictionary<long, IEntityPart> m_pSkillPart;

        // 模型ID
        protected int m_nSkinID;

        // 是否死亡态
        bool m_bIsDeathState;

        //3D坐标位置
        protected Vector3 m_Position;

        /// <summary>
        /// 3D朝向
        /// </summary>
        protected Vector3 m_Forward;

        /// <summary>
        /// 3D移动管理器
        /// </summary>
        protected C3DMoveManager m_3DMoveManager;

        /// <summary>
        /// 是否是英雄
        /// </summary>
        protected bool m_bIsHero;


        // 是否为虚拟人物
        protected bool m_bIsDeceitFlag;


        // 实体类型
        protected EMEntityType m_entitiyType;
        public CPerson()
        {
            // UID
            m_uid = 0;

            m_guid = "";

            // 玩家名字
            m_szName = string.Empty;

            // 实体视图ID
            m_nEntityViewID = 0;

            // 是否死亡态
            m_bIsDeathState = false;

            m_bIsHero = false;

            m_bIsDeceitFlag = false;

            m_pEntityPart = new IEntityPart[(int)EMENTITYPART.ENTITYPART_ENTITY_MAXID];
            // 实体部件
            for (int i = 0; i < m_pEntityPart.Length; i++)
            {
                m_pEntityPart[i] = null;
            }

            m_pSkillPart = new Dictionary<long, IEntityPart>();

            m_EntityClass = new CEntityClass();

            m_3DMoveManager = new C3DMoveManager();

            m_MessageSlot = new CMessageSlot(DGlobalMessage.MSG_ACTION_MAXID);
        }

        ~CPerson()
        {
            
        }

        public virtual void Init()
        {
            m_MessageSlot.Init(10);
            //移动管理器
            m_3DMoveManager.Create(this);

        }

        /// <summary>
        /// 还原数据状态
        /// </summary>
        public virtual void Restore()
        {
            /*if (m_bIsDeceitFlag)
            {
                // 从实体世界中移除
                CEntityWorld entityWorld = (CEntityWorld)GHelp.GetEntityClient().GetEntityWorld();
                entityWorld.RemoveDeceitEntity(this);

                // 是否为虚拟人物
                m_bIsDeceitFlag = false;
            }
            else*/
            {
                // 发消毁事件
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_START("PersonRelease_FireEvent");
#endif
                /*SEventEntityDestroryEntity_C eventdestroryentity;
                eventdestroryentity.uidEntity = m_uid;
                byte bSrcType = GetEventSourceType();
                GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
                                        eventdestroryentity);*/
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_STOP();
#endif
                m_MessageSlot.Close();
                ///////////////////////////////////////////////////////////////////
                // 释放部件
                for (int i = 0; i < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; i++)
                {
                    if (m_pEntityPart[i] != null)
                    {
                        m_pEntityPart[i].Restore();
                    }
                }

                foreach (KeyValuePair<long, IEntityPart> skillPart in m_pSkillPart)
                {
                    skillPart.Value.Restore();
                }

                // 从实体世界中移除
                ((CEntityClient)GHelp.GetEntityClient()).Remove(this);

                // 从场景中移除掉
                if (m_nEntityViewID != 0)
                {
                    EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
                    Destroy.ENTITY_ID = GetEntityViewID();
                    Destroy.ENTITY_UID = m_uid;

                    // 移除实体视图
                    GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);

                    m_nEntityViewID = 0;
                }

                if (m_3DMoveManager != null)
                {
                    m_3DMoveManager.Restore();
                }

                // UID
                m_uid = 0;

                m_guid = "";

                // 玩家名字
                m_szName = string.Empty;

                // 实体视图ID
                m_nEntityViewID = 0;

                // 是否死亡态
                m_bIsDeathState = false;

                // 是否为虚拟人物
                m_bIsDeceitFlag = false;

                m_bIsHero = false;
            }
        }

        /** 释放,会释放内存
        @param   
        @param   
        @return  
        */
        public virtual void Release()
        {
            /*if (m_bIsDeceitFlag)
            {
                // 从实体世界中移除
                CEntityWorld entityWorld = (CEntityWorld)GHelp.GetEntityClient().GetEntityWorld();
                entityWorld.RemoveDeceitEntity(this);

                // 是否为虚拟人物
                m_bIsDeceitFlag = false;
            }
            else*/
            {
                ///////////////////////////////////////////////////////////////////
                // 发消毁事件
#if OpenDebugInfo_Profiler
		        Api.PP_BY_NAME_START("PersonRelease_FireEvent");
#endif
                /*SEventEntityDestroryEntity_C eventdestroryentity;
                eventdestroryentity.uidEntity = m_uid;
                byte bSrcType = GetEventSourceType();
                GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
                                        eventdestroryentity);*/
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_STOP();
#endif

                m_MessageSlot.Close();
                ///////////////////////////////////////////////////////////////////
                // 释放部件
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_START("PersonRelease_FreePart");
#endif
                for (int i = 0; i < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; i++)
                {
                    if (m_pEntityPart[i] != null)
                    {
                        m_pEntityPart[i].Release();
                        m_pEntityPart[i] = null;
                    }
                }

                foreach (KeyValuePair<long, IEntityPart> skillPart in m_pSkillPart)
                {
                    skillPart.Value.Release();
                }
                m_pSkillPart.Clear();
#if OpenDebugInfo_Profiler
                Api.PP_BY_NAME_STOP();
#endif

                // 从实体世界中移除
                ((CEntityClient)GHelp.GetEntityClient()).Remove(this);

                // 从场景中移除掉
                if (m_nEntityViewID != 0)
                {
                    EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
                    Destroy.ENTITY_ID = GetEntityViewID();
                    Destroy.ENTITY_UID = m_uid;

                    // 移除实体视图
                    GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);

                    m_nEntityViewID = 0;
                }

                if (m_3DMoveManager != null)
                {
                    m_3DMoveManager.Restore();
                }
            }
        }

        /** 创建
        @param   
        @param   
        @return  
        */
        public virtual bool Create(Int64 uid)
        {
            /*if (uid == Guid.Empty)
            {
                return false;
            }*/
            m_uid = uid;

            // 实体类型
            m_EntityClass.SetClass((int)EMtEntity_Class.tEntity_Class_Person);

            // 创建实体视图
            if (!CreateView())
            {
                return false;
            }

            // 添加到实体世界中
            if (!((CEntityClient)GHelp.GetEntityClient()).Add(this))
            {
                return false;
            }

            // 创建实体视图（裸体）
            /*m_nDefaultResID = GHelp.GetPersonDefaultResourceID(GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_VOCATION), GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_SEX));
            ITitlePart pTitlePart = (ITitlePart)GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TITLE);
            // 修改显示
            if (pTitlePart != null)
            {
                pTitlePart.SetName((int)NameIndexType.NameIndex_PersonName, (int)ETitleEffect.ETitleEffect_PersonName, m_szName);
            }*/

            /*if (IsFighting())
            {
                // 通知表现层进入战斗状态
                GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_CHANGE_FIHGTMODE, 1, "", null);
            }

            // 如果已经死亡，直接播死亡动作
            if (m_nCurControlState == (int)EMCREATURE_CONTROLSTATE.CREATURE_CONTROLSTATE_DIE)
            {
                //参数填1表示不播放死亡过程，直接倒地，填0表示有过程
                GHelp.sendEntityCommand(m_nEntityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_ENTITY_DEAD, 1, "", null);
                SetDeathState(true);
            }*/

            return true;
        }

        public void SetDeathState(bool bDeath)
        {
            m_bIsDeathState = bDeath;
        }

        /// <summary>
        /// 获取3D移动实体管理器
        /// </summary>
        /// <returns></returns>
        public I3DMoveManager Get3DMoveManager()
        {
            return m_3DMoveManager;
        }

        /** 取得实体类型
        @param   
        @param   
        @return  
        */
        public IEntityClass GetEntityClass()
        {
            return m_EntityClass;
        }

        public EMEntityType GetEntityType()
        {
            return m_entitiyType;
        }

        /** 取得事件源类型,SOURCE_TYPE_PERSON, SOURCE_TYPE_MONSTER ... ...
      @param   
      @param   
      @return  
      */
        public byte GetEventSourceType()
        {
            return (byte)EMSOURCE_TYPE.SOURCE_TYPE_PERSON;
        }

        /** 取得UID
        @param   
        @param   
        @return  
        */
        public Int64 GetUID()
        {
            return m_uid;
        }

        public string GetStrGUID()
        {
            return m_guid;
        }

        //获得阵营
        public int GetCamp()
        {
            return GetNumProp((uint)eEntityProp.EEntityCamp);
        }

        /// <summary>
        /// 获取3D坐标
        /// </summary>
        /// <returns></returns>
        public Vector3 GetPosition()
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return Vector3.zero;
            Vector3 pos = pEntityFactory.GetPosition(m_nEntityViewID);
            return pos;
        }

        /// <summary>
        /// 设置3D坐标
        /// </summary>
        public void SetPosition(Vector3 vPos)
        {
        }

        /// <summary>
        /// 获取是否正在跳跃
        /// </summary>
        /// <returns></returns>
        public EntityJumpDef GetJumpState()
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return EntityJumpDef.None;
            EntityJumpDef isJump = pEntityFactory.GetJumpState(m_nEntityViewID);
            return isJump;
        }

        /// <summary>
		/// 设置跳跃状态
		/// </summary>
		/// <param name="state"></param>
		/// <returns></returns>
		public bool SetJumpState(EntityJumpDef state)
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return false;
            pEntityFactory.SetJumpState(m_nEntityViewID, state);
            return true;
        }

        /// <summary>
        /// 获取朝向
        /// </summary>
        /// <returns></returns>
        public Vector3 GetForward()
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return Vector3.zero;
            Vector3 forward = pEntityFactory.GetForward(m_nEntityViewID);
            return forward;
        }

        /// <summary>
        /// 设置朝向
        /// </summary>
        /// <param name="vForward"></param>
        public void SetForward(Vector3 vForward)
        {
            m_Forward = vForward;
        }

        /** 取得人物名字
      @param   
      @param   
      @return  
      */
        public string GetName()
        {
            return m_szName;
        }

        /** 是否已死亡
       @param   
       @param   
       @return  
       */
        public bool IsDeath()
        {
            return m_bIsDeathState;
        }

        /** 取得实体视图ID接口
       @param   
       @param   
       @return  
       */
        public UInt32 GetEntityViewID()
        {
            return m_nEntityViewID;
        }

        /** 是否正在移动
      @param   
      @param   
      @return  
      */
        public bool IsMoving()
        {
            if (m_nEntityViewID == 0)
            {
                return false;
            }

            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return false;

            return pEntityFactory.isMoving(m_nEntityViewID);
        }

        // 创建显示层实体
        public bool CreateView()
        {
            EntityViewItem item = GHelp.GetObjectItemEx<EntityViewItem>();
            if (!GetBasicViewInfo(ref item))
            {
                TRACE.ErrorLn("Person::createView getBasicViewInfo failed!");
                return false;
            }

            // 创建EntityView
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, 0, "", item);
            m_nEntityViewID = item.EntityViewID;
            if (m_nEntityViewID == 0)
            {
                TRACE.ErrorLn("Person::CreateView invalid viewID=" + m_uid);
                return false;
            }

            // 设置出生坐标
            cmd_creature_rigidbody_sync data = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
            data.bNotGround = false;
            data.nEntityID = GetEntityViewID();
            data.fPosition_x = m_Position.x;
            data.fPosition_y = m_Position.y;
            data.fPosition_z = m_Position.z;
            GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", data);

            // 加载皮肤模型
            GHelp.ChangeEntitiyPart(GetEntityViewID(), EntityParts.EntityPart_Body, item.nSkinID);
            //TRACE.TraceLn("createView id=" + m_uid);
            GHelp.RecycleObjectItemEx<EntityViewItem>(item);
            return true;
        }

        public virtual bool GetBasicViewInfo(ref EntityViewItem item)
        {
            item.EntityType = (byte)EMEntityType.typeActor;
            item.EntityViewID = 0;
            item.UID = m_uid;
            item.byIsHero = 0;
            item.szName = m_szName;
            item.fMoveSpeed = GetMoveSpeed();
            //设置角度
            /*UInt32 dwMapID = GlobalGame.Instance.EntityClient.GetMapID();
            SMapSchemeInfo mapInfo = GlobalGame.Instance.SchemeCenter.GetMapSchemeInfo((int)dwMapID);
            if (mapInfo != null && IsHero())
            {
                item.Angle = mapInfo.fAngle;
                int nAngle = GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_ANGLE);
                // 优先使用配置表内的角度
                if (mapInfo.fAngle == 0)
                {
                    item.Angle = nAngle;
                }
            }
            else
            {
                // 非主角先随机一个角度，以后服务器实现了，用服务器的角度
                //item.Angle = UnityEngine.Random.Range(0, 360);
                item.Angle = GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_ANGLE);
            }*/

            return true;
        }

        /// <summary>
		/// 是否能移动
		/// </summary>
		/// <returns></returns>
		public bool CanMove()
        {
            if (!m_bIsHero)
                return false;
            return true;
        }

        /// <summary>
        /// 判断是否是英雄
        /// </summary>
        /// <returns></returns>
        public bool IsHero()
        {
            return m_bIsHero;
        }

        public bool AddEntityPart(IEntityPart pEntityPart)
        {
            if (pEntityPart == null)
            {
                return false;
            }

            uint nPartID = pEntityPart.GetPartID();
            if (nPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || nPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
            {
                return false;
            }

            if (m_pEntityPart[nPartID] != null)
            {
                return false;
            }

            m_pEntityPart[nPartID] = pEntityPart;

            return true;
        }

        /** 移除实体部件
      @param   
      @param   
      @return  
      */
        public bool RemoveEntityPart(uint dwPartID)
        {
            if (dwPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || dwPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
            {
                return false;
            }

            m_pEntityPart[dwPartID] = null;

            return true;
        }

        /** 取得实体部件
        @param   
        @param   
        @return  
        */
        public IEntityPart GetEntityPart(uint dwPartID)
        {
            if (dwPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || dwPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
            {
                return null;
            }

            return m_pEntityPart[dwPartID];
        }

        public IEntityPart GetSkillPart(long skillID = 1)
        {
            IEntityPart part = null;

            m_pSkillPart.TryGetValue(skillID, out part);

            return part;
        }

        /// <summary>
		/// 获得全部的技能部件
		/// </summary>
		/// <returns></returns>
		public Dictionary<long, IEntityPart> GetAllSkillParts()
        {
            return m_pSkillPart;
        }

        /// <summary>
        /// 获得或者创建技能部件
        /// </summary>
        /// <param name="dwPartID"></param>
        /// <param name="uid"></param>
        /// <returns></returns>
        public IEntityPart GetOrCreateSkillPart(long skillID = 1)
        {
            IEntityPart part = null;

            return part;
        }


        public bool AddSkillPart(IEntityPart pEntityPart, long skillID = 1)
        {
            if (pEntityPart as ISkillPart == null)
            {
                return false;
            }

            if (m_pSkillPart.ContainsKey(skillID))
            {
                return false;
            }

            m_pSkillPart.Add(skillID, pEntityPart);

            return true;
        }
        public int GetEntityFactoryConfigID()
        {
            return 0;
        }
        public float GetMoveSpeed()
        {
            return GetNumProp((uint)eEntityProp.EEntityMoveSpeed) / DGlobalGame.FLOAT_SCALE_SIZE;
        }


        /** 设置数值属性
       @param   nValue ：属性值
       @param   
       @return  
       */
        public virtual bool SetNumProp(uint dwPropID, int nValue)
        {
            return false;
        }

        /** 取得数值属性
        @param   
        @param   
        @return  
        */
        public virtual int GetNumProp(uint dwPropID)
        {
            return 0;
        }

        public virtual bool SetStrProp(uint dwPropID, string pszValue)
        {
            return true;
        }

        /** 批量更新属性
      @param   
      @param   
      @return  
      */
        public virtual bool BatchUpdateProp(CPacketRecv pszProp, int nLen)
        {
            return false;
        }

        /** 批量更新属性
     @param   
     @param   
     @return  
     */
        public virtual bool BatchUpdateProp(IMessage pszProp, int nLen)
        {
            return false;
        }

        public IMessage GetConfigInfo()
        {
            return GHelp.GetConfigItem(m_entitiyType, 0);
        }

        public int GetModelID()
        {
            return m_nSkinID;
        }

        public int GetConfigID()
        {
            return 0;
        }

        public virtual bool OnMessage(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
            return m_MessageSlot.Fire(dwMsgID, pGameMsgHead, pszMsg);;
        }

        public bool Subscibe(uint dwMsgID, IMessageVoteSink pVoteSink, string pszDesc)
        {
            return m_MessageSlot.Subscibe(dwMsgID, pVoteSink, pszDesc);
        }

        public bool UnSubscibe(uint dwMsgID, IMessageVoteSink pVoteSink)
        {
            return m_MessageSlot.UnSubscibe(dwMsgID, pVoteSink); 
        }

        public bool Subscibe(uint dwMsgID, IMessageExecuteSink pExecuteSink, string pszDesc)
        {
            return m_MessageSlot.Subscibe(dwMsgID, pExecuteSink, pszDesc);
        }

        public bool UnSubscibe(uint dwMsgID, IMessageExecuteSink pExecuteSink)
        {
            return m_MessageSlot.UnSubscibe(dwMsgID, pExecuteSink);
        }

        public void sendCommand(uint cmdid, int nParam, string strParam, object ptrParam)
        {
            // 部件接收命令
            for (int id = 0; id < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; ++id)
            {
                if (m_pEntityPart[id] != null)
                {
                    if (m_pEntityPart[id].onCommand(cmdid, nParam, strParam, ptrParam))
                    {
                        return;
                    }
                }
            }
        }
    }
}
