﻿/// <summary>
/// EntityPropSubscribe
/// </summary>
/// <remarks>
/// 2021.4.21: 创建. 谌安 <br/>
/// 实体属性更新事件<br/>
/// </remarks>
using game.common;
using System;
using System.Collections.Generic;


namespace GLib.Common
{
    /// <summary>
    /// 属性变化回调函数
    /// </summary>
    /// <param name="nProp">当前监听的属性ID</param>
    public delegate void PropertyChangedFunctor(uint nProp);

    /// <summary>
    /// 属性连接器
    /// </summary>
    public class PropertyConnection
    {
        public uint m_nIndex = 0;                            // 属性ID  
	    public int m_nLast = 0;                             // 属性值
	    public PropertyChangedFunctor m_pFunctor = null;    // 回调对象
	    public PropertyConnectionList m_pConList = null;    // 实体连接器列表

	    public PropertyConnection(uint nIdx, int nLast, PropertyChangedFunctor pFunc, PropertyConnectionList pConlist)
        {
            m_nIndex = nIdx;
            m_nLast = nLast;
            m_pFunctor = pFunc;
            m_pConList = pConlist;
        }
    }/// class 

    /// <summary>
    /// 实体连接器列表
    /// </summary>
    public class PropertyConnectionList: IEventExecuteSink
    {
	    Int64 m_Uid;                                    // 实体UID
        List<PropertyConnection> m_ConList = null;      // 连接器列表
	    byte  m_SouceType;                              // 源类型

	    public PropertyConnectionList()
        {
        }

        public void Create(Int64 uid)
        {
            m_Uid = uid;
            m_ConList = new List<PropertyConnection>();
            m_SouceType = 0;

            Subscribe();
        }

	    public void Release()
        {
            UnSubscribe();

            m_SouceType = 0;
            if(m_ConList!=null)
            {
                m_ConList.Clear();
                m_ConList = null;
            }
            m_Uid = 0;
        }
    
        public Int64 uid
        {
            get { return m_Uid; }
            set { m_Uid = value; }
        }

        public void Add(PropertyConnection con)
	    {
		    m_ConList.Add(con);
	    }
	    public void Remove(PropertyConnection con)
	    {
		    m_ConList.Remove(con);
	    }

	    public bool Subscribe()
        {
            IEntity entity = GlobalGame.Instance.EntityClient.Get(m_Uid);
            if (entity == null)
            {
                m_SouceType = 0;
                return false;
            }

            m_SouceType = entity.GetEventSourceType();
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP, m_SouceType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_Uid), Api.NTR("属性更新"));

            return true;
        }

	    public bool UnSubscribe()
        {
            if (m_SouceType == 0)
                return false;

            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP, m_SouceType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_Uid));

            return true;
        }

	    public Int32 Size()
	    {
		    return m_ConList.Count;
	    }

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            if (wEventID != (ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP)
            {
                TRACE.ErrorLn("PropertyConnectionList.OnEcecute failed! wEventID!=EVENT_ENTITY_UPDATEPROP. wEventID = " + wEventID);
                return;
            }

            IEntity entity = GlobalGame.Instance.EntityClient.Get(m_Uid);
            if (entity == null)
            {
                return;
            }

            // 回调
            for (int i = 0; i < m_ConList.Count; i++)
            {
                PropertyConnection conn = m_ConList[i];
                int new_val = entity.GetNumProp((uint)conn.m_nIndex);
                if(new_val != conn.m_nLast)
                {
                    conn.m_pFunctor(conn.m_nIndex);
                    conn.m_nLast = new_val;
                }

                // 如果注册了最大属性变化，都触发回调，此处特殊处理
                if ((uint)conn.m_nIndex == (uint)eEntityProp.EEntityMax)
                {
                    conn.m_pFunctor(conn.m_nIndex);
                    conn.m_nLast = new_val;
                }
            }
        }
    }/// class 
    
    /// <summary>
    /// 管理所有实体的属性订阅
    /// </summary>
    public class PropertySubscriberManager : Singleton<PropertySubscriberManager>
    {
        List<PropertyConnectionList> m_listSubscriber = new List<PropertyConnectionList>();

        /// <summary>
        /// 监听属性变化
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="propid"></param>
        /// <param name="func"></param>
        /// <returns>返回链接对象，取消监听需要该对象</returns>
        public object Subscribe(Int64 uid, uint propid, PropertyChangedFunctor func)
        {
            if ((func == null) || ((uint)propid < 1 && (uint)propid > (uint)eEntityProp.EEntityMax))
            {
                TRACE.ErrorLn("PropertySubscriberManager_Subscribe failed! An Invalid UID or Callbackfunc. propid = " + propid);
                return null;
            }
                
            IEntity entity = GlobalGame.Instance.EntityClient.Get(uid);
            if (entity == null)
            {
                TRACE.ErrorLn("PropertySubscriberManager_Subscribe failed! An Invalid UID. propid = " + propid);
                return null;
            }

            PropertyConnectionList conList = findConnectionList(uid);
            if (conList == null)
            {
                conList = new PropertyConnectionList();
                conList.Create(uid);

                m_listSubscriber.Add(conList);
            }

            int cur = entity.GetNumProp(propid);
            PropertyConnection con = new PropertyConnection(propid, cur, func, conList);
            if (con == null)
            {
                return null;
            }
            conList.Add(con);

            return con;
        }

        /// <summary>
        /// 取消监听
        /// </summary>
        /// <param name="ob">监听时返回的对象</param>
        public void UnSubscribe(object ob)
        {
            if (ob == null)
            {
                return;
            }

            if (ob is PropertyConnection)
            {
                PropertyConnection conn = (PropertyConnection)ob;

                PropertyConnectionList conList = conn.m_pConList;
                conList.Remove(conn);

                if (conList.Size() == 0)
                {
                    conList.Release();
                    m_listSubscriber.Remove(conList);
                }
            }
        }

        
        public PropertyConnectionList findConnectionList(Int64 uid)
        {
            for (int i = 0; i < m_listSubscriber.Count; i++ )
            {
                PropertyConnectionList conlist = m_listSubscriber[i];
                if(conlist.uid == uid)
                {
                    return conlist;
                }
            }

            return null;
        }

        public void Release()
        {
            for (int i = 0; i < m_listSubscriber.Count; i++)
            {
                PropertyConnectionList conlist = m_listSubscriber[i];
                conlist.Release();
                conlist = null;
            }
            m_listSubscriber.Clear();
        }

    }/// classs

    

}/// namespace 