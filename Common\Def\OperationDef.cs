﻿/// <summary>
/// RevocationDef
/// </summary>
/// <remarks>
/// 2021/7/7 15:27:39: 创建. 王正勇 <br/>
/// 撤销功能相关数据类
/// </remarks>
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    /// <summary>
    /// 操作类型
    /// </summary>
    public enum OperationType
    {
        /// <summary>
        /// 属性操作
        /// </summary>
        ParameterOperation,
        /// <summary>
        /// 积木块操作
        /// </summary>
        BlockOperation
    }
    public enum BlockOperationType
    {
        None,
        /// <summary>
        /// 新增拖动积木块
        /// </summary>
        AddBlock,
        /// <summary>
        /// 拖动积木块
        /// </summary>
        DragBlock,
        /// <summary>
        /// 删除积木块
        /// </summary>
        DeleteBlock
    }
    public enum ParameterOperationType
    {
        /// <summary>
        /// 修改只
        /// </summary>
        UpdateValue,
    }
    public class InactiveBlock
    {
        public uint m_BlockProgramId;
        public int m_Index;
    }
    /// <summary>
    /// 当前操作的积木块
    /// </summary>
    public class NowOperationBlock
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public BlockOperationType m_BlockOperationType;
        public string m_nowBlockId;
        public uint m_nowBlockNodeId;
        /// <summary>
        /// 操作积木块前的数据
        /// </summary>
        public OperationFrontBlock m_OperationFrontBlock;
    }
    /// <summary>
    /// 操作前的积木块数据
    /// </summary>
    public class OperationFrontBlock
    {
        /// <summary>
        /// 积木块摆放的位置
        /// </summary>
        public Vector2 m_BlockPos;
        /// <summary>
        /// 父积木块ID
        /// </summary>
        public string m_ParentBlockId;
        /// <summary>
        /// 父积木块的工厂ID
        /// </summary>
        public uint m_ParentBlockNodeId;
        /// <summary>
        /// 所以位置，-1表示无索引
        /// </summary>
        public int m_indexes;
        /// <summary>
        /// 当前操作积木块组的数据
        /// </summary>
        public List<ProgramBlock> listProgramBlock;
        /// <summary>
        /// 摆放前内嵌到的属性
        /// </summary>
        public InactiveBlock m_InactiveBlock;
    }
    public class OperationLaterBlock
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public BlockOperationType m_BlockOperationType;
        /// <summary>
        /// 积木块摆放的位置
        /// </summary>
        public Vector2 m_BlockPos;
        /// <summary>
        /// 父积木块ID
        /// </summary>
        public string m_ParentBlockId;
        /// <summary>
        /// 索引位置，-1表示无索引
        /// </summary>
        public int m_indexes;
        /// <summary>
        /// 父积木块的工厂ID
        /// </summary>
        public uint m_ParentBlockNodeId;
        /// <summary>
        /// 连接到拖拽积木块后的积木块
        /// 如果拖拽积木块放到父积木块上
        /// 那么便是父积木块原来的子积木块的ID
        /// 如果没有父积木块，那么便是拼接到拖拽积木块上的积木块ID
        /// </summary>
        public string m_ConnectEndBlockId;
        /// <summary>
        /// 父节点的原子节点ID
        /// </summary>
        public uint m_ConnectEndBlockNodeId;
        public InactiveBlock m_InactiveBlock;
    }

    public class NowOperationParameter
    {
        public ParameterOperationType m_ParameterOperationType;
        public uint m_BlockParameterId;
        /// <summary>
        /// 操作前的属性值
        /// </summary>
        public OperationParameter m_OperationFrontParameter;
    }
    /// <summary>
    /// 属性的值
    /// </summary>
    public class OperationParameter
    {
        public string m_Value;
    }

    public class OperationDef
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public OperationType m_OperationType;
        /// <summary>
        /// 当前操作的积木块
        /// </summary>
        public NowOperationBlock m_NowOperationBlock;
        /// <summary>
        /// 操作积木块后的积木块数据
        /// </summary>
        public OperationLaterBlock m_OperationLaterBlock;


        /// <summary>
        /// 当前操作的属性
        /// </summary>
        public NowOperationParameter m_NowOperationParameter;
        /// <summary>
        /// 操作后的属性值
        /// </summary>
        public OperationParameter m_OperationLaterParameter;
    }
}
