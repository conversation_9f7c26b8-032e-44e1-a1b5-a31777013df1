﻿/// <summary>
/// SceneClient
/// </summary>
/// <remarks>
/// 2021.6.21: 创建. 吴航 <br/>
/// 场景客户端 <br/>
/// </remarks>
/// 
using GLib.Common;
using game.proto;
using game.scene;
using System.Collections.Generic;
using game.common;

namespace GLib.Client
{
    public class SceneClient : ISceneClient, IMessageHandler, IEventExecuteSink
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        private bool m_isReady = false;

        private string m_curSceneGUID = "";
        public SceneClient()
        {

        }

        public bool Create()
        {
            IEventEngine pEventEngine = GlobalGame.Instance.EventEngine;
            if (pEventEngine == null)
            {
                return false;
            }
            INetManager pNetManager = GlobalGame.Instance.NetManager;
            if (pNetManager == null)
            {
                return false;
            }

            pNetManager.RegisterMessageHandler(MSG_MODULEID.Scene, this);
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_CHANGE_ROLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
            return true;
        }

        /** 释放
		@param   
		@param   
		@return  
		*/
        public void Release()
        {
            INetManager pNetManager = GlobalGame.Instance.NetManager;
            if (pNetManager != null)
            {
                pNetManager.UnRegisterMessageHandler(MSG_MODULEID.Scene);
            }
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_CHANGE_ROLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            m_isReady = false;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE:
                case (ushort)DGlobalEvent.EVENT_CHANGE_ROLE:
                    {
                        //m_isReady = false;
                    }
                    break;
                case (ushort)DGlobalEvent.EVENT_NET_CONNERCT:
                    {//重连状态
                        bool state = (bool)pContext;
                        if (state)
                        {
                            //1:场景重连、2：擂台赛重连、3：搭建重连
                            ProductModel productModel = ProductGlobal.Instance.GetProductGlobal();
                            TRACE.TraceLn("NetReconnecd:" + productModel.ToString());
                            switch (productModel)
                            {
                                case ProductModel.ChallengeModule:
                                case ProductModel.SceneModule:
                                case ProductModel.EnterSceneModule:
                                    {
                                        //清除本地所有数据

                                        GlobalGame.Instance.EntityClient.ReconnectedClearAll();

                                        //TRACE.ErrorLn("重联成功:"+ m_curSceneGUID);
                                        //获取当前场景guid
                                        if (!string.IsNullOrEmpty(m_curSceneGUID))
                                        {
                                            int kind = (int)SceneReconnectedKind.Scene;
                                            if (productModel == ProductModel.ChallengeModule)
                                            {
                                                kind = (int)SceneReconnectedKind.Scene_Challenge;
                                            }
                                            if (GlobalGame.Instance.TaskControler.GetTaskType() != TaskType.None&& GlobalGame.Instance.TaskControler.GetTaskType()!= TaskType.TaskSettle)
                                            {
                                                SendReconnectedEvent(kind, m_curSceneGUID, GlobalGame.Instance.TaskControler.GetNowTaskID());
                                            }
                                            else
                                            {
                                                SendReconnectedEvent(kind, m_curSceneGUID,0);
                                            }
                                        }
                                        else
                                        {//未缓存当前场景任何信息，可能是在loading时未能正常与服务器交互，直接踢回课程界面
                                            GHelp.ExitModuleToMainWindow(false);
                                        }
                                    }
                                    break;
                                case ProductModel.RobotModule:
                                    {
                                        TRACE.TraceLn(string.Format("{0}{1} 任务Id:{2}", Api.NTR("任务状态:"), GlobalGame.Instance.TaskControler.GetTaskType(), GlobalGame.Instance.TaskControler.GetNowTaskID()));
                                        //机器人数据准备完成
                                        if (GlobalGame.Instance.TaskControler.GetTaskType() != TaskType.None && GlobalGame.Instance.TaskControler.GetTaskType() != TaskType.TaskSettle)
                                        {
                                            //需要判断当前搭建任务是否完成
                                            SendReconnectedEvent((int)SceneReconnectedKind.RobotBuild, "", GlobalGame.Instance.TaskControler.GetNowTaskID());
                                        }
                                    }
                                    break;
                            }
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update() { }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate() { }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate() { }

        public bool GetSceneServerReady()
        {
            return m_isReady; // m_isReady 不再需要该字段等待准备了
        }

        public void OpenSceneServerReady()
        {
            // 如果没有设置成true说明在这次重连中没有收到场景服准备消息，再次发送出去
            //if (!m_isReady)
            //{
            //    m_isReady = true;
            //    GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_SERVER_READY_SUCCESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
            //}
            m_isReady = true;
        }

        public void CloseSceneServerReady()
        {
            m_isReady = false;
        }

        public void SendClientReadyMessage()
        {
            PlayerReady_CS playerReady_CS = new PlayerReady_CS()
            {
                ClientTime = 0,
                ClientVersion = "0",
            };
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Scene;
            head.wKeyAction = (int)SceneMessageCodes.CodePlayerReadyCs;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            packet.PushPB<PlayerReady_CS>(playerReady_CS);

            GlobalGame.Instance.NetManager.SendMessage(packet);
            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_SERVER_READY_SUCCESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
        }

        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {
            package.Pop(out int nLen);
            package.ReadByteBuffer(out byte[] szValue, nLen);

            switch (head.wKeyAction)
            {
                case (int)SceneMessageCodes.CodeEnterScene:

                    break;
                case (int)SceneMessageCodes.CodeLeaveScene:
                    m_curSceneGUID = "";
                    break;
                case (int)SceneMessageCodes.CodeCreateScene:

#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.CreateScene");
#endif
                    Scene_CreateScene_CS_Reply response = Scene_CreateScene_CS_Reply.Parser.ParseFrom(szValue);
                    gamelogic_SceneChange_CreateFinish c = new gamelogic_SceneChange_CreateFinish();
                    c.reply = response;
                    m_curSceneGUID = c.reply.SceneGUID;
                    GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_SCENECHANGE_CONTROL_CREATEFINISH, 0, "", c);
                    TRACE.TraceLn("收到创建场景事件");
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    break;
                case (int)SceneMessageCodes.CodeSceneServerReadySc:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.CreateScene");
#endif
                        m_isReady = true;
                        SendClientReadyMessage();
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)SceneMessageCodes.CodeUpdatePlayerInfoSc:
                    {
                        GHelp.FireExecute((ushort)ViewLogicDef.ENTITY_TOVIEW_UPDATE_PLAYER_INFO, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, szValue);
                    }
                    break;
                case (int)SceneMessageCodes.CodeResetSceneSc:
                    {
                        //Scene_ResetScene_SC res = Scene_ResetScene_SC.Parser.ParseFrom(szValue);
                        //TRACE.ErrorLn("场景刷新完成:"+ res.SceneId);
                        GHelp.FireExecute((ushort)ViewLogicDef.EVENT_SCENE_RESETFINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, package);
                    }
                    break;
                case (int)SceneMessageCodes.CodeFindPathByNavigationSc:
                    {
                        Scene_FindPath_SC findPath = Scene_FindPath_SC.Parser.ParseFrom(szValue);
                        List<UnityEngine.Vector3> paths = GlobalGame.Instance.NavigationManager.FindPath(GHelp.CVector(findPath.StartPosition), GHelp.CVector(findPath.DestPosition));

                        if (paths.Count > 0)
                        {
                            UnityEngine.Vector3 endPath = paths[paths.Count - 1];
                            UnityEngine.Vector3 destPath = new UnityEngine.Vector3(findPath.DestPosition.X, findPath.DestPosition.Y, findPath.DestPosition.Z);
                            if (UnityEngine.Vector3.Distance(endPath, destPath) < 1.0f)
                            {//如果目标路径和导航结束路径小于1.0则重置导航网格结束路径为目标路径
                                paths[paths.Count - 1] = destPath;
                            }
                        }

                        SGameMsgHead headinfo;
                        headinfo.SerialNumber = GHelp.GenSerialNumber();
                        headinfo.SrcEndPoint = (int)ENDPOINT.Appclient;
                        headinfo.DestEndPoint = (int)ENDPOINT.Scene;
                        headinfo.wKeyModule = (int)MSG_MODULEID.Scene;
                        headinfo.wKeyAction = (int)SceneMessageCodes.CodeFindPathByNavigationCs;

                        CPacketSend packet = new CPacketSend();

                        packet.Push<SGameMsgHead>(headinfo);

                        Scene_FindPath_CS requset = new Scene_FindPath_CS();

                        requset.Token = findPath.Token;
                        foreach (UnityEngine.Vector3 p in paths)
                        {
                            requset.Paths.Add(new PBVec3() { X = p.x, Y = p.y, Z = p.z });
                        }

                        packet.PushPB<Scene_FindPath_CS>(requset);

                        GlobalGame.Instance.NetManager.SendMessage(packet);
                    }
                    break;
                case (int)SceneMessageCodes.CodeSceneReconnectedResultSc:
                    {
                        SceneReconnected_Result_CS result = SceneReconnected_Result_CS.Parser.ParseFrom(szValue);
                        if (result.ResultCode == -1)
                        {//未能找到断线重联数据。将用户踢回环节，或者课程
                            GHelp.ExitModuleToMainWindow(false);
                        }
                    }
                    break;
                default:
                    {
                        GHelp.ToLaDataByFunction("OnSceneMessage", head, szValue);
                    }
                    break;
            }

            
        }

        #region 断线重连逻辑

        private void SendReconnectedEvent(int kind, string sceneGuid, int TaskID)
        {
            SGameMsgHead headinfo;
            headinfo.SerialNumber = GHelp.GenSerialNumber();
            headinfo.SrcEndPoint = (int)ENDPOINT.Appclient;
            headinfo.DestEndPoint = (int)ENDPOINT.Scene;
            headinfo.wKeyModule = (int)MSG_MODULEID.Scene;
            headinfo.wKeyAction = (int)SceneMessageCodes.CodeSceneReconnectedCs;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(headinfo);

            SceneReconnected_CS requset = new SceneReconnected_CS();

            requset.ConnectKind = kind;
            requset.SceneGUID = sceneGuid;
            requset.TaskID = TaskID;

            packet.PushPB<SceneReconnected_CS>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);

            GHelp.FireExecute((ushort)DGlobalEvent.EVENT_RECONNECTED_REFRESH_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
        }

        #endregion
    }
}
