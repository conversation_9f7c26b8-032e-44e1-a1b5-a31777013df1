﻿/// <summary>
/// COpHandleCommandHeroMove
/// </summary>
/// <remarks>
/// 2023/5/10 9:44:44: 创建. 熊洋 <br/>
/// <br/>
/// </remarks>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;


namespace GLib.Common
{
    public class COpHandleCommandHeroMove : IHandleCommand
    {
        // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private int m_heroMoveId;

        private bool m_isOver;
        private List<IHandleCommand> m_others;
        public COpHandleCommandHeroMove(SOpHandleCommand_HeroMove data)
        {
            m_heroMoveId = data.heroMoveId;
            m_isPlay = false;
            m_isOver = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpHeroMove;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isOver = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                GlobalGame.Instance.SchemeCenter.GetCameraTransformCenter().GetHeroMoveInfoByID(m_heroMoveId);
                m_isPlay = true;
                m_isOver = true;
            }

            if (m_isOver)
            {
                return true;
            }
            return false;
        }

        public void update()
        {
        }
    }
}