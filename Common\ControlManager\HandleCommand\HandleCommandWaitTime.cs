﻿/// <summary>
/// CHandleCommandWaitTime
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// 等待时间命令<br/>
/// </remarks>
using System;
using UnityEngine;

namespace GLib.Common
{
    public class CHandleCommandWaitTime : IHandleCommand
    {
		float m_fWaitTIme;
		private float startTime;
		bool m_isEnd = false; // 是否不正确的执行完指令

		public CHandleCommandWaitTime(SHandleCommand_WaitTime data)
		{
			m_fWaitTIme = (float)data.nWaitTime / 1000.0f;
			startTime = Time.time;
		}

		public virtual EHandleCommandType GetTypeEX()
		{
			return EHandleCommandType.WaitTime;
		}

		public virtual CommandsType GetCommandType()
		{
			return CommandsType.Default;
		}
		public void release()
		{
			m_isEnd = false;
		}

		public bool run()
		{
			float iTime = Time.time - startTime;
			if (iTime >= m_fWaitTIme)
			{
				return true;
			}
			return false;
		}

		public void update()
		{

		}
		public bool finish()
		{
			return m_isEnd;
		}

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}
	}
}
