﻿/// <summary>
/// Component
/// </summary>
/// <remarks>
/// 2021.3.22: 创建. 王正勇 <br/>
/// 管理所有组件用到的类型和枚举
/// </remarks>
using Google.Protobuf;
using Google.Protobuf.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using pb = global::Google.Protobuf;

namespace GLib.Common
{
    /// <summary>
    /// 部件类型枚举
    /// </summary>
    public enum ComponentType
    {
        #region 动作部件
        /// <summary>
        /// 向上移动
        /// </summary>
        MoveTop,
        /// <summary>
        /// 向右移动
        /// </summary>
        MoveRight,
        /// <summary>
        /// 向左移动
        /// </summary>
        MoveLeft,
        /// <summary>
        /// 向下移动
        /// </summary>
        MoveDown,
        /// <summary>
        /// 往右旋转
        /// </summary>
        RotateRight,
        /// <summary>
        /// 往左旋转
        /// </summary>
        RotateLeft,
        /// <summary>
        /// 抓取
        /// </summary>
        Grab,
        /// <summary>
        /// 释放
        /// </summary>
        Discard,
        /// <summary>
        /// 伤害
        /// </summary>
        Attack,
        /// <summary>
        /// 跳高
        /// </summary>
        HighJump,
        /// <summary>
        /// 跳远
        /// </summary>
        BroadJump,
        /// <summary>
        /// 对话
        /// </summary>
        Dialogue, 
        #endregion

        #region 事件部件
        /// <summary>
        /// 点击
        /// </summary>
        Click,
        /// <summary>
        /// 按键
        /// </summary>
        ClickKey,
        /// <summary>
        /// 碰撞
        /// </summary>
        Crash, 
        #endregion

        #region 显示部件
        /// <summary>
        /// 改变大小
        /// </summary>
        ChanageSize,
        /// <summary>
        /// 改变颜色
        /// </summary>
        ChanageColour,
        /// <summary>
        /// 播放动画
        /// </summary>
        PlayAnimation,
        /// <summary>
        /// 变形
        /// </summary>
        Deformation,
        /// <summary>
        /// 显示和隐藏
        /// </summary>
        ShowAndHide,
        /// <summary>
        /// 透明
        /// </summary>
        lucency, 
        #endregion

        #region 多媒体部件
        /// <summary>
        /// 播放音频
        /// </summary>
        PlayAudio,
        /// <summary>
        /// 播放视频
        /// </summary>
        PlayVideo,
        /// <summary>
        /// 朗读文字
        /// </summary>
        ReadText,
        /// <summary>
        /// 录音
        /// </summary>
        Record,
        /// <summary>
        /// 录像
        /// </summary>
        PictureRecord,
        /// <summary>
        /// 拍照
        /// </summary>
        Photograph 
        #endregion
    }
    /// <summary>
    /// 按键类型枚举
    /// </summary>
    public enum ClickKeyType
    {
        /// <summary>
        /// 左
        /// </summary>
        Top,
        /// <summary>
        /// 右
        /// </summary>
        Right,
        /// <summary>
        /// 左
        /// </summary>
        Left,
        /// <summary>
        /// 下
        /// </summary>
        Down,
    }

    /// <summary>
    /// 部件数据接收类型
    /// </summary>
    public class ComponentData
    {
        /// <summary>
        /// 执行顺序
        /// </summary>
        public int ExecutionNum;
        /// <summary>
        /// 执行数据
        /// </summary>
        public CPacketRecv Data;
        /// <summary>
        /// 执行类型
        /// </summary>
        public ComponentType componentType;
    }
    /// <summary>
    /// 所有部件基础类
    /// </summary>
    public class BaseComponentData
    {
        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName;
        /// <summary>
        /// 部件描述
        /// </summary>
        public string ComponentDescrbe;
    }
    /// <summary>
    /// 转到模块直接出数据
    /// </summary>
    public partial class CreatorData :pb::IMessage<CreatorData>
    {
        private static readonly pb::MessageParser<CreatorData> _parser = new pb::MessageParser<CreatorData>(() => new CreatorData());
        public static pb::MessageParser<CreatorData> Parser { get { return _parser; } }
        /// <summary>
        /// 操作类型
        /// </summary>
        public ComponentType componentType;
        /// <summary>
        /// 操作数据基类
        /// </summary>
        public BaseComponentData baseComData;

        public MessageDescriptor Descriptor => throw new NotImplementedException();

        public int CalculateSize()
        {
            int size = 0;
            return size;
        }

        public CreatorData Clone()
        {
            return new CreatorData();
        }

        public bool Equals(CreatorData other)
        {
            return false;
        }

        public void MergeFrom(CreatorData message)
        {
        }

        public void MergeFrom(CodedInputStream input)
        {
        }

        public void WriteTo(CodedOutputStream output)
        {
        }
    }
    /// <summary>
    /// 自身操作数据类
    /// </summary>
    public class OneselfMotionData : BaseComponentData
    {
        /// <summary>
        /// 自身操作数量
        /// </summary>
        public float OneselfNum;
    }
    /// <summary>
    /// 操作数据类
    /// </summary>
    public class OperationData:BaseComponentData
    {
        /// <summary>
        /// 操作范围
        /// </summary>
        public float OperatScope;
        /// <summary>
        /// 操作目标
        /// </summary>
        public float OperatEventID;
    }
    /// <summary>
    /// 伤害数据类
    /// </summary>
    public class AttackData : BaseComponentData
    {
        /// <summary>
        /// 伤害速度
        /// </summary>
        public float AttackSpeed;
        /// <summary>
        /// 伤害距离
        /// </summary>
        public float AttackScope;
        /// <summary>
        /// 伤害目标
        /// </summary>
        public float AttackEventID;
        /// <summary>
        /// 伤害值
        /// </summary>
        public float AttackDamage;
    }
    /// <summary>
    /// 显示内容数据
    /// </summary>
    public class DialogueData : BaseComponentData
    {
        /// <summary>
        /// 显示时间
        /// </summary>
        public float ShowTime;
        /// <summary>
        /// 显示消息的实体ID
        /// </summary>
        public float DialogueEventID;
        /// <summary>
        /// 显示内容
        /// </summary>
        public string DialogueContent;
    }
    /// <summary>
    /// 点击数据
    /// </summary>
    public class ClickData : BaseComponentData
    {
        /// <summary>
        /// 需要点击的事件
        /// </summary>
        public float ClickEventID;
    }
    /// <summary>
    /// 按键数据
    /// </summary>
    public class ClickKeyData : BaseComponentData
    {
        /// <summary>
        /// 按键类型
        /// </summary>
        public ClickKeyType clickKeyType;
    }
    /// <summary>
    /// 碰撞体
    /// </summary>
    public class CrashData : BaseComponentData
    {
        /// <summary>
        /// 与哪个实体进行碰撞
        /// </summary>
        public float CrashEventID;
    }
    /// <summary>
    /// 改变大小
    /// </summary>
    public class ChanageSizeData : BaseComponentData
    {
        /// <summary>
        /// 改变大小的实体ID
        /// </summary>
        public float ChanageEventID = 0;
        /// <summary>
        /// 需要改变的大小
        /// </summary>
        public Vector3 ChanageSize;
    }

    /// <summary>
    /// 改变颜色
    /// </summary>
    public class ChanageColourData : BaseComponentData
    {
        /// <summary>
        /// 改变颜色的实体ID
        /// </summary>
        public float ChanageEventID;
        public float ColourR;
        public float ColourG;
        public float ColourB;
        public float ColourA;
    }
    /// <summary>
    /// 显示和隐藏
    /// </summary>
    public class ShowAndHideData : BaseComponentData
    {
        /// <summary>
        /// 显示隐藏的实体ID
        /// </summary>
        public float ShowHideEventID;
        /// <summary>
        /// 是否显示
        /// </summary>
        public bool IsShow=true;
    }
    /// <summary>
    /// 播放动画
    /// </summary>
    public class PlayAnimationData : BaseComponentData
    {
        /// <summary>
        /// 需要播放的动画ID
        /// </summary>
        public float PlayAnimationID;
        /// <summary>
        /// 需要播放动画的实体ID
        /// </summary>
        public float PlayAnimationEventID;
    }
    /// <summary>
    /// 变形
    /// </summary>
    public class DeformationData : BaseComponentData
    {
        /// <summary>
        /// 变形模型ID
        /// </summary>
        public float DeformationEventID;
        /// <summary>
        /// 变形ID
        /// </summary>
        public float DeformationID;
    }
    /// <summary>
    /// 播放管理
    /// </summary>
    public class PlayMrgData : BaseComponentData
    {
        /// <summary>
        /// 资源路径
        /// </summary>
        public string Resource;
    }
}
