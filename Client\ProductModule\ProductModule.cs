﻿/// <summary>
/// CGameModule
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 状态细节管理<br/>
/// </remarks>
using System;
using GLib.Common;

namespace GLib.Client
{
	public class CProductModule : IProductModule, IEventExecuteSink
    {
        enum ProductModuleState
        {
            Idle = 0,   // 空闲
            Check,      // 检查
        }

        private ProductModuleState m_state = ProductModuleState.Idle;

        public string ModuleName { get; set; }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress { get; set; }

		private bool m_bCanEnterSelState = false;

        /// <summary>
        /// 模块同步创建.
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
			IEventEngine pEventEngine = GHelp.GetEventEngine();
			if (pEventEngine != null)
			{
			}
			
            return true;
        }

        /// <summary>
        /// 模块释放
        /// </summary>
        public void Release()
        {
			IEventEngine pEventEngine = GHelp.GetEventEngine();
			if (pEventEngine != null)
			{
			}
        }

        ////////////////模块驱动基础接口//////////////////////

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update() { }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {
            OnGameStateUpdate();
        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate() { }

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			
		}



        #region "状态相关"
        private void SetState(ProductModuleState newState)
        {
            // 旧的流程
            ProductModuleState nOldState = m_state;

            // 当游戏流程退出
            OnStateExit(nOldState, newState);

            //修改当前状态
            m_state = newState;

            // 当游戏流程进入
            OnStateEnter(newState, nOldState);

        }


        private void OnStateEnter(ProductModuleState state, ProductModuleState oldState)
        {
            switch (state)
            {
                case ProductModuleState.Idle:
                    {
                        //空

                    }
                    break;
                case ProductModuleState.Check:
                    {
                    }
                    break;
                default:
                    break;


            }

        }


        private void OnGameStateUpdate()
        {
            switch (m_state)
            {
                case ProductModuleState.Idle:
                    {
                       //空
                    }
                    break;
                case ProductModuleState.Check:
                    {

                    }
                    break;
                default:
                    break;


            }



        }
        private void OnStateExit(ProductModuleState state, ProductModuleState oldState)
        {
            switch (oldState)
            {
                case ProductModuleState.Idle:
                    {
                        //空状态
                    }
                    break;
                case ProductModuleState.Check:
                    {
                        //检查状态
                    }
                    break;

                default:
                    break;


            }
        }


        #endregion


        /// <summary>
        /// 结束游戏流程
        /// </summary>
        public void OnExit(GameState nState, GameState nNewState)
        {
            //注销fixedupdate事件
            GlobalGame.Instance.UnRegisterModuleEvent(this);

            SetState(ProductModuleState.Idle);

            // 退出游戏状态
            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_EXIT_GAME_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);

        }
        /// <summary>
		/// 开始游戏流程
		/// </summary>
		public void OnEnter(GameState nState, GameState nOldState)
        {
            //订阅fixedupdate事件
            GlobalGame.Instance.RegisterModuleEvent(this, (UInt32)EMModuleEvent.FixedUpdate);
            
           
            SetState(ProductModuleState.Check);

			m_bCanEnterSelState = false;
        }


		/// <summary>
		/// 可以返回到选角状态了
		/// </summary>
		/// <returns></returns>
		public bool CanEnterSelState()
		{
			return m_bCanEnterSelState;
		}
    }
}
