﻿/// <summary>
/// HandleCommandEntityDead
/// </summary>
/// <remarks>
/// 2021.6.7: 创建. 王康阳 <br/>
/// 实体可见状态设置命令<br/>
/// </remarks>
using System;
using GLib;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
	public class HandleCommandEntityShow : IHandleCommand
	{
		uint m_uidEntity;
		bool m_visibility;
		bool m_isEnd = false; // 是否不正确的执行完指令
		float m_fOverTime = 2f; // 超时
		float m_fOverWaittingTime; // 超时等待时间

		public HandleCommandEntityShow(SHandleCommand_EntityShow data)
		{
			m_uidEntity = data.EntityUID;
			m_visibility = data.Visibility;
		}

		public virtual EHandleCommandType GetTypeEX()
		{ 
			return EHandleCommandType.Show;
		}

		public virtual CommandsType GetCommandType()
		{
			return CommandsType.Default;
		}

		public void release()
		{
			m_uidEntity = 0;
			m_visibility = false;
			m_isEnd = false;
			m_fOverWaittingTime = 0;
		}

		public bool run()
		{
			// 超时的话就直接强制结束指令
			if (m_fOverWaittingTime >= m_fOverTime)
			{
				m_isEnd = true;
				return false;
			}

			IEntity entity = GlobalGame.Instance.EntityClient.Get(m_uidEntity);
			if (entity == null)
			{
				m_fOverWaittingTime += Time.deltaTime;
				return false;
			}

			bool isCreateFinish = GHelp.GetEntityFactory().EntityCreateIsFinish(entity.GetEntityViewID());
			if (isCreateFinish)
			{
				// 设置实体可见性
				cmd_creature_Visible c_data = new cmd_creature_Visible()
				{
					bVisable = m_visibility
				};
				GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_VISABLE, 0, "", c_data);
				return true;
			}
			else
			{
				m_fOverWaittingTime += Time.deltaTime;
				return false;
			}
		}

		public void update()
		{

		}
		public bool finish()
		{
			return m_isEnd;
		}

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}
	}
}
