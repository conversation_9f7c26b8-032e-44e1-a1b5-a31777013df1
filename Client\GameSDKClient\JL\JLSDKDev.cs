﻿using Game.Messages;
using GLib.Common;
using JWT;
using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace GLib.Client
{
    public class JLSDKDev : JLBase, HTTP_Response_Handler
	{
        public static LoginReturn loginReturn;

        public override EMGameSDKType GetSDKType()
        {
            return  EMGameSDKType.JL_DEV;
        }

        public override EMGameLoginType GetLoginType()
        {
            return GlobalGame.Instance.LoginModule.LoginType;
        }

        public override bool Exit()
        {
            return false;
        }

		/// <summary>
		/// 获取操作结果
		/// </summary>
		/// <returns></returns>
		public override EMGameSDKResult GetResult()
		{
			return m_result;
		}

		public JLSDKDev()
		{
		}


		/// <summary>
		/// 设置当前获取订单结果
		/// </summary>
		/// <param name="nResult">结果码</param>
		/// <param name="productId">产品ID</param>
		/// <param name="nOrderID">订单ID</param>
		public void SetOrderResult(int nResult, int productId, Int64 nOrderID)
		{

		}

		/// <summary>
		/// 逻辑更新
		/// </summary>
		public override void FixedUpdate()
		{
			base.FixedUpdate();
		}

		/// <summary>
		/// 初始化
		/// </summary>
		public override void Init()
		{
			TRACE.WarningLn("CBCSDKDev::当前是<开发版>登录模式, 该模式是内网开发测试使用.只支持登录");
			base.Init();
		}

		/// <summary>
		/// 游戏登录，通过账号密码
		/// </summary>
		public override bool LoginByPassWord()
		{
			//WWWForm from = GSpawnPool.Instance.GetObjectItem<WWWForm>();
			//from.AddField("grant_type", "password");
			//from.AddField("client_id", "open.admin");
			//from.AddField("username", GlobalGame.Instance.LoginModule.UserName);
			//from.AddField("password", GlobalGame.Instance.LoginModule.Password);
			//CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, GHelp.GetLoginUrl(), this, from);//https://open.jinlinjishu.com/connect/token




			#region Byte方式
			var account = GHelp.AccountToJson(GlobalGame.Instance.LoginModule.UserName, GlobalGame.Instance.LoginModule.Password);
			byte[] databyte = Encoding.UTF8.GetBytes(account.ToString());
			CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, GHelp.GetLoginUrl(), this, null, "", databyte);
			#endregion
			return true;
		}

		/// <summary>
		/// 游戏登陆，通过手机验证码
		/// </summary>
		/// <returns></returns>
		public override bool LoginBySMS()
		{
			WWWForm from = GSpawnPool.Instance.GetObjectItem<WWWForm>();
			from.AddField("grant_type", "sms_code");
			from.AddField("client_id", "open.admin");
			from.AddField("phoneNumber", GlobalGame.Instance.LoginModule.PhoneNumber.ToString());
			from.AddField("sms_code_token", GlobalGame.Instance.LoginModule.SMSCodeToken);
			from.AddField("sms_code", GlobalGame.Instance.LoginModule.SMSCode);
			CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, GHelp.GetLoginUrl(), this, from);//https://open.jinlinjishu.com/connect/token

			return true;
		}

		public override bool LoginByAuto(string accessToken)
        {
			string decodedPayload = JsonWebToken.Decode(accessToken, "ABC", false);// "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "ABC", false);
			accesstokenData data = JsonUtility.FromJson<accesstokenData>(decodedPayload);

			SetUserInfo(accessToken, data.sub);
			SetPhoneNumber(data.phone_number);
			m_result = EMGameSDKResult.Succee;

			return true;
		}

		public override bool LoginByOneClick(string token)
        {
			WWWForm from = GSpawnPool.Instance.GetObjectItem<WWWForm>();
			from.AddField("grant_type", "auto_login");
			from.AddField("client_id", "open.admin");
			from.AddField("auto_login_token", token);
			CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, GHelp.GetLoginUrl(), this, from);

			return true;
		}

		/// <summary>
		/// 登出
		/// </summary>
		/// <returns></returns>
		public override bool Logout()
		{

			return false;
		}

		/// <summary>
		/// 切换帐号
		/// </summary>
		public override void SwitchLogin()
		{
		}

		/// <summary>
		/// 显示个人中心
		/// </summary>
		/// <returns></returns>
		public override bool ShowAccountCenter()
		{
			return false;
		}

		/// <summary>
		/// 请求服务器校验身份
		/// </summary>
		/// <returns></returns>
		public override bool ReqVerify()
		{
			return m_result == EMGameSDKResult.Succee;
		}

		public void OnResponse(byte[] pContent, uint dwLen, string url)
		{
			if (pContent == null)
			{
				return;
			}

			string szText = Encoding.UTF8.GetString(pContent);
			Debug.LogError(szText);
		}

		public void OnError(uint dwError, string url)
		{
			GHelp.HideWait();
			m_result = EMGameSDKResult.Failed;
			if(dwError == 0)
            {
				TRACE.TraceLn("===未知响应");
				GHelp.addSystemTipsWithIcon(Api.TR("网络错误"), EMFInfoIcon.Icon_False);
			}
			if(dwError >= 300)
            {
				string[] message = url.Split('~');
				if (message.Length > 1)
				{
					GHelp.addSystemTipsWithIcon(Api.TR(message[1]), EMFInfoIcon.Icon_False);
				}
				else
				{
					GHelp.addSystemTipsWithIcon(Api.TR("登录失败!"), EMFInfoIcon.Icon_False);
				}
            }
			GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_LOGIN_FAIL, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
		}

		public void OnLocation(string new_url, string url)
		{
		}
		public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
		{
			if (pData == null)
			{
				return true;
			}

			string szText = Encoding.UTF8.GetString(pData);
			LoginReturn node = GHelp.ToObject<LoginReturn>(szText);
            loginReturn = node;

			if (node.status == "success")
			{
				string token = string.Format("name={0}&stdClass={1}&stdNumber={2}&stdPhone={3}", node.data.name, node.data.stdClass, node.data.stdNumber, node.data.stdPhone);
				SetUserInfo(token, node.data.id.ToString());
				SetUserID(node.data.id.ToString());
				SetNickName(node.data.name);
				SetPhoneNumber(node.data.stdPhone);
				GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_LOGIN_SUCCESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
				m_result = EMGameSDKResult.Succee;
			}
			else
			{
				//GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenCenterBottom, node.error_description);
				GHelp.addSystemTipsWithIcon(Api.TR("登录失败!")/*node.msg*/, EMFInfoIcon.None);
				m_result = EMGameSDKResult.Failed;
				GHelp.HideWait();
				GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_LOGIN_FAIL, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
			}
			return true;
		}

		public EMHTTP_METHOD GetCurrentHttpMethod()
		{
			return EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST;
		}

		//private static readonly Customer customer = new Customer { FirstName = "Bob", Age = 37 };
		public static string ValidateJwtToken(string token, string secret)
		{
			try
			{
				//var jsonSerializer = new JavaScriptSerializer();
				//var expectedPayload = jsonSerializer.Serialize(customer);

				string decodedPayload = JsonWebToken.Decode(token, "ABC", false);


				//校验通过，返回解密后的字符串
				return decodedPayload;
			}
			catch (SignatureVerificationException)
			{
				//表示验证不通过
				return "invalid";
			}
			catch (Exception)
			{
				return "error";
			}
		}

		//登录请求节点
		[System.Serializable]
		class LoginRequsetNode
		{
			public string access_token;
			public int expires_in;
			public string token_type;
			public string refresh_token;
			public string scope;
			public string error;
			public string error_description;
		}

		[System.Serializable]
		class accesstokenData
		{
			public int nbf;
			public int exp;
			public string iss;
			public string client_id;
			public string sub;
			public int auth_time;
			public string idp;
			public int iat;
			public string[] scope;
			public string[] amr;
			public string phone_number;
		}

		[System.Serializable]
		public class LoginReturn
		{
			/// <summary>
			/// 返回信息
			/// </summary>
			public string msg;

			/// <summary>
			/// 返回用户个人信息
			/// </summary>
			public Data data;

			/// <summary>
			/// 状态
			/// </summary>
			public string status;
		}
		[System.Serializable]
		public class Data
		{
			public int id;
			public string name;
			public string stdClass;
			public string stdNumber;
			public string stdPhone;
		}
	}
}
