﻿/// <summary>
/// CourseManger
/// </summary>
/// <remarks>
/// 2022/12/8 15:02:48: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GLib.Client;
using System.Linq;

namespace GLib.Client
{
    public class CourseManger : ICourseMgr, IEventExecuteSink
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        /// <summary>
        /// 当前模式模式
        /// </summary>
        public CourseState m_CourseState;
        /// <summary>
        /// 当前课程数据
        /// </summary>
        //public CourseData m_NowCourseDate;

        public CaseInfoDef m_NowCaseInfoDef;

        public List<CourseData> m_CourseDataList;


        /// <summary>
        /// 课程记录
        /// </summary>
        private Dictionary<int, SingleCourse> m_AllSingleCourse = new Dictionary<int, SingleCourse>();

        /// <summary>
        /// 当前课程信息
        /// </summary>
        public SingleCourse m_currentSingle;


        /// <summary>
        /// 服务器获取的课程列表
        /// </summary>
        public List<DepartData> m_DepartDatas;
        /// <summary>
        /// 选择的当前课程数据
        /// </summary>
        public DepartData m_NowDepartData;



        /// <summary>
        /// 获取返回课程数据
        /// </summary>
        public CourseDataReturn m_CourseDataReturn;
        /// <summary>
        /// 当前选择的病例
        /// </summary>
        public CaseInfo m_NowCaseInfo;

        /// <summary>
        /// 服务器获取的当前课程的病例数据 
        /// </summary>
        public List<CaseInfo> m_CaseDatas;

        public int moveID;
        /// <summary>
        /// (止血)需要锁定的支线
        /// </summary>
        public List<int> LockTaskStep;
        /// <summary>
        /// 第几个支线完成了多少项
        /// </summary>
        public Dictionary<int, int> taskStepCurIndex=new Dictionary<int, int>();
        public int NowTaskStep { get; set; }
        public int ClickTaskStep { get; set; }

        public bool m_IsFenZhi { get; set; }
        public bool m_IsOpenTaskSelectWindow { get; set; }
        public bool Create()
        {
            LockTaskStep = new List<int>();
            GlobalGame.Instance.EventEngine.Subscibe(this, (int)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
            return true;
        }

        public void Release()
        {
            ReleaseData();
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (int)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
            m_NowCaseInfo = null;
        }

        public void LoadCourse(CourseData course)
        {
            if (m_AllSingleCourse.ContainsKey(course.Id))
            {
                m_currentSingle = m_AllSingleCourse[course.Id];
                m_currentSingle.UpCourseDate();
            }
            else
            {
                SingleCourse single = new SingleCourse();
                m_currentSingle = single;
                single.InitCourseData(course.Id);
                m_AllSingleCourse.Add(course.Id, single);
            }
            // GlobalGame.Instance.ScoreMgr.LoadScore(course);

        }

        int currCourseID;
        public void LoadCourse(int courseID)
        {
            currCourseID = courseID;
            if (m_AllSingleCourse.ContainsKey(courseID))
            {
                m_currentSingle = m_AllSingleCourse[courseID];
                m_currentSingle.UpCourseDate();
            }
            else
            {
                SingleCourse single = new SingleCourse();
                m_currentSingle = single;
                single.InitCourseData(courseID);
                m_AllSingleCourse.Add(courseID, single);
            }
           // m_currentSingle.SendSingleCourseData();
           // GlobalGame.Instance.ScoreMgr.LoadScore(courseID);
        }

        /// <summary>
        /// 根据病例读取配置表
        /// </summary>
        /// <param name="patientId"></param>
        public void LoadSingleCourse(int index,int patientId)
        {
            CourseData c = GlobalGame.Instance.SchemeCenter.GetCourseDate().GetCourseDateByID(currCourseID);
            m_currentSingle.Init(c,patientId,index);
        }

        /// <summary>
        /// 获取当前病例表格文件夹
        /// </summary>
        public int GetCurCoursePatientId(int courseId = 0, int patientId = 0)
        {
            int folderId;
            if (courseId ==0 && patientId == 0)
            {
                courseId = currCourseID;
                patientId = GetNowCaseInfo().patientId;
            }
            folderId = patientId == 0 ? courseId : courseId * 100 + patientId;

            return folderId;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW:
                    ReleaseData();
                    break;
            }
        }
        /// <summary>
        /// 获取当前课程数据
        /// </summary>
        /// <returns></returns>
        public DepartData GetNowDepartData()
        {
            return m_NowDepartData;
        }
        public void ReleaseData()
        {
            if (m_currentSingle != null)
            {
                m_currentSingle.Release();
            }
            NowTaskStep = 0;
            ClickTaskStep = 0;
            LockTaskStep.Clear();
            taskStepCurIndex.Clear();
            m_IsOpenTaskSelectWindow = false;
        }
        public Dictionary<int, OperateInfo> GetTaskInfo()
        {
            return m_currentSingle.TaskInfo;
        }
        public OperateInfo GetTaskInfo(int taskID)
        {
            OperateInfo tempOperate = null;
            m_currentSingle.TaskInfo.TryGetValue(taskID, out tempOperate);
            return tempOperate;
        }
        public List<ApparatusSelectDef> GetApparatusSelectDef()
        {
            return m_currentSingle.ApparatusSelectDef;
        }
        public Dictionary<string, OperationLogInfo> GetOperationLogInfo()
        {
            return m_currentSingle.OperateInfo;
        }
        public Dictionary<int, OperateConditionInfo> GetOperateConditionInfo()
        {
            return m_currentSingle.OperateCondition;
        }
        public Dictionary<int, AssistantNpcDef> GetAssistantNpcDef()
        {
            return m_currentSingle.AssistantNpcDef;
        }

        public Dictionary<int, MenuHelp> GetMenuHelp()
        {
            return m_currentSingle.MenuHelp;
        }
        public List<DoctorPreperativeInfo> GetAllDoctorPreperative()
        {
            return m_currentSingle.DoctorPreperativeInfo;
        }
        public List<AnswerUIInfo> GetAnswerUIInfo(int step)
        {
            return m_currentSingle.GetAllAnswereByStepID(step);
        }
        public List<DoctorDialogueInfo> GetAllGetDoctorDialogueInfo(int step)
        {
            return m_currentSingle.GetAllGetDoctorDialogueInfoByStepID(step);
        }
        public List<DoctorPreperativeInfo> GetAllGetDoctorPreperativeInfo()
        {
            return m_currentSingle.GetAllDoctorPreperative();
        }
        public Dictionary<int, ToolTransform> GetToolTrans()
        {
            return m_currentSingle.C_ToolTransform;
        }

        public void SetCaseDatas(List<CaseInfo> datas)
        {
            m_currentSingle.m_CaseDatas = datas;
        }

        public List<CaseInfo> GetCaseInfos()
        {
            return m_currentSingle.m_CaseDatas;
        }
   
        public void SetNowCaseInfo(CaseInfo data)
        {
            m_currentSingle.m_NowCaseInfo = data;
        }

        public CaseInfo GetNowCaseInfo()
        {
            return m_currentSingle.m_NowCaseInfo;
        }

        public CaseInfo GetNumCaseInfo(int num)
        {
            if (m_currentSingle.m_CaseDatas.Count > num)
                return m_currentSingle.m_CaseDatas[num];
            else
                return null;
        }

        public List<CameraTransformDef> GetOperaAreaTrans()
        {
            return m_currentSingle.GetAllOperaAreaTrans();
        }

        public void GetHeroMoveInfoByID(int tID)
        {
                //if (tID < 0)
                //    return;
            if (!m_currentSingle.OperaAreaTrans.ContainsKey(tID))
            {
                return;
            }

            CameraTransformDef info = m_currentSingle.OperaAreaTrans[tID];
            moveID = tID;
            if (info == null)
            {
                return;
            }
            SEventHeroMove heroMove = new SEventHeroMove();
            heroMove.vTragetPos = GHelp.StringToVec3(info.Pos);
            heroMove.vTragetEul = GHelp.StringToVec3(info.Rorate);
            heroMove.fAnimationDuration = info.DurationTime;
            if (info.DurationTime == 0)
            {
                heroMove.bAnimation = false;
            }
            else
            {
                heroMove.bAnimation = true;
            }
            GHelp.FireExecute(DGlobalEvent.EVENT_HERO_MOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, heroMove);
        }

        public int GetNowHeroMovePos()
        {
            return moveID;
        }
        public void SetStartHeroMovePos()
        {
            moveID = 0;
        }
        public int GetPosCount()
        {
            return m_currentSingle.OperaAreaTrans.Count;
        }

        #region 
        //训练场景ID

        public string GettTrainScnenID()
        {
            return m_currentSingle.m_trainScnenID;
        }
        public void SettTrainScnenID(string id)
        {
             m_currentSingle.m_trainScnenID = id;
        }



        public void LateUpdate()
        {
        }

        public void Update()
        {
        }

        public void SetCourseState(CourseState state)
        {
            m_CourseState = state;
            bool isExam = false;
            if (state == CourseState.MockTest)
            {
                isExam = true;
            }
            m_currentSingle.GetTrainID(isExam);
        }

        public CourseState GetCourseState()
        {
            return m_CourseState;
        }

        public void SetCourseDate(CourseData course) 
        {
            //m_NowCourseDate = course;
        }

        public CourseData GetCourseDate()
        {
            return m_currentSingle.C_NowCourseDate;
        }

        /// <summary>
        /// 当前选择病患数据
        /// </summary>
        public void SetNowCaseInfoDef(CaseInfoDef caseInfo )
        {
            m_NowCaseInfoDef = caseInfo;
        }

        public CaseInfoDef GetCaseInfoDef() 
        {
            return m_NowCaseInfoDef;
        }

        public List<CourseData> GetAllCourseData()
        {
            return m_CourseDataList;
        }

        /// <summary>
        /// 设置服务器获取的课程列表
        /// </summary>
        /// <param name="data"></param>
        public void SetDepartDatas(List<DepartData> data)
        {
            m_DepartDatas = data;
        }
        /// <summary>
        /// 获取课程列表
        /// </summary>
        /// <returns></returns>
        public List<DepartData> GetDepartDatas()
        {
            return m_DepartDatas;
        }
        /// <summary>
        /// 设置当前课程数据
        /// </summary>
        /// <param name="data"></param>
        public void SetNowDepartData(DepartData data)
        {
            m_NowDepartData = data;
        }

        public List<DoctorDialogueInfo> GetDoctorDialogueInfo(int stepID)
        {

            return m_currentSingle.GetAllDialogueByStepID(stepID);
        }

        public void FixedUpdate()
        {
        }

        public int GetTrainID()
        {
            if (m_currentSingle != null && m_currentSingle.m_CurrendTrainDate != null && m_currentSingle.m_CurrendTrainDate.data != null)
            {
                return m_currentSingle.m_CurrendTrainDate.data.id;
            }
            return -1;
        }

        public CourseDataReturn GetCourseDataReturn()
        {
            return m_CourseDataReturn;
        }

        public void SetCourseDataReturn(CourseDataReturn data)
        {
            m_CourseDataReturn = data;
        }
        #endregion

        #region 场景数据管理 

        public void RegPatientPlay(IPatientPlay patientPlay)
        {
            m_currentSingle.RegPatientPlay(patientPlay);
        }

        public IPatientPlay GetPatientPlay()
        {
            return m_currentSingle.GetPatientPlay();
        }
        public void SetPatientEntityID(uint eID, int scenePointID)
        {
            m_currentSingle.SetPatientEntityID(eID,scenePointID);
        }

        public CourseSceneInfo GetPatientSceneInfo
        {
            get { return m_currentSingle.GetPatient; }
        }


        public void SetOtherModelEntityID(uint eID, int scenePointID, string modelID)
        {
            m_currentSingle.SetOtherModelEntityID(eID, scenePointID, modelID);
        }
        public CourseSceneInfo GetOtherModelByID(string modelID)
        {
            return m_currentSingle.GetOtherModelByID(modelID);
        }




        #endregion

        public void SetTaskStageInfoDefs(List<TaskStageInfoDef> data)
        {
            m_currentSingle.TaskStageInfoDefs = data;
        }

        public List<TaskStageInfoDef> GetTaskStageInfoDefsNoChongFu()
        {
            List<TaskStageInfoDef> taskStageInfoDefsNoChongFu = new List<TaskStageInfoDef>();
            for (int i = 0; i < m_currentSingle.TaskStageInfoDefs.Count; i++)
            {
                if (taskStageInfoDefsNoChongFu.Find(item => (item.createTrainRoot.step.stepOrder == m_currentSingle.TaskStageInfoDefs[i].createTrainRoot.step.stepOrder)) == null)
                {
                    taskStageInfoDefsNoChongFu.Add(m_currentSingle.TaskStageInfoDefs[i]);
                }
            }
            return taskStageInfoDefsNoChongFu;
        }

        public List<TaskStageInfoDef> GetTaskStageInfoDefByOrder(int StageId)
        {
            List<TaskStageInfoDef> taskStageInfoDefs = new List<TaskStageInfoDef>();
            for (int i = 0; i < m_currentSingle.TaskStageInfoDefs.Count; i++)
            {
                if (m_currentSingle.TaskStageInfoDefs[i].createTrainRoot.step.stepOrder == StageId)
                {
                    taskStageInfoDefs.Add(m_currentSingle.TaskStageInfoDefs[i]);
                }
            }
            return taskStageInfoDefs;
        }
        /// <summary>
        /// 获取特殊任务ID(用于考核模式默认选择支线)
        /// </summary>
        /// <returns></returns>
        public List<string> GetSpecialID()
        {
            return m_currentSingle.m_SpecialID;
        }

        public List<int> GetLastTaskStep()
        {
            return LockTaskStep;
        }

        public void SetLastTaskStep(int TaskStep)
        {
            if(!LockTaskStep.Contains(TaskStep)&& TaskStep!=0)
            {
                LockTaskStep.Add(TaskStep);
            }
        }

        public Dictionary<int, int> GetLastTaskStepDict()
        {
            return taskStepCurIndex;
        }

        public void SetLastTaskStepDict(int step, int num)
        {
            if(taskStepCurIndex==null)
            {
                taskStepCurIndex=new Dictionary<int, int>();
            }
            taskStepCurIndex[step] = num;
        }

        TaskOpInfo stepInfo =new TaskOpInfo();
        public void SetCurrentStepInfo(int step,int id, string name)
        {
            if(step!=0)
            {
                stepInfo.step = step;
                stepInfo.taskId = id;
                stepInfo.name = name;
            }
        }

        public TaskOpInfo GetCurrentStepInfo()
        {
            return stepInfo;
        }

        public bool GetIsFenZhiTask()
        {
            //if (GlobalGame.Instance.CourseMgr.GetCurCoursePatientId() == 8304)
            //{
            //    return true;
            //}
            //return false;
            return m_IsFenZhi;
        }

    }

}