﻿/// <summary>
/// HeroInfoCenter
/// </summary>
/// <remarks>
/// 2021.5.8: 创建. 吴航 <br/>
/// 机器人目标(子物体)表数据处理中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;

namespace GLib.Client
{
    public class DefaultConfigCenter : ISchemeNode, IDefaultConfigCenter
    {
        private const string _INFO = "DefaultConfig";


        private List<DefalutConifgDef> m_DefaultConfigList;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public DefaultConfigCenter()
        {
            m_DefaultConfigList = new List<DefalutConifgDef>();
        }

        ~DefaultConfigCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = _INFO;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Data);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Data(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                DefalutConifgDef data = new DefalutConifgDef();

                data.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.Value = pCSVReader.GetString(nRow, tmp_col++, "");
                data.Content = pCSVReader.GetString(nRow, tmp_col++, "");
                data.Content1 = pCSVReader.GetString(nRow, tmp_col++, "");
                m_DefaultConfigList.Add(data);
            }

            return true;
        }

        public void Release()
        {
            m_DefaultConfigList.Clear();
        }
      
        public List<DefalutConifgDef> GetAllDefaultConfigInfo()
        {
            return m_DefaultConfigList;
        }

        public DefalutConifgDef GetDefaultConfigInfoById(int targetid)
        {
            for (int i = 0; i < m_DefaultConfigList.Count; i++)
            {
                if (m_DefaultConfigList[i].Id == targetid)
                {
                    return m_DefaultConfigList[i];
                }
            }
            return null;
        }

        public DefalutConifgDef GetDefaultConfigInfoByValue(string value)
        {
            for (int i = 0; i < m_DefaultConfigList.Count; i++)
            {
                if (m_DefaultConfigList[i].Value == value)
                {
                    return m_DefaultConfigList[i];
                }
            }
            return null;
        }
    }
}

