﻿/// <summary>
/// StageManager
/// </summary>
/// <remarks>
/// 2021.2.2: 创建. 谌安 <br/>
/// 切换场景相关处理<br/>
/// </remarks>
using System;
using System.Collections;
using System.Collections.Generic;
using GLib;
using UnityEngine;

namespace GLib.Common
{
    public class StageManager : Singleton<StageManager>, IEventExecuteSink
    {
        public class StageData
        {
            public string strSceneName;     // 场景名字(客户端的)
            public UInt32 nMapID;           // 地图ID
            public Vector3 vPosition;       // 出生点坐标
            public bool bSceneLoaded;       // 
            public bool bResourcePreloaded; // 
            public string nPanGuID;         // 盘古ID
            public string nLayoutID;        // 布局ID

            public StageData()
            {
                strSceneName = string.Empty;
                nMapID = 0;
                vPosition = Vector3.zero;
                bSceneLoaded = false;
                bResourcePreloaded = false;
            }

            public StageData(string sceneName, UInt32 mapid, Vector3 vec, string panguid, string layoutid)
            {
                strSceneName = sceneName;
                nMapID = mapid;
                vPosition = vec;
                bSceneLoaded = false;
                bResourcePreloaded = false;
                nPanGuID = panguid;
                nLayoutID = layoutid;
            }
        }

        /// <summary>
        /// 每个模块分配的进度尺度（总值100）
        /// </summary>
        private enum StageType
        {
            eMMainUIHUD,            // 主UI
            eMScene,                // 场景
            eMResourcePreload,       //资源预加载

            eMCount,                // 总的长度
        }

        private Dictionary<UInt32, StageData> m_tableStageData = new Dictionary<UInt32, StageData>();


        private int[][] m_arrProgressInfo = new int[(int)StageType.eMCount][]
        {
            new int[]{0,5},
            new int[]{0,80},
            new int[]{0,15},
        };
        /// <summary>
        /// 当前进度值
        /// </summary>
        private float m_fCurProgress;     // 最大值1

        //资源预加载超时时间
        private const float ResourcePreloadTimeOut = 20.0f;

        // 当前加载的客户端场景
        private string lastSceneName = string.Empty;

        private const int StageChangeEffectID = 776;

        public StageManager()
        {

        }

        /// <summary>
        /// 模块加载
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
            // 订阅事件
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_PROGRESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "StageManager::SceneLoadProgress");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "StageManager::SceneLoadFinish");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_RESOURCE_PRELOAD_PROGRESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "StageManager::ResourcePreloadProgress");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_RESOURCE_PRELOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "StageManager::ResourcePreloadFinish");

            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_MESSAGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "StageManager::SceneLoadMessage");
            GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_TIPS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "StageManager::SceneLoadMessage");

            return true;
        }

        /// <summary>
        /// 模块卸载
        /// </summary>
        public void Release()
        {
            m_tableStageData.Clear();

            // 反订阅事件
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_RESOURCE_PRELOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_RESOURCE_PRELOAD_PROGRESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_PROGRESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);

            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_MESSAGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_TIPS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
        }

        /// <summary>
        /// 事件响应
        /// </summary>
        /// <param name="wEventID"></param>
        /// <param name="bSrcType"></param>
        /// <param name="dwSrcID"></param>
        /// <param name="pContext"></param>
        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EVENT_SCENE_LOAD_PROGRESS:
                    {
                        SEventSceneLoadProgress data = (SEventSceneLoadProgress)pContext;
                        OnUpdateProgress(StageType.eMScene, data.nProgress);
                    }
                    break;
                case (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH:
                    {
                        SEventSceneLoadFinish sceneloadFinish = (SEventSceneLoadFinish)pContext;
                        StageData stageData = null;
                        if (m_tableStageData.TryGetValue(sceneloadFinish.nMapId, out stageData))
                        {
                            stageData.bSceneLoaded = true;
                        }

                        lastSceneName = sceneloadFinish.strSceneName;
                    }
                    break;
                case (ushort)DGlobalEvent.EVENT_RESOURCE_PRELOAD_PROGRESS:
                    {
                        SEventResourcePreLoadProgress progressData = (SEventResourcePreLoadProgress)pContext;
                        OnUpdateProgress(StageType.eMResourcePreload, progressData.nProgress);
                    }
                    break;
                case (ushort)DGlobalEvent.EVENT_RESOURCE_PRELOAD_FINISH:
                    {
                        UInt32 mapID = (UInt32)pContext;
                        StageData stageData = null;
                        if (m_tableStageData.TryGetValue(mapID, out stageData))
                        {
                            stageData.bResourcePreloaded = true;
                        }
                    }
                    break;

                case (ushort)DGlobalEvent.EVENT_SCENE_LOAD_MESSAGE:
                    {
                        SEventSceneLoadMessage data = (SEventSceneLoadMessage)pContext;
                        OnUpdateMessage(StageType.eMScene, data.strMessage);
                    }
                    break;

                case (ushort)DGlobalEvent.EVENT_SCENE_LOAD_TIPS:
                    {
                        OnUpdateTips(StageType.eMScene);
                    }
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        WaitForSeconds interval = new WaitForSeconds(0.03f);
        private bool showProgress;

        IEnumerator LoadEnumerator(UInt32 mapid)
        {
            StageData stageData = null;
            if (m_tableStageData.TryGetValue(mapid, out stageData))
            {
                //yield break;
                //如果内存压力不大，可以考虑和Scene卸载的Unload合并
                //GResources.UnloadUnusedAssets();
                //GC.Collect();

                OnStageStart(stageData);
                yield return null;

                //耗时较少加载
                OnStageLoading(stageData);
                yield return null; 

                // 通知场景加载       
                gamelogic_LoadSceneInfo loadSceneInfo = GHelp.GetObjectItem<gamelogic_LoadSceneInfo>();
                loadSceneInfo.strSceneName = stageData.strSceneName;
                loadSceneInfo.nMapID = stageData.nMapID;
                loadSceneInfo.mPosition = stageData.vPosition;
                loadSceneInfo.nLayoutID = stageData.nLayoutID;
                loadSceneInfo.nPanGuID = stageData.nPanGuID;
                GlobalGame.Instance.RenderViewProxy.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_MATCH_LOAD_SCENE, 0, "", loadSceneInfo);
                while (!stageData.bSceneLoaded)
                {
                    yield return null;
                }
                yield return null;

                //通知资源预加载
                gamelogic_PreloadResourceInfo preloadResourceInfo = GHelp.GetObjectItem<gamelogic_PreloadResourceInfo>();
                preloadResourceInfo.strSceneName = stageData.strSceneName;
                preloadResourceInfo.nMapID = stageData.nMapID;
                preloadResourceInfo.mPosition = stageData.vPosition;
                GlobalGame.Instance.RenderViewProxy.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_MATCH_PRELOAD_RESOURCE, 0, "", preloadResourceInfo);
                float timeResourcerPreload = 0;
                do
                {
                    yield return null;
                    timeResourcerPreload += Time.deltaTime;
                } while (!stageData.bResourcePreloaded && timeResourcerPreload < ResourcePreloadTimeOut);
                yield return null;

                OnStageEnd(stageData);
                m_tableStageData.Remove(stageData.nMapID);
            }
        }

        private void OnStageLoading(StageData stageData)
        {
            // 抛出事件事件
            SEventSenceLoadingData_C tempLoading = new SEventSenceLoadingData_C();
            tempLoading.nMapID = stageData.nMapID;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_STAGE_LOADING, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, tempLoading);

            //隐藏UI(切换场景不隐藏界面 ， 暂时注调)
            //cmd_Base tmpBase = new cmd_Base();
            //GameHelp.FireExecute((ushort)GameLogicDef.GVIEWCMD_UI_CHANGESCENE_HIDEWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, tmpBase);
            //yield return null;

            //显示MainHUD
            /*gamelogic_MainHUDShow info1 = GameHelp.GetObjectItem<gamelogic_MainHUDShow>();
            info1.wModel = WindowModel.MainSkillHUD_Window;
            info1.nState = WindowState.Show;
            GlobalGame.Instance.RenderViewProxy.sendControllerCommand((int)GameLogicDef.GVIEWCMD_LOAD_MAIN_HUD, 0, "", info1);*/
            // 通知主UI加载进度
            OnUpdateProgress(StageType.eMMainUIHUD, 1);

            //加载场景音乐
            GlobalGame.Instance.RenderViewProxy.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_PLAY_SCENE_MUSIC, (int)stageData.nMapID, "", null);

        }

        private void OnStageStart(StageData stageData)
        {
            if (String.IsNullOrEmpty(stageData.strSceneName))
            {
                TRACE.ErrorLn("String.IsNullOrEmpty(stageData.strSceneName) == true");
                return;
            }
            bool isSameScene = stageData.strSceneName.Equals(lastSceneName, StringComparison.Ordinal);

            showProgress = !isSameScene;

            // 抛出事件状态切换结束事件
            SEventStageStartData_C stageStart = new SEventStageStartData_C();
            stageStart.bShowProgress = showProgress;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_STAGE_START, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, stageStart);

            if (showProgress)
            {
                // 显示进度条
                OnStartProgress(stageData.nMapID);
            }
            else
            {
                // 无进度条切场景白光一现特效
                //GHelp.createBuffEffect(StageChangeEffectID);
            }
        }

        private void OnStageEnd(StageData stageData)
        {
            // 抛出事件状态切换结束事件
            SEventSenceEndData_C data = new SEventSenceEndData_C();
            data.position = stageData.vPosition;
            data.nMapID = stageData.nMapID;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_STAGE_END, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, data);

            // 关闭进度条
            if (showProgress)
            {
                OnEndProgress(stageData.nMapID);
            }


            //其他地方有能直接调用GameLogicDef.GVIEWCMD_MATCH_LOAD_SCENE，不受stageManager控制，如选角界面（跟服务器没有关联）
            //所以在收到场景加载完毕事件EVENT_SCENE_LOAD_FINISH后设置
            //lastSceneName = stageData.strSceneName;
        }


        private void InitProgress()
        {
            m_fCurProgress = 0.0f;

            // 计算已完成部件进度
            for (int i = 0; i < (int)StageType.eMCount; i++)
            {
                m_arrProgressInfo[i][0] = 0;
            }
        }

        /// <summary>
        /// 启动进度条
        /// </summary>
        private void OnStartProgress(UInt32 mapid)
        {
            InitProgress();
            /*gamelogic_Loading cmd = GHelp.GetObjectItem<gamelogic_Loading>();
            cmd.wModel = WindowModel.Loading;
            cmd.mapid = mapid;
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_LOADINGWIN_SHOW, 0, "", cmd);*/
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="stageType"></param>
        /// <param name="progress"></param>
        private void OnUpdateProgress(StageType stageType, float progress)
        {
            //TRACE.ErrorLn("stageType = " + stageType.ToString() + "  CurProgress = " + progress);
            m_fCurProgress = 0.0f;
            m_arrProgressInfo[(int)stageType][0] = 0;

            // 计算已完成部件进度
            float overProgress = 0;
            for (int i = 0; i < (int)StageType.eMCount; i++)
            {
                if (m_arrProgressInfo[i][0] == 1)
                {
                    overProgress += ((float)m_arrProgressInfo[i][1]) / 100.0f;
                }
            }
            m_arrProgressInfo[(int)stageType][0] = 1;

            float partProgress = ((float)m_arrProgressInfo[(int)stageType][1] / 100.0f) * progress;

            m_fCurProgress = overProgress + partProgress;

            // 设置进度条
            /*gamelogic_Loading data = GameHelp.GetObjectItem<gamelogic_Loading>();
            data.fProgress = m_fCurProgress;
            data.wModel = WindowModel.Loading;
            //TRACE.ErrorLn("------------------" + "stageType = " + stageType.ToString() + "  CurProgress = " + m_fCurProgress + " overProgress = " + overProgress + " partProgress = " + partProgress);
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_LOADINGWIN_UPDATE, 0, "", data);*/
        }

        /// <summary>
        /// 关闭进度条
        /// </summary>
        private void OnEndProgress(uint mapID)
        {
            // 关闭进度条
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_LOADINGWIN_HIDE, 0, "", null);
            /*if (mapID != 0)
            {
                //GlobalGame.Instance.ScenarioManager.SetSceneLoaded(true);
                ISchemeMapInfo m_MapInfo = GlobalGame.Instance.SchemeCenter.GetSchemeMapInfo();
                SMapSchemeInfo pInfo = m_MapInfo.GetMapSchemeInfo(mapID);
                if (pInfo != null)
                {
                    //TRACE.ErrorLn("SceneClient::OnMsgLoadSceneInfo 获取地图配置失败!!! ,m_dwMapID=" + m_dwMapID);
                    //return;
                    GlobalGame.Instance.NavigationManager.SetLowFlyHeight(pInfo.fFlyHeightMin, pInfo.fFlyHeightMax);
                    if (pInfo.bShowSceneTitle)
                    {
                        gamelogic_SceneChange info = GameHelp.GetObjectItem<gamelogic_SceneChange>();
                        info.sceneName = pInfo.szName;
                        info.LightEffectID = 44;
                        info.wModel = WindowModel.SceneChangeTitle;
                        info.aCommandState = AsyncCommandState.CreateCommmand;
                        GameHelp.FireExecute((UInt16)GameLogicDef.GVIEWCMD_SCENE_CHANGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, info);
                    }
                }
            }*/
            InitProgress();
        }

          /// <summary>
        /// 更新信息
        /// </summary>
        /// <param name="stageType"></param>
        /// <param name="progress"></param>
        private void OnUpdateMessage(StageType stageType, string szMessage)
        {
            // 设置进度条
            /*gamelogic_Loading_Message data = GameHelp.GetObjectItem<gamelogic_Loading_Message>();
            data.szMessage = szMessage;
            data.wModel = WindowModel.Loading;
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_LOADINGWIN_MESSAGE, 0, "", data);*/
        }

        /// <summary>
        /// 更新小提示信息
        /// </summary>
        /// <param name="stageType"></param>
        /// <param name="progress"></param>
        private void OnUpdateTips(StageType stageType)
        {
            // 设置进度条
            /*cmd_AsyncBase data = GHelp.GetObjectItem<cmd_AsyncBase>();
            data.wModel = WindowModel.Loading;
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_LOADINGWIN_TIPS, 0, "", data);*/
        }

        #region 对外接口

        public  void OnUpdateFinish(gamelogic_LoadSceneInfo data, bool bSucc)
        {
            if(!bSucc)
            {
                return;
            }
            if (m_tableStageData.ContainsKey(data.nMapID))
            {
                m_tableStageData.Remove(data.nMapID);
            }

            m_tableStageData.Add(data.nMapID, new StageData(data.strSceneName, data.nMapID, data.mPosition,data.nPanGuID, data.nLayoutID));
        
            //TRACE.ErrorLn("name = " + data.strSceneName.ToString() + "mapid = " + data.nMapID);
            GlobalGame.Instance.StartCoroutineEx(LoadEnumerator(data.nMapID));
        }

        #endregion
    }
}