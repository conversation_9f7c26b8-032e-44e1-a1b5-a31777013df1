﻿/// <summary>
/// TimeAxisWindow
/// </summary>
/// <remarks>
/// 2019.7.3: 创建. 谌安 <br/>
/// 资源管理系统 <br/>
/// 资源加载入口，主要处理缓存数据及ab交互释放 <br/>
/// </remarks>
//#define RES_DEBUG 
using System.Collections.Generic;
using UnityEngine;
using System.Collections;
using GLib;

namespace GLib.Common
{

    public class GResManager : IResManager
    {
        private Dictionary<string, int> m_CachePathRefCount = new Dictionary<string, int>();
        private Dictionary<string, Object> m_ResCaches = new Dictionary<string, Object>();
        private HashSet<string> m_Loading = new HashSet<string>();
        private Dictionary<int, CoroutineHolder> m_CoroutineHolder = new Dictionary<int, CoroutineHolder>();
        private int m_IdxCount = 0;
        private float m_Progress;

        public string ModuleName { get; set; }

        public EMModuleLoadState ModuleLoadState { get; set; }

        public float Progress
        {
            get { return m_Progress; }
            set { m_Progress = value; }
        }
        #region 资源加载相关接口

        public bool Create()
        {
            return true;
        }

        public T LoadResource<T>(string path, bool isCache = true) where T : Object
        {
            Object asset = null;

            if (isCache)
            {
                if (!m_ResCaches.TryGetValue(path, out asset))
                {
                    asset = GResources.Load<T>(path);
                    m_ResCaches[path] = asset;
                }
                if (asset != null)
                {
                    RetainResourceRefCount(path);
                }
            }
            else
            {
                asset = GResources.Load<T>(path);
            }

            return asset as T;
        }

        public bool LoadResource<T>(string path, ref T data, bool isCache = true) where T : Object
        {
            data = LoadResource<T>(path, isCache);

            bool isLoad = data != null;

            return isLoad;
        }

        /// <summary>
        /// 不推荐使用，需要自己管理协程，因外部引用较多，固暂时保留此方法,
        /// 后续使用请选择“LoadResourceAsync(string,callback)”
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="path"></param>
        /// <returns></returns>
        public ZAsyncRequest<T> LoadResourceAsync<T>(string path, bool canCancel = true, bool isCache = true) where T : Object
        {
            RetainResourceRefCount(path);
            ZAsyncRequest<T> req = GSpawnPool.Instance.GetObjectItem<ZAsyncRequest<T>>();
            req.onLoadFinish = null;
            Object asset;
            if (isCache && m_ResCaches.TryGetValue(path, out asset))
            {
                req.asset = asset as T;
                req.isDone = true;
            }
            else
            {
                int tmp = GetIdxID();
                CoroutineHolder holder = CoroutineHolderManager.Instance.GetGlobalCoroutineHolder("GResManager");
                holder.StartCoroutine(LoadResourceTask<T>(path, req, tmp, canCancel, isCache));
                m_CoroutineHolder[tmp] = holder;
            }
            return req;
        }

        public void LoadResourceAsync<T>(string path, ZAsyncRequest<T>.OnAsyncLoadFinish callback, bool canCancel = true, bool isCache = true) where T : Object
        {
            RetainResourceRefCount(path);
            ZAsyncRequest<T> req = GSpawnPool.Instance.GetObjectItem<ZAsyncRequest<T>>();
            req.onLoadFinish = callback;
            Object asset;
            if (isCache && m_ResCaches.TryGetValue(path, out asset))
            {
                req.asset = asset as T;
                req.isDone = true;
                Callback(path, req);
            }
            else
            {
                int tmp = GetIdxID();
                CoroutineHolder holder = CoroutineHolderManager.Instance.GetGlobalCoroutineHolder("GResManager");
                holder.StartCoroutine(LoadResourceTask<T>(path, req, tmp, canCancel, isCache));
                m_CoroutineHolder[tmp] = holder;
            }
        }

        //清除缓存/
        public void ReleaseRes(string path)
        {
            if (m_CachePathRefCount.ContainsKey(path))
            {
                m_CachePathRefCount[path]--;
                int refCount = m_CachePathRefCount[path];
                if (refCount <= 0)
                {
                    ReleaseRes(path, true);
                    m_CachePathRefCount.Remove(path);
                }
            }
            else
                ReleaseRes(path, true);
        }

        public void ReleaseRes(string path, bool unloadImmediately)
        {
            if (m_ResCaches.ContainsKey(path))
            {
                m_ResCaches.Remove(path);
                GResources.UnLoad(path, unloadImmediately);
            }
            else
            {
                GResources.UnLoad(path, unloadImmediately);
            }
        }
        #endregion

        #region private funcation
        private void Callback<T>(string path, ZAsyncRequest<T> data) where T : Object
        {
            if (data.onLoadFinish != null)
            {
                data.onLoadFinish(path, data.asset);
            }
        }
        /// <summary>
        /// 引用计数管理
        /// </summary>
        /// <param name="path"></param>
        private void RetainResourceRefCount(string path)
        {
            if (m_CachePathRefCount.ContainsKey(path))
            {
                m_CachePathRefCount[path]++;
            }
            else
            {
                m_CachePathRefCount.Add(path, 1);
            }
        }

        //资源加载/
        private IEnumerator LoadResourceTask<T>(string path, ZAsyncRequest<T> req, int idx, bool canCancel = true, bool isCache = true) where T : Object
        {
            if (!m_Loading.Contains(path))
            {
                m_Loading.Add(path);
                GAssetRequest<T> tmpReq = GResources.LoadAsync<T>(path, RunnablePriority.Default, canCancel);
                yield return tmpReq;
                if (isCache) m_ResCaches[path] = tmpReq.asset;
                if (req.isCanceled)
                {
                    ReleaseRes(path);
                    req.Recycle();
                }
                else
                {
                    req.asset = tmpReq.asset;
                    req.isDone = true;
                    Callback(path, req);
                }
                tmpReq.Recycle();
                m_Loading.Remove(path);

                HolderFinish(idx);
            }
            else
            {
                do
                {
                    yield return null;
                } while (m_Loading.Contains(path));

                if (req.isCanceled)
                {
                    ReleaseRes(path);
                    req.Recycle();
                }
                else
                {
                    req.asset = m_ResCaches[path] as T;
                    req.isDone = true;
                    Callback(path, req);
                }
                HolderFinish(idx);
            }
        }
        //协程回收事件/
        private void HolderFinish(int idx)
        {
            if (m_CoroutineHolder[idx] != null)
            {
                CoroutineHolderManager.Instance.RecycleCoroutineHolder(m_CoroutineHolder[idx]);
            }
        }

        //协程的唯一ID，方便管理协程
        private int GetIdxID()
        {
            m_IdxCount++;
            if (m_IdxCount > 65535)
            {
                m_IdxCount = 1;
            }
            return m_IdxCount;
        }

        #endregion

        public void Release()
        {
            m_CachePathRefCount.Clear();
            m_ResCaches.Clear();
            m_Loading.Clear();
            m_CoroutineHolder.Clear();
        }

        public void Update() { }

        public void FixedUpdate() { }

        public void LateUpdate() { }
    }
}
