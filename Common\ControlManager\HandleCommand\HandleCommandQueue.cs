﻿/// <summary>
/// CHandleCommandQueue
/// </summary>
/// <remarks>
/// 2021.4.21: 创建. 谌安 <br/>
/// 命令队列<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using GLib;

namespace GLib.Common
{
    public class CHandleCommandQueue
    {
        LinkedList<IHandleCommand>		m_vCommands;

        public CHandleCommandQueue()
        {
            m_vCommands = new LinkedList<IHandleCommand>();
        }

		public IHandleCommand GetHead()
		{
			if (m_vCommands.Count > 0)
				return m_vCommands.First.Value;
			return null;
		}

		public IHandleCommand GetTail()
		{
			if (m_vCommands.Count > 0)
				return m_vCommands.Last.Value;
			return null;
		}

		public virtual EHandleCommandType GetTypeEX()
		{
			return EHandleCommandType.Max;
		}

        public virtual CommandsType GetCommandType()
        {
            return CommandsType.Default;
        }
        /// <summary>
        /// 备份命令到list
        /// </summary>
        /// <returns></returns>
        public List<IHandleCommand> BackupToList()
		{
			if(m_vCommands == null || m_vCommands.Count == 0)
			{
				return null;
			}
			List<IHandleCommand> listCmd = new List<IHandleCommand>();
			while (m_vCommands.Count > 0)
			{
				IHandleCommand pCommand = m_vCommands.First.Value;
				listCmd.Add(pCommand);
				m_vCommands.Remove(pCommand);
				pCommand.OnPause();
			}

			return listCmd;
		}


        public void update()
        {
            if (m_vCommands.Count <= 0)
            {
                return;
            }

            IHandleCommand pCommand = m_vCommands.First.Value;
            if (pCommand != null)
            {
                if (pCommand.run())
                {
                    if (m_vCommands.Count <= 0)
                    {
                        return;
                    }

                    if (m_vCommands.First.Value == pCommand)
                    {
                        pCommand.release();
                        m_vCommands.RemoveFirst();  // pCommand可能已经被销毁
                    }
                }
                else
                {
                    if (m_vCommands.Count <= 0 || m_vCommands.First.Value != pCommand)
                        return;

                    pCommand.update();
                }
            }
        }

        public void clearAll()
        {
	        while(m_vCommands.Count>0)
	        {
                IHandleCommand pCommand = m_vCommands.First.Value;
		        m_vCommands.Remove(pCommand);
		        if(pCommand!=null)
                {
					//if (pCommand.GetTypeEX() == EHandleCommandType.AutoCollectOrKill)
					//{
					//	TRACE.ErrorLn("清除自动战斗命令！！！");
					//}
			        pCommand.release();
                }
	        }
        }

        public void pushBack(IHandleCommand pCommand)
        {
	        m_vCommands.AddLast(pCommand);
        }

        public void pushFront(IHandleCommand pCommand)
        {
            m_vCommands.AddFirst(pCommand);
        }

        public void clearBefore(IHandleCommand pCmd)
        {
	        while(m_vCommands.Count>0)
	        {
		        IHandleCommand pCommand =  m_vCommands.First.Value;
		        if(pCmd == pCommand)
                {
			        break;
                }
		        m_vCommands.Remove(pCommand);
		        pCommand.release();
	        }	
        }

        public void clearAfter(IHandleCommand pCmd)
        {
            LinkedListNode<IHandleCommand> currentNode = m_vCommands.Last;

            LinkedListNode<IHandleCommand> tmpNode;
            while (currentNode.Previous!=null && currentNode.Previous.Value.Equals(pCmd))
            {
                tmpNode = currentNode;
                currentNode = currentNode.Previous;
                tmpNode.Value.release();
                m_vCommands.Remove(tmpNode);   
            }
            
        }

        // 移除某项指令
        public void RemoveCommand(IHandleCommand pCommand)
        {
	        if (pCommand == null || m_vCommands.Count<=0)
	        {
		        return;
	        }
			pCommand.release();
	        m_vCommands.Remove(pCommand);  // pCommand可能已经被销毁
        }

        public void printInfo(int time)
        {
            TRACE.WarningLn("HandleCommandPursuit更新超时 200 毫秒" + time);
        }

        public void release()
        {

        }

        public bool run()
        {
            return true;
        }

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}

    }
}