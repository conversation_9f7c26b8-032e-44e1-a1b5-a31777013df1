﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public class LaPBInfo
    {
        //属性标签
        List<string> m_paramsFlag;
        //属性
        List<string> m_params;

        public LaPBInfo()
        {
            m_paramsFlag = new List<string>();
            m_params = new List<string>();
        }

        public void AppendParams(params string[] _data)
        {
            for (int i = 0; i < _data.Length; i += 2)
            {
                m_paramsFlag.Add(_data[i]);
                m_params.Add(_data[i + 1]);
            }
        }

        public void AppendParam(string flag, string param)
        {
            m_paramsFlag.Add(flag);
            m_params.Add(param);
        }

        public void GetPacketData(CPacketSend paceket)
        {
            for (int i = 0; i < m_paramsFlag.Count; i++)
            {
                string flag = m_paramsFlag[i];
                string param = m_params[i];
                switch (flag)
                {
                    case "U8String":
                        paceket.WriteU8String(param);
                        break;
                    case "U16String":
                        paceket.WriteU16String(param);
                        break;
                    case "int":
                        paceket.Push(int.Parse(param));
                        break;
                    case "long":
                        paceket.Push(long.Parse(param));
                        break;
                }
            }
        }

        public void Relese()
        {
            m_paramsFlag.Clear();
            m_params.Clear();
        }
    }
}
