﻿/// <summary>
/// CameraCapture
/// </summary>
/// <remarks>
/// 2021.8.30: 创建. 谌安 <br/>
/// 从某个相机截屏<br/>
/// </remarks>
using GLib.Common;
using UnityEngine;
namespace GLib.Common
{
    public class CameraCapture : MonoBehaviour
    {
        public delegate void onCaptureEnd(CameraCapture cap, Texture2D tex, ICaptureScreenCallback callback);
        public onCaptureEnd capEnd;

        public bool IsCapturing
        {
            get
            {
                return bCapturing;
            }
            private set
            {
            }
        }
        private bool bCapturing = false;
        private Material matMask = null;
        private Rect m_capRect;
        ICaptureScreenCallback m_callBackInstance;

        public void StartCapture(Rect capRect, ICaptureScreenCallback callBack)
        {
            m_capRect = capRect;
            m_callBackInstance = callBack;
            bCapturing = true;
        }

        void OnRenderImage(RenderTexture src, RenderTexture dest)
        {
            if (bCapturing)
            {
                bool bIsTemp = true;
                RenderTexture rt = null;
                if (null != matMask)
                {
                    bIsTemp = true;
                    rt = RenderTexture.GetTemporary(Screen.width, Screen.height);
                    Graphics.Blit(src, rt, matMask);
                }
                else {
                    bIsTemp = false;
                    rt = src;
                }

                RenderTexture.active = rt;
                Texture2D tex = new Texture2D(Screen.width, Screen.height);
                tex.ReadPixels(m_capRect, 0, 0);
                tex.Apply();

                RenderTexture.active = null;
                if (bIsTemp) {
                    RenderTexture.ReleaseTemporary(rt);
                }
                if (null != capEnd)
                {
                    capEnd(this, tex, m_callBackInstance);
                }


                bCapturing = false;
            }
        }
    }
}