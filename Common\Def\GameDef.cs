﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;

namespace GLib.Common
{
    public class Config
    {

        public const string LayerMonsterFriend = "Monster";
        public const string LayerHero = "Hero";
        public const string LayerCamera = "Camera";
        public const string LayerDefault = "Default";
        public const string LayerEntity = "Entity";
        public const string LayerGround = "ColliderGround";
        public const string LayerColliderBlock = "ColliderBlock";
        public const string LayerObstacle = "Obstacle";
        public const string LayerAirWall = "AirWall";
        public const string LayerNotRayHit = "NotRayHit";
    }


    /// <summary>
    /// 静态实体定义，已知实体名字
    /// </summary>
    public enum MapDef
    {
        None = 0
    }



    //add by zhongxiaoqiu
    public enum CoordinateSpace
    {
        WorldSpace,    //世界坐标，场景中的坐标
        ScreenSpace,   //屏幕坐标（原点在左下角，范围[0-1]）
        UISpace,       //UI坐标
    }

    /// 系统信息（含聊天信息）位置定义
	public enum EMInfoPos
    {
        /// <summary>
        /// 屏幕中底部消息，从底部向上移动，后消失
        /// </summary>
        InfoPos_ScreenCenterBottom = 0x1,
        /// <summary>
        /// 屏幕中上部消息，从中部向上移动后消失
        /// </summary>
        InfoPos_ScreenCenterTop=0x2,
        /// <summary>
        /// 停止在顶部
        /// </summary>
        InfoPos_ScreenTopStop=0x3,
        /// <summary>
        /// 停止在中间
        /// </summary>
        InfoPos_ScreenTopCenterStop = 0x4,
        /// <summary>
        /// 停止在尾部
        /// </summary>
        InfoPos_ScreenTopBottomStop = 0x5,
        /// <summary>
        /// 屏幕中间消息，渐进渐出
        /// </summary>
        InfoPos_ScreenCenterGradual = 0x6,
        /// <summary>
        /// 最大位置数
        /// </summary>
        MaxInfoPosCount = 0x3FFF,
    };

    /// <summary>
    /// 系统消息的图标
    /// </summary>
    public enum EMFInfoIcon
    {
        /// <summary>
        /// 无Icon
        /// </summary>
        None = 0,
        /// <summary>
        /// 正确Icon
        /// </summary>
        Icon_True = 0x1,
        /// <summary>
        /// 错误Icon
        /// </summary>
        Icon_False = 0x2,
        /// <summary>
        /// 最大数
        /// </summary>
        MaxIconCount = 0x3FFF,
    }

    public enum ShaderLODLevel
    {
        Fast = 100,
        Simple = 200,
        Good = 300,
        Beautiful = 400,
        Fantastic = 500,
    }
    public enum LayoutModel
    {
        /// <summary>
        /// 横排布局
        /// </summary>
        AcrossModel,
        /// <summary>
        /// 竖排布局
        /// </summary>
        VerticalModel,
    }

    public enum EntityJumpDef
    {
        None = 0,
        Jumping, //跳跃中
        JumpSuccess, //跳跃成功
        JumpFail,       //跳跃失败
    }
}
