﻿/// <summary>
/// CVoiceControl
/// </summary>
/// <remarks>
/// 2021/9/2 11:03:46: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// CVoiceControl
    /// </summary>
    public class CVoiceControl : IVoiceControl
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }
        private TimeSpan m_NowTimeSpan;
        private int m_PastDay = 7;

        public bool Create()
        {
            m_NowTimeSpan = new TimeSpan(DateTime.Now.Ticks);
            DeletePastDueVoice();
            return true;
        }
        private void DeletePastDueVoice()
        {
          string strPath= GVoiceManager.Instance.GetVoiceDirPath();
            
            if (Directory.Exists(strPath))
            {
                DirectoryInfo dir = new DirectoryInfo(strPath);
                List<FileSystemInfo> listFile = dir.GetFileSystemInfos().ToList();
                if (listFile != null)
                {
                    foreach (FileSystemInfo fileSystemInfo in listFile)
                    {
                        TimeSpan timeCreateSpan = new TimeSpan(fileSystemInfo.CreationTime.Ticks);
                        TimeSpan timeSpan = timeCreateSpan.Subtract(m_NowTimeSpan).Duration();
                        if (timeSpan.Days >= m_PastDay)
                        {
                            fileSystemInfo.Delete();
                        }
                    }
                }
            }
        }
        public void FixedUpdate()
        {
        }


        public void Update()
        {
        }
        public void LateUpdate()
        {
        }
        public void Release()
        {
        }


    }
}
