﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandModelClick : IHandleCommand, IEventExecuteSink
    {
        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isOver = false;
        private GameObject m_target;
        private bool m_isHighLight;
        private int taskId;
        private SOpHandle_RunInstance runInstance;
        private bool m_isPlay;
        private List<IHandleCommand> m_others;
        public COpHandleCommandModelClick(SOpHandleCommand_ModelClick data)
        {
            m_target = data.target;
            m_isHighLight = data.isHighLight;
            taskId = data.taskId;
            runInstance = data.runInstance;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpModelClick;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isOver = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)ViewLogicDef.COPHANDLE_OVERMODELCLICK_EVENT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
                cmd_OpModelClick cmd_OpModelClick = new cmd_OpModelClick(m_target,m_isHighLight,taskId);
                GHelp.FireExecute((ushort)ViewLogicDef.COPHANDLE_MODELCLICK_EVENT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, cmd_OpModelClick);
            }

            if (m_isOver)
            {
                return true;
            }

            return false;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)ViewLogicDef.COPHANDLE_OVERMODELCLICK_EVENT:
                    {
                        GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)ViewLogicDef.COPHANDLE_OVERMODELCLICK_EVENT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                        m_isOver = true;
                    }
                    break;
                default:
                    break;
            }
        }


        public void update()
        {
        }
    }
}
