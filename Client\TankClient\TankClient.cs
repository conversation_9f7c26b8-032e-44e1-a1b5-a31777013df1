﻿/// <summary>
/// TankClient
/// </summary>
/// <remarks>
/// 2021.4.2: 创建. 谌安 <br/>
/// 人物实体载具功能控制管理<br/>
/// </remarks>
//#define OpenDebugInfo_TankClient
using GLib.Common;
using System.Collections.Generic;
using System;

namespace GLib.Client
{

	public class CTankClient :  ITankClient, IMessageHandler, ITimerHandler, IEventExecuteSink
	{

		// 为0时所有周围本国玩家
		Int64[] m_uidEntityArray;

		int m_nEntityArraySize;
	
		/// <summary>
		/// 模块名称(模块实现者不用设置)
		/// </summary>
		public string ModuleName { get; set; }

		/// <summary>
		/// 异步模块的加载状态(注释:异步模块专用)
		/// </summary>
		public EMModuleLoadState ModuleLoadState { get; set; }

		/// <summary>
		/// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
		/// </summary>
		public float Progress { get; set; }

		public bool mIsLoaded { get; set; }

		////////////////模块驱动基础接口//////////////////////

		/// <summary>
		/// 每渲染帧
		/// </summary>
		public void Update()
		{
			return;
		}

		/// <summary>
		/// 每逻辑帧
		/// </summary>
		public void FixedUpdate()
		{
			return;
		}

		/// <summary>
		/// LateUpdate更新
		/// </summary>
		public void LateUpdate()
		{
			return;
		}
		//////////////////////////////

		/// <summary>
		/// 模块创建
		/// 如果是同步模块，Create成功就表示加载成功。
		/// 如果是异步模块, Create成功不表示加载成功，必须通过模块的ModuleLoadState属性来判断。
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
			if (LoadScheme() == false)
			{
				return false;
			}

			// 订阅 MSG_MODULEID_TANK 模块消息
			//GlobalGame.Instance.NetManager.RegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_TANK, (IMessageHandler)(this));

			return true;
		}

		/// <summary>
		/// 模块释放
		/// </summary>
		public void Release()
		{
			// 取消订阅 MSG_MODULEID_TANK 模块消息
			//GlobalGame.Instance.NetManager.UnRegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_TANK);

			return;
		}


		// 构造函数
		public CTankClient()
		{
			//m_nIndex = 0;
			m_nEntityArraySize = 1024;
			m_uidEntityArray = new Int64[1024];
			Api.memset<Int64>(m_uidEntityArray, 1024, 0);

		}

		// 加载脚本
		private bool LoadScheme()
		{
			return true;
		}

		

		/** 消息处理
		@param msg 服务器发来的网络消息
		*/
		public void OnMessage(SGameMsgHead head, CPacketRecv data)
		{
		}

		/** 
		@param   wEventID ：事件ID
		@param   bSrcType ：发送源类型
		@param   dwSrcID : 发送源标识（实体为UID中"序列号"部份，非实体就为0）
		@param   pszContext : 上下文
		@param   nLen : 上下文长度
		@return  
		@note     
		@warning 
		@retval buffer 
		*/
		public void OnExecute(ushort wEventID, byte bSrcType, UInt32 dwSrcID, object pszContext)
		{

			switch (wEventID)
			{
				default:
					{
						TRACE.ErrorLn("载具客户端有一个事件订阅了但未处理，wEventID = " + wEventID);
					}
					break;
			}
		}


		/**
		@purpose          : 定时器触发后回调,你可以在这里编写处理代码
		@param	 dwTimerID: 定时器ID,用于区分是哪个定时器
		@return		      : empty
		*/
		public void OnTimer(TimerInfo dwTimerID)
		{

		}

		// 创建一个人物实体载具功能控制部件
		public IPersonTankPart CreatePersonTankPart()
		{
			return new CPersonTankPart();
		}


		/**  取得列表个数
		@return  
		*/
		public int GetListCount()
		{
			return m_nEntityArraySize;
		}

		/**  取得列表当前指向的数据
		@return  
		*/
		public List<IPerson> GetListData()
		{
			/// 获取实体管理器
			//IEntityClient pEntityClient = gGlobalClient->getEntityClient();
			IEntityClient pEntityClient = GHelp.GetEntityClient();
			if (pEntityClient == null || m_nEntityArraySize<1)
			{
				return null;
			}

			List<IPerson> ltPerson = new List<IPerson>();

			for(UInt16 i=0;i<m_nEntityArraySize;i++)
			{
				long nUID = m_uidEntityArray[i];
				if (nUID<1)
				{
					continue;
				}

				IPerson pEntity = (IPerson)pEntityClient.Get(nUID, (uint)EMtEntity_Class.tEntity_Class_Person);
				if (null == pEntity || !(pEntity.GetEntityClass().IsPerson()))
				{
					continue;
				}
				ltPerson.Add(pEntity);
			}

			return ltPerson;
		}
	}
}
