﻿using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandMoveAndShaderChange : IHandleCommand
    {
        private GameObject m_target;
        private List<Vector3> m_pathList;
        private Vector3 m_orgPoint;
        private Vector3 m_pTargetPos;
        protected Vector3 m_ptTarget;      // 调整过后的目标点
        private bool m_bFirstCheck = true;//第一次检查是否达到终点
        private bool m_isLocal = false;

        private Material material;
        private string m_key;
        private float m_orgNum;
        private float m_targetNum;
        private float m_curNum;

        // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;
        private float moveSpeed = 1.0f;

        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;

        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;

        public COpHandleCommandMoveAndShaderChange(SOpHandleCommand_MoveAndShaderChange data)
        {
            m_target = data.target;
            m_pathList = new List<Vector3>();
            if (data.pathList != null)
            {
                m_pathList.AddRange(data.pathList);
            }
            else
            {
                m_pathList.Add(data.ptTargetTile);
            }
            m_orgPoint = data.orgPoint;
            m_isLocal = data.isLocal;
            m_ptTarget = m_pathList[0];
            moveSpeed = data.speed;

            if (data.target != null)
                material = data.target.GetComponent<Renderer>().material;
            m_key = data.m_shaderKey;
            m_orgNum = data.m_orgNum;
            m_targetNum = data.m_targetNum;

            runInstance = data.runInstance;
            m_others = data.otherCommand;
        }
        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpMoveAnShaderChange;
        }

        public void OnPause()
        {
        }

        public void release()
        {
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                        m_target = t.gameObject;
                }
                if (material == null)
                {
                    Renderer t = OperateHandleCommandHelp.FindTarget<Renderer>(runInstance);
                    if (t != null)
                        material = t.sharedMaterial;
                }
            }
            if (m_target == null)
            {
                m_isEnd = true;
                return false;
            }
            if (!m_isPlay)
            {//开始时重新设置默认坐标
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                if (m_orgPoint.x > -9999)
                {
                    if (m_isLocal)
                        m_target.transform.localPosition = m_orgPoint;
                    else
                        m_target.transform.position = m_orgPoint;
                }
                material.SetFloat(m_key, m_orgNum);
                m_curNum = m_orgNum;
            }
            Vector3 ptSource = m_target.transform.position;
            if (m_isLocal)
                ptSource = m_target.transform.localPosition;

            Vector2 TempSource = ptSource;
            Vector2 TempTarget = m_ptTarget;

            float dis = Vector3.SqrMagnitude(TempTarget - TempSource);
            // 不用寻路就已经达到目标点
            if (dis <= MOVE_STATE_MIN_DISTANCE)
            {
                if (m_isLocal)
                    m_target.transform.localPosition = m_pathList[0];
                
                else
                    m_target.transform.position = m_pathList[0];
                m_pathList.RemoveAt(0);
                if (m_pathList.Count <= 0)
                {
                    SetFinalMaterial();
                    return true;
                }
                else
                    m_ptTarget = m_pathList[0];
            }
            Vector3 pos = Vector3.zero;
            //移动
            pos = Vector3.MoveTowards(ptSource, m_ptTarget, Time.deltaTime * moveSpeed);
            if (m_isLocal)
                m_target.transform.localPosition = pos;
            else
                m_target.transform.position = pos;
            DoMaterial();
            return false;
        }

        private void DoMaterial()
        {
            m_curNum += Time.deltaTime * moveSpeed;
            material.SetFloat(m_key, m_curNum);
        }

        private void SetFinalMaterial()
        {
            material.SetFloat(m_key, m_targetNum);
        }

        public void update()
        {
        }
    }
}
