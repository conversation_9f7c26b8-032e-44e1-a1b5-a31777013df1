﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public class LoginSMSDef
    {
        public string messageToken;
        public bool result;
        public string errorMessage;
    }

    public enum LoginState 
    {
        None = 0,
        // 未登录
        NoLogin = 1,
        // 登录中
        Logging = 2,
        // 登录完成
        LoginFinish = 3,
    }

    //登录校验节点
    [System.Serializable]
    public class LoginCheckNode
    {
        public string access_token;
        public int expires_in;
        public string token_type;
        public string refresh_token;
        public string scope;
    }

    public struct LoginInfos
    {
        
    }

    public class LoginDef
    {
    }
}
