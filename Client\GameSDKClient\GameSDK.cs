﻿
using GLib.Common;
using UnityEngine;

namespace GLib.Client
{
    public class GameSDK : IGameSDK, IEventExecuteSink
    {
        private IGameSDKInterface m_gameSDKInterface = null;

        private bool m_UserLoginFlag = false;

        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        // 返回登录界面的时候需要登出
        public bool NeedLogout { get; set; }

        public bool Create()
        {
            GlobalGame.Instance.RegisterModuleEvent(this, (uint)EMModuleEvent.FixedUpdate);
            // 获取配置
            EMGameSDKType sdkType = GHelp.GetConfigGameSDK();
            m_gameSDKInterface = new JLSDKDev();// 开发版本
            /*switch (sdkType)
			{
#if UNITY_STANDALONE_WIN || UNITY_ANDROID || UNITY_EDITOR
				case EMGameSDKType.JL_DEV:
					{
						m_gameSDKInterface = new JLSDKDev();// 开发版本
					}
					break;
#endif
			}*/
            // 初始化方法
            m_gameSDKInterface.Init();

            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EntityClient::Create");
                pEventEngine.Subscibe(this, (int)ViewLogicDef.GVIEWCMD_LOGINWIN_ONLOGIN_CLICK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "点击了登录按钮，初始化值");
            }

            return true;
        }

        public void FixedUpdate()
        {
        }

        public CSDKUserInfo GetUserInfo()
        {
            if (m_gameSDKInterface == null)
            {
                return null;
            }
            return m_gameSDKInterface.GetUserInfo();
        }

        public void SetPhoneNumber(string num)
        {
            if (m_gameSDKInterface != null)
            {
                m_gameSDKInterface.SetPhoneNumber(num);
            }
        }

        public void LateUpdate()
        {
            if (m_gameSDKInterface != null)
                m_gameSDKInterface.FixedUpdate();
        }

        public void Release()
        {
            GlobalGame.Instance.UnRegisterModuleEvent(this);
            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                pEventEngine.UnSubscibe(this, (int)ViewLogicDef.GVIEWCMD_LOGINWIN_ONLOGIN_CLICK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            }
        }

        public void Update()
        {
        }

        /// <summary>
		/// 获取SDK类型
		/// </summary>
		/// <returns></returns>
		public EMGameSDKType GetSDKType()
        {
            return m_gameSDKInterface.GetSDKType();
        }

        /// <summary>
		/// 获取操作结果
		/// </summary>
		/// <returns></returns>
		public EMGameSDKResult GetResult()
        {
            return m_gameSDKInterface.GetResult();
        }

        public void SetUserID(string userID)
        {
            m_gameSDKInterface.SetUserID(userID);
            PluginPlatform.Instance.Plugin().SetBuglyUserID(userID);
        }

        public void ResetUserInfo()
        {
            m_gameSDKInterface.ResetUserInfo();
        }

        /// <summary>
        /// 游戏登录 外面传参,决定是试玩还是正式
        /// </summary>
        public bool Login()
        {
            TRACE.WarningLn("GameSDK::Login()");
            // 开发测试版本不要用试玩账号登录
            if (m_gameSDKInterface.GetSDKType() == EMGameSDKType.JL_DEV)
            {
                if (m_gameSDKInterface.GetLoginType() == EMGameLoginType.PASSWORD_LOGIN)
                {
                    return m_gameSDKInterface.LoginByPassWord();
                }
                else if (m_gameSDKInterface.GetLoginType() == EMGameLoginType.SMS_LOGIN)
                {
                    return m_gameSDKInterface.LoginBySMS();
                }
                else if (m_gameSDKInterface.GetLoginType() == EMGameLoginType.AUTO_LOGIN)
                {
                    string accessToken = PlayerPrefs.GetString("AccessToken", "");
                    if (!string.IsNullOrEmpty(accessToken))
                    {
                        return m_gameSDKInterface.LoginByAuto(accessToken);
                    }
                }
                // 一键登录
                else if(m_gameSDKInterface.GetLoginType() == EMGameLoginType.ONE_CLICK)
                {
                    string oneClickToken = PlayerPrefs.GetString("OneClickToken", "");
                    return m_gameSDKInterface.LoginByOneClick(oneClickToken);
                }   
            }

            // 必须强制用户登录
            return m_gameSDKInterface.LoginByPassWord();
        }

        /// <summary>
		/// 登出
		/// </summary>
		/// <returns></returns>
		public bool Logout()
        {
            TRACE.WarningLn("GameSDK::Logout()");
            return m_gameSDKInterface.Logout();
        }

        /// <summary>
		/// 切换帐号
		/// </summary>
		public void SwitchLogin()
        {
            TRACE.WarningLn("GameSDK::SwitchLogin()");
            m_gameSDKInterface.SwitchLogin();
        }

        /// <summary>
		/// 显示个人中心
		/// </summary>
		/// <returns></returns>
		public bool ShowAccountCenter()
        {
            TRACE.WarningLn("GameSDK::ShowAccountCenter()");
            return m_gameSDKInterface.ShowAccountCenter();
        }

        /// <summary>
        /// 弹出退出游戏对话框，等待游戏退出
        /// </summary>
        public void Exit()
        {
            if (!m_gameSDKInterface.Exit())
            {
            }
        }

        /// <summary>
        /// 请求服务器校验身份
        /// </summary>
        /// <returns></returns>
        public bool ReqVerify()
        {
            // 开发版
            if (m_gameSDKInterface.GetSDKType() == EMGameSDKType.JL_DEV)
            {
                return m_gameSDKInterface.ReqVerify();
            }

            return true;
        }

        /// <summary>
        /// 正式登录进入游戏，方便玩家试玩账号转正用
        /// </summary>
        /// <param name="bFlag"></param>
        public void SetUserLoginFlag(bool bFlag)
        {
            m_UserLoginFlag = bFlag;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE:
                    {
                        m_gameSDKInterface.SetResult(EMGameSDKResult.None);
                        break;
                    }
                case (int)ViewLogicDef.GVIEWCMD_LOGINWIN_ONLOGIN_CLICK:
                    {
                        m_gameSDKInterface.SetResult(EMGameSDKResult.None);
                        m_gameSDKInterface.Init();
                        break;
                    }

                default: break;
            }
        }
    }
}
