﻿/// <summary>
/// IUpdateEngine
/// </summary>
/// <remarks>
/// 2021.10.18: 创建. 谌安 <br/>
/// 更新引擎 <br/>
/// </remarks>
using System.Collections.Generic;
using UnityEngine.Events;

namespace GLib.Common
{
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    /**
    @name     : 更新引擎 接口
    */
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    public interface IUpdateEngine
    {
        /// <summary>
        ///  请求开始分块下载
        /// </summary>
        /// <returns></returns>
        /// <param name="szFileName"></param>updatescheme名字
        /// <param name="projectNameList"></param>本次需要更新的project(之前逻辑会一次把所有的project都更新一次)
        /// <returns></returns>
        bool RequestBlockDownload(string szFileName, List<string> projectNameList);

        /// <summary>
        /// 请求停止分块下载
        /// </summary>
        /// <returns></returns>
        bool StopBlockDownload();

        /// <summary>
        ///  获取当前还需下载的字节数（返回格式：10M）
        ///  这里的还需下载的字节数是指，GetBlockDownloadFinish中还未完成的
        /// </summary>
        /// <returns></returns>
        string GetNeedDownloadSize();

        /// <summary>
        /// 获取当前已下载的字节数（返回格式：10M）
        /// 这里的已下载的字节数是指，GetBlockDownloadFinish中还未完成的
        /// </summary>
        /// <returns></returns>
        string GetFinishDownloadSize();

        /// <summary>
        /// 分块下载当前的完成进展百分比（取值范围：0~100）
        /// 这里的完成进度是指，GetBlockDownloadFinish中还未完成的
        /// </summary>
        /// <returns></returns>
        int GetTotalProgress();

        /// <summary>
        /// 获取渠道版本整包下载地址
        /// </summary>
        /// <returns></returns>
        string GetChannelDownloadUrl();

        /// <summary>
        /// 输出更新过程中的各种信息
        /// </summary>
        /// <param name="msg"></param>
        void OutputMsg(string msg);

        /// <summary>
        /// 重启客户端
        /// </summary>
        void DoRestart();

        /// <summary>
        /// 是否更新成功
        /// </summary>
        /// <returns></returns>
        bool IsUpdateOK(); 

        /// <summary>
        /// 取得更新进度
        /// </summary>
        /// <returns></returns>
           
	    int GetProgress(); 
        
	    /// <summary>
        /// 设置更新进度
        /// </summary>
        /// <param name="value"></param>
	    void SetProgress(int value);
 
        /// 取得更新文本信息
        string GetUpdateMsg();

        /// <summary>
        /// 获取版本信息
        /// </summary>
        /// <returns></returns>
        string GetVer();

        /// <summary>
        /// 是否需要重启
        /// </summary>
        /// <returns></returns>
        bool GetNeedReStart();

        /// <summary>
        /// 初始化是否完成
        /// </summary>
        /// <returns></returns>
        bool GetInitFinish();

        /**
	    @ name  : 从服务器获取最新的服务入口地址
	    @ param : entry_list_url 服务入口地址
	    @ param : backup_url 备用服务入口地址
	    */
        bool RequestEntry(string entry_list_url, string backup_url);

        /// <summary>
        /// 设置获取需更新的新版本流程的状态
        /// </summary>
        /// <returns></returns>
        void SetCheckConfirmFlag();

        /// <summary>
        /// 获得事件引擎
        /// </summary>
        /// <returns></returns>
        IEventEngine GetEventEngine();

        /// <summary>
        /// 获取更新远程配置的最新版本
        /// </summary>
        /// <param name="CallBack">获取成功后的回调</param>
        void CheckNewVersion(UnityAction CallBack = null);

        /// <summary>
        /// 获取更新远程配置的最新版本是否比本地版本新
        /// </summary>
        /// <returns></returns>
        bool GetNeedUpdate();

        /// <summary>
        /// 获取QQ群信息
        /// </summary>
        /// <returns></returns>
        string GetQQinfo();

        /// <summary>
        ///  获取网页版公告地址
        /// </summary>
        /// <returns></returns>
        string GetBillboardinfo();

        /// <summary>
        ///  获取信息地址
        /// </summary>
        /// <returns></returns>
        string GetSubscribeinfo();

        /// <summary>
        /// 获取微端信息地址
        /// </summary>
        /// <returns></returns>
        string GetMicroDownloadInfo();

        /// <summary>
        ///  切换更新配置文件地址
        /// </summary>
        /// <returns></returns>
        void SwitchVersionUrl();

        /// <summary>
        /// 是否正在下载中
        /// </summary>
        /// <returns></returns>
        bool GetIsDownload();


		/// <summary>
		/// 获取iOS 12 是否播放CG
		/// </summary>
		/// <returns></returns>
		bool GetiOS12Play();

        /// <summary>
        /// 本地更新配置XML(分包下载)
        /// </summary>
        /// strName、remote_version、local_version、temp_dir 不能为空
        void UpdateSchemeXML(List<UPDATE_PROJECT> datas);

        /// <summary>
        /// 获得功能更新状态
        /// </summary>
        /// <param name="projectName"></param>
        /// <returns></returns>
        bool GetProjectUpdateState(string projectName);

        /// <summary>
        /// 注册需要下载的工程
        /// </summary>
        /// <param name="projectName"></param>
        void RegDownloadingProject(string projectName);
        /// <summary>
        /// 注册需要下载的工程
        /// </summary>
        /// <param name="projectName"></param>
        void RegDownloadingProject(List<string> projectName);
        /// <summary>
        /// 所有项目服务器版本
        /// </summary>
        Dictionary<string, string> AllProjectServerVersion { get; set; }
        /// <summary>
        /// 本地版本大于服务器版本则需要更新
        /// </summary>
        /// <param name="projectName"></param>
        /// <returns></returns>
        bool ProjectVersionIsNeedUpdate(string projectName);
        /// <summary>
        /// 获取当前更新流程
        /// </summary>
        /// <returns></returns>
        IUpdateFlow GetCurrentFlow();
    }

    public interface IBoot
    {
        /// <summary>
        /// 关闭启动器
        /// </summary>
        void CloseLauncher();
    }

    public interface IBackDownloadHandle
    {
        void Init(int file_count);
        void Clear();
        void PushFile(string file_name);
    }


    // 后台下载控制器
    // 主要对资源列表进行优先级排序
    public interface IBackDownloadController
    {
        /** 创建
        @param   
        @param   
        @return  
        */
        bool Create();

        /// <summary>
        /// 设置更新资源列表的回调
        /// </summary>
        /// <param name="handle"></param>
        void SetHandle(IBackDownloadHandle handler);

        /// <summary>
        /// 设置英雄等级
        /// </summary>
        /// <param name="level"> 英雄等级 </param>
        /// <param name="calc_now"> 是否立即重新计算下载优先级 </param>
        void SetHeroLevel(int level, bool calc_now = false);

        /// <summary>
        /// 通知下载完成
        /// </summary>
        /// <param name="file_name"></param>
        /// <param name="success"></param>
        /// <returns>返回true表示可以开始下一个</returns>
        void OnDownloadFinish(string file_name);
    }
}    