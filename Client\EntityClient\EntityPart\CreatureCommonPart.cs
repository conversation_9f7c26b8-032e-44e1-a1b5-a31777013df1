﻿/// <summary>
/// CCreatureCommonPart
/// </summary>
/// <remarks>
/// 2021.3.19: 创建. 谌安 <br/>
/// 生物通用部件 <br/>
/// </remarks>
using game.common;
using game.schemes;
using Game.Entity;
using Google.Protobuf;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client.Entity
{
	class CCreatureCommonPart : IEntityPart,IMessageVoteSink
	{
		ICreature m_pMaster;
		// 上次移动时的tick
		uint m_dwLastPrepMoveTick;

		// 上次路径所需时间
		int m_nLastPathNeedTime;

		//上次调整高度的时间
		float m_fLastAdjustHeightTime = 0.0f;
		public CCreatureCommonPart()
		{
			// 主人
			m_pMaster = null;

			// 上次移动时的tick
			m_dwLastPrepMoveTick = 0;

			// 上次路径所需时间
			m_nLastPathNeedTime = 0;
		}

		/** 释放,会释放内存
	   @param   
	   @param   
	   @return  
	   */
		public void Release()
		{
			// 上次移动时的tick
			m_dwLastPrepMoveTick = 0;

			// 上次路径所需时间
			m_nLastPathNeedTime = 0;


			//上次调整高度的时间
			m_fLastAdjustHeightTime = 0.0f;

			if (m_pMaster != null)
			{
				//MSG_ACTION_3D_MOVE
				m_pMaster.UnSubscibe(DGlobalMessage.MSG_ACTION_3D_MOVE, this);

				m_pMaster = null;
			}
		}

		/// <summary>
		/// 还原,不释放对象，只将状态还原到创建时状态
		/// </summary>
		public void Restore()
		{
			Release();
		}

		/** 创建，重新启用，也会调此接口
	   @param   
	   @param   
	   @return  
	   */
		public bool Create(IEntity pMaster, object pszData)
		{
			if (pMaster == null || !pMaster.GetEntityClass().IsCreature())
			{
				return false;
			}
			m_pMaster = (ICreature)pMaster;
			;
			//MSG_ACTION_3D_MOVE
			m_pMaster.Subscibe(DGlobalMessage.MSG_ACTION_3D_MOVE, this, "CCreatureCommonPart::Create");

			return true;
		}

		/** 取是部件ID
        @param   
        @param   
        @return  
        */
		public uint GetPartID()
		{
			return (uint)EMENTITYPART.ENTITYPART_CREATURE_COMMON;
		}

		/** 消息
	 @param   
	 @param   
	 @return  
	 */
		public int OnMessage(uint dwMsgID, CPacketRecv pszMsg)
		{
			switch (dwMsgID)
			{
				case (uint)EMENTITYPART.ENTITYPART_CREATURE_MOVE:
					{
						OnMovePartMsg(pszMsg);
					};
					break;
				default:
					{
						TRACE.ErrorLn("CreatureCommonPart::OnMessage解析异常 解析部件ID=0，但是没有接受到消息，怀疑是解部件数据出错了");
					}
					break;
			}

			return 0;
		}

		/** 取是主人
        @param   
        @param   
        @return  
        */
		public IEntity GetMaster()
		{
			return m_pMaster;
		}

		// 显示层的命令(true表示此部件处理,其他不处理,false交由其他部件继续处理)
		public bool onCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
		{
			return false;
		}

		public void OnExecute(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			switch (dwMsgID)
			{
				case DGlobalMessage.MSG_ACTION_3D_MOVE:
					{
						ICreature creature = m_pMaster as ICreature;
						if (creature == null)
							return;
						I3DMoveManager moveMgr = creature.Get3DMoveManager();
						if (moveMgr != null)
							moveMgr.OnMessage(pGameMsgHead, pszMsg);
					}
					break;
				case DGlobalMessage.MSG_ACTION_DIE:
					{
						//OnDie(pszMsg as IMessage);
					}
					break;
				default: break;
			}
		}

		/// <summary>
		///移动部件数据
		/// </summary>
		/// <param name="pszMsg"></param>
		void OnMovePartMsg(CPacketRecv pszMsg)
		{
			if (pszMsg == null)
			{
				TRACE.ErrorLn("CreateCommonPart::On3DMove pszMsg == NULL");
				return;
			}
			byte bFlag = 0;
			UInt16 wPathLen = 0;
			List<Vector3> pathList = new List<Vector3>();
			UInt32 dwMovedTime = 0;
			try
			{
				pszMsg.Pop(out bFlag);
				pszMsg.Pop(out wPathLen);
				pszMsg.Pop(out dwMovedTime);
				//第一个点是实体的最新服务器位置
				Vector3 vEntityPos = new Vector3();
				pszMsg.Pop<Vector3>(ref vEntityPos);
				pathList.Add(vEntityPos);
				if (wPathLen > 100)
				{
					TRACE.ErrorLn("CreatureCommonPart::On3DMove 获取路径节点长度异常!!! 路径节点长度=" + wPathLen);
					return;
				}
				//移动的目标节点列表
				for (int i = 0; i < wPathLen; i++)
				{
					Vector3 vec = new Vector3();
					pszMsg.Pop<Vector3>(ref vec);
					pathList.Add(vec);
				}
			}
			catch (Exception ex)
			{
				TRACE.ErrorLn("CreatureCommonPart::On3DMove error:"+ ex.Message);
				return;
			}

			//如果路径节点大于1个
			if (pathList.Count > 1)
			{
				UInt32 dwPathLen = (UInt32)pathList.Count;

				/*SGameMsgHead head = default(SGameMsgHead);
				head.SrcEndPoint = (byte)EMMSG_ENDPOINT.MSG_ENDPOINT_CLIENT;
				head.DestEndPoint = (byte)EMMSG_ENDPOINT.MSG_ENDPOINT_CLIENT;
				head.wKeyModule = (ushort)EMMSG_MODULEID.MSG_MODULEID_ACTION;
				head.wKeyAction = (ushort)DGlobalMessage.MSG_ACTION_PREP3DMOVE;

				CPacketSend packet = new CPacketSend();
				//public struct SMsgActionPre3DMove_CC
				//{
				//	public UInt32 dwPathLen;		//路径长度
				//	//dwPathLen * Vector3			//路径节点
				//};
				packet.Push(bFlag);
				packet.Push(dwPathLen);
				dwMovedTime += GlobalGame.Instance.NetManager.GetLatency();
				packet.Push(dwMovedTime);
				for (int i = 0; i < dwPathLen; i++)
				{
					packet.Push(pathList[i].x);
					packet.Push(pathList[i].y);
					packet.Push(pathList[i].z);
				}
				byte[] data = packet.GetByte();
				CPacketRecv recvPackage = new CPacketRecv();
				recvPackage.Init(data);
				//客户端向客户端发消息
				m_pMaster.OnMessage((uint)DGlobalMessage.MSG_ACTION_PREP3DMOVE, head, recvPackage);*/
			}
		}

		/** MSG_ACTION_DIE
        @param   
        @param   
        @return  
        */
		public void OnDie(Entity_Die_SC pszMsg)
		{
			if (m_pMaster.GetEntityClass().IsPerson())
			{
				OnPersonDie(pszMsg);
			}
			else if (m_pMaster.GetEntityClass().IsMonster())
			{
				OnMonsterDie(pszMsg);
			}
			/*else if (m_pMaster.GetEntityClass().IsTank())
			{
				OnTankDie(pszMsg);
			}*/
		}

		/** 人物死亡
        @param   
        @param   
        @return  
        */
		void OnPersonDie(Entity_Die_SC pszMsg)
		{
			if (pszMsg == null)
				return;

			UInt32 nEntityViewID = m_pMaster.GetEntityViewID();
			if (nEntityViewID == 0)
			{
				return;
			}

			Int64 uid = m_pMaster.GetUID();

			// 不管三七二十一，把血减为0先
			if (m_pMaster.GetNumProp((uint)eEntityProp.EEntityCurHp) != 0)
			{
				m_pMaster.SetNumProp((uint)eEntityProp.EEntityCurHp, 0);
				byte bSrcType = m_pMaster.GetEventSourceType();
				GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(uid), null);

				//属性是否变化处理结束,清理标识
				//m_pMaster.ClsNumPropChanged();
			}

			// 设置死亡标记
			CPerson pPerson = (CPerson)(m_pMaster);
			pPerson.SetDeathState(true);

			GHelp.sendEntityCommand(m_pMaster.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ENTITY_DEAD, 0, "", null);

			// 如果主角，则转到死亡状态
			if (m_pMaster == GHelp.GetHero())
			{
				//清空逻辑层相关指令
				IHandleCommandManager commandMgr = GlobalGame.Instance.ControlManager.GetHandleCommandManager();
				commandMgr.clear();

				// 通知表现层清空路径并站立
				//GHelp.sendEntityCommand(m_pMaster.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ClEARPATH, 0, "", null);

				//GHelp.GetHero().GotoState((uint)EMCREATURE_CONTROLSTATE.CREATURE_CONTROLSTATE_DIE, null);
			}

			// 发死亡事件
			SEventCreatureDie_C eventcreaturedie;
			eventcreaturedie.uidMurderer = Api.GuidCInt(pszMsg.MurdderGuid);
			GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_CREATURE_DIE, m_pMaster.GetEventSourceType(),
				(uint)UID_DATA.ANALYZEUID_SERIALNO(uid), eventcreaturedie);


		}

		/** 怪物死亡
        @param   
        @param   
        @return  
        */
		void OnMonsterDie(Entity_Die_SC pszMsg)
		{
			UInt32 nEntityViewID = m_pMaster.GetEntityViewID();
			if (nEntityViewID == 0)
			{
				return;
			}

			// 是否播放死亡慢镜头效果
			//bool bPlaySlowCamEffect = false;
			if (m_pMaster.GetEntityClass().IsMonster())
			{
				IMonster monster = (IMonster)m_pMaster;
				Monster.Types.Item schemeInfo = monster.GetMonsterInfo();
				if (schemeInfo != null )
				{
					//bPlaySlowCamEffect = false;

					if (schemeInfo.ShowName == 1)
					{
						GHelp.GetEntityFactory().removeFlag(m_pMaster.GetEntityViewID(), (uint)EntityFlags.flagDrawName);
					}

					if (schemeInfo.ShowHP == 1)
					{
						GHelp.GetEntityFactory().removeFlag(m_pMaster.GetEntityViewID(), (uint)EntityFlags.flagDrawHP);
					}

					cmd_entity_TopInfoChange tmp_data = GHelp.GetObjectItem<cmd_entity_TopInfoChange>();

					tmp_data.IsShow = true;
					GlobalGame.Instance.RenderViewProxy.sendEntityCommand(m_pMaster.GetEntityViewID(), (Int32)EntityLogicDef.ENTITY_TOPINFO_CHANGE, 0, "", tmp_data);
				}
			}

			Int64 uid = m_pMaster.GetUID();
			// 不管三七二十一，把血减为0先
			if (m_pMaster.GetNumProp((uint)eEntityProp.EEntityCurHp) != 0)
			{
				m_pMaster.SetNumProp((uint)eEntityProp.EEntityCurHp, 0);
				byte bSrcType = m_pMaster.GetEventSourceType();
				GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(uid), null);

				//属性是否变化处理结束,清理标识
				//m_pMaster.ClsNumPropChanged();
			}

			// 设置死亡标志
			CMonster pMonster = (CMonster)(m_pMaster);
			pMonster.SetDieFlag();

			//如果需要播放死亡慢镜头效果就nParam参数填100
			/*cmd_CadaverShowTime showtime = new cmd_CadaverShowTime();
			showtime.nCadaverShowTime = pMonster.GetMonsterSchemeInfo().nCadaverShowTime;*/
			GHelp.sendEntityCommand(m_pMaster.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ENTITY_DEAD, 0, "", null);

			// 创建尸体
			/*CCadaver pCadaver = new CCadaver();
			pCadaver.Create(pMonster.GetEntityViewID());
			pCadaver = null;*/


			// 发死亡事件
			SEventCreatureDie_C eventcreaturedie;
			eventcreaturedie.uidMurderer = Api.GuidCInt(pszMsg.MurdderGuid);
			GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_CREATURE_DIE, m_pMaster.GetEventSourceType(),
				(uint)UID_DATA.ANALYZEUID_SERIALNO(uid), eventcreaturedie);
			// 回收怪物实体
			m_pMaster.Restore();			
		}

		/** 载具死亡
        @param   
        @param   
        @return  
        */
		void OnTankDie(Entity_Die_SC pszMsg)
		{
			if (pszMsg == null)
				return;
			/*long nDataLen = pszMsg.GetLength();
			SMsgActionDie_SC pMsg = new SMsgActionDie_SC();
			if (nDataLen != Marshal.SizeOf(pMsg))
				return;
			pszMsg.Pop<SMsgActionDie_SC>(ref pMsg);

			UInt32 nEntityViewID = m_pMaster.GetEntityViewID();
			if (nEntityViewID == 0)
			{
				return;
			}

			Int64 uid = m_pMaster.GetUID();
			// 不管三七二十一，把血减为0先
			if (m_pMaster.GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_CUR_HP) != 0)
			{
				m_pMaster.SetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_CUR_HP, 0);
				byte bSrcType = m_pMaster.GetEventSourceType();
				GameHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(uid), null);
				//属性是否变化处理结束,清理标识
				m_pMaster.ClsNumPropChanged();
			}

			// 设置死亡标志
			CTank pTank = (CTank)(m_pMaster);
			pTank.SetDieFlag();

			GameHelp.sendEntityCommand(m_pMaster.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ENTITY_DEAD, 0, "", null);

			// 创建尸体
			CCadaver pCadaver = new CCadaver();
			pCadaver.Create(pTank.GetEntityViewID());
			pCadaver = null;

			// For Test by Litao
			/*
	         播死亡动作
            pEntityView.onCommand(EntityCommand.EntityCommand_Stand, 0, 0);
            pEntityView.onCommand(EntityCommand.EntityCommand_Death, 5000 //暂时固定五秒//, 0);
            */

			// 发死亡事件
			/*SEventCreatureDie_C eventcreaturedie;
			eventcreaturedie.uidMurderer = pMsg.uidMuderer;
			GameHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_CREATURE_DIE, m_pMaster.GetEventSourceType(),
			(uint)UID_DATA.ANALYZEUID_SERIALNO(uid), eventcreaturedie);*/

			/* 待翻译成c# 先注释原c++代码
			 * /
			 /*
			 // 播死亡音效
			 ISoundManager  pSoundManager = g_ExternalFacade.GetSoundManager();
			 if(pSoundManager != null)
			 {
				 int nDieSoundID = pEntityView.getSoundId(SoundType_Death);
				 if(nDieSoundID > 0)
				 {
					 POINT ptTile = m_pMaster.GetMapLoc();
					 pSoundManager.play(nDieSoundID, false, ptTile.x, ptTile.y);
				 }
			 }	
			 */

			// 释放怪物实体
			//m_pMaster.Release();

			// 这里不能调用SAFE_RELEASE(m_pMaster)来释放怪物实体，因为内部会将此部件释放，
			// 而后会又去设置m_pMaster为空，而m_pMaster已释放，所以就非法了。
		}

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			if (m_pMaster == null)
			{
				return;
			}

			Int64 uid = m_pMaster.GetUID();
			if (UID_DATA.ANALYZEUID_SERIALNO(uid) != dwSrcID)
			{
				return;
			}
		}

        public bool OnVote(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
			return true;
        }

        public bool Active(object pszContext)
        {
			return true;
        }

        public bool Freeze(object pszContext)
        {
			return true;
		}

        public bool ImportBuildContext(IMessage pszContext, int nLen)
        {
			return true;
        }
    }
}
