﻿/// <summary>
/// GameClient
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 客户端管理<br/>
/// </remarks>
#define OpenDebugInfo_GameClient

using UnityEngine;
using System;
using GLib.Common;

namespace GLib.Boot
{
	public class GameClient : IModule, IGameClient, IEventExecuteSink
	{
        // 单实例
		private static GameClient m_instance = null;
		// 模块加载器流程状态
		public GameState m_nState = GameState.None;
		// 流程类型触发fix时间
		private float[] m_fStateTime = new float[(int)GameState.MAX];

		// 请求进入某个状态
		private bool[] m_bRequestEnter = new bool[(int)GameState.MAX];

		// 请求进入某个状态的时间
		private float[] m_bRequestEnterTime = new float[(int)GameState.MAX];

        // 游戏状态轮询冷却时间 
        private float[] m_fIntervalTime = new float[(int)GameState.MAX];

        // 游戏状态最后检查时间
        private float[] m_fLastCheckTime = new float[(int)GameState.MAX];

        // 当前时间
        private float fNow = 0.0f;

        private EnterGameContext m_EnterGameContext;
        #region<<各系统模块声明>>

        /////////基础设施层///////////////////////////////////////////////////////////////

        // 初始化
        GameStateInit m_Init = null;

		// 开始加载资源
		GameStateCreate m_Create = null;

		// 登录
		GameStateLogin m_Login = null;

		// 游戏
		GameStateGame m_Game = null;


		#endregion

		#region<<取得各模块的接口(属性)>>
		// 单实例
		public static GameClient Instance { get { return m_instance; } }

		// 初始化
		public GameStateInit InitState { get { return m_Init; } }

		// 开始加载资源
		public GameStateCreate CreateState { get { return m_Create; } }
		//登录
		public GameStateLogin LoginState { get { return m_Login; } }

		//主城游戏
		public GameStateGame StateGame { get { return m_Game; } }


		#endregion

		/// <summary>
		/// 模块中文名称
		/// </summary>
		public string ModuleName { get; set; }
		/// <summary>
		/// 模块异步加载状态(异步模块)
		/// </summary>
		public EMModuleLoadState ModuleLoadState { get; set; }

		/// <summary>
		/// 模块异步加载的进度,范围(0.0f,1.0f)
		/// </summary>
		public float Progress { get; set; }

		///<summary>
		/// 构造函数
		/// </summary>
		public GameClient()
		{
			m_instance = this;
			// 开始加载
			m_Create = new GameStateCreate();

			// 初始化
			m_Init = new GameStateInit();

			// 登录
			m_Login = new GameStateLogin();
			// 游戏
			m_Game = new GameStateGame();

            // 请求进入某个状态
			for (int i = 0; i < m_bRequestEnter.Length; i++)
			{
				m_bRequestEnter[i] = false;
			}
            // 流程类型触发fix时间
			for (int i = 0; i < m_fStateTime.Length; i++)
			{
				m_fStateTime[i] = 0.0f;
			}

            // 游戏状态轮询冷却时间 
            for (int i = 0; i < m_fIntervalTime.Length; i++)
            {
                m_fIntervalTime[i] = 0.5f;
            }
            // 游戏状态最后检查时间
            for (int i = 0; i < m_fLastCheckTime.Length; i++)
            {
                m_fLastCheckTime[i] = 0.0f;
            }

			//m_EnterHomeContext.nMapID = COMMON_DEF.MAIN_SCENE_ID;
		}

		/// <summary>
		/// 获取当前状态
		/// </summary>
		/// <returns></returns>
		public GameState GetState()
		{
			return m_nState;
		}


		/// <summary>
		/// 模块同步创建.
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
            // 模块名赋值 add by zhanggx
            this.ModuleName = "GameClient";

			// 注册逻辑调用 FixedUpdate，是在固定的时间间隔执行，不受游戏帧率（fps）的影响
			CGame.Instance.RegisterModuleEvent(this, (uint)EMModuleEvent.FixedUpdate);

			CGame.Instance.GameLoader.StartLoadAllModule();

            // 启动 进入开始基础资源加载状态
            SetState(GameState.None);

			// 事件注册
			IEventEngine pEventEngine = GHelp.GetEventEngine();
			if (pEventEngine != null)
			{
				pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EntityClient::Create");
				pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_CHANGE_ROLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EntityClient::Create");
			}

			return true;
		}

		/// <summary>
		/// 模块释放
		/// </summary>
		public void Release()
		{
			// 启动进入初始化状态
			SetState(GameState.Close);
			// 注册逻辑调用
			CGame.Instance.UnRegisterModuleEvent(this);

			// 事件注销
			IEventEngine pEventEngine = GHelp.GetEventEngine();
			if (pEventEngine != null)
			{
				pEventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
				pEventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_CHANGE_ROLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
			}
		}

		////////////////模块驱动基础接口//////////////////////

		/// <summary>
		/// 每逻辑帧
		/// </summary>
		public void FixedUpdate()
		{
			DoTask();
		}

		/// <summary>
		/// 每渲染帧
		/// </summary>
		public void Update()
		{
		}

		/// <summary>
		/// LateUpdate更新
		/// </summary>
		public void LateUpdate()
		{
		}
		///////////////////////////////
		/// <summary>
		/// /进入游戏状态
		/// </summary>
		/// <param name="ctx"></param>
		/// <returns></returns>
		public bool EnterGame(EnterGameContext ctx)
		{
			if (GameState.Game == m_nState)
			{
				return false;
			}
			// 重复请求不处理
			if (m_bRequestEnter[(int)GameState.Game] == true)
				return true;
			m_EnterGameContext = ctx;
			m_bRequestEnter[(int)GameState.Game] = true;
			m_bRequestEnterTime[(int)GameState.Game] = Time.realtimeSinceStartup;
			return true;
		}

		/// <summary>
		/// 正在请求进入某个状态
		/// </summary>
		/// <param name="state"></param>
		/// <returns></returns>
		public bool IsRequestEnterState(GameState state)
		{
			return m_bRequestEnter[(int)state];
		}

	    ///<summary>
		/// 改变游戏流程,不允许外部调用
		/// </summary>
		/// <param name="nState">当前状态</param>
		/// <returns>是否成功</returns>
		private bool SetState(GameState nState)
		{
            if (nState == m_nState)
			{
				return false;
			}

			// 清除请求状态
			m_bRequestEnter[(int)nState] = false;
			m_bRequestEnter[(int)m_nState] = false;

			// 旧的流程
			GameState nOldState = m_nState;

            m_fStateTime[(int)nOldState] = Time.realtimeSinceStartup;

			// 当游戏流程退出
			OnExit(nOldState, nState);

            // 改变流程
            m_nState = nState;

            m_fStateTime[(int)nState] = Time.realtimeSinceStartup;

			// 当游戏流程进入
			OnEnter(nState, nOldState);


#if OpenDebugInfo_GameClient
            TRACE.TraceLn("GameClient.SetState():" + nOldState.ToString() + "->" + nState.ToString());
#endif

            SEventGameStateChange data = new SEventGameStateChange();
			data.nOldState = (int)nOldState;		// 老状态
			data.nNewState = (int)nState;			// 新状态
			GlobalGame.Instance.EventEngine.FireExecute(DGlobalEvent.EVENT_STATE_CHANGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, data);

			return true;
		}

		/// <summary>
		/// 当游戏流程进入
		/// </summary>
		/// <param name="nState">新状态</param>
		/// <param name="nOldState">旧状态</param>
		private void OnEnter(GameState nState, GameState nOldState)
		{
			// 流程
			switch (nState)
			{
				case GameState.None:		// 初始化前
					{

					}
					break;
				case GameState.Create:			// 开始加载资源
					{
						m_Create.OnEnter(nState, nOldState);
					}
					break;
				case GameState.Init:			// 初始化
					{
						ModuleLoadState = EMModuleLoadState.Loading;
						GameClient.Instance.InitState.OnEnter(nState, nOldState);
					}
					break;
				case GameState.Login:
					{
						m_Login.OnEnter(nState, nOldState);
					}
					break;
				case GameState.Game:		    // 主城游戏
					{
						ModuleLoadState = EMModuleLoadState.Success;
                         
						m_Game.OnEnter(nState, nOldState);

#if OpenDebugInfo_GameClient
						TRACE.TraceLn("GameClient.OnEnter() Finish :" + nOldState.ToString() + "->" + nState.ToString());
#endif
					}
					break;
				case GameState.Logout:			// 退出游戏
					{
                        SetState(GameState.None);
                        //UIManager.Instance.CreateAsyncWindow(WindowModel.Loading, (object parentObj, object currentObj) =>
                        //{
                        //    (currentObj as GameObject).GetComponent<UIWindow>().Show();
                        //    GHelp.FireExecute((ushort)ViewLogicDef.GVIEWCMD_LOADINGWIN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);

                        //    gamelogic_Create gloc = GHelp.GetObjectItem<gamelogic_Create>();
                        //    gloc.wModel = WindowModel.LoginWindow;
                        //    gloc.aCommandState = AsyncCommandState.CreateCommmand;
                        //    GHelp.FireExecute((ushort)ViewLogicDef.GVIEWCMD_LOGINWIN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, gloc);

                        //});

                        ////关闭网关
                        //GlobalGame.Instance.NetManager.Close();

                    }
					break;
				case GameState.Error:			// 错误状态
					{
					}
					break;
				case GameState.Close:			// 关闭
					{
					}
					break;
			}

		}

		/// <summary>
		/// 驱动游戏流程
		/// </summary>
		private void DoTask()
		{
			if (EMModuleLoadState.Fail == CGame.Instance.GameLoader.ModuleLoadState)
			{
				// 不要重复提示
				if (GameState.Error != m_nState)
				{
					TRACE.ErrorLn("[GameClient:DoTask()] 模块加载器失败了,请检查");
					SetState(GameState.Error);
				}
				
				return;
			}

			//////////////////////////////////////////////////////////////////////////
            fNow = Time.realtimeSinceStartup;
			// 流程
			switch (m_nState)
			{
				case GameState.None:		// 加载基础资源，相互之间不可以存在依赖关系
					{
                        if (fNow < (m_fLastCheckTime[(int)GameState.None] + m_fIntervalTime[(int)GameState.None]))  // CHECK_END_INTERVAL
                        {
                            break;
                        }
                        // 更新最后一次检查时间
                        m_fLastCheckTime[(int)GameState.None] = fNow;

                        // 加载下一个状态需要使用的资源
                        bool bFinish = CGame.Instance.GameLoader.IsLoadFinish(GameState.Create);
                        if (bFinish)
                        {
                            SetState(GameState.Create);
                        }
					}
					break;
                case GameState.Create:          // 开始加载资源
                    {
                        // 冷却
                        if (fNow < (m_fLastCheckTime[(int)GameState.Init] + m_fIntervalTime[(int)GameState.Init]))  // CHECK_END_INTERVAL
                        {
                            break;
                        }
                        // 更新最后一次检查时间
                        m_fLastCheckTime[(int)GameState.Init] = fNow;

                        // 检测模块是否加载完成，完成则切换下个游戏状态
                        bool bFinish = CGame.Instance.GameLoader.IsLoadFinish(GameState.Init);
                        if (bFinish)
                        {
                            SetState(GameState.Init);
                        }

                    }
                    break;
                case GameState.Init:			// 初始化
					{
                        //fNow = Time.realtimeSinceStartup;//Time.realtimeSinceStartup;
                        // 冷却
                        if (fNow < (m_fLastCheckTime[(int)GameState.Create] + m_fIntervalTime[(int)GameState.Create]))  // CHECK_END_INTERVAL
                        {
                            break;
                        }
                        // 更新最后一次检查时间
                        m_fLastCheckTime[(int)GameState.Create] = fNow;

                        // 加载下一个状态需要使用的资源，GameState.Init资源是在进入GameState.Init状态时需要调用
						bool bFinish = CGame.Instance.GameLoader.IsLoadFinish(GameState.Login);
                        bool bEnd = m_Init.IsEnd;
                        if (bFinish && bEnd)
						{
							SetState(GameState.Login);
						}
                        
					}
					break;
				case GameState.Login:
					{
						m_Login.OnFixedUpdate();
						// 冷却
						if (fNow < (m_fLastCheckTime[(int)GameState.Login] + m_fIntervalTime[(int)GameState.Login]))  // CHECK_END_INTERVAL
						{
							break;
						}
						// 更新最后一次检查时间
						m_fLastCheckTime[(int)GameState.Login] = fNow;
						bool bFinish = CGame.Instance.GameLoader.IsLoadFinish(GameState.Game);
						bool bEnd = m_Login.IsEnd;
						if (bFinish && bEnd)
						{
							SetState(GameState.Game);
						}

					}
					break;				
				case GameState.Game:		    // 游戏
					{
                        // 检查状态
                        m_Game.OnFixedUpdate();

                        // 冷却
                        if (fNow < (m_fLastCheckTime[(int)GameState.Game] + m_fIntervalTime[(int)GameState.Game]))  // CHECK_END_INTERVAL
                        {
                            break;
                        }
                        // 更新最后一次检查时间
                        m_fLastCheckTime[(int)GameState.Game] = fNow;
                        
					}
					break;
				case GameState.Logout:			// 退出游戏
					{
						SetState(GameState.None);
					}
					break;
				case GameState.Error:			// 错误状态
					{

					}
					break;
				case GameState.Close:			// 关闭
					{
					}
                    break;
			}

			//////////////////////////////////////////////////////////////////////////


		}

		/// <summary>
		/// 当游戏流程退出
		/// </summary>
		/// <param name="nState">旧状态</param>
		/// <param name="nNewState">新状态</param>
		private void OnExit(GameState nState, GameState nNewState)
		{
			// 流程
			switch (nState)
			{
				case GameState.None:		// 初始化前
					{
					}
					break;
				case GameState.Create:			// 开始加载资源
					{
						m_Create.OnExit(nState, nNewState);
					}
					break;
				case GameState.Init:			// 初始化
					{
						m_Init.OnExit(nState, nNewState);
					}
					break;
				case GameState.Login:           // 登录
					{
						m_Login.OnExit(nState, nNewState);
					}
					break;
				case GameState.Game:		    // 主城游戏
					{
						m_Game.OnExit(nState, nNewState);
					}
					break;
				case GameState.Logout:			// 退出游戏
					{
                        //todo 待需求
					}
					break;
				case GameState.Error:			// 错误状态
					{
					}
					break;
				case GameState.Close:			// 关闭
					{
                        //todo 待需求
					}
					break;
			}
		}

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			switch (wEventID)
			{
				case (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE:
					{
						SetLoginOut();
						break;
					}
				case (ushort)DGlobalEvent.EVENT_CHANGE_ROLE:
					{
						SetLoginIn();
						break;
					}
				default: break;
			}
		}

		// 退出游戏登录
		private void SetLoginOut()
        {
			SetState(GameState.Logout);

		}

		// 重新回到登录状态，用于角色切换
		private void SetLoginIn()
        {
            SetState(GameState.Login);
        }
    }

}


