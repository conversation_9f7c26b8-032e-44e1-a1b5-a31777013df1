﻿/// <summary>
/// CServerConnection
/// </summary>
/// <remarks>
/// 2020.8.17: 创建. 谌安 <br/>
/// 连接处理对象<br/>
/// </remarks>
//#define OpenDebugInfo_ServerCon

using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using GLib;
namespace GLib.Client
{
	/// <summary>
	/// 外部网络需要被通知的对象，由GameStateManager继承实现，再由GameStateManager分派到状态对象中处理
	/// </summary>
	public interface INetStatuCallBack
	{
		// 连接成功
		void OnConnected();

		// 数据接收
        bool OnRecv(byte[] message); 

        //发送成功，返回发送字节数
		bool OnSendFinish(int nLen);

		// 网络错误
		void OnError(EMNetErrorCode nErrorCode,string strErr);

		// 服务器断开连接
		void OnDisConnected();
	}


	class CServerConnection : IConnectionEventHandler
	{
		private CPacketSend _pPacketOut;
		private IConnection _pConnection;
        
		private CPacketRecv _pPacketIn;

		private int _nCloseTick;

		private int _nPort;

		private string _szIP;

		private INetStatuCallBack _pNetCallBack = null;

		private int _nRecvTick = 0;

		public CServerConnection(INetStatuCallBack callBack)
		{
			_pNetCallBack = callBack;
			_pConnection = null;
			_nCloseTick = 0;
			_pPacketOut = new CPacketSend();
			_pPacketIn = new CPacketRecv();
		}

		/// <summary>
		/// 连接
		/// </summary>
		/// <param name="ip">地址</param>
		/// <param name="port">端口</param>
		public bool Connect(string ip, int port)
		{
			if (_pConnection != null)
			{
				_pConnection.Release();
				_pConnection = null;
			}
			_pConnection = CNetManager.NET_MANAGER.NewConnection(this);
			if (_pConnection != null)
			{
				_szIP = ip;
				_nPort = port;
				if (_pConnection.Connect(ip, port))
				{
					return true;
				}
			}
			return false;

		}

		/// <summary>
		/// 主动关闭连接
		/// </summary>
		public void Close()
		{
			if (_pConnection != null)
			{
				_pConnection.Release();
				_pConnection = null;
			}

			_nCloseTick = Environment.TickCount;

			_pNetCallBack = null;
		}

		/// <summary>
		/// 连接成功
		/// </summary>
		/// <param name="conn"></param>
		public void OnConnected(IConnection conn)
		{
#if OpenDebugInfo_ServerCon
        TRACE.TraceLn("CServerConnection::OnConnected 已连接");
#endif

			if (_pNetCallBack != null)
			{
				_pNetCallBack.OnConnected();
			}
		}

		/// <summary>
		/// 数据接收
		/// </summary>
		/// <param name="conn"></param>
		/// <param name="data"></param>
        public void OnRecv(IConnection conn, byte[] data)        
        {
			if (_pNetCallBack != null)
			{
                _pNetCallBack.OnRecv(data); //直接用 licc

            }
		}

		public void OnSendFinish(IConnection conn, int nLen)
		{
			if (_pNetCallBack != null)
			{
				//复制一份数组处理,以后正式替换之后可直接用data
				_pNetCallBack.OnSendFinish(nLen);
			}
		}

		/// <summary>
		/// 发包
		/// </summary>
		/// <param name="data"></param>
		/// <param name="len"></param>
		/// <returns></returns>
		public bool SendData(byte[] data, ushort len)
		{
			if (_pConnection != null)
			{
				return _pConnection.SendData(data, len);
			}
			return false;
		}

        public int PackData(ref List<CPacketSend> sendList, ref MemoryStream sendStream, ref List<CPacketSend> sendingPacket)
        {
            if (_pConnection != null)
            {
                return _pConnection.PackData(ref sendList, ref sendStream, ref sendingPacket);
            }

            return 0;
        }

		/// <summary>
		/// 网络连接报错
		/// </summary>
		/// <param name="conn"></param>
		/// <param name="nErrorCode"></param>
		/// <param name="strErr"></param>
		public void OnError(IConnection conn, EMNetErrorCode nErrorCode, string strErr)
		{
			if (_pNetCallBack != null)
			{
				_pNetCallBack.OnError(nErrorCode, strErr);
			}
		}
		/// <summary>
		/// 直接发送字节数组
		/// </summary>
		/// <param name="msg"></param>
		/// <returns></returns>
		public bool SendMsg(Byte[] msg)
		{
			if (_pConnection != null)
			{
				return _pConnection.SendData(msg, (ushort)msg.Length);
			}
			return false;
		}


		/// <summary>
		/// 连接被服务器断开
		/// </summary>
		/// <param name="conn"></param>
		public void OnClose(IConnection conn)
		{
			if (_pNetCallBack != null)
			{
				_pNetCallBack.OnDisConnected();
			}
#if OpenDebugInfo_ServerCon
		TRACE.TraceLn("CServerConnection::OnClose 服务器断开连接");
#endif
			//Close();
		}

		/// <summary>
		/// 是否连接成功
		/// </summary>
		/// <returns></returns>
		public bool IsConnected()
		{
			if (_pConnection == null)
			{
				return false;
			}
			return _pConnection.IsConnected();
		}

		/// <summary>
		/// 设置最后网络层收包的时间
		/// </summary>
		/// <param name="nTime"></param>
		public void SetLastRecvTime(int nTime)
		{
			_nRecvTick = nTime;
		}

		/// <summary>
		/// 获取最后网络层收包的时间
		/// </summary>
		/// <returns></returns>
		public int GetLastRecvTime()
		{
			return _nRecvTick;
		}
	}
}



