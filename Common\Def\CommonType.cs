﻿using UnityEngine;
/// <summary>
/// EnumType
/// </summary>
/// <remarks>
/// 2022.6.6 <br/>
///  公用的类型 枚举 URL等 <br/>
/// </remarks>
namespace GLib.Common
{
    #region Old
    public class WebURL
    {
        public static string webUrl = "";
        //登录域名
        public static string LoginUrl = "";

        public static string UserPrivacyUrl = "";

        public static string UserAgreementUrl = "";

        public static string AppUrl = "";

        public static string AppIOSUrl = "";
        /// <summary>
        /// 计划页支付调整默认域名
        /// </summary>
        public static string LoadDomainName = "";

        //App启动
        public static string AppStartUrl = "";
        //App后台
        public static string AppEndUrl = "";

        public static string OSSs = "oss";


        #region OSS
        /// <summary>
        /// 保存文件
        /// </summary>
        public static string PostSaveFileUrl { get { return string.Format("{0}{1}", GHelp.GetWebApiUrl(OSSs), "/api/blob/file"); } }
        /// <summary>
        /// 获取文件资源数据
        /// </summary>
        public static string GainFileUrl { get { return string.Format("{0}{1}", GHelp.GetWebApiUrl(OSSs), "/api/blob/file"); } }
        #endregion

        //意见反馈
        public static string FeedbackUrl { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/feedback"); } }

        /// <summary>
        /// 获取科室及所有课程信息
        /// </summary>
        public static string GetDepartment { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/v2/departList"); } }
        /// <summary>
        /// 获取科室api
        /// </summary>
        public static string GetOperByDepartmentID { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/projects/"); } }//+ID

        /// <summary>
        /// 获取近期训练
        /// </summary>
        public static string GetRecentTraining { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/getRecentTraining"); } }


        /// <summary>
        /// 获取课程病例信息
        /// </summary>
        public static string CaseInfoDataUrl { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/case/bypro/"); } }


        /// <summary>
        /// 血常规图片地址
        /// </summary>
        public static string XueChangGuiImgUrl { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/case/bpic/"); } }//+课程id  

        /// <summary>
        /// 生化指标图片地址
        /// </summary>
        public static string ShengHuaZhiBiaoImgUrl { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/case/cpic/"); } }//+课程id 
        /// <summary>
        /// 获取项目得分点(后续拼接项目ID)
        /// </summary>
        /// <param name=""></param>
        public static string CheckPoints { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/checkpoints/"); } }

        /// <summary>
        /// 任务数据上传
        /// </summary>
        public static string UpdateTask { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/addPractise"); } }
        /// <summary>
        /// 创建考核
        /// </summary>
        public static string TrainID { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/createV2New/"); } }


        public static string ProjectStep { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/steps/"); } }
        /// <summary>
        /// 考核数据上传
        /// </summary>
        public static string AddMockExam { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/addMockExam"); } }


        public static string GetCourseData { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/SingleCourseData"); } }
        /// <summary>
        /// 填充训练和考核结果数据
        /// </summary>
        public static string FInishDate { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/saveTrainingResult"); } }

        #region 个人中心
        /// <summary>
        /// 登录获取token
        /// </summary>
        public static string GetToken { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/auth/login"); } }

        /// <summary>
        /// 根据年份获取全年训练次数
        /// </summary>
        public static string GetTrainCountByYear { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vrback/training/trainingcountbymonth"); } }

        /// <summary>
        /// 修改密码
        /// </summary>
        public static string ChangePassword { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/mgr/user/changePassword"); } }
        /// <summary>
        /// 获取错题集
        /// </summary>
        public static string GetMistakeInfo { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vr/mistakescollection"); } }

        /// <summary>
        /// 修改个人信息  
        /// </summary>
        public static string ChangeUserInfos { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vr/student"); } }

        /// <summary>
        /// 获取头像
        /// </summary>
        public static string GetHeadIcon { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vr/student/avatar"); } }

        /// <summary>
        /// 获得班级近7天的模考排行
        /// </summary>
        public static string GetMockRank { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vr/practiceranking"); } }

        /// <summary>
        /// 获得公告列表
        /// </summary>
        public static string GetNotice { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vrback/bulletin"); } }

        /// <summary>
        /// 获取个人信息
        /// </summary>
        public static string GetPersonalInfo { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/api/vr/student/detail"); } }
        #endregion

        /// <summary>
        /// 首页顶部轮播图
        /// </summary>
        public static string TopImgData { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vrback/bulletin/carousel"); } }

        /// <summary>
        /// 根据TopImgData的id来获取图片
        /// </summary>
        public static string TopImgByID { get { return string.Format("{0}{1}", GHelp.GetTrainingWebUrl(), "/vrback/bulletin/pic/"); } }  //+ID
        
        /// <summary>
        /// 消息中心数据
        /// </summary>
        public static string MessageData { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/student/questionList"); } }

        /// <summary>
        /// 删除提问数据
        /// </summary>
        public static string DeleteQuesData { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vrback/guidance/del/"); } }//+id

        /// <summary>
        /// 添加修改提问数据
        /// </summary>
        public static string AddQuesData { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/student/ask"); } }

        /// <summary>
        /// 请求deepseek
        /// </summary>
        public static string RequestDeepSeek { get { return string.Format("{0}", "https://api.deepseek.com/chat/completions"); } }

        
        
        public static string RequestShuHouAI { get { return string.Format("{0}", "https://edu.uue.cn/api/partner/llm/evaluate_result"); } }

        /// <summary>
        /// 请求百度语音识别
        /// </summary>
        public static string RequestBaiduAudio { get { return "https://vop.baidu.com/pro_api"; } }

        /// <summary>
        /// 请求百度语音识别Token
        /// </summary>
        public static string RequestBaiduToken { get { return "https://aip.baidubce.com/oauth/2.0/token"; } }
        
        /// <summary>
        /// 获取绑定设备信息
        /// </summary>
        public static string GetBindDeviceInfo { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/student/deviceInfo/"); } }

        /// <summary>
        /// 绑定设备
        /// </summary>
        public static string BindDeviceUrl { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/student/ask"); } }

        /// <summary>
        /// 解绑设备
        /// </summary>
        public static string UnbindDeviceUrl { get { return string.Format("{0}{1}", GHelp.GetDefaultWebUrl(), "/api/vr/student/unbinding"); } }

    }

    public class EffectPath
    {
        public static string AssemblySnap = "Prefab/Effect/Assembly/MagicExplosionFire";
    }

    #region 组装
    public class SnapColor
    {
        //吸附重叠变红
        public static Color Red = new Color(1, 0, 0, 0.5f);
        //还原
        public static Color White = new Color(1, 1, 1, 0.5f);
    }


    /// <summary>
    /// 模型吸附类型 点击吸附、拖拽吸附、旋转吸附
    /// </summary>
    public enum EMAssemblySnapType
    {
        None = 0,
        Click = 1,
        Drag = 2,
        Rorate = 3,
    }

    /// <summary>
    /// 模型组装摄像机移动状态
    /// </summary>
    public enum EMAssemblyCameraMoveType
    {
        /// <summary>
        /// 正常
        /// </summary>
        Add = 0,
        /// <summary>
        /// 移近了
        /// </summary>
        Sub = 1,
    }

    /// <summary>
    /// 拼装吸附名字类型(拼装规则)
    /// </summary>
    public class AssemblyNameType
    {
        public static string SnapCommon = "AssemblySnap_Common_";
        public static string SnapSame = "AssemblySnap_Same_";
        public static string SnapNoAngelSame = "AssemblySnap_NoAngel_Same_";
        public static string SnapMore = "AssemblySnap_More_";
    }

    public enum EMRobotType
    {
        /// <summary>
        /// 场景中的机器人
        /// </summary>
        Scene = 1,
        /// <summary>
        /// 拼装界面的机器人
        /// </summary>
        Assembly,
        /// <summary>
        /// 其他
        /// </summary>
        Other,
        /// <summary>
        /// 点击查看模型的机器人
        /// </summary>
        TipTexture,
        /// <summary>
        /// 右侧栏部件
        /// </summary>
        Part,
    }

    #endregion

    public enum NavigationArea
    {
        AllArea = -1,
        Walkable = 1,
        Jump = 2,
        road = 5,
    }

    #region 对话模块

    public enum TalkState
    {
        None = 0,
        Start = 1,//对话开始
        TalkIn = 2,//对话中
        Finish = 3,//对话完成
    }

    public class TalkInfo
    {
        public string TalkId;
        public TalkState talkState;
    }


    #endregion

    #region 错误指令模块

    public enum ErrActionState
    {
        None = 0,
        Start,   // 开始展示
        Showing, // 展示中
        Finish,  // 展示结束
    }

    public class ErrActionInfo
    {
        public string ErrId;
        public ErrActionState ErrState;
        public bool TaskFail = false;
    }


    #endregion

    #region 系列课程相关
    /// <summary>
    /// 课程标志性状态
    /// </summary>
    public enum EMCourseSignType
    {
        None,
        /**
         * 作业待完成
         */
        HomeworkWaitDone,
        /**
         * 作业待评价
         */
        HomeworkWaitEvaluated
    }

    /// <summary>
    /// 课程流转状态
    /// </summary>
    public enum EMCourseTurnType
    {
        /**
         * 未解锁
         */
        NotUnLock,
        /**
         * 可上课
         */
        CanClass,
        /**
         * 已完成
         */
        Finish
    }

    /// <summary>
    /// 事件类型
    /// </summary>
    public enum EMEventType
    {
        /// <summary>
        ///  主动画
        /// </summary>
        MainAnimaionVideo = 1,
        /// <summary>
        /// 过场动画 不显示操作按钮 快进那些
        /// </summary>
        CutScene,
        /// <summary>
        /// 动画视频 显示操作按钮 快进那些
        /// </summary>
        AnimaionVideo,
        /// <summary>
        ///  答题
        /// </summary>
        Answer,
        /// <summary>
        /// 机器人搭建
        /// </summary>
        RobotAssembly,
        /// <summary>
        /// 编程试操作
        /// </summary>
        ProgramTestOperation,
        /// <summary>
        /// 编程教学
        /// </summary>
        ProgramTeach,
        /// <summary>
        /// 编程搭建
        /// </summary>
        Program,
        /// <summary>
        /// 小游戏
        /// </summary>
        MiniGame,
        /// <summary>
        /// 错题练习
        /// </summary>
        WrongTopicPractice,
        /// <summary>
        /// 作业
        /// </summary>
        HomeWork,
        /// <summary>
        /// 学习报告
        /// </summary>
        StudyReport,
        /// <summary>
        /// 点击交互
        /// </summary>
        Click,
        /// <summary>
        /// 擂台赛
        /// </summary>
        Challenge,
        /// <summary>
        /// 训练报告
        /// </summary>
        PraticeReport,
        //新的在lua加 c#非特殊情况不在新增课程事件类型
    }

    /// <summary>
    /// 课程事件状态枚举
    /// </summary>
    public enum EMEventState
    {
        None,
        /// <summary>
        /// 开始
        /// </summary>
        Start,
        /// <summary>
        /// 暂停
        /// </summary>
        Return,
        /// <summary>
        /// 结束
        /// </summary>
        End
    }

    public enum EMLinkType
    {
        /// <summary>
        /// 时间环节 到了时间开启下个事件
        /// </summary>
        TimeLink = 1,
        /// <summary>
        /// 顺序环节 该事件结束 直接到下个事件
        /// </summary>
        OrderLink,
        /// <summary>
        /// 该环节不会有单独的linkcell 放到下个linkid一起(主动画前面还有一个小动画的需求)
        /// </summary>
        OtherLink,
    }

    /// <summary>
    /// 事件功能类型
    /// </summary>
    public enum EMEventFunctionType
    {
        None = 0,
        /// <summary>
        /// 单纯的UI
        /// </summary>
        UI,
        /// <summary>
        /// 需要加载场景
        /// </summary>
        Scene,
    }

    public enum EMSeriesCourseWeb
    {
        Start,
        Finish,
    }

    #endregion

    /// <summary>
    /// 时间结构 年 月 日 时 分 秒
    /// </summary>
    public class TimeIntData
    {
        public int year;
        public int month;
        public int day;
        public int hour;
        public int minute;
        public int second;
        /// <summary>
        /// 周几
        /// </summary>
        public string weekStr;
    }

    /// <summary>
    /// AvPro视频资源
    /// </summary>
    public enum FileLocation
    {
        /// <summary>
        /// Url路径
        /// </summary>
        AbsolutePathOrURL,
        /// <summary>
        /// 跟Assets同级的路径
        /// </summary>
        RelativeToProjectFolder,
        /// <summary>
        /// 相对于StreamingAssets文件夹
        /// </summary>
        RelativeToStreamingAssetsFolder,
        /// <summary>
        /// 项目的根目录下
        /// </summary>
        RelativeToDataFolder,
        /// <summary>
        ///  固定路径
        /// </summary>
        RelativeToPersistentDataFolder,
    }

    /// <summary>
    /// 碰撞触发类型(发到lua层)
    /// </summary>
    public enum EMTriggerType
    {
        TriggerEnter2D = 1,
        TriggerStay2D = 2,
        TriggerExit2D = 3,
        CollisionEnter2D = 4,
        CollisionStay2D = 5,
        CollisionExit2D = 6,
        TriggerEnter = 7,
        TriggerStay = 8,
        TriggerExit = 9,
        CollisionEnter = 10,
        CollisionStay = 11,
        CollisionExit = 12,
    }
    #endregion

    /// <summary>
    /// 平台
    /// </summary>
    public enum SystemPlatform
    {
        PC = 0,
        Pico = 1,
        Quest = 2,
    }

    /// <summary>
    /// 平台预设路径
    /// </summary>
    public class SystemPlatformPrefabPath
    {
        public static string PC = "Prefab/Boot/XRRig_PC";
        public static string Pico = "Prefab/Boot/XRRig_Pico";
        public static string Quest = "Prefab/Boot/XRRig_Quest";
    }

    public enum EMComputerTestType
    {
        /// <summary>
        /// 网络设备拆装与机房上架实验
        /// </summary>
        NetworkEquipment,
        /// <summary>
        /// 计算机的构成与运行机制实验
        /// </summary>
        ComputerConstitution,
        /// <summary>
        /// 计算机网络综合布线VR系统实验
        /// </summary>
        VRSystem,
    }

    /// <summary>
    ///计算机实验工序类型 
    /// </summary>
    public enum EMTestProcessType
    {
        /// <summary>
        /// 拆
        /// </summary>
        PullDown,
        /// <summary>
        /// 装
        /// </summary>
        Hold,
    }

    /// <summary>
    /// 操作类型
    /// </summary>
    public enum EMHandOPType
    {
        None,
        /// <summary>
        /// 扳机
        /// </summary>
        Trigger,
        /// <summary>
        /// 碰触加扳机
        /// </summary>
        TouchAndTrigger,
        /// <summary>
        /// 先把模型放到队应位置 在用工具触碰模型
        /// </summary>
        GrabToolTouchModel,
        /// <summary>
        /// 扣扳机按住不放拖拽
        /// </summary>
        TriggerStill,
        /// <summary>
        /// UI
        /// </summary>
        UI,
        /// <summary>
        /// 碰触加扳机触碰物体然后再生成UI
        /// </summary>
        TouchAndTriggerFinialUI,
        /// <summary>
        /// 生成UI然后再碰触加扳机触碰物体
        /// </summary>
        CreateUITouchAndTrigger,
        /// <summary>
        /// 左手抓取碰触模型
        /// 把模型放到队应位置
        /// </summary>
        GrabTouchModel,
        /// <summary>
        /// 左键射线+扳机
        /// </summary>
        LeftRightLineAndTrigger,
        /// <summary>
        /// 左手物体右手工具并且工具需要触碰物体
        /// 然后按扳机播放动画
        /// </summary>
        LeftObjRightToolAndTouch,
        /// <summary>
        /// 左右手都能抓取碰触模型
        /// 把模型放到队应位置
        /// </summary>
        LeftRightGrabTouchModel,
        /// <summary>
        /// 右键射线+扳机
        /// </summary>
        RightLineAndTrigger,
        /// <summary>
        /// 长按右扳机1秒
        /// </summary>
        RightTriggerStill1S,
        /// <summary>
        /// 有UI安装步骤的
        /// </summary>
        UIInstallMove,
        /// <summary>
        /// 不做任何操作的类型
        /// </summary>
        MaxOpType = 999,
    }

    /// <summary>
    /// 操作结束之后判断类型（UI判断可以与其它并存）
    /// </summary>
    public enum EMEndOPType
    {
        None,
        /// <summary>
        /// 位置判断
        /// </summary>
        Position = 1,
        /// <summary>
        /// UI点击判断
        /// </summary>
        UI = 2,
        /// <summary>
        /// 碰触
        /// </summary>
        Touch = 3,
        /// <summary>
        /// 碰触加扳机
        /// </summary>
        TouchAnTrigger = 4,
        /// <summary>
        /// 动画判断（碰触加扳机后播放动画，待动画结束）
        /// </summary>
        Animation = 5,
        /// <summary>
        /// 手柄操作旋转
        /// </summary>
        CompleteRotion = 6,
        /// <summary>
        /// 抓取
        /// </summary>
        Grab = 7
    }

    /// <summary>
    /// 操作模型移动类型
    /// </summary>
    public enum OpModelMoveType
    {
        None,
        /// <summary>
        /// 世界坐标
        /// </summary>
        Word = 1,
        /// <summary>
        /// 相对坐标
        /// </summary>
        Local = 2,
        /// <summary>
        /// 放回
        /// </summary>
        Back = 3,
        /// <summary>
        /// 不作移动，播放动画
        /// </summary>
        PlayTimeLine = 4,
    }

    /// <summary>
    /// 工具类型
    /// </summary>
    public enum EMToolType
    {
        None = 20000,
        LuoSiDao,
        GuiZhi,
        XianCao,//线槽
        QiaoJia,//桥架
        DaXianQi,//打线器
        BoXianQian,//剥线钳
        BiaoQianJi,//标签机
    }

    public enum RayEventState
    {
        None,
        Enter,
        Click,
        Hover,
        Exit,
        Release,
        Up,
        Down,
    }

    /// <summary>
    /// 垃圾桶类型
    /// </summary>
    public enum EMHandType
    {
        Left,
        Right,
        /// <summary>
        /// 俩只手
        /// </summary>
        All,
    }

    public enum EMPunctureStep
    {
        None,   
        /// <summary>
        /// 选择左手固定点位
        /// </summary>
        SelectLeftPoint,
        /// <summary>
        /// 左手固定皮肤
        /// </summary>
        LeftHandFixSkin,
        /// <summary>
        /// 选择针管
        /// </summary>
        SelectNeedle,
        /// <summary>
        /// 选择穿刺点位
        /// </summary>
        SelectPoint,
        /// <summary>
        /// 选择进针角度
        /// </summary>
        SelectNeedleAngle,
        /// <summary>
        /// 进针开始
        /// </summary>
        JinZhen,
        /// <summary>
        /// 进针结束
        /// </summary>
        Puncture_0,
        /// <summary>
        /// 出针
        /// </summary>
        ChuZhen,
        /// <summary>
        /// 开始穿刺第一层
        /// </summary>
        Puncture_1,
        /// <summary>
        /// 开始穿刺第二层
        /// </summary>
        Puncture_2,
        /// <summary>
        /// 开始穿刺第三层
        /// </summary>
        Puncture_3,
        /// <summary>
        /// 左手抓取无菌棉球按压穿刺部位
        /// </summary>
        WuJunMianQiu,
        /// <summary>
        /// 选择无菌棉球 纱布 按压点
        /// </summary>
        SelectWuJunMianQiuPoint,
        /// <summary>
        /// 无菌棉球 纱布 按压
        /// </summary>
        WuJunMianQiuPress,
        /// <summary>
        /// 选择橡皮塞
        /// </summary>
        SelectXiangPiSai,
        /// <summary>
        /// 橡皮塞
        /// </summary>
        XiangPiSai,
        /// <summary>
        /// 检查样本
        /// </summary>
        JianChaYangBen,
    }

    public enum ApparatusItem { 
        /// <summary>
        /// 碘伏
        /// </summary>
        DianFu,
        /// <summary>
        /// 棉签
        /// </summary>
        MianQianBao,
        /// <summary>
        /// 橡皮塞
        /// </summary>
        XiangPiSai,
       /// <summary>
       /// 洞巾
       /// </summary>
        DongJin,
        /// <summary>
        /// 手套
        /// </summary>
        ShouTao,
        /// <summary>
        /// 纱布
        /// </summary>
        ShaBu,
        /// <summary>
        /// 注射器
        /// </summary>
        ZhuSheQi,
    }

    public enum EMComputerExerciseProcess
    {
        /// <summary>
        /// 主机元件
        /// </summary>
        Component,
        /// <summary>
        /// 主机系统
        /// </summary>
        System,
        /// <summary>
        /// 磁盘分区
        /// </summary>
        Disk,
    }
    /// <summary>
    /// 场景
    /// </summary>
    public enum EMScene
    {
        /// <summary>
        /// 构成
        /// </summary>
        Start0,
        /// <summary>
        /// 上架
        /// </summary>
        Start1,
        /// <summary>
        ///布线
        /// </summary>
        Start2,


        /// <summary>
        ///综合布线----中心机房场景
        /// </summary>
        CentralRoomScene = 5,
        /// <summary>
        ///综合布线---楼层分管机房场景
        /// </summary>
        RoomScene,
        /// <summary>
        ///综合布线----教室场景
        /// </summary>
        ClassScene,
        /// <summary>
        ///综合布线----办公室场景
        /// </summary>
        OfficeScene,
    }
    /// <summary>
    /// 操作阶段
    /// </summary>
    public enum EMOpState
    {
        /// <summary>
        /// 无状态
        /// </summary>
        NoneState,
        /// <summary>
        /// 基础认知状态
        /// </summary>
        BasicState,
        /// <summary>
        /// 训练状态
        /// </summary>
        TrainState,
        /// <summary>
        /// 考试状态
        /// </summary>
        CheckState,
    }

    public enum ToolTweenType
    {
        /// <summary>
        /// 旋转
        /// </summary>
        Rorate,
        /// <summary>
        /// 位移
        /// </summary>
        Move,
    }

    public enum ToolTweenMoveType
    {
        /// <summary>
        /// 会根据模型不同移动到不同的位置
        /// </summary>
        OperateModel,
        /// <summary>
        /// 表里配置的位置
        /// </summary>
        Config,
    }

    /// <summary>
    /// 模型手姿势
    /// </summary>
    public enum HHandPoseType
    {
        /// <summary>
        ///待机姿势
        /// </summary>
        Idel,
        /// <summary>
        ///把脉姿势
        /// </summary>
        BaMai,
        /// <summary>
        /// 摊开手掌姿势
        /// </summary>
        TangKai,
        /// <summary>
        /// 固定皮肤姿势食指中指分开
        /// </summary>
        GuDingPiFU_A,
        /// <summary>
        /// 固定皮肤姿势食指中指合并
        /// </summary>
        GuDingPiFU_B,
        /// <summary>
        /// 按压纱布姿势
        /// </summary>
        AnYaShaBu

    }

    public enum BingRenAniNode
    {
        None,
        ShaBu,
        DongJin,
    }

    /// <summary>
    /// 垃圾桶类型
    /// </summary>
    public enum TrashCanType
    {

        /// <summary>
        /// 医疗垃圾桶
        /// </summary>
        YiLiao,
    }
    public enum LogicType
    {
        None,
        /// <summary>
        /// 值类型是否达成
        /// </summary>
        ValueTpye,

        /// <summary>
        ///  操作是否完成
        /// </summary>
        OperateEnd,

        /// <summary>
        /// 数据组合日志描述
        /// </summary>
        GroupSend,
        


    }
    public enum ResultType
    {
        /// <summary>
        /// 错误失败
        /// </summary>
        Fail,
        /// <summary>
        /// 成功
        /// </summary>
        Succeed,


        /// <summary>
        /// 重复操作
        /// </summary>
        Repetition
    }
    public class OperateLogDataDisPose
    {
        /// <summary>
        /// 任务ID 
        /// </summary>
        public int ID;
        /// <summary>
        /// 日志描述 
        /// </summary>
        public string OperateName;
        /// <summary>
        /// 分数
        /// </summary>
        public string ScoreAlias;
        /// <summary>
        ///  类别 
        ///  举例：(0推荐项1必要性2不属于任何项)
        /// </summary>
        public int NecessaryOperation;

        /// <summary>
        /// 结果
        ///举例 (1正确0错误 )
        /// </summary>
        public int Result;
        //记录时间
        public string Time;
    }

    public class AddMockExamData
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string student;
        //public NEntity student;

        /// <summary>
        /// 得分点id
        /// </summary>
        public string checkpointEntity;

        /// <summary>
        /// 步骤名
        /// </summary>
        public string stepName;

        /// <summary>
        /// 判断条件名称
        /// </summary>
        public string name;

        /// <summary>
        /// 训练id
        /// </summary>
        public string trainInfo;

        /// <summary>
        /// 实际打分
        /// </summary>
        public float point;

        /// <summary>
        /// 别名
        /// </summary>
        public string alias;

        /// <summary>
        /// 是否操作
        /// </summary>
        public string IsDone;

        /// <summary>
        /// 步骤ID
        /// </summary>
        public int stepId;
    }

    public class TaskStepUpdate
    {
        /// <summary>
        /// userID
        /// </summary>
        public string userId;
        /// <summary>
        /// 训练ID
        /// </summary>
        public string trainInfo;
        /// <summary>
        /// 任务阶段
        /// </summary>
        public int feedId;
        /// <summary>
        /// 阶段描述
        /// </summary>
        public string feedDetail;
        /// <summary>
        /// 当前阶段数
        /// </summary>
        public int feedTotalNumber;
        /// <summary>
        /// 任务ID
        /// </summary>
        public int taskId;
        /// <summary>
        /// 任务名称
        /// </summary>
        public string taskName;
        /// <summary>
        /// 是否完成
        /// </summary>
        public string isDone;
    }
    public class FinishDate
    {
        /// <summary>
        /// 训练ID
        /// </summary>
        public string id;

        /// <summary>
        /// 训练细节
        /// </summary>
        public string detail;

        /// <summary>
        /// 成绩
        /// </summary>
        public float score;

        /// <summary>
        /// 使用时间
        /// </summary>
        public int timeUsed;

        /// <summary>
        /// 病例ID
        /// </summary>
        public int medRecordId;
        /// <summary>
        /// 累计历史数据
        /// </summary>
        public string historyDetail;
    }
    public class GetCourseData
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string userid;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string courseid;
    }

    public class P3dHitNearbyData
    {

        public string Interval;

        public string HitSpacing;

        public string HitLimit;


    }
    public enum FengHeType
    {
        None = 0,
       
    /// <summary>
    ///    单纯连续缝合,
    /// </summary>
    DanChu,
      /// <summary>
      ///  外八缝合,
      /// </summary>
     WaiBa,

      /// <summary>
      ///  内八缝合,
      /// </summary>
      NeiBa,

      /// <summary>
      ///  间断垂直褥式外翻缝合,
      /// </summary>
      JianDuanCuiZhiWaiFan,

        /// <summary>
        /// 间断垂直褥式内翻缝合,
        /// </summary>
        JianDuanCuiZhiNeiFan,
        
        /// <summary>
        /// 单纯间断缝合,
        /// </summary>
        DanChuJianDan,

        /// <summary>
        /// 间断水平褥式内翻缝合,
        /// </summary>
        JianDuanShuiPingNeiFan,

        /// <summary>
        /// 间断水平褥式外翻缝合,
        /// </summary>
        JianDuanShuiPingWaiFan,

        /// <summary>
        ///   连续外翻缝合,
        /// </summary>
        LianXuWaiFan,


        /// <summary>
        ///  荷包
        /// </summary>
        HeBao,

    }

    [SerializeField]
    public class DeviceBindData
    {

        public DeviceBindDataInfo data;

        public string message;

        public string status;
    }

    [SerializeField]
    public class DeviceBindDataInfo
    {
        public int id;
        public string name;
        public string createTime;
        public string updateTime;
        public int userID;
        public string deviceID;
        public string deviceName;

    }

}