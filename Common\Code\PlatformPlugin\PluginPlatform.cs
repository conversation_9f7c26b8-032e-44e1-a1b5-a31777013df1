﻿/// <summary>
/// AndoridPlugin
/// </summary>
/// <remarks>
/// 2021.1.19: 创建. 谌安 <br/>
/// 与安卓/ios平台交互 <br/>
/// </remarks>
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using UnityEngine;
#if UNITY_ANDROID
using UnityEngine.Android;
#endif

namespace GLib.Common
{
    public enum PluginPlatformType
    {
        None = 0,
        JL_Android = 1,
        JL_IOS = 100
    }

    public enum PaymentState{
        OK,
        Cancel,
        Other,
    }

    public interface IPluginPlatform
    {
        //注册监听
        void RegListener(IPluginListener listener);

        // 取消监听
        void UnRegListener(IPluginListener listener);
        /// <summary>
        /// 设置用户ID
        /// </summary>
        /// <param name="userID"></param>
        void SetBuglyUserID(string userID);

        //存放照片路径
        void addImageToGallery(string fileName);
        /// <summary>
        ///  变换手机方向
        /// </summary>
        /// <param name="screenVector"></param>切屏之前大小
        /// <param name="bHorizontalScreen"></param>是否横屏
        /// <param name="action"></param>
        void ChangeScreenOrientation(Vector2 screenVector, bool bHorizontalScreen, Action action);
        /// <summary>
        /// 变换手机方向完成回调
        /// </summary>
        /// <param name="bHorizontalScreen">是否横屏</param>
        void ChangeScreenOrientationFinish(int changeScreenIndex);
        /// <summary>
        /// 调用系统相册
        /// </summary>
        void GetSystemPhoto();
        /// <summary>
        /// 调用系统摄像机 没搞安卓
        /// </summary>
        void TakePhoto();

        string PID();

        string Radid();

        string Rsid();

        /// <summary>
        /// 打开摄像机拍照保存至系统相册
        /// </summary>
        void OpenCameraAndSaveTexture();
        //IOS 支付 内购ID 订单ID 价格 名字
        void IOSZF(string productID, string PayNum,
            string OrderItem, string OrderNo, string OrderSign, string currencyType);

        void IOSZFComplete(string transactionIdentifier);

        /// <summary>
        /// 获取系统相册选择的图片  平台回调回来信息    ios未实现
        /// </summary>
        /// <param name="str"></param>
        void GetImagePath(string str);
        /// <summary>
        /// 设置状态栏  暂未实现功能
        /// </summary>
        /// <param name="isShwo">是否显示</param>
        void SetStatusBar(bool isShwo);
        /// <summary>
        /// 获得mac地址
        /// </summary>
        /// <returns></returns>
        string GetMac();

        //--------------login
        void GetPhoneInfo(Action<string> phoneInfo);

        void AuthLogin(Action<string> info);
        /// <summary>
        /// 查找手机是否有指定APP
        /// </summary>
        /// <param name="AppIdentify">APP表示</param>
        /// <returns></returns>
        bool FindPhoneHaveAssignApp(string AppIdentify);

        void SetAgreementDialogState(int state);
        /// <summary>
        /// 检查任意权限
        /// </summary>
        /// <param name="permission"></param>
        /// <param name="permissionState">1:本次拒绝 2:拒绝并不允许 3:已获得权限</param>
        void CheckPermission(string permission,Action<int> permissionState);

        /// <summary>
        /// 获得推、广信息
        /// </summary>
        /// <returns></returns>
        string GetExtensionInfo();
        /// <summary>
        /// 权限回调
        /// </summary>
        /// <param name="permissionState"></param>
        void PermissionState(string permissionState);
        /// <summary>
        /// 判断是否是刘海屏
        /// </summary>
        /// <returns></returns>
        bool NotchInScreen();
        int[] GetNotchSize();

        /// <summary>
        /// 支付宝支付
        /// </summary>
        /// <param name="orderInfo"></param>
        void AliPayByApp(string orderInfo, Action<PaymentState, string> callBack);
        /// <summary>
        /// 微信支付
        /// </summary>
        /// <param name="appId"></param>
        /// <param name="partnerId"></param>
        /// <param name="prepayId"></param>
        /// <param name="nonceStr"></param>
        /// <param name="timeStamp"></param>
        /// <param name="sign"></param>
        void WxPayByApp(string appId, string partnerId, string prepayId, string nonceStr, string timeStamp, string sign, Action<PaymentState, string> callBack);
        
        /// <summary>
        /// 检测应用市场更新
        /// </summary>
        /// <param name="type"></param>
        void CheckMarketUpdate(EMMarketType type);
    }

    public class PluginPlatform
    {
        private static PluginPlatform _Instance;

        private IPluginPlatform m_PluginP = null;

        public int gAppendPosX = 88;

        public static PluginPlatform Instance
        {
            get
            {
                //为方便编辑器中使用，这里在编辑器模式下（非运行模式）手动查找场景中的实例

                if (_Instance == null)
                {
                    _Instance = new PluginPlatform();

                }
                return _Instance;
            }
        }

        public PluginPlatform()
        {
            GameObject obj = GameObject.Find("_Manager(Clone)");

            if (obj != null)
            {
                GameObject cur = new GameObject("PluginPlatform");

                cur.transform.SetParent(obj.transform, false);

#if UNITY_ANDROID && !UNITY_EDITOR
                m_PluginP = (IPluginPlatform)cur.AddComponent<AndoridPlugin>();
#elif UNITY_IPHONE && !UNITY_EDITOR
                m_PluginP = (IPluginPlatform)cur.AddComponent<IOSPlugin>();

#else
                m_PluginP = (IPluginPlatform)cur.AddComponent<IOSPlugin>();
#endif
            }
        }

        public IPluginPlatform Plugin()
        {
            return m_PluginP;
        }

        public bool GetIsIntranet()
        {
            //int ver = GameIni.Instance.GetInt(CGameSDKInIKey.SDK_RELEASE_VER, 1);

            return false;
        }
    }

    static class IOSSDKOS
    {
#if UNITY_IPHONE
        //[DllImport("__Internal")]
        //public static extern void  JLInit(bool isIntranet);
        //[DllImport("__Internal")]
        //public static extern void addImageToGallery(string fileName);
        //[DllImport("__Internal")]
        //public static extern string  UUID();
        //[DllImport("__Internal")]
        //public static extern void IOSZF(string productID, string UserID, string PayNum,
        //    string OrderItem, string OrderNo, string OrderSign, string currencyType);
        //[DllImport("__Internal")]
        //public static extern void IOSZFComplete(string transactionIdentifier);
        [DllImport("__Internal")]
        public static extern void _iosOpenPhotoLibrary();
        //[DllImport("__Internal")]
        //public static extern void _iosOpenCamera();
        //[DllImport("__Internal")]
        //public static extern void GetPhotoPermission();
        //[DllImport("__Internal")]
        //public static extern void GetPhotoPermissionState();
        //[DllImport("__Internal")]
        //public static extern void GetCameraPermission();
        //[DllImport("__Internal")]
        //public static extern void GetCameraPermissionState();
        //[DllImport("__Internal")]
        //public static extern void GetMicrophonePermission();
        //[DllImport("__Internal")]
        //public static extern void GetMicrophonePermissionState();
        [DllImport("__Internal")]
        public static extern void _WechatPay(string appid, string partnerid, string prepayid, string noncestr, string timestamp, string sign);
        [DllImport("__Internal")]
        public static extern void _AliPay(string orderInfo);
#else
        public static void JLInit(bool isIntranet) { }
        public static void addImageToGallery(string fileName) { }

        public static void IOSZF(string productID, string UserID, string PayNum,
          string OrderItem, string OrderNo, string OrderSign, string currencyType)
        { }
        public static void IOSZFComplete(string transactionIdentifier) { }
        public static void _iosOpenPhotoLibrary() { }
        public static void _iosOpenCamera() { }
        public static void GetPhotoPermission() { }
        public static void GetPhotoPermissionState() { }
        public static void GetCameraPermission() { }
        public static void GetCameraPermissionState() { }
        public static void GetMicrophonePermission() { }
        public static void GetMicrophonePermissionState() { }
        public static void _WechatPay(string appid, string partnerid, string prepayid, string noncestr, string timestamp, string sign) { }
        public static void _AliPay(string orderInfo) { }
        public static string UUID() { return SystemInfo.deviceUniqueIdentifier; }
#endif
    }

    public class IOSPlugin : MonoBehaviour, IPluginPlatform
    {
        /// <summary>
        /// 手机切屏回调
        /// </summary>
        private Dictionary<int, Action> m_ScreenOrientationCallBackDic = new Dictionary<int, Action>();
        /// <summary>
        /// 切屏协程
        /// </summary>
        private Dictionary<int, IEnumerator> m_ScreenOrientationcoroutineDic = new Dictionary<int, IEnumerator>();

        private List<IPluginListener> m_listeners = new List<IPluginListener>();

        /// <summary>
        /// 切屏队列
        /// </summary>
        private int m_changeScreenIndex = 0;
        private Action<string> m_phoneInfo;
        private Action<string> m_autoLoginInfo;
        private Action<int> m_permissionState;
        //WaitForSeconds m_ScreenOrientation = new WaitForSeconds(0.3f);
        public void RegListener(IPluginListener listener)
        {
            m_listeners.Add(listener);
        }

        public void UnRegListener(IPluginListener listener)
        {
            m_listeners.Remove(listener);
        }

        /// <summary>
        /// 设置用户ID
        /// </summary>
        /// <param name="userID"></param>
        public void SetBuglyUserID(string userID)
        {

        }

        private void Awake()
        {

        }

        private void Start()
        {
            //IOSSDKOS.JLInit(false);
        }

        public string GetMac()
        {
            string strMacId = string.Empty;
            if (string.IsNullOrEmpty(strMacId))
            {
              //  strMacId = IOSSDKOS.UUID();
            }
            return strMacId;
        }
        public string PID()
        {
            return "";
        }

        public string Radid()
        {
#if UNITY_EDITOR
            string radid = PlayerPrefs.GetString("EditorRadid");
            if (string.IsNullOrEmpty(radid))
            {
                return (int)PluginPlatformType.JL_IOS + "";
            }
            else
            {
                return radid;
            }
#else
          return (int)PluginPlatformType.JL_IOS + "";
#endif
        }

        public string Rsid()
        {
            return "0";
        }

        public void IOSZF(string productID, string PayNum,
            string OrderItem, string OrderNo, string OrderSign, string currencyType)
        {
            //IOSSDKOS.IOSZF(productID, "", PayNum, OrderItem, OrderNo, OrderSign, currencyType);
        }

        public void JLZFCallback(string data)
        {
            //Debug.Log("ZF  " + data);

            string[] dataList = data.Split('_');

            if (dataList.Length > 1)
            {
                //返回ID  0：成功  -1:失败/取消
                int resultId = int.Parse(dataList[0]);

                string msg = dataList[1];

                foreach (IPluginListener t in m_listeners)
                    t.PayCallback(resultId, msg);
            }
        }

        public void IOSZFComplete(string transactionIdentifier)
        {
           // IOSSDKOS.IOSZFComplete(transactionIdentifier);
        }

        public void addImageToGallery(string fileName)
        {
           // IOSSDKOS.addImageToGallery(fileName);
        }

        public void ChangeScreenOrientation(Vector2 screenVector, bool bHorizontalScreen, Action action)
        {
            m_changeScreenIndex++;
            if (m_ScreenOrientationcoroutineDic.ContainsKey(m_changeScreenIndex))
            {
                if (m_ScreenOrientationcoroutineDic[m_changeScreenIndex] != null)
                {
                    StopCoroutine(m_ScreenOrientationcoroutineDic[m_changeScreenIndex]);
                }
            }
            IEnumerator task = ChangeScreenOrientationEx(screenVector, bHorizontalScreen, action, m_changeScreenIndex);
            m_ScreenOrientationcoroutineDic[m_changeScreenIndex] = task;
            StartCoroutine(task);
        }
        /// <summary>
        /// 变换手机方向
        /// </summary>
        /// <param name="bHorizontalScreen">是否横屏</param>
        private IEnumerator ChangeScreenOrientationEx(Vector2 screenVector, bool bHorizontalScreen, Action action, int changeScreenIndex)
        {
            bool iswait = false;
            m_ScreenOrientationCallBackDic[changeScreenIndex] = action;
            DateTime starttime, endtime;
            starttime = DateTime.Now;
            if (bHorizontalScreen)
            {//横屏
                Screen.orientation = ScreenOrientation.LandscapeLeft;
                Screen.autorotateToPortrait = false;
                Screen.autorotateToPortraitUpsideDown = false;
                Screen.autorotateToLandscapeLeft = true;
                Screen.autorotateToLandscapeRight = true;
                while (Screen.width != screenVector.y || Screen.height != screenVector.x)
                {
                    endtime = DateTime.Now;
                    //如果5秒还没切屏完成,直接切屏回调
                    if (GHelp.GetTwoDateTimeInterval(starttime, endtime, 1) > 2)
                    {
                        iswait = true;
                        ChangeScreenOrientationFinish(changeScreenIndex);
                        if (m_ScreenOrientationcoroutineDic[changeScreenIndex] != null)
                        {
                            StopCoroutine(m_ScreenOrientationcoroutineDic[changeScreenIndex]);
                        }
                    }
                    yield return null;
                }
            }
            else
            {
                //竖屏
                Screen.orientation = ScreenOrientation.Portrait;
                Screen.autorotateToPortrait = true;
                //竖屏上下颠倒
                Screen.autorotateToPortraitUpsideDown = false;
                Screen.autorotateToLandscapeLeft = false;
                Screen.autorotateToLandscapeRight = false;
                while (Screen.width != screenVector.y || Screen.height != screenVector.x)
                {
                    endtime = DateTime.Now;
                    //如果5秒还没切屏完成,直接切屏回调
                    if (GHelp.GetTwoDateTimeInterval(starttime, endtime, 1) > 2)
                    {
                        iswait = true;
                        ChangeScreenOrientationFinish(changeScreenIndex);
                        if (m_ScreenOrientationcoroutineDic[changeScreenIndex] != null)
                        {
                            StopCoroutine(m_ScreenOrientationcoroutineDic[changeScreenIndex]);
                        }
                    }
                    yield return null;
                }
            }
            //如果等待了5秒的话 那里已经执行了完成回调 这里就不需要在执行了
            if (!iswait)
            {
                ChangeScreenOrientationFinish(changeScreenIndex);
                if (m_ScreenOrientationcoroutineDic[changeScreenIndex] != null)
                {
                    StopCoroutine(m_ScreenOrientationcoroutineDic[changeScreenIndex]);
                }
            }
        }

        public void ChangeScreenOrientationFinish(int changeScreenIndex)
        {
            TRACE.TraceLn("ChangeScreenOrientationFinish");
            if (m_ScreenOrientationCallBackDic[changeScreenIndex] != null)
            {
                m_ScreenOrientationCallBackDic[changeScreenIndex].Invoke();
            }
        }

        public void GetSystemPhoto()
        {
#if UNITY_IPHONE
            TRACE.WarningLn("调取ios相册");
            IOSSDKOS._iosOpenPhotoLibrary();
#endif
        }

        public void TakePhoto()
        {
#if UNITY_IPHONE
            //IOSSDKOS._iosOpenCamera();
#endif
        }

        public void OpenCameraAndSaveTexture()
        {
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_OPENCAMERA_SAVETEXTURE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
        }

        public void GetImagePath(string str)
        {
            if (str.Equals("Cancel"))
            {
                TRACE.TraceLn("Unity iOS取消选择 --> ");
                return;
            }
            TRACE.TraceLn("ios GetImagePath GetImagePath str = " + str);
            GHelp.FireExecute((ushort)ViewLogicDef.GET_SYSTEMPHOTO_PATH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, str);
            Texture2D tex = new Texture2D(2, 2, TextureFormat.ARGB32, false);
            try
            {
                byte[] bytes = System.Convert.FromBase64String(str);
                tex.LoadImage(bytes);
                TRACE.TraceLn("Unity tex " + tex.width + "; height= " + tex.height);
                GHelp.FireExecute((ushort)ViewLogicDef.GET_SYSTEMPHOTO_TEXTURE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, tex);
            }
            catch (System.Exception ex)
            {
                TRACE.ErrorLn(ex.Message);
            }
        }

        public void SetStatusBar(bool isShwo)
        {
        }

        //--------------login
        public void GetPhoneInfo(Action<string> phoneInfo)
        {
            m_phoneInfo = phoneInfo;
            phoneInfo.Invoke("未知错误&电信&1111****1111");
        }

        public void GetPhoneInfoCallback(string s)
        {
            Debug.LogWarning(s);
        }

        public void AuthLogin(Action<string> info)
        {
            m_autoLoginInfo = info;
            info.Invoke("1&tokentoken");

        }

        public void AuthLoginCallback(string s)
        {
            if (m_autoLoginInfo != null)
            {//1&tokentoken   第一个值1代表获取成功  第二个值代表token
                m_autoLoginInfo.Invoke(s);
            }
        }

        public bool FindPhoneHaveAssignApp(string AppIdentify)
        {
            return false;
        }

        public void SetAgreementDialogState(int state)
        {

        }

        /// <summary>
        /// 检查任意权限
        /// </summary>
        /// <param name="permission"></param>
        /// <param name="permissionState">1:本次拒绝 2:拒绝并不允许 3:已获得权限</param>
        public void CheckPermission(string permission, Action<int> permissionState)
        {
            switch (permission)
            {
                case PermissionDef.Camera:
                    {
                       // IOSSDKOS.GetCameraPermission();
                        m_permissionState = permissionState;
                    }
                    break;
                case PermissionDef.ExternalStorageRead:
                    {
                        //IOSSDKOS.GetPhotoPermission();
                        m_permissionState = permissionState;
                    }
                    break;
                case PermissionDef.Microphone:
                    {
                        //IOSSDKOS.GetMicrophonePermission();
                        m_permissionState = permissionState;
                    }
                    break;
                default:
                    {
                        permissionState(3);
                    }
                    break;
            }
            
        }

        public void PermissionState(string permissionState)
        {
            TRACE.TraceLn("获取的权限结果:"+ permissionState);
            if (m_permissionState != null)
            {
                m_permissionState(Convert.ToInt32(permissionState));
            }
        }

        public bool NotchInScreen()
        {
            //return IOSSDKOS.IsIphoneX();
            return false;
        }

        public int[] GetNotchSize()
        {
            int[] notchsize = new int[2] { 0, 0 };
            return notchsize;
        }

        public string GetExtensionInfo()
        {
            return "";
        }

#region 支付相关
        Action<PaymentState, string> OnOpenAliPayAction;

        public void AliPayByApp(string orderInfo, Action<PaymentState, string> callBack)
        {
            TRACE.WarningLn("iOS支付宝支付!");
            OnOpenAliPayAction = callBack;
            IOSSDKOS._AliPay(orderInfo);
        }

        public void AliPayCallback(string content)
        {
            TRACE.WarningLn($"iOS支付宝支付回调=[{content}]");
            if(content.Equals("true")){
                OnOpenWechatPayAction?.Invoke(PaymentState.OK, "1");
            }
            else{
                OnOpenWechatPayAction?.Invoke(PaymentState.Cancel, "0");
            }
        }

        Action<PaymentState, string> OnOpenWechatPayAction;
        public void WxPayByApp(string appId, string partnerId, string prepayId, string nonceStr, string timeStamp, string sign, Action<PaymentState, string> callBack)
        {
            TRACE.WarningLn("iOS微信支付!");
            OnOpenWechatPayAction = callBack;
            IOSSDKOS._WechatPay(appId, partnerId, prepayId, nonceStr, timeStamp, sign);
        }
 
        public void WXPayCallback(string content)
        {
            TRACE.WarningLn($"iOS微信支付回调=[{content}]");
    
            string[] args = content.Split(';');
    
            switch (args[0])
            {
                case "0":
                    OnOpenWechatPayAction?.Invoke(PaymentState.OK, args[1]);
                    break;
                case "-2":
                    OnOpenWechatPayAction?.Invoke(PaymentState.Cancel, args[1]);
                    break;
                default:
                    OnOpenWechatPayAction?.Invoke(PaymentState.Other, args[1]);
                    break;
    
            }
        }
#endregion

        public void CheckMarketUpdate(EMMarketType type)
        {
        }
    }

    public class AndoridPlugin : MonoBehaviour, IPluginPlatform
    {
        private static AndroidJavaObject m_AndroidJO;
        private static AndroidJavaObject m_Window;

        private List<IPluginListener> m_listeners = new List<IPluginListener>();

        /// <summary>
        /// 手机切屏回调
        /// </summary>
        private Dictionary<int, Action> m_ScreenOrientationCallBackDic = new Dictionary<int, Action>();
        /// <summary>
        /// 切屏协程
        /// </summary>
        private Dictionary<int, IEnumerator> m_ScreenOrientationcoroutineDic = new Dictionary<int, IEnumerator>();

        /// <summary>
        /// 切屏队列
        /// </summary>
        private int m_changeScreenIndex = 0;
        /// <summary>
        /// 状态栏显示隐藏值
        /// </summary>
        private int m_flagsValue = 0;
        //WaitForSeconds m_ScreenOrientation = new WaitForSeconds(0.3f);
        private Action<string> m_phoneInfo;
        private Action<string> m_autoLoginInfo;
        private Action<int> m_permissionState;
        public void Awake()
        {
#if UNITY_ANDROID && !UNITY_EDITOR

            try
            {
                AndroidJavaClass jc = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                if (null == jc)
                {
                }
                m_AndroidJO = jc.GetStatic<AndroidJavaObject>("currentActivity");
                //Debug.LogWarning($"YSH Awake m_AndroidJO = {m_AndroidJO.GetType()}");

                m_AndroidPayJo = new AndroidJavaObject("com.plugins.androidLib.MainActivity");
                // m_AndroidPayJo.Call("SendMsgToUnity", gameObject.name, "ReceiveFromAndroid", "YSH 44332211");
                // Debug.LogWarning($"YSH InitPay2 m_AndroidPayJo is null = {m_AndroidPayJo == null}, m_AndroidPayJo == m_AndroidJo: {m_AndroidPayJo.Equals(m_AndroidJO)}, Type = {m_AndroidPayJo.GetType()}");
                //m_AndroidPayJo.CallStatic("InitWeChat", "PackageName", "AppID");
                m_AndroidPayJo.CallStatic("InitAndroidLib");
                SetCrashAppVersion();
            }
            catch (Exception e)
            {

            }
#endif
        }

        private void ReceiveFromAndroid(string message)
        {
            Debug.LogWarning($"YSH ReceiveFromAndroid msg = {message}");
            GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, Api.NTR("Android msg = " + message));
        }

        public void RegListener(IPluginListener listener)
        {
        }

        public void UnRegListener(IPluginListener listener)
        {
        }

        public void SetCrashAppVersion()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                m_AndroidJO.Call("SetCrashAppVersion", ProductConfig.Version);
            }
            catch (Exception e)
            {

            }
#endif
        }

        /// <summary>
        /// 设置用户ID
        /// </summary>
        /// <param name="userID"></param>
        public void SetBuglyUserID(string userID)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                m_AndroidJO.Call("SetBuglyUserID", userID);
            }
            catch (Exception e)
            {

            }
#endif
        }

        public string GetMac()
        {
            string strMacId = string.Empty;
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                if (string.IsNullOrEmpty(strMacId))
                {
                    strMacId = m_AndroidJO.Call<string>("getMac");
                }
            }
            catch (Exception e)
            {
                Debug.Log("SDK  getMac");
            }
#endif
            TRACE.TraceLn("macaddress:" + strMacId);
            return strMacId;
        }

        public string PID()
        {
            string tmp = "1";
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                tmp = m_AndroidJO.Call<string>("JLPID");
            }
            catch (Exception e)
            {
                TRACE.WarningLn("PID Not Get");
            }
#endif
            return tmp;
        }

        public string Radid()
        {
            string tmp = "";
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                tmp = m_AndroidJO.Call<string>("JLRadid");
            }
            catch (Exception e)
            {
                TRACE.WarningLn("Radid Not Get");
            }
#endif
            return tmp;
        }

        public string Rsid()
        {
            string tmp = "";
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                tmp = m_AndroidJO.Call<string>("JLRsid");
            }
            catch (Exception e)
            {
                TRACE.WarningLn("Rsid Not Get");
            }
#endif
            return tmp;
        }

        public void IOSZF(string productID, string PayNum,
           string OrderItem, string OrderNo, string OrderSign, string currencyType)
        {
        }

        public void IOSZFComplete(string transactionIdentifier)
        {
        }

        public void addImageToGallery(string fileName)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                m_AndroidJO.CallStatic<int>("addImageToGallery", fileName);
            }
            catch (Exception e)
            {
                TRACE.WarningLn("addImageToGallery Not Get");
            }
#endif
        }

        public void ChangeScreenOrientation(Vector2 screenVector, bool bHorizontalScreen, Action action)
        {
            m_changeScreenIndex++;
            if (m_ScreenOrientationcoroutineDic.ContainsKey(m_changeScreenIndex))
            {
                if (m_ScreenOrientationcoroutineDic[m_changeScreenIndex] != null)
                {
                    StopCoroutine(m_ScreenOrientationcoroutineDic[m_changeScreenIndex]);
                }
            }
            IEnumerator task = ChangeScreenOrientationEx(screenVector, bHorizontalScreen, action, m_changeScreenIndex);
            m_ScreenOrientationcoroutineDic[m_changeScreenIndex] = task;
            StartCoroutine(task);
        }
        /// <summary>
        /// 变换手机方向
        /// </summary>
        /// <param name="bHorizontalScreen">是否横屏</param>
        private IEnumerator ChangeScreenOrientationEx(Vector2 screenVector, bool bHorizontalScreen, Action action, int changeScreenIndex)
        {
            bool iswait = false;
            m_ScreenOrientationCallBackDic[changeScreenIndex] = action;
            DateTime starttime, endtime;
            starttime = DateTime.Now;
            TRACE.TraceLn("bHorizontalScreen:" + bHorizontalScreen);
            if (bHorizontalScreen)
            {//横屏
                //Screen.SetResolution(1920, 1080, false);
                Screen.orientation = ScreenOrientation.LandscapeLeft;
                Screen.autorotateToPortrait = false;
                Screen.autorotateToPortraitUpsideDown = false;
                Screen.autorotateToLandscapeLeft = true;
                Screen.autorotateToLandscapeRight = true;
                while (Screen.width != screenVector.y || Screen.height != screenVector.x)
                {
                    endtime = DateTime.Now;
                    //如果5秒还没切屏完成,直接切屏回调
                    if (GHelp.GetTwoDateTimeInterval(starttime, endtime, 1) > 2)
                    {
                        iswait = true;
                        ChangeScreenOrientationFinish(changeScreenIndex);
                        if (m_ScreenOrientationcoroutineDic[changeScreenIndex] != null)
                        {
                            StopCoroutine(m_ScreenOrientationcoroutineDic[changeScreenIndex]);
                        }
                    }
                    yield return null;
                }
            }
            else
            {
                // Screen.SetResolution(1080, 1920, false);
                //竖屏
                Screen.orientation = ScreenOrientation.Portrait;
                Screen.autorotateToPortrait = true;
                //竖屏上下颠倒
                Screen.autorotateToPortraitUpsideDown = false;
                Screen.autorotateToLandscapeLeft = false;
                Screen.autorotateToLandscapeRight = false;
                while (Screen.width != screenVector.y || Screen.height != screenVector.x)
                {
                    endtime = DateTime.Now;
                    //如果5秒还没切屏完成,直接切屏回调
                    if (GHelp.GetTwoDateTimeInterval(starttime, endtime, 1) > 2)
                    {
                        iswait = true;
                        ChangeScreenOrientationFinish(changeScreenIndex);
                        if (m_ScreenOrientationcoroutineDic[changeScreenIndex] != null)
                        {
                            StopCoroutine(m_ScreenOrientationcoroutineDic[changeScreenIndex]);
                        }
                    }
                    yield return null;
                }
            }
            //如果等待了5秒的话 那里已经执行了完成回调 这里就不需要在执行了
            if (!iswait)
            {
                ChangeScreenOrientationFinish(changeScreenIndex);
                if (m_ScreenOrientationcoroutineDic[changeScreenIndex] != null)
                {
                    StopCoroutine(m_ScreenOrientationcoroutineDic[changeScreenIndex]);
                }
            }
        }

        public void ChangeScreenOrientationFinish(int changeScreenIndex)
        {
            TRACE.TraceLn("ChangeScreenOrientationFinish");
            if (m_ScreenOrientationCallBackDic[changeScreenIndex] != null)
            {
                m_ScreenOrientationCallBackDic[changeScreenIndex].Invoke();
            }
        }


        public void GetSystemPhoto()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                TRACE.WarningLn("调取Android相册");
                m_AndroidJO.Call("TakePhoto");
            }
            catch (Exception e)
            {
                TRACE.WarningLn("GetSystemPhoto Not Get");
            }
#endif
        }

        public void TakePhoto()
        {

        }

        public void GetImagePath(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                TRACE.TraceLn("Unity Android取消选择 --> ");
                return;
            }
            TRACE.TraceLn("Android GetImagePath===str:" + str);
            //str = Application.persistentDataPath + "/" + str;
            GHelp.FireExecute((ushort)ViewLogicDef.GET_SYSTEMPHOTO_PATH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, str);
            GHelp.LoadFileTexture(str, (texture) =>
            {
                // GHelp.addSystemTipsWithIcon(Api.TR("测试获取图片1"), EMFInfoIcon.Icon_True);
                GHelp.FireExecute((ushort)ViewLogicDef.GET_SYSTEMPHOTO_TEXTURE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, texture);
            });
        }

        public void OpenCameraAndSaveTexture()
        {
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_OPENCAMERA_SAVETEXTURE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
        }



        public void SetStatusBar(bool isShwo)
        {
#if UNITY_ANDROID
            if (m_Window == null)
            {
                if (m_AndroidJO != null)
                {
                    m_Window = m_AndroidJO.Call<AndroidJavaObject>("getWindow");
                }
            }
            applyUIStates(isShwo);
#endif
        }

        private void applyUIStates(bool isShwo)
        {
            if (Application.platform == RuntimePlatform.Android)
            {
                m_flagsValue = 0;
                if (isShwo)
                {
                    //显示
                    m_flagsValue |= 0x00000800;
                }
                else
                {
                    //隐藏
                    m_flagsValue |= 0x00000400 | 0x00000100;
                }
                //if (Screen.fullScreen) Screen.fullScreen = false;
                //setFlags(newFlagsValue);
                //设置状态栏显示状态
                runOnAndroidUiThread(setFlagsInThread);
                runOnAndroidUiThread(setSystemUiVisibilityInThread);

                //改变状态栏背景颜色
                runOnAndroidUiThread(applyUIColorsAndroidInThread);

            }
        }
        private void runOnAndroidUiThread(Action target)
        {
            m_AndroidJO.Call("runOnUiThread", new AndroidJavaRunnable(target));
        }
        private void setFlagsInThread()
        {
            m_Window.Call("setFlags", m_flagsValue, (int)0x7FFFFFFF);
        }
        private void setSystemUiVisibilityInThread()
        {
            using (var view = m_Window.Call<AndroidJavaObject>("getDecorView"))
            {
                view.Call("setSystemUiVisibility", 0x00002000);
            }
        }
        /// <summary>
        /// 设置背景颜色
        /// </summary>
        private static void applyUIColorsAndroidInThread()
        {
            m_Window.Call("setStatusBarColor", unchecked((int)0xffFFFFFF));
        }

        //--------------login
        public void GetPhoneInfo(Action<string> phoneInfo)
        {
            m_phoneInfo = phoneInfo;
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidJO != null)
            {
                m_AndroidJO.Call("GetPhoneInfo");
            }
#endif
        }

        public void GetPhoneInfoCallback(string s)
        {
            if (m_phoneInfo != null)
            {//true&电信&181****2315   第一个值true代表获取成功  第二个值代表运营商  第三个值 代表手机号信息
                m_phoneInfo.Invoke(s);
            }
            Debug.LogWarning("OnTokenListener Success callback:" + s);
        }

        public void AuthLogin(Action<string> info)
        {
            m_autoLoginInfo = info;
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidJO != null)
            {
                m_AndroidJO.Call("AuthLogin");
            }
#endif
        }

        public void AuthLoginCallback(string s)
        {
            if (m_autoLoginInfo != null)
            {//1&tokentoken   第一个值1代表获取成功  第二个值代表token
                m_autoLoginInfo.Invoke(s);
            }
        }

        public bool FindPhoneHaveAssignApp(string AppIdentify)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidJO != null)
            {
                AndroidJavaObject packageManager = m_AndroidJO.Call<AndroidJavaObject>("getPackageManager");
                AndroidJavaObject appList = packageManager.Call<AndroidJavaObject>("getInstalledPackages", 0);
                int num = appList.Call<int>("size");
                for (int i = 0; i < num; i++)
                {
                    AndroidJavaObject appInfo = appList.Call<AndroidJavaObject>("get", i);
                    string packageNew = appInfo.Get<string>("packageName");
                    if (packageNew.CompareTo(AppIdentify) == 0)
                    {
                        return true;
                    }
                }
            }
#endif
            return false;
        }

        public void SetAgreementDialogState(int state)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidJO != null)
            {
                m_AndroidJO.Call("SetAgreementDialogState", state + "");
            }
#endif
        }

        /// <summary>
        /// 检查任意权限
        /// </summary>
        /// <param name="permission"></param>
        /// <param name="permissionState">1:本次拒绝 2:拒绝并不允许 3:已获得权限</param>
        public void CheckPermission(string permission, Action<int> permissionState)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidJO != null)
            {
                m_permissionState = permissionState;
                m_AndroidJO.CallStatic("CheckPermission", permission);
            }
#endif
        }

        public void PermissionState(string permissionState)
        {
            TRACE.TraceLn("GHelp_进入到PermissionState");
            if (m_permissionState != null)
            {
                m_permissionState(Convert.ToInt32(permissionState));
            }
        }


        public bool NotchInScreen()
        {
            bool ret = false;
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                ret = m_AndroidJO.Call<bool>("hasNotchInScreen");
            }
            catch (Exception e)
            {
                TRACE.WarningLn("Q1PID IsIphoneX");
            }
#endif
            return ret;
        }

        public int[] GetNotchSize()
        {
            int[] notchsize = null;
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                notchsize = m_AndroidJO.Call<int[]>("getNotchSize");
            }
            catch (Exception e)
            {
                TRACE.WarningLn("Q1PID IsIphoneX");
            }
#endif
            return notchsize;
        }
        public string GetExtensionInfo()
        {
            string info = "";
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidJO != null)
            {
                info = m_AndroidJO.Call<string>("GetExtensionInfo");
            }
#endif
            return info;
        }

#region 支付相关
        private static AndroidJavaObject m_AndroidPayJo;

        public void AliPayByApp(string orderInfo, Action<PaymentState, string> callBack)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidPayJo != null)
            {
                Debug.LogWarning("YSH AliPayByApp----------------------------");
                m_AndroidPayJo.CallStatic("AliPayByApp", orderInfo);
            }
#endif
        }

        public void WxPayByApp(string appId, string partnerId, string prepayId, string nonceStr, string timeStamp, string sign, Action<PaymentState, string> callBack)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            if (m_AndroidPayJo != null)
            {
                Debug.LogWarning("YSH WxPayByApp----------------------------");
                m_AndroidPayJo.CallStatic("WxPayByApp", appId, partnerId, prepayId, nonceStr, timeStamp, sign);
            }
#endif
        }
#endregion

        /// <summary>
        /// 检测应用市场更新
        /// </summary>
        public void CheckMarketUpdate(EMMarketType type)
        {
        #if UNITY_ANDROID && !UNITY_EDITOR
                    if (m_AndroidPayJo != null)
                    {
                        if (type == EMMarketType.Honor)
                        {
                            Debug.LogWarning("YSH CheckHonorUpdate----------------------------");
                            m_AndroidPayJo.CallStatic("CheckHonorUpdate");
                        }
                    }
        #endif
        }

        public void ReceiveFromHonor(string response)
        {
            Debug.LogWarning("YSH CheckHonorUpdate ReceiveFromHonor response = " + response);
            // yshTODO:这里不管回什么先进登录界面。
            // response结构 => responseInfo = AppResponseInfo{resultCode=1, message='respondCode:-1, msg:java.lang.NoClassDefFoundError: Failed resolution of: Lokhttp3/MediaType;', versionCode='null', versionName='null', apkSize=0, desc='null', force=false, appName='null', apkUrl='null', iconUrl='null', appInfoType=0} bSuc = false
            GHelp.LoginWindow();
        }
    }
}