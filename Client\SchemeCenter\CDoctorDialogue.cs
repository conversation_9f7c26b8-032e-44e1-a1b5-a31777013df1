﻿/// <summary>
/// CourseHomepage
/// </summary>
/// <remarks>
/// 2023/02/07 15：00：00: 创建. 郭才志 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CDoctorDialogue : ISchemeNode, IDoctorDialogue
    {
        public const string Task_Info = "DoctorDialogueInfo";
        private Dictionary<int, Dictionary<int, DoctorDialogueInfo>> m_TaskInfoById;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Action<Dictionary<int, DoctorDialogueInfo>> m_action;
        private int m_courseId;
        public CDoctorDialogue()
        {
            m_TaskInfoById = new Dictionary<int, Dictionary<int, DoctorDialogueInfo>>();
        }
        ~CDoctorDialogue()
        {
        }
        public bool Create()
        {
            //if (!LoadScheme())
            //{
            //    return false;
            //}
            return true;
        }
        public bool LoadScheme(int CourseId, Action<Dictionary<int, DoctorDialogueInfo>> action = null)
        {
            m_action = action;
            m_courseId = CourseId;
            if (!m_TaskInfoById.ContainsKey(CourseId))
            {
                string strPath = "CourseCsv/" + CourseId + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<int, DoctorDialogueInfo> datas = new Dictionary<int, DoctorDialogueInfo>();
                m_TaskInfoById.TryGetValue(CourseId, out datas);
                m_action?.Invoke(datas);
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, DoctorDialogueInfo> datas = null;
            m_TaskInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, DoctorDialogueInfo>();
                m_TaskInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                DoctorDialogueInfo preperativeInfo = new DoctorDialogueInfo();

                preperativeInfo.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.StepID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.PerosnIdentity = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.PerosnName = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.Meassage = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.AudioID = pCSVReader.GetInt(nRow, tmp_col++, 0);  
                preperativeInfo.LaterTime = pCSVReader.GetInt(nRow, tmp_col++, 0);
                datas.Add(preperativeInfo.ID, preperativeInfo);
            }
            m_action?.Invoke(datas);
            return true;
        }


        public void Release()
        {
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
        }

        public DoctorDialogueInfo GetCourseHomepageInfoByID(int tID)
        {
            DoctorDialogueInfo info = null;
            Dictionary<int, DoctorDialogueInfo> datas = new Dictionary<int, DoctorDialogueInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            datas.TryGetValue(tID, out info);
            return info;
        }
        public List<DoctorDialogueInfo> GetAllDialogue(int StepID)
        {
            List<DoctorDialogueInfo> tempAll = new List<DoctorDialogueInfo>();
            Dictionary<int, DoctorDialogueInfo> datas = new Dictionary<int, DoctorDialogueInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            foreach (var item in datas.Values)
            {
                if (item.StepID == StepID)
                {
                    tempAll.Add(item);
                }
            }
            return tempAll;
        }

    }
}
