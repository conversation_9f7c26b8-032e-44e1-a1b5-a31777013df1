﻿using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Client.Entity
{
    public class CEntityWorld : IEntityWorld
    {
        // 所有实体列表
        Dictionary<Int64, IEntity> m_mapEntity;

        // 所有人物列表
        Dictionary<uint, IEntity> m_mapPerson;

        // 所有实体数组（序列号）
        Dictionary<uint, IEntity> m_mapEntitySNO;

		// 所有虚拟实体列表
		Dictionary<Int64, IEntity> m_mapDeceitEntity;

		// 所有实体名称列表
		Dictionary<string, List<IEntity>> m_mapEntityName;

		// 所有实体配置ID列表
		Dictionary<int, List<IEntity>> m_mapEntityConfigID;

		/// <summary>
		/// 所有载具列表
		/// </summary>
		Dictionary<Int64, IEntity> m_mapTankEnity;
		//延时删除MapEntity
		List<Int64> m_DelayDeceitMapEntity;

		private List<IEntity> m_allEntity  = new List<IEntity>();

		private List<IEntity> m_TempArray = new List<IEntity>();
		//获取实体世界实体数量
		public int getEntityCount()
        {
            return m_mapEntity.Count;
        }
		CEntityWorld g_pEntityWorld = null;
		/** 
        @param   
        @param   
        @return  
        */
		public CEntityWorld()
		{
			g_pEntityWorld = this;
		}

		public void Init()
		{
			// 所有实体列表
			m_mapEntity = new Dictionary<Int64, IEntity>();

			// 所有人物列表
			m_mapPerson = new Dictionary<uint, IEntity>();

			// 所有实体数组（序列号）
			m_mapEntitySNO = new Dictionary<uint, IEntity>();

			// 所有实体名称列表
			m_mapEntityName = new Dictionary<string, List<IEntity>>();

			m_mapEntityConfigID = new Dictionary<int, List<IEntity>>();

			// 所有虚拟实体列表
			m_mapDeceitEntity = new Dictionary<Int64, IEntity>();
			m_DelayDeceitMapEntity = new List<Int64>();
			m_mapTankEnity = new Dictionary<long, IEntity>();

		}

		/** 关闭，清内存
        @param   
        @param   
        @return  
        */
		public void Close(bool bReleaseHero)
		{
			// 主角要最后释放
			IPerson pHero = GHelp.GetHero();
			List<IEntity> delList = new List<IEntity>();
			///////////////////////////////////////////////////////////////////
			// 释放所有对像
			foreach (KeyValuePair<Int64, IEntity> elem in m_mapEntity)
			{
				// 主角不急于释放
				if (elem.Value == pHero && !bReleaseHero)
				{
					CHero __pHero = (CHero)(pHero);

					// 从场景上移除
					__pHero.RemoveFromScene();
				}
				else
				{
					delList.Add(elem.Value);
				}
			}

			for (int i = 0; i < delList.Count; i++)
			{
				if (delList[i] == null)
					continue;
				IEntity pEntity = delList[i];
				//目前只有怪物，非主角玩家和宝箱才才支持Restore
				if (pEntity.GetEntityClass().IsMonster())
				{
					pEntity.Restore();
				}
				else if (pEntity.GetEntityClass().IsPerson())
				{
					IPerson person = pEntity as IPerson;
					if (person.IsHero())
					{
						pEntity.Release();
					}
					else
					{
						pEntity.Restore();
					}
				}
				else
				{
					pEntity.Release();
				}
			}

			// 所有实体列表
			m_mapEntity.Clear();

			m_allEntity.Clear();

			// 所有人物列表
			m_mapPerson.Clear();

			// 所有实体数组（序列号）
			m_mapEntitySNO.Clear();

			// 所有实体名称列表
			m_mapEntityName.Clear();

			// 所有实体配置ID列表
			m_mapEntityConfigID.Clear();

			m_mapTankEnity.Clear();

			// **** 还得将主角添加进去 *********
			if (!bReleaseHero)
			{
				Add(pHero);
			}

			foreach (KeyValuePair<Int64, IEntity> elem in m_mapDeceitEntity)
			{
				if (elem.Value != null)
				{
					elem.Value.Release();
				}
			}

			foreach (Int64 keyID in m_DelayDeceitMapEntity)
			{
				m_mapDeceitEntity.Remove(keyID);
			}

			m_mapDeceitEntity.Clear();

			m_DelayDeceitMapEntity.Clear();
		}

		/** 增加实体
        @param   
        @param 
        @return  
        */
		public bool Add(IEntity pEntity)
		{
			if (pEntity == null)
			{
				return false;
			}

			Int64 nUID = pEntity.GetUID();
			// 是否有重复的
			if (Get(nUID, pEntity.GetEntityClass().Class()) != null)
			{
				TRACE.ErrorLn("EnrityWorld::Add 重复创建实体，实体类：" + pEntity.GetEntityClass().Class());
				return false;
			}

			// 压入实体列表
			m_mapEntity[nUID] = pEntity;
			m_allEntity.Add(pEntity);
			// 所有实体数组（序列号）
			m_mapEntitySNO[(uint)UID_DATA.ANALYZEUID_SERIALNO(nUID)] = pEntity;

			// 压入实体名称列表
			List<IEntity> tEntity = new List<IEntity>();
			List<IEntity> temp;
			if (m_mapEntityName.TryGetValue(pEntity.GetName(), out temp))
			{
				m_mapEntityName[pEntity.GetName()].Add(pEntity);
            }
            else
            {
				tEntity.Add(pEntity);
				m_mapEntityName[pEntity.GetName()] = tEntity;
			}

			// 压入实体配置ID列表
			List<IEntity> tEntityConfigId = new List<IEntity>();
			List<IEntity> tempConfigId;
			if (m_mapEntityConfigID.TryGetValue(pEntity.GetConfigID(), out tempConfigId))
			{
				m_mapEntityConfigID[pEntity.GetConfigID()].Add(pEntity);
			}
			else
			{
				tEntityConfigId.Add(pEntity);
				m_mapEntityConfigID[pEntity.GetConfigID()] = tEntityConfigId;
			}

			// 压入人物列表
			/*if (pEntity.GetEntityClass().IsPerson())
			{
				uint dwPDBID = (uint)pEntity.GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_PDBID);
				m_mapPerson[dwPDBID] = pEntity;
			}*/

			if (pEntity.GetEntityType() == EMEntityType.typeRobot)
			{
				m_mapTankEnity[pEntity.GetUID()] = pEntity;
			}

			return true;
		}

		/** 删除实体（非人物对像）
        @param   
        @param   
        @return  
        */
		public void Remove(IEntity pEntity)
		{
			if (pEntity == null)
			{
				return;
			}
			Int64 uid = pEntity.GetUID();

			if (m_mapEntity.ContainsKey(uid))
			{
				m_mapEntity.Remove(uid);
				m_allEntity.Remove(pEntity);
			}


			uint dwSerialNo = (uint)UID_DATA.ANALYZEUID_SERIALNO(uid);
			if (m_mapEntitySNO.ContainsKey(dwSerialNo))
			{
				m_mapEntitySNO.Remove(dwSerialNo);
			}

            if (m_mapEntityName.ContainsKey(pEntity.GetName()))
            {
				List<IEntity> entityNameList = m_mapEntityName[pEntity.GetName()];
				// 删除要从后往前删
                for (int i = entityNameList.Count-1; i >=0; i--)
                {
					if (entityNameList[i].GetUID() == uid)
					{
						m_mapEntityName[pEntity.GetName()].Remove(entityNameList[i]);
					}
				}
                if (entityNameList.Count <= 0)
                {
					m_mapEntityName.Remove(pEntity.GetName());

				}
			}

			if (m_mapEntityConfigID.ContainsKey(pEntity.GetConfigID()))
			{
				List<IEntity> entityConfigList = m_mapEntityConfigID[pEntity.GetConfigID()];
				// 删除要从后往前删
				for (int i = entityConfigList.Count - 1; i >= 0; i--)
				{
					if (entityConfigList[i].GetUID() == uid)
					{
						m_mapEntityConfigID[pEntity.GetConfigID()].Remove(entityConfigList[i]);
					}
				}
				if (entityConfigList.Count <= 0)
				{
					m_mapEntityConfigID.Remove(pEntity.GetConfigID());

				}
			}

			if (pEntity.GetEntityType() == EMEntityType.typeRobot)
			{
				m_mapTankEnity.Remove(uid);
			}

			/*if (pEntity.GetEntityClass().IsPerson())
			{
				uint dwPDBID = (uint)pEntity.GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_PDBID);
				if (m_mapPerson.ContainsKey(dwPDBID))
				{
					m_mapPerson.Remove(dwPDBID);
				}
			}*/
		}

		/** 通过UID取得实体
        @param   
        @param   dwGOClass ：是为了防止外部直接用IEntity转成相应的实体接口，对服务器稳定的威胁
        @                    （tEntity_Class_Person， tEntity_Class_Monster，tEntity_Class_Equipment，tEntity_Class_Leechdom）
        @return  
        */
		public IEntity Get(Int64 uid, uint dwGOClass)
		{
			IEntity pEntity = null;

			if (m_mapEntity.TryGetValue(uid, out pEntity))
			{
				if (pEntity.GetEntityClass().Class() != dwGOClass)
				{
					return null;
				}

				return pEntity;
			}

			return null;
		}

		/** 取得某一类客户端实体
	   @param   EntityArray ：输出实体的数组，如果为空，表示返回实体数量
	   @param   nSize       ：输入＝EntityArray的大小，输出＝实体数量
	   @param   dwClass     ：实体类型
	   @return  
	   */
		public bool Get(ref Int64[] uidEntityArray, ref int nSize, uint dwClass)
		{
			int nMaxSize = nSize;
			nSize = 0;

			foreach (KeyValuePair<Int64, IEntity> elem in m_mapEntity)
			{
				if ((elem.Value.GetEntityClass().Class() & dwClass) > 0)
				{
					if (uidEntityArray != null)
					{
						if (nSize >= nMaxSize)
						{
							return false;
						}

						uidEntityArray[nSize] = elem.Value.GetUID();
					}

					nSize++;
				}
			}

			return true;
		}

		public IEntity Get(int entityFactoryConfigID)
		{
			IEntity cur = null;
			foreach (KeyValuePair<Int64, IEntity> elem in m_mapEntity)
			{
				if (elem.Value.GetEntityFactoryConfigID() == entityFactoryConfigID)
				{
					cur = elem.Value;
					break;
				}
			}
			return cur;
		}

		/** 通过UID中的序列号取得实体
        @param   
        @param   
        @return  
        */
		public IEntity GetBySNO(uint dwSNO)
		{
			IEntity pEntity = null;

			if (m_mapEntitySNO.TryGetValue(dwSNO, out pEntity))
			{
				return pEntity;
			}

			return null;
		}

		/** 通过名称取得实体列表，因为名称可能重复，所以获取的是list
        @param   
        @param   
        @return  
		*/
		public List<IEntity> GetByName(string name)
        {
			List<IEntity> pEntity = null;

			if(m_mapEntityName.TryGetValue(name,out pEntity))
            {
				return pEntity;
            }
			return null;
        }

		/** 通过PDBID取得人物实体
        @param   
        @param   
        @return  
        */
		/*public IEntity Get(uint dwPDBID)
		{
			IEntity pEntity = null;

			if (m_mapPerson.TryGetValue(dwPDBID, out pEntity))
			{
				return pEntity;
			}

			return null;
		}*/


		/** 通过UID取得实体
        @param   
        @param	
        @return  
        */
		public IEntity Get(Int64 uid)
		{
			IEntity pEntity = null;

			if (m_mapEntity == null)
			{
				return null;
			}

			if (m_mapEntity.TryGetValue(uid, out pEntity))
			{
				return pEntity;
			}

			return null;
		}

		public List<IEntity> GetAll()
		{
			return m_allEntity;
		}

		/** 取得附近的所有玩家
	  @param   
	  @param   
	  @return  List<IEntity> ：输出附近玩家列表
	  */
		public List<IEntity> GetNearPlayerList()
		{
			List<IEntity> pEntityArray = new List<IEntity>();

			foreach (KeyValuePair<uint, IEntity> elem in m_mapPerson)
			{
				pEntityArray.Add(elem.Value);
			}

			return pEntityArray;
		}

		/** 向客户端内实体广播消息
	   @param   
	   @param   dwClass：实体类型，支持tEntity_Class_Person|tEntity_Class_Monster
	   @return  
	   */
		public void BroadcastMessage(uint dwMsgID, CPacketRecv pMsg, uint dwClass)
		{
			foreach (KeyValuePair<Int64, IEntity> elem in m_mapEntity)
			{
				uint dwEntityClass = elem.Value.GetEntityClass().Class();
				if ((dwClass & dwEntityClass) == dwEntityClass)
				{
					elem.Value.OnMessage(dwMsgID, default(SGameMsgHead), pMsg);
				}
			}
		}

		/** 取得客户端有多少人
        @param   
        @param   
        @return  
        */
		public int GetClientPersonCount()
		{
			return m_mapPerson.Count;
		}

		/** 取得客户端所有实体
	   @param   EntityArray ：输出实体的数组，如果为空，表示返回实体数量
	   @param   
	   @return  
	   */
		public bool GetAllEntity(ref List<IEntity> ppEntityArray)
		{
			foreach (KeyValuePair<Int64, IEntity> elem in m_mapEntity)
			{
				ppEntityArray.Add(elem.Value);
			}

			return true;
		}

		public bool GetAllTankEntity(ref List<IEntity> pEntitiyArray)
		{
			foreach (KeyValuePair<Int64, IEntity> elem in m_mapTankEnity)
			{
				pEntitiyArray.Add(elem.Value);
			}

			return true;
		}

		public bool GetEntityByType(EMEntityType type, ref List<IEntity> ppEntityArray)
		{
			foreach (KeyValuePair<Int64, IEntity> elem in m_mapEntity)
			{
				if (elem.Value.GetEntityType() == type)
				{
					ppEntityArray.Add(elem.Value);
				}
			}

			return true;
		}

		/** 增加虚拟实体
	   @param   
	   @param   
	   @return  
	   */
		public bool AddDeceitEntity(IEntity pEntity)
		{
			if (pEntity == null)
			{
				return false;
			}

			// 压入实体列表
			m_mapDeceitEntity[pEntity.GetUID()] = pEntity;

			return true;
		}

		/** 删除实体
        @param   
        @param   
        @return  
        */
		public void RemoveDeceitEntity(IEntity pEntity)
		{
			if (pEntity == null)
			{
				return;
			}

			Int64 nUID = pEntity.GetUID();

			if (m_mapDeceitEntity.ContainsKey(nUID))
			{
				m_DelayDeceitMapEntity.Add(nUID);
			}
		}

		/// <summary>
		/// 找到目标从参数
		/// </summary>
		/// <param name="t"></param>
		/// <param name="camp"></param>
		/// <param name="targetName"></param>
		/// <returns></returns>
		public List<IEntity> GetEntityByParam(EMEntityType t, int camp, string targetName)
		{
			m_TempArray.Clear();
			bool r = GetEntityByType(t, ref m_TempArray);
			if (r)
			{

			}
			return m_TempArray;
		}


		/** 通过UID取得虚拟实体
	   @param   
	   @param	
	   @return  
	   */
		public IEntity GetDeceitEntity(Int64 uid)
		{
			IEntity pEntity = null;

			if (m_mapDeceitEntity.TryGetValue(uid, out pEntity))
			{
				return pEntity;
			}

			return null;
		}

		public List<IEntity> GetByConfigID(int configId)
        {
			List<IEntity> pEntity = null;

			if (m_mapEntityConfigID.TryGetValue(configId, out pEntity))
			{
				return pEntity;
			}
			return null;
        }
	}
}
