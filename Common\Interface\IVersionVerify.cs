﻿/// <summary>
/// IVersionVerify
/// </summary>
/// <remarks>
/// 2021.10.18: 创建. 谌安 <br/>
/// 版本检查器 <br/>
/// </remarks>
using System;
using System.Collections;
using UnityEngine;

namespace GLib.Common
{
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    /**
    @name     : 版本检查处理接口
    @brief    : 应用层调用版本检查器检查一组文件的版本,并实现版本检查处理接口,来对版本检查结果进行处理
    */
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    public interface IVersionVerifyHandler
    {
	    /**
	    @name           : 版本匹配
	    @param nVersion : 匹配的版本号
	    */
	    void onVersionMatch( int nVersion );

	    /**
	    @name           : 版本不匹配
	    @param nVersion : 不匹配的版本号
	    @param szErrInfo: 错误信息
	    */
	    void onVersionNotMatch( int nVersion,string szErrInfo );

	    /**
	    @name           : 版本匹配失败
	    @brief          : 这个失败并不是表示不匹配,可能是版本信息文件没获取到
	    @param nVersion : 不匹配的版本号
	    @param szErrInfo: 错误信息
	    */
	    void onVersionMatchFailed( int nVersion,string szErrInfo );
    };

    ///////////////////////////////////////////////////////////////////////////////////////////////////
    /**
    @name     : 版本检查器
    @brief    : 每次启动游戏之前简单检查一下版本,有两个用途
                1.自动更新有时会出现错误,导致版本号和文件内容不符合,这时客户端需要一个诊断和修复的机制,比如回退版本号重新更新
			    2.外挂或病毒修改文件后有一定的可能可以被检查到
    */
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    public interface IVersionVerify
    {
	    /**
	    @name           : 检查版本
	    @param nVersion : 本地版本号
	    @param szVersionInfoURL : 版本信息文件地址(如果包含http前缀则是服务器上的远程文件,否则为客户端本地文件)
	    @param pHandler : 处理器接口,通过该接口通知检查结果
	    */
	    void VerifyVersion( int nVersion,string szVersionInfoURL,IVersionVerifyHandler  pHandler );

	    /**
	    @name           : 取消检查
	    @brief          : 如果pHandler需要销毁,则可以主动调用CancelVerify取消校验
	    @param pHandler : 处理器接口
	    */
	    void CancelVerify(IVersionVerifyHandler  pHandler);

	    /**
	    @name           : 取得文件版本号
	    @param szFileName : 文件名
	    @return         : 返回文件版本字串,获得失败返回空字串.
	    */
	    string GetFileVersion(string szFileName );

	    /**
	    @name             : 验证某个文件是否匹配
	    @param szFileName : 文件名
	    @param szVersion  : 版本号 例如"1.3.10.1"
	    @param nFileSize  : 文件大小
	    */
	    bool VerifyFileVersion(string szFileName,string szVersion,int nFileSize );

	    // 取得错误信息
	    string GetErrorMessage();

	    /**
	    @name           : 销毁检查器
	    */
	    void Release();
    };

    public enum EMUpateFlowFlag
    {
        None,
        SUSPEND,
        MAX
    };

    //////////////////////////////////////////////////////////////////////////
    /**
    @name : 更新流程中的一个子任务
    @brief: 例如:从服务器获取最新版本信息,更新到某个版本,执行某个指令等都是一个单独的小任务
    */
    public interface  IUpdateFlow
    {
	    bool Run();

	    void Release();

	    // 在这个更新过程中用户能否退出？安装资源包时不建议用户退出程序，以免资源文件被损坏
        bool CanExit();

        /// <summary>
        /// 设置子任务扩展数据
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="bState"></param>
        void SetFlag(EMUpateFlowFlag flag, bool bState);

        /// <summary>
        /// 获得子任务扩展数据
        /// </summary>
        /// <param name="flag"></param>
        /// <returns></returns>
        bool GetFlag(EMUpateFlowFlag flag);
    };
}