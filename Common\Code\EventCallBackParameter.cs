﻿using System.Collections.Generic;
using UnityEngine;

namespace GLib
{
    public enum EventType
    {
        /// <summary>
        /// 无
        /// </summary>
        None = 0,
        /// <summary>
        /// 射线进入
        /// </summary>
        EventEnter,
        /// <summary>
        /// 射线离开
        /// </summary>
        EventExit,
        /// <summary>
        /// 射线移动
        /// </summary>
        EventHover,
        /// <summary>
        /// 事件按下
        /// </summary>
        EventPress,
        /// <summary>
        /// 事件释放
        /// </summary>
        EventRelease,
        /// <summary>
        /// 手柄拖拽事件开始
        /// </summary>
        EventDragStart,
        /// <summary>
        /// 手柄拖拽中
        /// </summary>
        EventDrag,
        /// <summary>
        /// 手柄拖拽事件结束
        /// </summary>
        EventDragEnd,
        /// <summary>
        /// 手柄拖放事件，在EventDragEnd事件之后
        /// </summary>
        EventDrop,
        /// <summary>
        /// 抓取
        /// </summary>
        EventGrap,
        /// <summary>
        ///  抓取丢弃
        /// </summary>
        EventGrapRelease,
        /// <summary>
        /// 选择进入
        /// 射线指到物体上按扳机
        /// </summary>
        EventSelectEnter,
        /// <summary>
        /// 选择退出
        /// 射线指到物体上松开扳机
        /// </summary>
        EventSelectExit,
        /// <summary>
        /// 事件按下
        /// </summary>
        EventDown,
        /// <summary>
        /// 事件抬起
        /// </summary>
        EventUp,
        ///键位和上面的射线是2种类型
        /// <summary>
        /// A键
        /// </summary>
        AButton = 100,
        /// <summary>
        /// B键
        /// </summary>
        BButton,
        /// <summary>
        /// X键
        /// </summary>
        XButton,
        /// <summary>
        /// Y键
        /// </summary>
        YButton,
        /// <summary>
        /// 左扳机键
        /// </summary>
        LeftTrigger,
        /// <summary>
        /// 左抓取键
        /// </summary>
        LeftGrip,
        /// <summary>
        /// 左摇杆键
        /// </summary>
        LeftTouchClick,
        /// <summary>
        /// 右扳机键
        /// </summary>
        RightTrigger,
        /// <summary>
        /// 右抓取键
        /// </summary>
        RightGrip,
        /// <summary>
        /// 右摇杆键
        /// </summary>
        RightTouchClick,
        /// <summary>
        /// ZSpaceButton
        /// </summary>
        ZSpaceButton,
    }
    /// <summary>
    /// 接口ICombinactionExecuteSink的事件参数
    /// </summary>
    public class EventCallBackParameter
    {
        //当前射线指到物体上的位置
        public Vector3 CurrentPoint;
        public Quaternion objectRotation;
        //当前点位置相对上一帧的偏移量
        public Vector3 Detaly;
        public bool isLeftHand;
        /// <summary>
        /// 抓取的物体
        /// </summary>
        public GameObject obj;
        /// <summary>
        /// 触发键位
        /// </summary>
        public EventType triggerModule;
        /// <summary>
        /// 由于射线退出事件触发，我们不确定什么时候应该把这个事件在。当前所有按下的事件类型--------->
        /// Help.Instance.m_combinactionManager.m_eventTypeList移除 
        /// 所以退出触发一次 就移除。bool值为false代表不可触发退出事件的逻辑 在射线进入时改为true
        /// 如果没有这个bool 那么射线退出的会移植存在Help.Instance.m_combinactionManager.m_eventTypeLis里
        /// 下面2个也一样
        /// </summary>
        public bool isExit;
        public bool isGrapRealease;
        public bool isPressRealease;
        /// <summary>
        /// 按键传递参数(暂时只有Zspace会传)
        /// 参数1:0:主按键   1：右边按键  2：左边按键(如果为-1,代表是射线事件而不是按键事件)
        /// 参数2:ZPointer pointer
        /// </summary>
        public object[] param;
    }

    public enum XRControllerDef
    {
        Left = 0,
        Right,
    }

    public enum KeyState
    {
        None,
        /// <summary>
        /// 按下状态
        /// </summary>
        PressDown,
        /// <summary>
        /// 抬起状态
        /// </summary>
        PressUp,
    }

    /// <summary>
    /// 单键
    /// </summary>
    public class cmd_OneKeyChange
    {
        public KeyState mKeyState;
        public EventType triggerKey;
        /// <summary>
        /// 按键时间
        /// </summary>
        public long time;
        public EventCallBackParameter param;
    }

    /// <summary>
    /// 多键
    /// </summary>
    public class cmd_MultiKeyChange
    {
        public List<cmd_OneKeyChange> mList;
    }

    /// <summary>
    /// 单键
    /// </summary>
    public class cmd_OneRayChange
    {
        public EventType triggerKey;
        public EventCallBackParameter param;
    }

    /// <summary>
    /// 抓取
    /// </summary>
    public struct SEventGrabPrefab
    {
        public GameObject m_grabGameObject;
        public bool m_IsLeftHand;
        public EventGrabType m_grabType;
    }

    /// <summary>
    /// 抓取类型
    /// </summary>
    public enum EventGrabType
    {
        Grab = 0,
        Discard = 1
    }

    /// <summary>
    /// 选择
    /// </summary>
    public struct SEventSelectPrefab
    {
        public GameObject m_selectGameObject;
        public bool m_IsLeftHand;
        public EventSelectType m_selectType;
    }

    /// <summary>
    /// 选择类型
    /// </summary>
    public enum EventSelectType
    {
        Enter = 0,
        Exit = 1
    }

    /// 执行事件sink 
    public interface ICombinactionExecuteSink
    {
        /// <summary>
        /// 本次发送的射线组合键
        /// </summary>
        List<cmd_OneRayChange> m_eventRayTypes { get; set; }
        /// <summary>
        /// 射线进入、移动、退出
        /// </summary>
        cmd_OneRayChange m_RayEnterType { get; set; }
        /// <summary>
        /// 抓取和释放
        /// </summary>
        cmd_OneRayChange m_RayGrapType { get; set; }
        /// <summary>
        /// 鼠标的按下和释放
        /// </summary>
        cmd_OneRayChange m_RayPressType { get; set; }
        /// <summary>
        /// 射线选择进入和退出
        /// </summary>
        cmd_OneRayChange m_RaySelectType { get; set; }
        /// <summary>
        ///  接受按下组合键监听
        /// </summary>
        void OnPressDownExecute(List<EventType> eventTypes, EventCallBackParameter param);
        /// <summary>
        ///  接受抬起组合键监听
        /// </summary>
        void OnPressUpExecute(EventType eventType, EventCallBackParameter param = null);

    };

    public interface IG_CombinactionManager
    {
        /// <summary>
        /// 本次按键(从第一个键pressDown到最后一个键pressUp)开始到结束 已经发送过的事件
        /// 发送过的key 不需要再次发送
        /// 比如A+C键 是A、C、A+C各发一次
        /// 相同的在按键一次中 只触发一次
        /// </summary>
        List<List<EventType>> m_FireConbinationKeys { get; set; }
        /// <summary>
        /// 发送射线事件
        /// </summary>
        /// <param name="eventType"></param>
        /// <param name="param"></param>
        void FireConbinationRay(ICombinactionExecuteSink sink, List<cmd_OneRayChange> cmd_OneRayChanges, ICombinactionExecuteSink curExecute = null);
        /// <summary>
        /// 设置键值的事件
        /// </summary>
        /// <param name="eventTypes"></param>
        void SetEventTypeKeys(List<EventType> eventTypes);
        /// <summary>
        /// 发送按下键位事件
        /// </summary>
        /// <param name="eventTypes"></param>
        void FirePressDownConbinationKey();
        /// <summary>
        ///  注册按下组合键监听
        /// </summary>
        void RegPressDownCombination(GameObject obj, ICombinactionExecuteSink sink, params EventType[] args);
        /// <summary>
        /// 取消按下组合键监听
        /// </summary>
        void UnRegPressDownCombination(GameObject obj, ICombinactionExecuteSink sink, params EventType[] args);
        /// <summary>
        /// 发送抬起键位事件
        /// </summary>
        /// <param name="eventTypes"></param>
        void FirePressUpConbinationKey(EventType eventType, EventCallBackParameter param = null);
        /// <summary>
        ///  注册抬起组合键监听
        /// </summary>
        void RegPressUpCombination(GameObject obj, ICombinactionExecuteSink sink, EventType eventType);
        /// <summary>
        /// 取消抬起组合键监听
        /// </summary>
        void UnRegPressUpCombination(GameObject obj, ICombinactionExecuteSink sink, EventType eventType);
        /// <summary>
        /// 比较list和数组是否完全相等,顺序可不同
        /// </summary>
        /// <param name="eventTypes"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        bool CompareListEquals(List<EventType> eventTypes, params EventType[] param);
        /// <summary>
        /// 比较eventTypes2是否包含eventTypes1,顺序可不同
        /// </summary>
        /// <param name="eventTypes1"></param>
        /// <param name="eventTypes2"></param>
        /// <returns></returns>
        bool CompareListContains(List<EventType> eventTypes1, List<EventType> eventTypes2);
        /// <summary>
        /// 比较2个List是否相等,顺序可不同
        /// </summary>
        /// <param name="eventTypes1"></param>
        /// <param name="eventTypes2"></param>
        /// <returns></returns>
        bool CompareListEquals(List<EventType> eventTypes1, List<EventType> eventTypes2);
        /// <summary>
        /// 发送射线事件
        /// </summary>
        /// <param name="m_eventRayTypes"></param>已经发送的射线事件
        /// <param name="m_RayEnterType"></param>射线进入、退出、移动
        /// <param name="m_RayPressType"></param>射线按下和抬起(鼠标)
        /// <param name="m_RayGrapType"></param>射线抓取和丢弃
        /// <param name="triggerKeyMapp"></param>本次射线事件
        /// <param name="param"></param>
        void SendRayEvent(ICombinactionExecuteSink sink, EventType triggerKeyMapp, EventCallBackParameter param, ICombinactionExecuteSink curExecute = null);
        /// <summary>
        /// 右手摇杆值
        /// </summary>
        Vector2 m_RightScrollValue { get; set; }

        /// <summary>
        /// 左手摇杆值
        /// </summary>
        Vector2 m_LeftScrollValue { get; set; }
};

    /// <summary>
    /// 力反馈设备硬件返回数据
    /// </summary>
    public class HDeviceData
    {
        /// <summary>
        /// 工具ID
        /// </summary>
        public int ToolID;
        /// <summary>
        /// 触摸的物体
        /// </summary>
        public GameObject Touchobj;
        /// <summary>
        /// 抓取的物体
        /// </summary>
        public GameObject Grabobj;
        /// <summary>
        /// button是否点击
        /// </summary>
        public bool[] IsButtonClicks;
        /// <summary>
        /// 是否抓取中
        /// </summary>
        public bool IsGrabbing;
        /// <summary>
        /// 工具父物体
        /// </summary>
        public Transform ToolParent;
        public Vector3 Pos;
        public Vector3 Ros;
        public Vector3 Scale;
    }

    public struct Actuator
    {
        public string DeviceName;
        public int AnalogMax;
        public int AnalogL0;
        public int Analog;
        public int AnalogL1;
        public int ID;
        public int AnalogH0;
        public int AnalogH1;
    }

    public enum HapticFunctionsType
    {
        None = 0,
        /// <summary>
        /// 消毒
        /// </summary>
        Disinfect,
        /// <summary>
        /// 标记
        /// </summary>
        Mark,
        /// <summary>
        /// 射线
        /// </summary>
        radial,
        /// <summary>
        /// 打结
        /// </summary>
        DaJie,
        /// <summary>
        /// 抓取
        /// </summary>
        Grab,
        /// <summary>
        /// 抽液
        /// </summary>
        Aspirate,
        /// <summary>
        /// 穿刺
        /// </summary>
        Puncture,
        /// <summary>
        /// 新消毒
        /// </summary>
        NewDisinfect,
        /// <summary>
        /// 蘸碘伏
        /// </summary>
        OnIodine,
        /// <summary>
        /// 垃圾桶
        /// </summary>
        TrashCan,
    }

    public enum HapticToolsType
    {
        None = 0,
        //手术刀3
        ShouShuDao_3 = 160,
        //手术刀4
        ShouShuDao_4,
        //压舌板
        YaSheBan,
        //胸穿针12
        XiongChuangZhen_12,
        //胸穿针16
        XiongChuangZhen_16,
        //骨穿针16
        GuChuangZhen_16,
        //腰穿针12
        YaoChuangZhen_12,
        //
        YiJiMoChuangZhen,
        //腹腔针
        FuQiangZhen,
        //剪刀
        JianDao,
        //有齿镊
        YouCiNie,
        //无齿镊
        WuCiNie,
        //喉镜
        HouJing,
        //按下
        Touch,
        //注射器20毫升
        ZhuSeQi_20,
        //注射器10毫升
        ZhuSeQi_10,
        //注射器5毫升
        ZhuSeQi_5,
        //注射器2_5毫升
        ZhuSeQi_2_5,
        //持针器
        ChiZhenQI,
        //止血钳
        ZhiXueQian,
        //注射器50毫升
        ZhuSeQi_50,
        //头皮针
        TouPiZhen,
    }

    public enum ToolsType
    {
        None = 0,
        ShouShuDao_3 = 160,
        ShouShuDao_4,
        YaSheBan,
        XiongChuangZhen_12,
        XiongChuangZhen_16,
        GuChuangZhen_16,
        YaoChuangZhen_12,
        YiJiMoChuangZhen,
        FuQiangZhen,
        JianDao,
        YouCiNie,
        WuCiNie,
        HouJing,
        Touch,
        ZhuSeQi_20,
        ZhuSeQi_10,
        ZhuSeQi_5,
        ZhuSeQi_2_5,
        ChiZhenQI,
        ZhiXueQian,
        ZhuSeQi_50,
        TouPiZhen,
    }

}