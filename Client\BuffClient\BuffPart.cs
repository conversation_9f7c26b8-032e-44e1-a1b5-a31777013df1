﻿/// <summary>
/// BuffPart
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// BUFF部件<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using Game.Entity;
using Google.Protobuf;
using GLib.Common;

namespace GLib.Client
{
    using TLIST_BUFF = List<CBuff>;

    public class CBuffPart :IMessageExecuteSink, IBuffPart
    {

        // 主人
        IEntity             m_pMaster;

        // BUFF列表
        TLIST_BUFF         m_listBuff;

        //@param   
        //@param   
        //@return  
        public CBuffPart()
        {
            // 主人
            m_pMaster = null;

            // BUFF列表
            m_listBuff = new TLIST_BUFF();
        }

        //@param   
        //@param   
        //@return  
        ~CBuffPart()
        {

        }

        // 释放,会释放内存
        //@param   
        //@param   
        //@return  
        public void Release()
        {
			// list删除要用倒顺
			for (int i = m_listBuff.Count - 1; i >= 0; i--)
			{
				RemoveBuff(m_listBuff[i]);
			}

			m_listBuff.Clear();

            if(m_pMaster != null)
            {
                // 取消订阅 MSG_ACTION_ADDBUFF 消息
                m_pMaster.UnSubscibe(DGlobalMessage.MSG_ACTION_ADDBUFF, this);

                // 取消订阅 MSG_ACTION_REMOVEBUFF 消息
                m_pMaster.UnSubscibe(DGlobalMessage.MSG_ACTION_REMOVEBUFF, this);

                m_pMaster = null;
            }
        }

        /// <summary>
        /// 还原,不释放对象，只将状态还原到创建时状态
        /// </summary>
        public void Restore()
        {
			// list删除要用倒顺
			for (int i = m_listBuff.Count - 1; i >= 0; i--)
            {
                RemoveBuff(m_listBuff[i]);
            }
            m_listBuff.Clear();

            if (m_pMaster != null)
            {
                // 取消订阅 MSG_ACTION_ADDBUFF 消息
                m_pMaster.UnSubscibe(DGlobalMessage.MSG_ACTION_ADDBUFF, this);

                // 取消订阅 MSG_ACTION_REMOVEBUFF 消息
                m_pMaster.UnSubscibe(DGlobalMessage.MSG_ACTION_REMOVEBUFF, this);

                m_pMaster = null;
            }
        }

        // 创建，重新启用，也会调此接口
        //@param   
        //@param   
        //@return  
        public bool Create(IEntity pMaster, object pszData)
        {
            if(pMaster == null)
            {
                TRACE.ErrorLn("CBuffPart::Create pMaster == null");
                return false;
            }

            // 主人
            m_pMaster = pMaster;

            // 订阅 MSG_ACTION_ADDBUFF 消息	
            m_pMaster.Subscibe(DGlobalMessage.MSG_ACTION_ADDBUFF, this, "CBuffPart::Create");

            // 订阅 MSG_ACTION_REMOVEBUFF 消息
            m_pMaster.Subscibe(DGlobalMessage.MSG_ACTION_REMOVEBUFF, this, "CBuffPart::Create");	

            return true;
        }

        // 取是部件ID
        //@param   
        //@param   
        //@return  
        public UInt32 GetPartID()
        {
            return (UInt32)EMENTITYPART.ENTITYPART_ENTITY_BUFF;
        }

        // 消息
        //@param   
        //@param   
        //@return  
        public int OnMessage(uint dwMsgID, CPacketRecv pszMsg)
        {
            return 0;
        }

        // 取是主人
        //@param   
        //@param   
        //@return  
        public IEntity GetMaster()
        {
            return m_pMaster;
        }

        // 激活部件
        //@param   
        //@param   
        //@return  
        public bool Active(object pszContext)
        {
            return true;
        }

        // 冻结部件
        //@param   比如当生物转到坐下状态，就需冻结移动部件
        //@param   
        //@return  
        public bool Freeze(object pszContext)
        {
            return true;
        }

        // 导入初始化数据
        //@param   
        //@param   
        //@return  
        public bool ImportBuildContext(IMessage pszContext, int nLen)
        {
            Entity_AddBuff_SC info = (Entity_AddBuff_SC) pszContext;

            if (info != null)
            {
                OnAddBuff(info);
            }
            return true;
        }

        // 通过buff index取得buff
        //@param   
        //@param   
        //@return  
        public IBuff GetBuff(UInt32 dwBuffIndex)
        {
            foreach (CBuff pBuff in m_listBuff)
            {
                if (pBuff.GetIndex() == dwBuffIndex)
                {
                    return pBuff;
                }
            }

            return null;
        }

        // 通过buff id取得buff
        //@param   
        //@param   
        //@return  
        public IBuff GetBuffByID(UInt32 dwBuffID)
        {
            foreach (CBuff pBuff in m_listBuff)
            {
                if (pBuff.GetBuffID() == dwBuffID)
                {
                    return pBuff;
                }
            }

            return null;
        }

        // 通过buff index取得buff
        //@param   
        //@param   
        //@return  
        public IBuff GetBuffByGUID(string _guid)
        {
            foreach (CBuff pBuff in m_listBuff)
            {
                if (pBuff.GetGUID().Equals(_guid))
                {
                    return pBuff;
                }
            }

            return null;
        }

        // 某个BUFF是否存在
        //@param   
        //@param  
        //@return  
        public bool IsExist(UInt32 dwBuffID)
        {
            foreach (CBuff pBuff in m_listBuff)
            {
                if (pBuff.GetBuffID() == dwBuffID)
                {
                    return true;
                }
            }

            return false;
        }

        // 获得所有BUFF的index
        //@param   pBuffIndex ：buff index的数组
        //@param   nCount ：IN = 数组大小，OUT = buff数
        //@return  成功：true, 失败：false，表示数组过小
        public bool GetAllBuff(UInt32[] pBuffIndex, ref int nCount)
        {
			if (pBuffIndex == null || nCount == 0)
				return false;

            int nTotalCount = nCount;
            nCount = 0;
			for (int i = 0; i < m_listBuff.Count; i++)
			{
				if (nCount > nTotalCount)
				{
					return false;
				}
				pBuffIndex[nCount] = m_listBuff[i].GetIndex();
				nCount++;
			}
            return true;
        }

        // 
        //@param   
        //@param   
        //@return  
        public bool AddBuff(CBuff pBuff)
        {
            if(pBuff == null || m_pMaster == null)
            {
                return false;
            }

            //byte bSrcType = m_pMaster.GetEventSourceType();
			//UInt32 dwSrcID = (uint)UID_DATA.ANALYZEUID_SERIALNO(m_pMaster.GetUID());
           
            // 启动BUFF
            pBuff.Start();

            // 压入列表中
            m_listBuff.Add(pBuff);

            // 播光效动画
			IEffectViewManager pEffectViewManager = GHelp.GetEffectViewManager();
			if(pEffectViewManager!=null)
            {
                List<UInt32> listFlashID = pBuff.GetFlashIDList();

				for (int i = 0; i < listFlashID.Count; i++)
				{
					if (listFlashID[i] > 0)
					{
						uint viewid = m_pMaster.GetEntityViewID();      // 技能效果发起者
                        pEffectViewManager.EffectView.PlayEffect(viewid, listFlashID[i], viewid, viewid , pBuff.GetBuffID());

						break;
					}
				}
            }

            // 发送添加BUFF事件
            SEventCreatureAddBuff_C eventaddbuff=new SEventCreatureAddBuff_C();
            eventaddbuff.dwIndex = pBuff.GetIndex();

            GHelp.FireExecute(DGlobalEvent.EVENT_CREATURE_ADDBUFF,
                                       0,
                                       0,
                                       eventaddbuff);
            
            return true;
        }

        // 
        //@param   
        //@param   
        //@return  
        public bool RemoveBuff(CBuff pBuff)
        {
            if (m_listBuff.Contains(pBuff))
            {
                // 发送移除BUFF事件
                    SEventCreatureRemoveBuff_C eventaddbuff;
                    eventaddbuff.dwIndex = pBuff.GetIndex();

                    byte bSrcType = 0;
                    Int64 uidMaster = m_pMaster.GetUID();
                    UInt32 dwSrcID = (UInt32)UID_DATA.ANALYZEUID_SERIALNO(uidMaster);
                    GHelp.FireExecute(DGlobalEvent.EVENT_CREATURE_REMOVEBUFF,bSrcType,dwSrcID,eventaddbuff);

                    // 停止身上的光效
                    List<UInt32> listFlashID = pBuff.GetFlashIDList();

					IEffectViewManager pEffectViewManager = GHelp.GetEffectViewManager();
					if (pEffectViewManager != null)
					{
						for (int i = 0; i < listFlashID.Count; i++ )
						{
							if (listFlashID[i] > 0)
							{
								pEffectViewManager.EffectView.StopEffect(m_pMaster.GetEntityViewID(), (int)listFlashID[i],pBuff.GetBuffID());
								break;
							}
						}
					}

                    // 结束buff
                    pBuff.Stop();

					// 效果清理
					pBuff.Release();

                    // 删除节点
                    m_listBuff.Remove(pBuff);
		
                    return true;
            }
			else
			{
				TRACE.ErrorLn("BuffPart::RemoveBuff 找不到buff，移除失败");
			}

            return false;
        }

        // 
        //@param   dwMsgID ：消息ID
        //@param   pszMsg ：消息结构
        //@param   nLen ：消息长度
        //@return  
        public void OnExecute(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
            switch(dwMsgID)
            {
            case DGlobalMessage.MSG_ACTION_ADDBUFF:
                {
                    //OnAddBuff(pszMsg as Entity_AddBuff_SC);
                }
                break;
            case DGlobalMessage.MSG_ACTION_REMOVEBUFF:
                {
                    //OnRemoveBuff(pszMsg);
                }
                break;
            default:break;
            }
        }

        // 
        //@param   
        //@param   
        //@return  
        public void OnAddBuff(Entity_AddBuff_SC pszMsg)
        {
            // 创建buff
            CBuff pNewBuff = new CBuff();
            pNewBuff.SetBuffGUID(pszMsg.Buffguid);
            if(!pNewBuff.Create(this,
                                 (uint)pszMsg.BuffId,
                                 pszMsg.EffectIds))
            {
				pNewBuff.Release();
                pNewBuff = null;
                return;
            }

            // 增加
            AddBuff(pNewBuff);
        }

        // 
        //@param   
        //@param   
        //@return  
        public void OnRemoveBuff(Entity_CancelBuff_SC pszMsg)
        {
            // 通过buff index取得buff,是否有重复
            CBuff pOldBuff = (CBuff)GetBuffByGUID(pszMsg.Buffguid);
            if(pOldBuff == null)
            {
                return;
            }

            // 移除
            RemoveBuff(pOldBuff);
        }

        // 显示层的命令(true表示此部件处理,其他不处理,false交由其他部件继续处理)
        public bool onCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
        {
            return false;
        }

        public void OnDie(Entity_Die_SC pszMsg)
        {
        }
    }
}