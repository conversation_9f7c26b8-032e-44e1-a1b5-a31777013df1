﻿/// <summary>
/// SceneModelClient
/// </summary>
/// <remarks>
/// 2023.1.6: 创建. 吴航 <br/>
/// 场景模型 <br/>
/// </remarks>
/// 
using GLib.Common;
using System.Collections.Generic;
using System.Diagnostics;

namespace GLib.Client
{
    public class SceneModelClient : ISceneModelClient, IEventExecuteSink
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }
        /// <summary>
        /// 是否是需要加载完成才能发场景物体加载完成事件的物体
        /// </summary>
        private int m_sceneLoadPrefabFinishCount;
        /// <summary>
        /// 加载的实体idList
        /// </summary>
        private List<int> m_Entitys;
        /// <summary>
        /// 本次是否发送了加载完成
        /// </summary>
        private bool m_isSendLoadFinish;
        public SceneModelClient()
        {

        }

        public bool Create()
        {
            m_Entitys = new List<int>();
            GHelp.GetEventEngine().Subscibe(this, DGlobalEvent.EVENT_SCENE_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EVENT_SCENE_LOAD_FINISH");
            GHelp.GetEventEngine().Subscibe(this, (ushort)ViewLogicDef.EVENT_ENTITY_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "EVENT_ENTITY_LOAD_FINISH");
            GHelp.GetEventEngine().Subscibe(this, (int)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
            return true;
        }

        public void Release()
        {
            m_Entitys.Clear();
            GHelp.GetEventEngine().UnSubscibe(this, DGlobalEvent.EVENT_SCENE_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GHelp.GetEventEngine().UnSubscibe(this, (ushort)ViewLogicDef.EVENT_ENTITY_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GHelp.GetEventEngine().UnSubscibe(this, (int)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH:
                    {
                        SEventSceneLoadFinish startData = (SEventSceneLoadFinish)pContext;
                        //MapInfoDef info = GlobalGame.Instance.SchemeCenter.GetMapInfo().GetMapInfoByID((int)startData.nMapId);
                        List<ScenePointDef> info = GlobalGame.Instance.SchemeCenter.GetScenePoint().GetScenePointByID((int)startData.nMapId);
                        if (info != null)
                        {
                            LoadScenePrefab(info);
                        }
                    }
                    break;
                case (ushort)ViewLogicDef.EVENT_ENTITY_LOAD_FINISH:
                    {
                        int EntityId = (int)pContext;
                        if (m_Entitys.Contains((int)EntityId))
                        {
                            m_Entitys.Remove((int)EntityId);
                        }
                        /*TRACE.WarningLn(" entitiy :"+m_Entitys .Count+ "  ");
                        if (m_Entitys.Count > 0)
                        {
                            string ttt = "";
                            foreach (int t in m_Entitys)
                            {
                                ttt += " " + t;
                            }
                            TRACE.WarningLn(" entitiy :" +ttt);
                        }*/
                        if (m_Entitys.Count == 0 && !m_isSendLoadFinish)
                        {
                            m_isSendLoadFinish = true;
                            ///场景加载完成清楚画图工具贴图错位问题
                            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CLEAR_TEXTURE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
                            GHelp.FireExecute((ushort)DGlobalEvent.EVENT_SCENE_OBJLOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                        }
                    }
                    break;
                case (ushort)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW:
                    {
                        ReleaseData();
                    }
                    break;
            }
        }
        public void ReleaseData()
        {
            m_Entitys.Clear();
        }
        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update() { }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate() { }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate() { }

        public void LoadScenePrefab(List<ScenePointDef> Data)
        {
            m_Entitys.Clear();
            m_isSendLoadFinish = false;
            for (int i = 0; i < Data.Count; i++)
            {
                ScenePointDef scenePointDef = Data[i];
                int entityViewID = GHelp.GetCreateEntityViewID(GlobalGame.Instance.SchemeCenter.GetEntityInfo().GetEntityInfoByID(int.Parse(Data[i].SceneOtherPrefab
                    )).PrefabPath, int.Parse(Data[i].SceneOtherPrefab));
                string[] curpos = Data[i].SceneOtherPrefabPos.Split('_');
                string[] curposRot = Data[i].SceneOtherPrefabRot.Split('_');
                string[] curScale = Data[i].SceneOtherPrefabScale.Split('_');
                // 设置出生坐标
                cmd_creature_rigidbody_sync data = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
                data.bNotGround = false;
                data.nEntityID = (uint)entityViewID;
                data.fPosition_x = float.Parse(curpos[0]);
                data.fPosition_y = float.Parse(curpos[1]);
                data.fPosition_z = float.Parse(curpos[2]);
                data.fRot_x = float.Parse(curposRot[0]);
                data.fRot_y = float.Parse(curposRot[1]);
                data.fRot_z = float.Parse(curposRot[2]);
                GHelp.sendEntityCommand((uint)entityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", data);
                //设置scale
                cmd_creature_scale_sync scaledata = GHelp.GetObjectItem<cmd_creature_scale_sync>();
                scaledata.fScale_x = float.Parse(curScale[0]);
                scaledata.fScale_y = float.Parse(curScale[1]);
                scaledata.fScale_z = float.Parse(curScale[2]);
                GHelp.sendEntityCommand((uint)entityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_SCALE, 0, "", scaledata);
                if (Data[i].IsDefaultShow != 1)
                {
                    cmd_creature_Visible c_data = new cmd_creature_Visible()
                    {
                        bVisable = false
                    };
                    GHelp.sendEntityCommand((uint)entityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_VISABLE, 0, "", c_data);
                }
                if (scenePointDef.IsPatient == 1)
                {
                    GlobalGame.Instance.CourseMgr.SetPatientEntityID((uint)entityViewID, scenePointDef.Id);
                }
                else if (scenePointDef.IsPatient == 2)
                {//其它需要存储的属性，目前的ky先使用modelID
                    GlobalGame.Instance.CourseMgr.SetOtherModelEntityID((uint)entityViewID, scenePointDef.Id, scenePointDef.SceneOtherPrefab);
                }
                //if (scenePointDef.SceneOtherPrefabRot > -999)
                //{
                //    uint entityViewId = (uint)entityViewID;
                //    cmd_RotateAngle c_data = new cmd_RotateAngle()
                //    {
                //        Angle = scenePointDef.SceneOtherPrefabRot,
                //        targetAngle = scenePointDef.SceneOtherPrefabRot,
                //        timeCount = 1
                //    };
                //    GHelp.sendEntityCommand(entityViewId, (int)EntityLogicDef.ENTITY_ROTATION_BY_ONE, 0, "", c_data);
                //}
                if (int.Parse(Data[i].IsSceneLoadFinish) == 1)
                {
                    if (!m_Entitys.Contains(entityViewID))
                    {
                        m_Entitys.Add(entityViewID);
                    }
                }   
            }
            ///器械选择加载问题，下一步点击过快还未加载
            foreach (ApparatusSelectDef def in GlobalGame.Instance.CourseMgr.GetApparatusSelectDef())
            {
                if (def.IsRight == 1 || def.IsRight == 4)
                {
                    //防止modelId重复
                    m_Entitys.Add(int.Parse(def.Id.ToString() + def.ModelID.ToString()));
                }
            }
        }

    }
}
