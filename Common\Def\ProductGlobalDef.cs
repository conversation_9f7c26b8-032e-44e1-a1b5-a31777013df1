﻿/// <summary>
/// ProductGlobalDef
/// </summary>
/// <remarks>
/// 2021/11/29 12:13:23: 创建. 王正勇 <br/>
/// 产品全局数据类
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// 产品模块
    /// </summary>
    public enum ProductModel
    {
        /// <summary>
        /// 无
        /// </summary>
        None,
        /// <summary>
        /// 首页
        /// </summary>
        HomePageModule,
        /// <summary>
        /// 课程
        /// </summary>
        CourseModule,
        /// <summary>
        /// 环节
        /// </summary>
        LinkModule,
        /// <summary>
        /// 我的模块
        /// </summary>
        MineModule,
        /// <summary>
        /// 登陆模块
        /// </summary>
        LoginModule,
        /// <summary>
        ///  主动画
        /// </summary>
        MainAnimaionVideoModule,
        /// <summary>
        /// 过场动画 不显示操作按钮 快进那些
        /// </summary>
        CutSceneModule,
        /// <summary>
        /// 动画视频 显示操作按钮 快进那些
        /// </summary>
        AnimaionVideoModule,
        /// <summary>
        /// 机器人搭建模块
        /// </summary>
        RobotModule,
        /// <summary>
        /// 编程模块
        /// </summary>
        SceneModule,
        /// <summary>
        /// 小游戏模块
        /// </summary>
        MiniGamesModule,
        /// <summary>
        /// 擂台赛模块
        /// </summary>
        ChallengeModule,
        /// <summary>
        /// 答题模块
        /// </summary>
        AnswerModule,
        /// <summary>
        /// 作业模块
        /// </summary>
        HomeWrokModule,
        /// <summary>
        /// 学习报告模块
        /// </summary>
        LearnReportModule,
        /// <summary>
        /// 意见反馈
        /// </summary>
        FeedBackModule,
        /// <summary>
        /// 抢金币
        /// </summary>
        GoldModule,
        /// <summary>
        /// 点击交互
        /// </summary>
        ClickModule,
        /// <summary>
        /// 进入场景过程中
        /// </summary>
        EnterSceneModule,
    }

    /// <summary>
    /// ProductGlobalDef
    /// </summary>
    public class ProductGlobalDef
    {
    }
}
