﻿/// <summary>
/// VariableBlockDef
/// </summary>
/// <remarks>
/// 2021/7/5 10:24:45: 创建. 王正勇 <br/>
/// 自定义变量积木块的数据交互类
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public class VariateData
    {
        /// <summary>
        /// 变量积木块ID
        /// </summary>
        public string m_VariateId;
        /// <summary>
        /// 用户自定义类型
        /// </summary>
        public string m_Type;
        /// <summary>
        /// 变量积木块名称
        /// </summary>
        public string m_Name;
        /// <summary>
        /// 变量积木块类型,0表示全局，1表示当前角色
        /// </summary>
        public string m_VariateType;
        /// <summary>
        /// 变量积木块值
        /// </summary>
        public string m_Valeue;
        public void Reast()
        {
            m_VariateId = "";
            m_Type = "";
            m_Name = "";
            m_VariateType = "";
            m_Valeue = "";
        }
        public void SetVariateData(VariateData variateData)
        {
            m_VariateId = variateData.m_VariateId;
            m_Type = variateData.m_Type;
            m_Name = variateData.m_Name;
            m_VariateType = variateData.m_VariateType;
            m_Valeue = variateData.m_Valeue;
        }
    }


    /**********自定义变量交互类**********/
    #region 自定义变量

    public class LoadIngVarIate
    {
        /// <summary>
        ///通知更新积木块回调
        /// </summary>
        public Action<List<VariateData>> actionLoadCallBack;
    }
    public class VariableEdit
    {
        public string m_VariableId;
        public VariableEditType m_EditType;
        public string m_EditValue;
        public void Rest()
        {
            m_VariableId = string.Empty;
            m_EditType = VariableEditType.None;
            m_EditValue = string.Empty;
        }
    }
    public enum VariableEditType
    {
        None,
        EditName,
        EditValue,
    }
    #endregion
}
