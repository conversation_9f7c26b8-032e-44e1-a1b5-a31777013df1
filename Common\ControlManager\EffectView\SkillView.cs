﻿
/// <summary>
/// CSkillView
/// </summary>
/// <remarks>
/// 2021.5.12: 创建. 谌安 <br/>
/// 技能包装 <br/>
/// </remarks>
using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using game.schemes;

namespace GLib.Common
{
    public class CSkillView : ISkillView
    {
        //技能击中时播放技能音效
        public void PlaySoundEffect(Int64 EnityId, int effectID, Vector3 pos)
        {   
            /*
            SkillViewScheme skillData = GlobalGame.Instance.SchemeCenter.GetSchemeSkillView().GetSkillViewScheme(effectID);
            if (skillData != null)
            {
                SkillEffectContext context = new SkillEffectContext();
                context.id = (uint)effectID;
                context.ptTarget = pos;
                context.animationTicks = (uint)skillData.shakeTime;
                //GameHelp.sendEntityCommand(UID_DATA.ANALYZEUID_SERIALNO(EnityId), (int)EntityLogicDef.ENTITY_TOVIEW_PLAY_SKILL, 0, "", context);
            }*/
        }

        IEnumerator HitPlayCameraShake(Int64 EnityId, float time, Skill.Types.Item skillData)
        {
            yield return new WaitForSeconds(time);
            //Debug.LogError("HitTime:" + time.ToString());
            IEntity pEntity = GlobalGame.Instance.EntityClient.Get(EnityId);
            if (pEntity != null)
            {
               /* CameraShakeContext contexts = GHelp.GetObjectItem<CameraShakeContext>();
                contexts.type = (short)skillData.shakeType;
                contexts.shakeTime = skillData.shakeTime;
                contexts.shakeAmplitude = skillData.shakeAmplitude;
                contexts.roughness = skillData.roughness;
                contexts.delay = skillData.delay;
                contexts.totaltime = skillData.totaltime;
                GHelp.sendEntityCommand(pEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_SHAKE_CAMERA, 0, "", contexts);//传递一下受击者，因为主角受击时也要震屏*/
            }
        }

        //技能效果(含角色动作)
        public void PlaySkillEffect(Int64 EnityId, SkillEffectContext context, bool bAffectHero)
        {
          
        }

        

        //技能效果(含角色动作)
        public void StopSkillEffect(Int64 EnityId, int effectID)
        {
          
        }

        //技能蓄气效果
        public void PlayStoreUpEffect(Int64 EnityId, SkillEffectContext context)
        {
           
        }

        //打断技能蓄气效果
        public void StoreUpEffectBreak(Int64 EnityId, int effectID)
        {
        }

        //计算技能飞行的时间
        public uint CalcSkillAttackTime(Int64 EnityId, int effectID, Vector3 source, Vector3 target)
        {
            return 0;
        }

        //播放受击效果
        public void PlayHurtEffect(Int64 EnityId, int effectID, HurtType type, EDamageEffectType damagetype, bool isMonster)
        {
          
        }

        //播放死亡效果
        public void PlayDeadEffect(Int64 EnityId, int effectID, EDeathEffectType type)
        {
            IEntity pEntity = GlobalGame.Instance.EntityClient.Get(EnityId);
            if (pEntity == null)
            {
                return;
            }

            type = EDeathEffectType.DeathEffectType_Fly;
            if (type != EDeathEffectType.DeathEffectType_Normal)
            {
                //GHelp.sendEntityCommand(pEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_DEATH_EFFECT, (int)type, "", null);
            }
        }
    }
}
