﻿/// <summary>
/// ExecuteLogicDef
/// </summary>
/// <remarks>
/// 2021.4.27: 创建. 吳航 <br/>
/// 执行模块<br/>
/// </remarks>

namespace GLib.Common
{
    public enum EMActionType
    {
        /// <summary>
        /// 移动
        /// </summary>
        Move = 1,
        /// <summary>
        /// 释放技能
        /// </summary>
        UseSkill,
        /// <summary>
        /// 转体
        /// </summary>
        Turn,
        /// <summary>
        /// 跳
        /// </summary>
        Jump,
        /// <summary>
        /// 体积缩放
        /// </summary>
        Zoom,
        /// <summary>
        /// 设置颜色
        /// </summary>
        SetColor,
        /// <summary>
        /// 播放指定动画动作
        /// </summary>
        PlayAnimated,
        /// <summary>
        /// 变形
        /// </summary>
        Deformed,
        /// <summary>
        /// 设置可见状态
        /// </summary>
        Show,
        /// <summary>
        ///  邀请乘客
        /// </summary>
        Invite,
        /// <summary>
        /// 与指定对像对话
        /// </summary>
        Talk,
        /// <summary>
        /// 设置推力
        /// </summary>
        SetThrust,
        /// <summary>
        /// 装载指定目标
        /// </summary>
        Loading,
        /// <summary>
        /// 查找视野内指定类型单位
        /// </summary>
        FindByType,
        /// <summary>
        /// 查找视野内存活敌方单位
        /// </summary>
        FindEnemies,
        /// <summary>
        /// 查找视野内存活友方单位
        /// </summary>
        FindFriends,
        /// <summary>
        /// 查找本地图所有的危险（敌对且会伤害我们的实体）
        /// </summary>
        FindHazards,
        /// <summary>
        /// 查找视野内所有物品
        /// </summary>
        FindItems,
        /// <summary>
        /// 查找最近的单位
        /// </summary>
        FindNearest,
        /// <summary>
        /// 查找视野内最近的敌方存活单位
        /// </summary>
        FindNearestEnemy,
        /// <summary>
        /// 查找视野内最近的物品
        /// </summary>
        FindNearestItem,
        /// <summary>
        /// 获取机器人到目标的距离
        /// </summary>
        DistanceTo,
        /// <summary>
        /// 修改服务器变量
        /// </summary>
        ModifyVar,
        /// <summary>
        /// 获取服务器变量的值
        /// </summary>
        GetVar,
        /// <summary>
        /// 设置信号量
        /// </summary>
        SetSgnal,
        /// <summary>
        /// 获取信号量的值
        /// </summary>
        GetSgnal,
    }

    public enum TurnDirection
    {
        /// <summary>
        /// 未知方向
        /// <summary>
        Unknow = 0,
        /// <summary>
        /// 左转
        /// <summary>
        Left = 1,
        /// <summary>
        /// 右转
        /// <summary>
        Right = 2,
        /// <summary>
        /// 后转
        /// <summary>
        Backward = 3,
        /// <summary>
        /// 指定角度
        /// <summary>
        ToAngle = 4,
        /// <summary>
        /// 东
        /// <summary>
        East = 5,
        /// <summary>
        /// 南
        /// <summary>
        South = 6,
        /// <summary>
        /// 西
        /// <summary>
        West = 7,
        /// <summary>
        /// 北
        /// <summary>
        North = 8,
    }

    public enum UIGuidePlaceType
    {
        /// <summary>
        /// 任务视频
        /// </summary>
        TaskCamera = 0,
        /// <summary>
        /// 任务指引
        /// </summary>
        TaskGuide = 1,
        /// <summary>
        /// 小地图
        /// </summary>
        TinyMap = 2,
        /// <summary>
        /// 上一步
        /// </summary>
        BackStep = 3,
        /// <summary>
        /// 运行
        /// </summary>
        Run = 4,
    }


    /// <summary>
    /// 对象的可见状态
    /// </summary>
    public enum ObjectVisibility
    {
        /// <summary>
        /// 隐藏状态
        /// </summary>
        Invisible = 0,
        /// <summary>
        /// 显示状态
        /// </summary>
        Visible = 1,
    }

    public enum SetThrustState 
    {
        /// <summary>
        /// 无效状态
        /// </summary>
        Invalid = 0,
        /// <summary>
        /// 生效
        /// </summary>
        Effective = 1,
    }

    public enum LoadActionState
    {
        /// <summary>
        /// 卸载
        /// </summary>
        Uninstall = 0,
        /// <summary>
        /// 装载
        /// </summary>
        Install = 1,
    }

    public enum ShowObjectState
    {
        /// <summary>
        /// 不显示
        /// </summary>
        UnShow = 0,
        /// <summary>
        /// 显示
        /// </summary>
        Show = 1,
    }

    public enum MoveDirection
    {
        #region MoveDirection Members

        /// <summary>
        /// 上
        /// </summary>
        Top = 1,
        /// <summary>
        /// 下
        /// </summary>
        Bottom = 2,
        /// <summary>
        /// 左
        /// </summary>
        Left = 3,
        /// <summary>
        /// 右
        /// </summary>
        Right = 4,
        /// <summary>
        /// 自动寻路
        /// </summary>
        AutoFindPath = 60,

        #endregion
    }

    public enum ShowJoystickType
    {
        /// <summary>
        /// 东南西北型
        /// </summary>
        Pos = 0,
        /// <summary>
        /// 方向型
        /// </summary>
        Angle = 1,
        /// <summary>
        /// 移动东南西北型
        /// </summary>
        MovePos = 2,
        /// <summary>
        /// 技能
        /// </summary>
        Skill=3
    }

    public enum JoystickDisType
    {
        /// <summary>
        /// 东
        /// </summary>
        East = 0,
        /// <summary>
        /// 南
        /// </summary>
        South = 1,
        /// <summary>
        /// 西
        /// </summary>
        West = 2,
        /// <summary>
        /// 北
        /// </summary>
        North = 3
    }

    /// <summary>
    /// 移动方式
    /// </summary>
    public enum MoveMode
    {
        #region MoveMode Members

        /// <summary>
        /// 移动一格
        /// </summary>
        OneUnit = 1,
        /// <summary>
        /// 一直移动，碰到障碍停止
        /// </summary>
        MoveUnitlObstacle = 2,
        /// <summary>
        /// 到达目的地为止
        /// </summary>
        ArriveTheDestination = 3

        #endregion
    }

    //二维地图查找定义
    public enum MapPathResult
    {
        None = 0,
        /// <summary>
        /// 障碍物
        /// </summary>
        Obstacle,
        /// <summary>
        /// 超出范围
        /// </summary>
        OutSide,
        /// <summary>
        /// 完成
        /// </summary>
        Finish,
    }

    /// <summary>
    /// 开始处理执行行为数据
    /// </summary>
    public class ExecuteHandleData
    {
        public uint enityID;
        public string Opcode;
    }

    /// <summary>
    /// 判断执行完成需要的参数
    /// </summary>
    public class JudgeExecuteParameter
    {
        public ExecuteModelBase executeModelBase;
        public uint enityID;
    }

}
