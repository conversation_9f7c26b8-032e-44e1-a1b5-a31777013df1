﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{

    public class SkillCommon
    {
        public const int SKILL_HIT_MAX_COUNT = 20;//技能命中最大数量
        public const float SKILL_SPLASH_SPEEED = 50.0f;

    }

    // 通用效果
    public class SkillEffectContext : cmd_EntityView
    {
        public uint id;			/// 效果Id;
        public uint src;			/// 技能效果发起者
        public uint target;			/// 技能效果承受者
        public Vector3 ptCenter;		/// 技能效果中心点(施放位置)
        public Vector3 ptTarget;		/// 目标点
        public bool loop;           /// 光效是否循环
        public bool blink;           /// 是否位移
		public float fblinkSpeed;           /// 位移速度
        public bool bhide;           /// 是隐藏
		public bool bTeleport;		 /// 是否瞬移(瞬移到ptTarget位置)
        public bool canbreak;			/// 是否被打断移动
        public uint pre_count;		/// 预选目标数(用于像闪电链之类的技能)
        public uint animationTicks;		/// 整个动画过程的时间
        public uint attackTime;       ///技能攻击时间
		public uint hitTime;		  /// 技能命中时间
        public CoordinateSpace coordSpace;         //坐标是否是UI坐标系
        public bool bflyskill;		 //是否是飞行特效技能
        public string cameraJump;    //相机跳动
        public uint feedbackID; //逻辑层传的ID标识，用于标记、删除和碰撞是返回标识。<=65535
        public override void ReSet()
        {
            id = 0;
            src = 0;
            target = 0;
            ptCenter = Vector3.zero;
            ptTarget = Vector3.zero;
            loop = false;
            blink = false;
            fblinkSpeed = 0.0f;
            bhide = false;
            bTeleport = false;
            canbreak = false;
            pre_count = 0;
            animationTicks = 0;
            attackTime = 0;
            hitTime = 0;
            coordSpace = CoordinateSpace.ScreenSpace;
            bflyskill = false;
            cameraJump = null;
        }
    };

    /// 伤害击中中效果
	public enum EDamageEffectType
    {
        DamageEffectType_None = 0,
        DamageEffectType_Fall,				/// 击倒
		DamageEffectType_Back,				/// 击退
		DamageEffectType_Fly,				/// 击飞
		DamageEffectType_MaxID,
    };

    /// 死亡效果类型
    public enum EDeathEffectType
    {
        DeathEffectType_Normal = 0,     //正常
        DeathEffectType_Fly,            //击飞
        DeathEffectType_Crack,			//击啐
    };

    public enum SkillResult
    {
        SkillResult_Success,			/// 成功
	    SkillResult_InvalidArgs,		/// 无效参数（当不能识别时用）
	    SkillResult_Unknow,             /// 未知错误

        // 参数错误
	    SkillResult_SkillIDError,		/// 指定技能的ID错误
	    SkillResult_SkillTypeError,		/// 技能类型参数错误
	    SkillResult_InvalidAttackCount, /// 无效的攻击次数（次数为0？）

        // 逻辑错误
        SkillResult_ScantyMP,			/// 法术值不够
	    SkillResult_ScantyHP,			/// HP不够
	    SkillResult_TooFar,				/// 距离太远
	    SkillResult_TooNear,			/// 距离太近
	    SkillResult_IAmDead,			/// 自己已经死亡
	    SkillResult_NoTarget,			/// 目标不存在
	    SkillResult_InvalidTarget,		/// 无效目标
	    SkillResult_Block,				/// 被阻挡了
	    SkillResult_Colding,			/// 技能还在冷却中
	    SkillResult_SkillNotFinished,	/// 上一个技能还未结束
	    SkillResult_StateError,			/// 状态错误
	    SkillResult_NoUse,				/// 禁止使用

        MaxSkillResultCount,
    };

    public struct SSkillHitEntity
    {
        public UInt32 dwEntityID;           // 实体序列号
        public UInt16 wHitTime;             // 命中时间
    };

    //使用技能时上下文
    public struct SkillContext
    {
        public Int64 uidOperator; 			/// 使用技能的操作者（不一定是发起者，如载具上面的人），如果填0，表示操作者为技能部件拥有者 实体序号
		public Int64 uidTarget;				/// 使用技能时鼠标点击的位置上的实体对象 实体序号
		public float fPosX;					/// 使用技能的目标位置X
		public float fPosY;					/// 使用技能的目标位置Y
		public float fPosZ;					/// 使用技能的目标位置Z
		public float fForwardX;				/// 使用技能的朝向X
		public float fForwardY;				/// 使用技能的朝向Y
		public float fForwardZ;				/// 使用技能的朝向Z
		public float fAngle;				/// 使用技能的角度
		public UInt16 skillid;				/// 技能大类ID
		public UInt16 skillsubid;			/// 技能子ID
	    public Int32 result;                /// 技能结果(SkillResult)
        //bool		noBreakMove;	        /// 是否不打断移动
        public uint hitTicks;       /// 击中时间（由客户端技能系统发起者自行计算）
        //ulong		flyTicks;		        /// 飞行光效耗时
        public uint pre_count;		/// 预选目标数(用于像闪电链之类的技能)
	    public uint viewid;			/// 视图ID(客户端自行随机确定)
	    public uint tick;			/// 客户端tick
	    public uint arrived_tick;	/// 到达服务器时的tick
		public uint breakFlag;		/// 当前技能的打断标志
		public uint dwZoneID;				/// 场景ID
		public List<SSkillHitEntity> listHitEntity;
    };

    /// <summary>
    /// 使用技能槽类型
    /// </summary>
    public enum ESkillSlot
    {
        None = -1,                  // 技能无效槽位
        Slot0,                      // 技能0槽 //普通攻击
        Slot1,                      // 技能1槽 
        Slot2,                      // 技能2槽
        Slot3,                      // 技能3槽
        Slot4,                      // 技能4槽
        Slot5,                      // 技能5槽
        Slot6,                      // 技能6槽
        Slot7,                      // 技能7槽
        Slot8,                      // 技能8槽
        Slot9,                      // 技能9槽
        Slot10,                     // 技能10槽
        Slot11,                     // 技能11槽
        Slot12,                     // 技能12槽
        Slot13,                     // 技能13槽
        Slot14,                     // 技能14槽
        Slot15,                     // 技能15槽
        Slot16,                     // 技能16槽
        Slot17,                     // 技能17槽

        Slot_Max,
    }

    //技能攻击范围类型（范围类型，单选）
    public enum ESkillRangeType
    {
        SkillRangeType_Single = 0,						/// 单体选择
		SkillRangeType_Circle,							/// 圆形区域类
		SkillRangeType_Rect,							/// 矩形区域类
		SkillRangeType_Sector,                          /// 扇形区域类
        SkillRangeType_MaxID							/// 最大ID
    };

    /// 使用技能时的现场
    public class SkillScene
    {
        public ICreature srcEntity;		/// 使用技能的发起者
        public IEntityClient entityWorld;	/// 实体管理器
        //ISceneManager2 mapZone;		/// 地图区域
    };

    /// 技能操作类型（单选）
    public enum ESkillSelectType
    {
        SkillSelectType_NoSelect = 0,			/// 不需要选择
		SkillSelectType_SelectPos,				/// 选择位置
		SkillSelectType_SelectEntity,			/// 选择目标（单体攻击必须要有目标才能使用）
		SkillSelectType_SelectFace,				/// 选择面向(技能的方向就是当前实体的朝向）(冲刺)
		SkillSelectType_SelectFaceEx,			/// 选择面向优先实体
		SkillSelectType_SelectAngle,			/// 选择方向
		SkillSelectType_MaxID					/// 最大ID
    };
    // 对象筛选方式之阵营(单选)
    public enum ESkillCampType
    {
        SkillCampType_NoCamp = 0,		/// 无阵营
		SkillCampType_Enmity,			/// 敌方
		SkillCampType_Amity,			/// 友方
		SkillCampType_MaxID				/// 最大ID
    };

    //技能通用选项（多选） 定义一些技能杂项类型
    public enum ESkillGeneralOption
    {
        SGO_NearAttack = 0x0001,	/// 是否是近程攻击（涉及到随机选择攻击动画）
	    SGO_PreSelTarget = 0x0002,	/// 是否由服务器计算目标，发给客户端
	    SGO_OrderViewId = 0x0004,	/// 顺序执行viewid，否则为随机（当viewid是一个列表时有效）
	    SGO_LongRange = 0x0008,	/// 远程技能用（如战场大炮，攻击距离超过了9宫格）
	    SGO_ClientGoFirst = 0x0010,	/// 是否允许客户端先行 
	    SGO_NoAutoAmity = 0x0020,	/// 目标必须为友方，但当目标不是友方时，技能不自动更换目标
	    SGO_NoDistCheck = 0x0040,	/// 服务器不做距离校验（仅仅客户端校验，用于比如武圣的疾风突刺、天师的移形换影等依赖位置同步的技能，减少技能失败的可能性）
	    SGO_Strike = 0x0080,	/// 技能是否直线穿透
		SGO_Fly_Jump = 0x0100,	/// 飞行特技技能 飞行状态时使用的技能类型
		SGO_ClientFindTgt = 0x0200,	/// 是否允许客户端搜怪 必须同时配置了SGO_ClientGoFirst选项才有用 并且只能配置到瞬发技能
		SGO_Fly = 0x0400,	/// 飞行技能
    };

    /// 效果中心点(中心点类型，单选)
    public enum ESkillCenterType
    {
        SkillCenterType_Myself = 0,			/// 以自己为中心点
		SkillCenterType_Target,				/// 以目标(点)为中心点
		SkillCenterType_MaxID				/// 最大ID
    };

    public class SkillId
    {
        public UInt16 id;		/// 技能ID
        public SkillId(int skillid)
        {
            id = (UInt16)skillid;
        }

        public SkillId(UInt16 id1)
        {
            id = id1;
        }

        public void Reset()
        {
            id = 0;
        }

        public int GetID()
        {
            return id ;
        }

        public void SetID(int nSkillID)
        {
            id = (UInt16)nSkillID ;
        }
    };
}
