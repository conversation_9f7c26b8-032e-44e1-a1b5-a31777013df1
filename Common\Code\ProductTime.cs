﻿/// <summary>
/// ProductTime
/// </summary>
/// <remarks>
/// 2019.6.24: 创建. 谌安 <br/>
/// 时间管理类 <br/>
/// </remarks>
using System;
using UnityEngine;
using GLib;
namespace GLib.Common
{
	
	public class ProductTime
	{
		// C#日期时间Ticks与秒数换算关系
		public const long TICKS_TO_SECONDS = 10000000;

		private static uint m_nServerTime;
		private static float m_nServerTick;

		public static void Init()
		{
			TimeSpan ts = DateTime.Now - DateTime.Parse("1970-1-1");
			m_nServerTime = (uint)ts.TotalSeconds;
			m_nServerTick = ProductTime.GetTickCount();
		}

		public static void SetTime(uint serverTime)
		{
			uint oldServerTick = m_nServerTime;
			m_nServerTime = serverTime;
			m_nServerTick = ProductTime.GetTickCount();
		}

		/// <summary>
		/// 获取服务器时间，1970.1.1至今的秒数
        /// 获取比较大的时间变化时，由float 转换成 int 时，转换过程中会有误差，导致通过获取服务器描述不变，
        /// 如果需要获取服务器时间秒数时，建议使用GetServerTime() add by zjc
		/// </summary>
		/// <returns></returns>
		public static uint GetTime()
		{
			uint dwRetTime = 0;
			if (m_nServerTime > 0 && m_nServerTick > 0)
			{
				uint nTicks = (uint)(ProductTime.GetTickCount() - m_nServerTick);
				if (nTicks >= 0)
				{
					dwRetTime = m_nServerTime + nTicks/1000;
				}
				else
				{
					dwRetTime = JLCDateTime.ConvertCurZoneTimeToUtcTimeSec(DateTime.Now);
				}
			}
			else
			{
				dwRetTime = JLCDateTime.ConvertCurZoneTimeToUtcTimeSec(DateTime.Now);
			}
			return dwRetTime;
		}

		/// <summary>
		/// 将服务器时间转化成c#国际标准日期时间
		/// </summary>
		/// <returns></returns>
		public static DateTime GetDateTime()
		{
			// 获取当前日期和时间
			//DateTime refTime = new DateTime(1970, 1, 1, 0, 0, 0);//621355968000000000L
			DateTime curdate = new DateTime(621355968000000000L + (int)GetTime() * TICKS_TO_SECONDS);
			return curdate;
		}

		/// <summary>
		/// 将服务器时间转化成c#国际标准日期时间
		/// </summary>
		/// <returns></returns>
		public static DateTime GetDateTime(int seconds)
		{
			// 获取当前日期和时间
			//DateTime refTime = new DateTime(1970, 1, 1, 0, 0, 0);//621355968000000000L
			DateTime curdate = new DateTime(621355968000000000L + seconds * TICKS_TO_SECONDS);
			return curdate;
		}

		/// <summary>
		/// 获取游戏中国服务器日期时间
		/// </summary>
		/// <returns></returns>
		public static DateTime GetGameTime()
		{
			// 获取当前日期和时间
			//DateTime refTime = new DateTime(1970, 1, 1, 8, 0, 0);//621355968000000000L + 2880000000L
			DateTime curdate = new DateTime(621356256000000000L + (int)GetTime() * TICKS_TO_SECONDS);
			return curdate;
		}

		/// <summary>
		/// 获得游戏启动以来的tick数  游戏压入后台不会暂停计数
		/// </summary>
		/// <returns></returns>
		public static int GetTickCount()
		{
			return Api.GetTickCount();
		}
	}
}

