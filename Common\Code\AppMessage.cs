﻿using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class AppMessage : Singleton<AppMessage>, HTTP_Response_Handler
    {
        private string _radid = "";
        private string _rsid = "";
        private string _mac = "";

        // 发送App启动
        public void SendAppStart()
        {
        }

        // 发送App结束
        public void SendAppEnd()
        {
        }

        private WWWForm GetFrom()
        {
            string radid = _radid;
            if (string.IsNullOrEmpty(radid))
            {
                _radid = PluginPlatform.Instance.Plugin().Radid();
                radid = _radid;
            }
            string rsid = _rsid;
            if (string.IsNullOrEmpty(rsid))
            {
                _rsid = PluginPlatform.Instance.Plugin().Rsid();
                rsid = _rsid;
            }
            string mac = _mac;
            if (string.IsNullOrEmpty(mac))
            {
                _mac = PluginPlatform.Instance.Plugin().GetMac();
                mac = _mac;
            }
            string version = ProductConfig.Version;
            string platform = ResUtil.GetCurrentPlatformName();
            WWWForm from = GSpawnPool.Instance.GetObjectItem<WWWForm>();
            from.AddField("mac", mac);
            from.AddField("radid", radid);
            from.AddField("rsid", rsid);
            from.AddField("version", version);
            from.AddField("platform", platform);
            return from;
        }

        public EMHTTP_METHOD GetCurrentHttpMethod()
        {
            return EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST;
        }

        public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
        {
            return true;
        }

        public void OnError(uint dwError, string url)
        {
            
        }

        public void OnLocation(string new_url, string url)
        {
            
        }

        public void OnResponse(byte[] pContent, uint dwLen, string url)
        {
            
        }
    }
}
