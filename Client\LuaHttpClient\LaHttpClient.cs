﻿/// <summary>
/// LuaHttpClient
/// </summary>
/// <remarks>
/// 2021.10.21: 创建. 王康阳 <br/>
/// lua请求httpclient<br/>
/// </remarks>
using game.schemes;
using GLib.Common;
using UnityEngine;

namespace GLib.Client
{
    public class LaHttpClient : ILaHttpClient,IMessageHandler
    {

        public LaHttpClient()
        {

        }

        ~LaHttpClient()
        {

        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName
        {
            get;
            set;
        }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState
        {
            get;
            set;
        }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress
        {
            get;
            set;
        }
        public bool Create()
        {
            return true;
        }

        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }

        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {

        }

        public void Release()
        {
        }

        public void Response(string keyName,EMHTTP_METHOD requestType, string szUrl, WWWForm data = null, string Authorization = "", byte[] bodyRaw = null)
        {
            LaHttp la = new LaHttp();
            la.SendWebMessage(keyName,requestType, szUrl, data, Authorization, bodyRaw);
        }

        public void Update()
        {
           
        }
    }
}
