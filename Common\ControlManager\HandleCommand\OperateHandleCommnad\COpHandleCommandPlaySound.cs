﻿/// <summary>
/// COpHandleCommandPlaySound
/// </summary>
/// <remarks>
/// 2023/5/16 16:02:41: 创建. 熊洋 <br/>
/// <br/>
/// </remarks>

using GLib;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

namespace GLib.Common
{
    public class COpHandleCommandPlaySound : IHandleCommand
    {
        int m_audioId;
        bool m_isPlay;
        bool m_isEnd = false; // 是否不正确的执行完指令
        private List<IHandleCommand> m_others;
        public COpHandleCommandPlaySound(SOpHandleCommand_PlaySound data)
        {
            m_audioId = data.audioId;
            m_isPlay = data.isPlay;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpPlaySound;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_audioId = 0;
        }

        public bool run()
        {
            // 超时的话就直接强制结束指令
            if (m_isPlay)
            {
                GHelp.PauseMusic(m_audioId, AudioTagClass.OPCodeAudio);
                GHelp.PlayMusic(m_audioId, AudioTagClass.AudioSound, AudioTagClass.OPCodeAudio);
            }
            else
            {
                GHelp.PauseMusic(m_audioId, AudioTagClass.OPCodeAudio);
            }
            OperateHandleCommandHelp.AppendCommand(m_others);
            return true;
        }

        public void update()
        {
        }
    }
}