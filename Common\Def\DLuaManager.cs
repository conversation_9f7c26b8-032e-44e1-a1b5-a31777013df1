﻿/// <summary>
/// LuaParam
/// </summary>
/// <remarks>
/// 2021.5.28: 创建. 王康阳 <br/>
/// Lua模块事件公共定义<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Collections;
using System.Runtime.InteropServices;
using GLib.LitJson;
using GLib;

namespace GLib.Common
{
    // 委托
    public delegate void LuaMessageDelegate(string strKey, JsonD<PERSON> json);

    // 委托 回调函数
    public delegate void LuaCommandDelegate(string strKey, CLuaParam CParam);


    /// <summary>
    /// Lua命令参数管理
    /// </summary>
    public class CLuaParam
    {

        List<string> m_ltParam = new List<string>();

        private CLuaParam OutLuaParam = null;

        /// <summary>
        /// 获取参数的个数
        /// </summary>
        public int Count
        {
            get { return m_ltParam.Count; }
        }

        public CLuaParam()
        {

        }

        /// <summary>
        /// 保存参数到堆栈
        /// </summary>
        /// <param name="text">保存的数据</param>
        public void Push(string text)
        {
            m_ltParam.Add(text);
        }

        /// <summary>
        /// 保存参数到堆栈
        /// </summary>
        /// <param name="text">保存的数据</param>
        public void PushOutLuaParam(string text)
        {
            if (null == OutLuaParam)
            {
                OutLuaParam = new CLuaParam();
            }
            OutLuaParam.Push(text);
        }

        /// <summary>
        /// 返回参数
        /// </summary>
        /// <returns></returns>
        public CLuaParam GetOutLuaParam()
        {
            return OutLuaParam;
        }

        /// <summary>
        /// 获取Int数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public int GetInt(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            int nValue = 0;
            try
            {
                nValue = System.Int32.Parse(str);
            }catch(Exception Ex){
                TRACE.ErrorLn("CLuaParam::GetInt 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return nValue;
        }

        /// <summary>
        /// 获取Int16数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public Int16 GetInt16(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            Int16 nValue = 0;
            try
            {
                nValue = System.Int16.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::GetInt16 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return nValue;
        }

        /// <summary>
        /// 获取UInt16数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public UInt16 GetUInt16(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            UInt16 nValue = 0;
            try
            {
                nValue = System.UInt16.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::GetUInt32 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return nValue;
        }

        /// <summary>
        /// 获取UInt32数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public UInt32 GetUInt32(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            UInt32 nValue = 0;
            try
            {
                nValue = System.UInt32.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::GetUInt32 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return nValue;
        }

        /// <summary>
        /// 获取Int64数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public Int64 GetInt64(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            Int64 nValue = 0;
            try
            {
                nValue = System.Int64.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::GetInt64 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return nValue;
        }

        /// <summary>
        /// 获取UInt64数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public UInt64 GetUInt64(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            UInt64 nValue = 0;
            try
            {
                nValue = System.UInt64.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::GetUInt64 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return nValue;
        }

        /// <summary>
        /// 获取bool数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public bool Getbool(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return false;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return false;
            }

            bool bValue = false;
            try
            {
                bValue = (str.Trim().ToLower().Equals("true")) ? true : false; //(bool)System.Boolean.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::Getbool 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return bValue;
        }

        /// <summary>
        /// 获取float数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public float Getfloat(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            float fValue = 0.0f;
            try
            {
                fValue = System.Single.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::GetFloat 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return fValue;
        }

        /// <summary>
        /// 获取double数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public double Getdouble(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            double dValue = 0.0d;
            try
            {
                dValue = System.Double.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::Getdouble 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return dValue;
        }

        /// <summary>
        /// 获取byte数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public byte Getbyte(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return 0;
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return 0;
            }

            byte dValue = 0;
            try
            {
                dValue = System.Byte.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::Getbyte 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return dValue;
        }

        /// <summary>
        /// 获取char数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public char Getchar(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return '\0'; 
            }
            string str = m_ltParam[index];
            if (string.IsNullOrEmpty(str))
            {
                return '\0';
            }

            char cValue = '\0'; ;
            try
            {
                cValue = System.Char.Parse(str);
            }
            catch (Exception Ex)
            {
                TRACE.ErrorLn("CLuaParam::Getchar 出错 str= " + str + ",Ex=" + Ex.ToString());
            }
            //
            return cValue;
        }

        /// <summary>
        /// 获取string数据
        /// </summary>
        /// <param name="index">参数序号,0开始</param>
        /// <returns></returns>
        public string GetString(int index)
        {
            if (index < 0 || index > m_ltParam.Count - 1)
            {
                return string.Empty;
            }
            //
            return m_ltParam[index];

        }

        


    }

    /// <summary>
    /// Lua模块中需要使用的一下常量定义
    /// </summary>
    public class DLuaManager
    {
        //////////////////////////////////////////////////////////////////////////
        // 公共定义

    }
}
