﻿/// <summary>
/// CEffectClient
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// 效果客户端<br/>
/// </remarks>
using game.schemes;
using GLib.Common;

namespace GLib.Client
{
    public class CEffectClient : IEffectClient
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        /** 
        @param   
        @param   
        @return  
        */
        public CEffectClient()
        {
        }

        /** 
        @param   
        @param   
        @return  
        */
        ~CEffectClient()
        {

        }

        /** 释放
        @param   
        @param   
        @param   
        @return  
        @note     释放所有资源，并且销毁此对像
        @warning 
        @retval buffer 
        */
        public void Release()
        {
        }

        /** 
        @param   
        @param   
        @return  
        */
        public bool Create()
        {
	        return true;
        }

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {

        }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {

        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {

        }

        /** 创建效果
        @param   
        @param   
        @return  
        */
        public IEffect Build(uint dwEffectID, object pszContext, int nLen)
        {
			BuffEffect.Types.Item pSchemeInfo = GlobalGame.Instance.SchemeCenter.GetSchemeEffect().GetEffectSchemeInfo(dwEffectID); 
			if (pSchemeInfo == null)
			{
				TRACE.ErrorLn("EffectClient::Build 获取效果配置失败 dwEffectID=" + dwEffectID);
				return null;
			}
			/*
	        switch(pSchemeInfo.nGroupID)
	        {
                case (int)EMEEffectGroupID.EEffectGroupID_Unable_Move:
		        {
			        CEffectVetoActionMsg pEffect = new CEffectVetoActionMsg();
                    if (!pEffect.Create(dwEffectID, pSchemeInfo, DGlobalMessage.MSG_ACTION_PREP3DMOVE))
			        {
						pEffect.Release();
				        return null;	
			        }

			        return pEffect;
		        }
		        break;
                case (int)EMEEffectGroupID.EEffectGroupID_Unable_UseGoods:
		        {
			        CEffectVetoActEvent pEffect = new CEffectVetoActEvent();
                    if (!pEffect.Create(dwEffectID, pSchemeInfo, DGlobalEvent.EVENT_ENTITY_USELEECHDOM))
			        {
						pEffect.Release();
				        return null;	
			        }

			        return pEffect;
		        }
		        break;
            case (int)EMEEffectGroupID.EEffectGroupID_Unable_Attack:
		        {
			        CEffectVetoUseSkill pEffect = new CEffectVetoUseSkill();
			        if(!pEffect.Create(dwEffectID, pSchemeInfo, false))
			        {
						pEffect.Release();
				        return null;				
			        }

			        return pEffect;
		        }
		        break;
            case (int)EMEEffectGroupID.EEffectGroupID_Unable_UseSkill:
		        {
			        CEffectVetoUseSkill pEffect = new CEffectVetoUseSkill();
			        if(!pEffect.Create(dwEffectID, pSchemeInfo, true))
			        {
						pEffect.Release();
				        return null;				
			        }

			        return pEffect;
		        }
		        break;
            case (int)EMEEffectGroupID.EEffectGroupID_CreatureSit:
		        {
			        CEffectCreatureSit pEffect = new CEffectCreatureSit();
			        if(!pEffect.Create(dwEffectID, pSchemeInfo))
			        {
						pEffect.Release();
				        return null;				
			        }

			        return pEffect;
		        }
		        break;
			case (int)EMEEffectGroupID.EEffectGroupID_RideMount:
				{
					CEffectRideMount pEffect = new CEffectRideMount();
					if (!pEffect.Create(dwEffectID, pSchemeInfo))
					{
						pEffect.Release();
						return null;
					}

					return pEffect;
				}
				break;
            case (int)EMEEffectGroupID.EEffectGroupID_FuriousMove:
		        {
			        CEffectVetoActionMsg pEffect = new CEffectVetoActionMsg();
                    if (!pEffect.Create(dwEffectID, pSchemeInfo, DGlobalMessage.MSG_ACTION_PREP3DMOVE))
			        {
						pEffect.Release();
				        return null;	
			        }

			        return pEffect;
		        }
		        break;
			//case (int)EMEEffectGroupID.EEffectGroupID_SuspendEntity:
			//	{
			//		CEffectSuspendEntity pEffect = new CEffectSuspendEntity();
			//		if (!pEffect.Create(dwEffectID, pSchemeInfo))
			//		{
			//			return null;
			//		}

			//		return pEffect;
			//	}
			//	break;
			//case (int)EMEEffectGroupID.EEffectGroupID_CustomAction:			// 自定义动作
			//	{
			//		CEffectCustomAction pEffect = new CEffectCustomAction();
			//		if (!pEffect.Create(dwEffectID, pSchemeInfo))
			//		{
			//			return null;
			//		}

			//		return pEffect;
			//	}
			//	break;
	        default:
		        {
			        CEffectDefault pEffect = new CEffectDefault();
			        if(!pEffect.Create(dwEffectID, pSchemeInfo))
			        {
						pEffect.Release();
				        return null;
			        }

			        return pEffect;
		        }
		        break;
	        }*/

			return null;
        }

        /** 取得效果的描述
        @param   
        @param   
        @return  
        */
        public string GetEffectDesc(uint dwEffectID)
        {
			BuffEffect.Types.Item pInfo = GlobalGame.Instance.SchemeCenter.GetSchemeEffect().GetEffectSchemeInfo(dwEffectID);
	        if(pInfo == null)
	        {
		        return null;
	        }

	        return pInfo.Description;
        }

        /** 某个效果ID是否存在
        @param   
        @param   
        @return  
        */
        public bool IsExistEffectID(uint dwEffectID)
        {
			BuffEffect.Types.Item pInfo = GlobalGame.Instance.SchemeCenter.GetSchemeEffect().GetEffectSchemeInfo(dwEffectID);
			if (pInfo == null)
	        {
		        return false;
	        }

	        return true;
        }


    };
}
