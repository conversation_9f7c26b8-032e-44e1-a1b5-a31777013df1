﻿/// <summary>
/// DGlobalEvent
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 事件定义<br/>
/// </remarks>

using System;
using System.Runtime.InteropServices;
using UID = System.Int64;
using UnityEngine;
using UnityEngine.Events;
using System.Collections.Generic;

namespace GLib.Common
{
    public class DGlobalEvent
    {
        #region 框架事件不可更改
        // 加载器加载模块失败
        public const int EVENT_LOADER_FAIL = 1;

        // 状态变化
        public const int EVENT_STATE_CHANGE = 2;

        public const int EVENT_EXIT_GAME_STATE = 3;

        // 场景加载完成
        public const int EVENT_SCENE_LOAD_FINISH = 4;

        // 场景加载开始
        public const int EVENT_SCENE_LOAD_START = 5;

        // 场景加载进度
        public const int EVENT_SCENE_LOAD_PROGRESS = 6;
        // 场景加载小提示信息
        public const int EVENT_SCENE_LOAD_TIPS = 7;
        //事件系统 按下
        public const int EVENT_OP_PRESS = 8;
        //主角移动
        public const int EVENT_HERO_MOVE = 9;
        //主角操作状态
        public const int EVENT_HERO_OPERATE_STATE = 10;
        //主角是否允许闪现事件
        public const int EVENT_HERO_BLINK_STATE = 11;
        //触摸板上操作事件
        public const int EVENT_TOUCHPAD_CONTROLLER = 12;
        //相机抖动事件
        public const int EVENT_CAMERA_SHAKE = 13;
        // 获取相机姿态事件
        public const int EVENT_CAMERA_POSE = 15;

        //场景切换开始
        public const int EVENT_STAGE_START = 16;
        // 场景切换结束
        public const int EVENT_STAGE_END = 17;
        // 资源预加载进度（切场景）
        public const int EVENT_RESOURCE_PRELOAD_PROGRESS = 18;
        // 资源预加载完成（切场景）
        public const int EVENT_RESOURCE_PRELOAD_FINISH = 19;
        // 场景加载信息
        public const int EVENT_SCENE_LOAD_MESSAGE = 20;
        // 场景切换过程中（用于预加载资源）
        public const int EVENT_STAGE_LOADING = 21;
        //退出登录状态
        public const int EVENT_EXIT_LOGIN_STATE = 22;
        // 登录状态变化
        public const int EVENT_LOGIN_STATE_CHANGE = 23;
        //注册完成
        public const int EVENT_LOGIN_STATE_REGISTER_OK = 24;
        //登录完成(未获取IP地址)
        public const int EVENT_LOGIN_STATE_LOGIN_SUCCESS = 25;
        // 开始移动，移动过程中，不是此事件
        public const int EVENT_CREATURE_STARTMOVE = 26;
        // 移动完成
        public const int EVENT_CREATURE_DONEMOVE = 27;
        // 销毁了场景
        public const int EVENT_SYSTEM_DESTORYZONE = 28;
        // 客户端创建英雄（也就是客户端主角）
        public const int EVENT_PERSON_CREATEHERO = 29;

        // 客户端销毁英雄（也就是客户端主角）
        public const int EVENT_PERSON_DESTROYHERO = 30;
             // 客户端创建非英雄（也就是不是客户端主角）
        public const int EVENT_PERSON_CREATENOTHERO = 31;

        // 客户端创人物，不管是不是客户端主角
        public const int EVENT_PERSON_CREATE = 32;
        // 创建实体
        public const int EVENT_ENTITY_CREATEENTITY = 33;
        // 删除实体
        public const int EVENT_ENTITY_DESTROYENTITY = 34;
        // 准备移动投票事件
        public const int EVENT_CREATURE_PRE3DMOVE = 35;
        //选角完成
        public const int EVENT_SELECTROLE_FINISH = 36;
        // 实体添加BUFF
        public const int EVENT_CREATURE_ADDBUFF = 37;

        // 实体移除BUFF
        public const int EVENT_CREATURE_REMOVEBUFF = 38;

        // 实体更新属性
        public const int EVENT_ENTITY_UPDATEPROP = 39;
        // 生物死亡
        public const int EVENT_CREATURE_DIE = 40;

        //进入战斗模式事件
        public const int EVENT_PERSON_FIGHT_MODE_ENTER = 41;

        //退出战斗模式事件
        public const int EVENT_PERSON_FIGHT_MODE_EXIT = 42;
        // 主角技能状态切换回正常或者攻击等待状态
        public const int EVENT_SKILL_STATE_NORMAL = 43;
        //开始构建地图
        public const int EVENT_SYSTEM_BUILDZONE = 44;
        // 退出游戏到登陆界面
        public const int EVENT_EXIT_GAMELOGIN_STATE = 45;
        // 切换人物角色
        public const int EVENT_CHANGE_ROLE = 46;

        // 执行事件
        public const int EXECUTE_RECIVE_OPCODE = 47;
        //行为各种原因失败
        public const int EXECUTE_FAIL = 48;
        //行为逻辑完成
        public const int EXECUTE_LOGIC_FINISH = 49;
        //行为执行成功 执行下一步
        public const int EXECUTE_SUCCESS = 50;
        //一段代码运行完成
        public const int BLOCK_SEGMENT_END_RUN = 51;
        // 获取场景服对客户端发送的移动消息
        public const int EXECUTE_MOVETO_SC = 52;
        // 执行子事件
        public const int EXECUTE_RECIVE_CELLOPCODE = 53;
        /// <summary>
        /// 退出场景
        /// </summary>
        public const int EVENT_QUIT_SCENE = 54;
        //登录完成(已获取IP地址)
        public const int EVENT_ALL_LOGIN_SUCCESS = 55;
        //场景服准备完毕
        public const int EVENT_SERVER_READY_SUCCESS = 56;
        //AppMy准备完毕
        public const int EVENT_APP_MY_SUCCESS = 57;
        //登出
        public const int EVENT_LOGIN_OUT = 58;
        //场景核心模型信息
        public const int EVENT_SCENE_COREMODEL_INFO = 59;
        //重联网关结束
        public const int EVENT_NET_RECONNECTED = 60;
        //重联网关成功 (false:开始重连 true:重连成功)
        public const int EVENT_NET_CONNERCT = 61;
        //重联成功刷新场景
        public const int EVENT_RECONNECTED_REFRESH_SCENE = 62;
        /// <summary>
        /// 通知回到菜单界面，不退出登陆
        /// </summary>
        public const int INFORM_GO_BACK_MEUN_NOT_LOGINOUT = 63;
        /// <summary>
        /// 登录失败
        /// </summary>
        public const int EVENT_LOGIN_FAIL = 64;
        /// <summary>
        /// 通知项目进入到前台运行
        /// </summary>
        public const int PRODUCT_IN_FRONT_DESK = 65;
        /// <summary>
        /// 通知项目进入到后台运行
        /// </summary>
        public const int PRODUCT_IN_BACKSTAGE = 66;
        /// <summary>
        /// 通知项目后台运行超过时长
        /// </summary>
        public const int PRODUCT_EXCEED_BACKSTAGE_TIME = 67;
        /// <summary>
        /// 登录信息过期
        /// </summary>
        public const int EVENT_LOGIN_EXCEED_TIME = 68;
        //主角眨眼
        public const int EVENT_HERO_BLINK_START = 69;
        public const int EVENT_HERO_BLINK_END = 70;
        /// <summary>
        /// 场景物体皮肤加载完成
        /// 不是所有的物体,而是一些没皮肤加载完成会影响到其他逻辑报错的关键物体
        /// 例如:发了这个事件,就马上得获取到该物体皮肤的一些节点或者节点上的脚本
        /// </summary>
        /// 
        public const int EVENT_SCENE_OBJLOAD_FINISH = 71;
        /// <summary>
        /// 场景进入
        /// </summary>
        public const int EVENT_SCENE_START = 72;


        /// <summary>
        /// 登录成功
        /// </summary>
        public const int EVENT_LOGIN_SUCCESS = 73;
        // 最大事件码
        public const int EVENT_ALL_MAXID = 1000;
        #endregion

        #region 系统事件开始

        public const int EVENT_COMMANDHANDLE_FINISH = 1100;

        #endregion
        #region Hand_Event


        /// <summary>
        ///   //变换手的姿势
        /// </summary>
        public const int EVENT_HAND_POSECHANGE = 2000;
        /// <summary>
        /// 设置手的子物体的激活状态
        /// </summary>
        public const int EVENT_HAND_SETCHILDACTIVE = 2001;
        /// <summary>
        /// 设置手的激活状态
        /// </summary>
        public const int EVENT_HAND_SETHANDACTIVE = 2002;
        #endregion

    }

    // 实体类型
    public enum EMtEntity_Class
    {
        /*
							00000000 00000001		// 人物 （1）
							00000000 00000010		// 怪物 （2）
							00000000 00000011		// 宝箱 （3）
							00000000 00000100		// 药品 （4）
							00000000 00000101		// 陷阱 （5）
							00000000 00000110		// 载具 （6）
							00000000 00000111		// 旗杆 （7）

						1 00000000 00000000		// 是否为生物
						10 00000000 00000000		// 是否为物品


        1010000       11111000       11111000
        |或OR         & And          ^ 异或XOR
        10010001      10011101       00011100
        10110001      10011000       11100100
		11111111 11111111 11111111 11111111		// .....	
		//*/

        tEntity_Class_Person = 0x0001,          // 人物
        tEntity_Class_Monster = 0x0002,         // 怪物	
        tEntity_Class_Box = 0x00004,            // 宝箱
        tEntity_Class_Trap = 0x00008,             // 陷阱
        tEntity_Class_Tank = 0x00010,			// 载具	
        tEntity_Class_Mast = 0x00020,           // 旗杆 （传送门... ...）
        tEntity_Class_Max = 0x00040,            // 最大
        tEntity_Class_Bullet = 0x00080,         //子弹
        // -- 以下是外部无需用到的，都是通过接口实现 ----
        tEntity_IsClass_Creature = 0x10000,     // 是否为生物
        tEntity_IsClass_Goods = 0x20000,        // 是否为物品
    };

    public struct SEventCaptureGame
    {
        /// <summary>
        /// 线性速度
        /// </summary>
        public Vector3 m_linearVelocity;
        /// <summary>
        /// 角速度
        /// </summary>
        public Vector3 m_angularVelocity;
    }
    public struct SEVENTRadial
    {
        /// <summary>
        /// 摄像机情况管理
        /// </summary>
        public Ray m_Ray;
        public GameObject m_hitGameObject;

    }

    /// <summary>
    /// 相机抖动事件
    /// </summary>
    public struct SEventCameraShake
    {
    }

    /// <summary>
    /// 相机姿态事件
    /// </summary>
    public struct SEventCameraPose
    {
        public Transform HeadCamera;
    }

    public class UID_DATA
    {
        // 分解出序列号
        public static uint ANALYZEUID_SERIALNO(UID uid)
        {
            return (uint)(uid & 0XFFFFFFFF);
        }

        // 分解出句柄
        public static uint ANALYZEUID_HANDLE(UID uid)
        {
            return ((uint)(((ulong)uid & 0XFFFFFFFF00000000) >> 32));
        }
    }

    #region 框架内容，不可更改
    /// 发送源类型
    public enum EMSOURCE_TYPE
    {
        SOURCE_TYPE_UNKNOW = 0, // 类型ID根
        SOURCE_TYPE_PERSON,				// 玩家实体
        SOURCE_TYPE_SYSTEM, // 系统事件
        SOURCE_TYPE_MONSTER,			// 怪物实体
        SOURCE_TYPE_UI, // UI事件
        SOURCE_TYPE_BOX,				// 宝箱实体
        SOURCE_TYPE_MAST,               //旗杆，传送门
        SOURCE_TYPE_TANK,               //载具
        SOURCE_TYPE_EXECUTEMODEL,      // 执行模块
        SOURCE_TYPE_UPDATE,             //更新模块

        SOURCE_TYPE_MAX,
    };

    // 场景加载完成
    // EVENT_SCENE_LOAD_FINISH;
    public struct SEventSceneLoadFinish
    {
        public UInt32 nMapId; // 地图ID
        public string strSceneName; // 场景名字
    };

    //场景加载开始
    //GVIEWCMD_SCENE_LOAD_AIDSCENE
    public struct SEventAidSceneStart
    {
        public string strSceneName; // 地图ID
        public string SceneType; // 场景类型
    }

    // 场景加载开始
    // EVENT_SCENE_LOAD_START;
    public struct SEventSceneLoadStart
    {
        public UInt32 nMapId; // 地图ID
        public string strSceneName; // 场景名字
        public bool needLoadLevel; // 是否需要进行正真的场景加载操作
    };

    // 场景加载进度
    // EVENT_SCENE_LOAD_PROGRESS;
    public struct SEventSceneLoadProgress
    {
        public UInt32 nMapId; // 地图ID
        public string strSceneName; // 场景名字
        public float nProgress; // 进度值
        public bool isHotUpdate;//是否是热更新进度
    };
        /// <summary>
         /// 重新显示 
         /// </summary>
    public struct SEventSceneReShow
    {
        public UInt32 nMapId; // 地图ID
        public string strSceneName; // 场景名字
    }
    // EVENT_GAME_STATE_CHANGE
    public struct SEventGameStateChange
    {
        public int nOldState; // 老状态
        public int nNewState; // 新状态
    };
    // EVENT_LOGIN_STATE_CHANGE
    public struct SEventLoginStateChange_C
    {
        public int nOldState;       // 老状态
        public int nNewState;       // 新状态
    };
    public struct SExitScene
    {
        public UInt32 nMapId; // 地图ID
        public string strSceneName; // 场景名字
    }

    // 场景加载信息
    // EVENT_SCENE_LOAD_MESSAGE;
    public struct SEventSceneLoadMessage
    {
        public string strMessage;		// 加载信息
    };

    // 资源预加载进度（切场景）
    // EVENT_RESOURCE_PRELOAD_PROGRESS;
    public struct SEventResourcePreLoadProgress
    {
        public float nProgress;		    // 进度值
    };
    // 资源预加载事件（切场景）
    public struct SEventSenceLoadingData_C
    {
        public UInt32 nMapID;           // 地图ID
    };
    public struct SEventStageStartData_C
    {
        public bool bShowProgress;
    };
    public struct SEventSenceEndData_C
    {
        public UInt32 nMapID;           // 地图ID
        public Vector3 position;
    };

    public struct SEventEntityCreateEntity_C
    {
        public Int64 uidEntity;		// 实体uid
    };
    public class cmd_Base
    {
        public int nParam;
        public string strParam;
        private int mRefs = 0; // 引用计数

        public int GetRefCount()
        {
            return mRefs;
        }

        public int IncRef()
        {
            return ++mRefs;
        }

        public int DecRef()
        {
            if (--mRefs <= 0) mRefs = 0;
            return mRefs;
        }

        /// <summary>
        /// 重置数据（如果结构体对象会回收，结构对象中Object数据需要重置为null）
        /// </summary>
        /// <returns></returns>
        public virtual void ReSet()
        {
        }
    }

    //异步命令状态
    public enum AsyncCommandState
    {
        None = 0,
        //创建命令
        CreateCommmand,
        //只有界面被创建及显示才会刷新，否则跳过事件处理/
        UpdateCommand,
    }

    public class cmd_AsyncBase : cmd_Base
    {
        public WindowModel wModel;
        public AsyncCommandState aCommandState = AsyncCommandState.UpdateCommand;

        public Vector3 position = Vector3.zero;
        //3d节点父类
        public bool parent3DUI = false;
        //加载完成回调
        public WindowLoadCall loadCall = null;
    }

      public class ApperatusModel : cmd_Base
    {
        public bool Addclick;
    }
    /// <summary>
    /// 主角移动
    /// </summary>
    public struct SEventHeroMove
    {
        //移动目标
        public Vector3 vTragetPos;
        /// <summary>
        /// 移动角度
        /// </summary>
        public Vector3 vTragetEul;
        //是否动画
        public bool bAnimation;

        //动画时间
        public float fAnimationDuration;

        //移动后的回调
        public UnityAction CallBack;
    };
    #endregion


    // 客户端
    [StructLayout(LayoutKind.Sequential, Pack = 1,CharSet = CharSet.Unicode)]
    public struct SEventEntityDestroryEntity_C
    {
	    public Int64		uidEntity;		// 实体uid
    };

    // 消息体MSG_ACTION_JOYSTICK_3D_MOVE
    [StructLayout(LayoutKind.Sequential, Pack = 1, CharSet = CharSet.Unicode)]
    public struct SMsgActionJoystick3DMove_CS
    {
        public UInt32 dwTimeMark;       // 时间戳
        public UInt32 dwZoneID;         // 场景ID

        public float fPosx;             // 实体当前位置x
        public float fPosy;             // 实体当前位置y
        public float fPosz;             // 实体当前位置z
        public float fAngle;            // 和世界X轴正方向的角度(逆时针)
        public byte bMoveCmd;           // 移动命令类型（参照EMJoystickCmd枚举）		
    };
    //添加buff
    public struct SEventCreatureAddBuff_C
    {
        public UInt32 dwIndex;		// buff index
    };

    //移除buff
    public struct SEventCreatureRemoveBuff_C
    {
        public uint dwIndex;		// buff index
    };
    //生物死亡
    public struct SEventCreatureDie_C
    {
        public Int64 uidMurderer;	// 凶手
    };
    //场景退出
    // EVENT_QUIT_SCENE;
    public struct SEventSceneQuit
    {
        public UInt32 nMapId; // 地图ID
        public string strSceneName; // 场景名字
    };

    public struct SEventHHandChildAcetive
    {

        public bool isLeft;
        public string path;
        public bool isTrue;
    }
    public struct SEventHHandPose
    {
        public bool isLeft;
        public HHandPoseType handPoseType;
    }
}