﻿/// <summary>
/// ScoreManger
/// </summary>
/// <remarks>
/// 2023/2/15 13:43:50: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace GLib.Client
{
    public class ScoreManger : IScoreMgr,IEventExecuteSink
    {
        public string ModuleName { get ; set ; }
        public EMModuleLoadState ModuleLoadState { get ; set ; } 
        public float Progress { get ; set ; }

        private Dictionary<int, SingleScore> m_AllSingleScore = new Dictionary<int, SingleScore>();

        private SingleScore m_CurrSingleScore;

        public bool Create()
        {
            GHelp.GetEventEngine().Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_SCORE_UPDATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
            GHelp.GetEventEngine().Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_REC_FINALSCORE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");

            return true;
        }

        /// <summary>
        /// 课程ID
        /// </summary>
        /// <param name="ID"></param>
        public void InitScore(CourseData _CourseData)
        {
            if (m_AllSingleScore.ContainsKey(_CourseData.Id))
            {
                m_CurrSingleScore = m_AllSingleScore[_CourseData.Id];
                m_CurrSingleScore.Init(_CourseData);
                // m_CurrSingleScore.UpdateSelf();
            }
            else
            {
                SingleScore single = new SingleScore();
                single.Init(_CourseData);
                m_AllSingleScore.Add(_CourseData.Id, single);
                m_CurrSingleScore = single;
            }
        }
        /// <summary>
        /// 课程ID
        /// </summary>
        /// <param name="ID"></param>
        public void LoadScore(int patientId)
        {
            m_CurrSingleScore.LoadScoreData(patientId);
        }

        public SingleScore GetCurrentScore()
        {
            return m_CurrSingleScore;
        }

        #region 
        public void FixedUpdate()
        {
           
        }
        public void LateUpdate()
        {
            
        }
        public void Release()
        {
            GHelp.GetEventEngine().UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_SCORE_UPDATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            GHelp.GetEventEngine().UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_REC_FINALSCORE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);

        }
        public void Update()
        {
            
        }
        #endregion


        public void ScoreCalculation() 
        {
            
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (int)ViewLogicDef.EVENT_SCORE_UPDATE:
                    UpdateScoreDate updateScore = pContext as UpdateScoreDate;
                    m_CurrSingleScore.CalculateScore(updateScore.IsJumpStep,updateScore.AliasInt,updateScore.ConiditionDate,updateScore.IsSucced);
                    break;
                case (int)ViewLogicDef.EVENT_REC_FINALSCORE:
                    ReSetTotleScore();
                    break;
            }
        }

        public float GetTotleScore()
        {
            return m_CurrSingleScore.TotleScore;
        }

        public float GetTotlePoint()
        {
            return m_CurrSingleScore.TotalPoints;
        }
        public List<ScoreStepInfo> GetFinishDetail()
        {
            return m_CurrSingleScore.GetFinishDetail();
        }

        public List<ScoreStepInfo> GetFinishDetailShowScoreWindow()
        {
            return m_CurrSingleScore.GetFinishDetailShowScoreWindow();
        }
        public List<ScoreInfo> GetLineDetail()
        {
            return m_CurrSingleScore.GetLineFinish();
        }
        //GetLineFinishDetail
        public List<ScoreStepInfo> GetLineFinishDetail()
        {
            return m_CurrSingleScore.GetLineFinishDetail();
        }
        public Step GetProjectStep(int projectID)
        {
            return m_CurrSingleScore.GetProjectStep(projectID);
        }
        
        public Step GetProjectStepAll(int projectID)
        {
            return m_CurrSingleScore.GetProjectStepAll(projectID);
        }

        public string GetProjectStepByID(int ID)
        {
            return m_CurrSingleScore.GetProjectStepByID(ID);
        }

        public List<Step> GetProjectStepCount()
        {
            return m_CurrSingleScore.m_ProjectInfoAll;
        }
        public List<CreateTrainRoot> GetProjectTrainStep()
        {
            return m_CurrSingleScore.m_TrainRoot;
        }
        public List<Step> GetAllProjectStep() {
            return m_CurrSingleScore.m_ProjectInfo;
        }
            
        public List<Step> GetAllProjectStepShowScoreWindow() {
            return m_CurrSingleScore.m_ProjectInfoAll;
        }
        public int GetIsOrder(int projectID)
        {
            return m_CurrSingleScore.GetIsOrder(projectID);
        }

        public List<ScoreInfo> GetAddMockExamDatas()
        {
            return m_CurrSingleScore.GetAddMockExamDatas();
        }
        public List<ScoreInfo> GetAllScoreInfo()
        {
            return m_CurrSingleScore.GetAllScoreInfo();
        }

        public void SetCurrentScoreInit(CourseData courseID)
        {
            m_CurrSingleScore.Init(courseID);
        }

        public void ReSetTotleScore()
        {
            m_CurrSingleScore.TotleScore = 0;
        }
        /// <summary>
        /// 获取完成的得分点数量
        /// </summary>
        /// <returns></returns>
        public int GetFinishScoreCount()
        {
            return m_CurrSingleScore.GetFinfishCount();
        }

        /// <summary>
        /// 获取所有得分点的数量
        /// </summary>
        /// <returns></returns>
        public int GetTotalPointsNumber()
        {
            return m_CurrSingleScore.TotalPointsNumber;
        }

        public ScoreInfo GetScoreByID(string alis)
        {
            return m_CurrSingleScore.GetScoreInfoByID(alis);
        }
        /// <summary>
        /// 获取当前阶段下的数量
        /// </summary>
        /// <returns></returns>
        public int GetCurrentScoreStepIDNumber(int stepID)
        {
            return m_CurrSingleScore.GetScoreStepID(stepID);
        }


        public List<MistakeInfo> GetMistakeInfo()
        {
            return m_CurrSingleScore.GetMistake();
        }

        public void SetMisTakeInfo(List<MistakeInfo> tempMistake)
        {
            m_CurrSingleScore.SetMistake(tempMistake);
        }

        public Dictionary<int, int> GetUpdatDetail()
        {
            return m_CurrSingleScore.GetUpdateDetail();
        }

        /// <summary>
        /// 根据任务别名获取是否有分（后台配置的分数）
        /// </summary>
        /// <returns></returns>
        public float GetAliasAddScore(string alis)
        {
            var list = m_CurrSingleScore.GetAllScoreInfo();
            foreach (var item in list)
            {
                if (item.Alias.Equals(alis))
                {
                    return item.AddScore;
                }
            }
            return 0f;
        }
    }
}