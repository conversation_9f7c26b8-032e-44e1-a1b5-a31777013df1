﻿/// <summary>
/// Mast
/// </summary>
/// <remarks>
/// 2021.3.26: 创建. 谌安 <br/>
/// 传送门<br/>
/// </remarks>
using System;
using System.Runtime.InteropServices;
using System.Text;
using game.common;
using game.scene;
using game.schemes;
using Game.Entity;
using Google.Protobuf;
using GLib;
using GLib.Common;
using UnityEngine;
using static Game.Entity.Entity_CreateEntity.Types;

namespace GLib.Client.Entity
{

	public class CMast : IMast
    {
        // UID
	    Int64					m_uid;

        string                  m_guid;

	    // 名字
	    string					m_szName;
        //名字颜色
        string                  m_nameColor;

	    // 数值属性
	    int[]					m_nNumProp;

	    // 实体类型
	    CEntityClass			m_EntityClass;

	    // 信号槽
	    //CMessageSlot			m_MessageSlot;

        // 实体视图ID
        UInt32                  m_nEntityViewID;

        // 模型id
        int                     m_nSkinID;
        //实体工厂配置ID（服务器刷怪表ID）
        int m_entityFactoryConfigID = 0;

        Vector3 m_Position;
        // 实体类型
        EMEntityType m_entitiyType;
        //实体ID
        int m_configID = 0;
        /** 
        @param   
        @param   
        @return  
        */
        public CMast()
        {
	        // UID
	        m_uid =DGlobalGame.INVALID_UID;
            m_guid = "";
            // 名字
            m_szName ="";
            m_nEntityViewID = 0;
            
        }

        public void Init()
        {
            m_EntityClass = new CEntityClass();

            //m_MessageSlot = new  CMessageSlot(DGlobalMessage.MSG_ACTION_MAXID);

            // 数值属性
            m_nNumProp = new int[(int)eEntityProp.EEntityMax];
			for(int i = 0; i < m_nNumProp.Length; i++)
			{
				m_nNumProp[i] = 0;
			}
        }

        /** 释放,会释放内存
        @param   
        @param   
        @return  
        */
        public void Release()
        {
	        // 发送事件
	        /*SEventEntityDestroryEntity_C eventdestroryentity;
	        eventdestroryentity.uidEntity = m_uid;	
	        byte bSrcType = GetEventSourceType();
            GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY,
							            bSrcType,
                                        (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
							            eventdestroryentity);*/

	        // 从实体世界中移除
            ((CEntityClient)GHelp.GetEntityClient()).Remove(this);

	        // 从场景中移除掉
	        if(m_nEntityViewID!= 0)
	        {
                EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
                Destroy.ENTITY_ID =GetEntityViewID();
                Destroy.ENTITY_UID = m_uid;

                // 移除实体视图
                GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);
                
                m_nEntityViewID = 0;
	        }

	        // 消息槽
	        //m_MessageSlot.Close();
		}

		/// <summary>
		/// 还原,不释放对象，只将状态还原到创建时状态
		/// </summary>
		public void Restore()
		{
			TRACE.ErrorLn("CMast::Restore 未实现");
		}

        /** 创建
        @param   
        @param   
        @return  
        */
        public bool Create()
        {
	        // 实体类型
	        m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Mast);

            // 创建实体视图
            bool isShowName = false;

            // 信号槽
            //m_MessageSlot.Init(10);

            // 创建实体视图
            if (!CreateView())
			{
				return false;
			}
				
			// 添加到实体世界中
			if (!((CEntityClient)GHelp.GetEntityClient()).Add(this))
			{
				return false;
			}

         
           
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory != null)
			{
				pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagSelectable);// | (uint)EntityFlags.flagNoShadow
                if (isShowName)
                {
                    pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName);
                }
            }
           

            return true;
        }

        /** 取得实体类型
        @param   
        @param   
        @return  
        */
        public IEntityClass  GetEntityClass()
        {
	        return m_EntityClass;
        }

        public EMEntityType GetEntityType()
        {
            return m_entitiyType;
        }

        /** 取得UID
        @param   
        @param   
        @return  
        */
        public Int64 GetUID()
        {
	        return m_uid;
        }

        public string GetStrGUID()
        {
            return m_guid;
        }

		/// <summary>
		/// 获取3D坐标
		/// </summary>
		/// <returns></returns>
		public Vector3 GetPosition()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
            Vector3 pos = pEntityFactory.GetPosition(GetEntityViewID());
			return pos;
		}

		/// <summary>
		/// 设置3D坐标
		/// </summary>
		public void SetPosition(Vector3 vPos)
		{
		}

        /// <summary>
        /// 获取是否正在跳跃
        /// </summary>
        /// <returns></returns>
        public EntityJumpDef GetJumpState()
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return EntityJumpDef.None;
            EntityJumpDef isJump = pEntityFactory.GetJumpState(m_nEntityViewID);
            return isJump;
        }

        /// <summary>
		/// 设置跳跃状态
		/// </summary>
		/// <param name="state"></param>
		/// <returns></returns>
		public bool SetJumpState(EntityJumpDef state)
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return false;
            pEntityFactory.SetJumpState(m_nEntityViewID, state);
            return true;
        }

        /// <summary>
        /// 获取朝向
        /// </summary>
        /// <returns></returns>
        public Vector3 GetForward()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
            Vector3 forward = pEntityFactory.GetForward(GetEntityViewID());
			return forward;
		}

		/// <summary>
		/// 设置朝向
		/// </summary>
		/// <param name="vForward"></param>
		public void SetForward(Vector3 vForward)
		{
		}

		/// <summary>
		/// 获取移动速度
		/// </summary>
		/// <returns></returns>
		public float GetMoveSpeed()
		{
            return GetNumProp((uint)eEntityProp.EEntityMoveSpeed) / DGlobalGame.FLOAT_SCALE_SIZE;
        }

        /** 设置数值属性
        @param   nValue ：属性值
        @param   
        @return  
        */
        public bool SetNumProp(uint dwPropID, int nValue)
        {
	       /* if(dwPropID < 0 || dwPropID >= (uint)EMMAST_PROP.MAST_PROP_BROADCAST)
	        {
		        return false;
	        }

	        m_nNumProp[dwPropID] = nValue;*/

	        return true;
        }

        /** 取得数值属性
        @param   
        @param   
        @return  
        */
        public int GetNumProp(uint dwPropID)
        {
            /*if(dwPropID < 0 || dwPropID >= (uint)EMMAST_PROP.MAST_PROP_BROADCAST)
	        {
		        return 0;
	        }*/

            return 0;// m_nNumProp[dwPropID];
        }

        /** 设置字符属性
        @param   pszValue ：属性值
        @param   
        @return  
        */
        public bool SetStrProp(uint dwPropID, string pszValue)
        {
            int _value = 0;
            GHelp.ConvertEntityPropInt((eEntityProp)dwPropID, pszValue, ref _value);
            m_nNumProp[dwPropID] = _value;
            return true;
        }
        public int GetEntityFactoryConfigID()
        {
            return m_entityFactoryConfigID;
        }
        /** 批量更新属性
        @param   
        @param   
        @return  
        */
        public bool BatchUpdateProp(IMessage pszProp, int nLen)
        {

            int nInLen = nLen;
            EntityInfo other = pszProp as EntityInfo;
            m_uid = Api.GuidCInt(other.Guid);
            m_Position = new UnityEngine.Vector3(other.Position.X, other.Position.Y, other.Position.Z);
            m_szName = other.Name;
            m_entitiyType = (EMEntityType)other.EntityType;
            m_configID = other.ConfigId;
            m_guid = other.Guid;
            m_entityFactoryConfigID = other.EntityFactoryConfigID;
            foreach (ProItem item in other.Props)
            {
                SetStrProp((uint)item.PropType, item.PropValue);
            }
            return true;
        }

        /** 消息
        @param   
        @param   
        @return  true：正常执行；false：被否决  
        */
        public bool OnMessage(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
            return true;// m_MessageSlot.Fire(dwMsgID, pGameMsgHead, pszMsg);
        }

        /** 订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageVoteSink  pVoteSink, string  pszDesc)
        {
            return true;// m_MessageSlot.Subscibe(dwMsgID, pVoteSink, pszDesc);
        }

        /** 取消订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageVoteSink  pVoteSink)
        {
            return true;// m_MessageSlot.UnSubscibe(dwMsgID, pVoteSink);
        }

        /** 订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink, string pszDesc)
        {
            return true;// m_MessageSlot.Subscibe(dwMsgID, pExecuteSink, pszDesc);
        }

        /** 取消订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink)
        {
            return true;// m_MessageSlot.UnSubscibe(dwMsgID, pExecuteSink);
        }

        /** 取得名字
        @param   
        @param   
        @return  
        */
        public string GetName()
        {
	        return m_szName;
        }

        /** 取得实体视图ID接口
        @param   
        @param   
        @return  
        */
        public UInt32 GetEntityViewID()
        {
	        return m_nEntityViewID;
        }

        /** 取得事件源类型,SOURCE_TYPE_PERSON, SOURCE_TYPE_MONSTER ... ...
        @param   
        @param   
        @return  
        */
        public byte GetEventSourceType()
        {
	        return (byte)EMSOURCE_TYPE.SOURCE_TYPE_MAST;
        }

    
        /** 属性是否变化
        @param   
        @param   
        @return  
        */
        public bool IsNumPropChanged(uint dwPropID)
        {
	        return false;
        }


        /** 属性是否变化处理结束,清理标识
        @param   
        @param   
        @return  
        */
        public  bool ClsNumPropChanged()
        {
	        return false;
        }

        // 创建显示层实体
        public bool CreateView()
        {
            EntityViewItem item = GHelp.GetObjectItemEx<EntityViewItem>();
            if (!GetBasicViewInfo(ref item))
            {
                TRACE.ErrorLn("Mast::CreateView getBasicViewInfo failed!");
                return false;
            }

            // 创建EntityView
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, 0, "", item);
            m_nEntityViewID = item.EntityViewID;
            if (m_nEntityViewID == 0)
            {
				TRACE.ErrorLn("Mast::CreateView invalid viewID= 0 uid=" + m_uid);
                return false;
            }

            // 设置出生坐标
            cmd_creature_rigidbody_sync data = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
            data.bNotGround = false;
            data.nEntityID = GetEntityViewID();
            data.fPosition_x = m_Position.x;
            data.fPosition_y = m_Position.y;
            data.fPosition_z = m_Position.z;
            GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", data);

            // 加载皮肤模型
            GHelp.ChangeEntitiyPart(GetEntityViewID(), EntityParts.EntityPart_Body, item.nSkinID);

            //TRACE.TraceLn("createView id=" + m_uid);
            GHelp.RecycleObjectItemEx<EntityViewItem>(item);
            return true;
        }

        public bool GetBasicViewInfo(ref EntityViewItem item)
        {
            Model.Types.Item modelItem = GHelp.GetModelItem(m_entitiyType, m_configID);
            item.EntityType = (byte)EMEntityType.typePortal;
            item.EntityViewID = 0;
            item.UID = m_uid;
            item.byIsHero = 0;
            //item.prefabPath = GHelp.GetModelPathByType(m_entitiyType, m_entitiyID);
            item.szName = m_szName;
            m_nSkinID = item.nSkinID = modelItem.Id;
            item.ConfigID = m_configID;
            item.nameColor = m_nameColor;
            item.Angle = GetNumProp((int)eEntityProp.EEntityAngle);
            item.fMoveSpeed = GetMoveSpeed();
            item.EntityFactoryConifgID = m_entityFactoryConfigID;
            return true;
        }

        public IMessage GetConfigInfo()
        {
            return GHelp.GetConfigItem(m_entitiyType, m_configID);
        }

        public int GetModelID()
        {
            return m_nSkinID;
        }

        public int GetConfigID()
        {
            return m_configID;
        }

        public void sendCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
        {
            
        }

		/// <summary>
		/// 设置实体称号
		/// </summary>
		/// <param name="nIndex">TitleIndexType 称号位置</param>
		/// <param name="nEffectID">ETitleEffect 格式化效果ID,	0表示无,可在szTitleName中字定义格式化字串</param>
		/// <param name="szTitleName">称号名字</param>
		/// <returns></returns>
		public bool SetEntityTitle(int nIndex, int nEffectID, string szTitleName, bool bUpdateView = true)
		{
			return false;
		}
    }
}
