﻿/// <summary>
/// CMessageSlot
/// </summary>
/// <remarks>
/// 2021.5.18: 创建. 谌安 <br/>
/// 信号槽<br/>
/// </remarks>
using System.Collections.Generic;
using GLib.Common;
using GLib;
using System.Runtime.InteropServices;
using System;

namespace GLib.Client
{

    public class CMessageSlot
    {
        ///////////////////////////////////////////////////////////////////
	    // 一个投票订阅sink信息
	    public class SVoteSinkInfo
	    {
		    // 否决sink
            public IMessageVoteSink pSink;

		    // 订阅描述
            public string szDesc;
            public SVoteSinkInfo(IMessageVoteSink pVoteSink, string pszDesc)
		    {
			    // 否决sink
			    pSink = pVoteSink;

			    // 订阅描述
			    szDesc=pszDesc;
		    }
	    };

        // 所有消息的所有投票订阅sink
	    public class SVoteSinkList
	    {
		    // 投票订阅者sink
		    public List< SVoteSinkInfo >		listVoteSink;

		    // 是否在轮询过程中，有删除节点发生
            public bool bRemoveFlag;

		    public int ByteSize()
		    {
                SVoteSinkInfo info=new SVoteSinkInfo(null,"");
                return listVoteSink.Count * Marshal.SizeOf(info) + sizeof(bool);
		    }
	    };

        ///////////////////////////////////////////////////////////////////
	    // 一个执行订阅sink信息
	    public class SExecuteSinkInfo
	    {
		    // 执行sink
		    public IMessageExecuteSink 			pSink;

		    // 订阅描述
		    public string						szDescs;

		    // 不允许循环发送消息，比如发送A消息，回调还未返回，又发送A送消息
		    public int							nEndFlag;
            public SExecuteSinkInfo(IMessageExecuteSink  pExecuteSink, string pszDesc)
		    {
			    // 执行sink
			    pSink = pExecuteSink;

			    // 订阅描述
			    szDescs= pszDesc;

                nEndFlag = 0;
		    }

            public void Clean()
		    {
                pSink = null;
                szDescs = "";
                nEndFlag = 0;
		    }
		
	    };

		// 最在消息码
		int					m_nMaxMsgID = 0;
        // 消息码映射表
	    int[]					m_nMsgIDSinkTable;

	    // 投票订阅sink
         Dictionary <int,SVoteSinkList> m_vectorVoteSink;

	    // 否决订阅sink
         Dictionary<int, SExecuteSinkInfo> m_vectorExecuteSink;

	    // vector最大开放
	    int					    m_nVectorOpenSize;

	    // 当前vector分配到何个位置了
	    int					    m_nVectorCurAllot;

        /** 
        @param   
        @param   
        @return  
        */
        public CMessageSlot(int nMaxMsgID)
        {
			m_nMaxMsgID = nMaxMsgID;
            // vector最大开放
	        m_nVectorOpenSize = 0;
	        // 当前vector分配到何个位置了
	        m_nVectorCurAllot = 0;
			// 消息码映射表
			m_nMsgIDSinkTable = new int[m_nMaxMsgID];
			for (int i = 0; i < m_nMsgIDSinkTable.Length; i++)
			{
				m_nMsgIDSinkTable[i] = -1;

			}

			// 投票订阅sink
			m_vectorVoteSink = new Dictionary<int, SVoteSinkList>();

			// 否决订阅sink
			m_vectorExecuteSink = new Dictionary<int, SExecuteSinkInfo>();
        }

        /** 还原,不释放内存，只将状态还原到创建时状态
        @param   未来继续使用
        @param   
        @return  
        */
        public void Close()
        {
	        // 消息码映射表
            for (int i = 0; i < m_nMsgIDSinkTable.Length; i++)
            {
                m_nMsgIDSinkTable[i] = -1;

            }

	        // 投票订阅sink
	        m_vectorVoteSink.Clear();

	        // 否决订阅sink
            m_vectorExecuteSink.Clear();

	        // vector最大开放
	        m_nVectorOpenSize = 0;

	        // 当前vector分配到何个位置了
	        m_nVectorCurAllot = 0;
        }

     
        /** 初始化
        @param   
        @param   
        @return  
        */
        public void Init(int dwInitOpenSize)
        {
	        // vector最大开放
	        m_nVectorOpenSize = dwInitOpenSize;

	        // 设置投票订阅sink vector大小
	        //m_vectorVoteSink.resize(m_nVectorOpenSize);

	        // 设置否决订阅sink vector大小
	        //m_vectorExecuteSink.resize(m_nVectorOpenSize);
	        //PP_BY_NAME_STOP();
        }

        /** 订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageVoteSink  pVoteSink, string pszDesc)
        {
	        if(dwMsgID < 0 || dwMsgID >= m_nMaxMsgID || pVoteSink == null || pszDesc == null)
	        {
		        return false;
	        }

	        int nThisLoc = m_nMsgIDSinkTable[dwMsgID];

			bool bAdd = false;

			//判断是否存在，不存在就进行相关的初始化(fixed by daiyueqiang)
			if (!m_vectorVoteSink.ContainsKey(nThisLoc))
			{
				// 压入	
				SVoteSinkInfo votesinkinfo = new SVoteSinkInfo(pVoteSink, pszDesc);
				SVoteSinkList votesinklist = new SVoteSinkList();
				votesinklist.listVoteSink = new List<SVoteSinkInfo>();

				votesinklist.listVoteSink.Add(votesinkinfo);
				m_vectorVoteSink.Add(nThisLoc, votesinklist);
				bAdd = true;
			}

	        if(nThisLoc == -1)
	        {
		        m_nVectorCurAllot++;
		        if(m_nVectorCurAllot >= m_nVectorOpenSize)
		        {
			        ResizeVector();
		        }

		        m_nMsgIDSinkTable[dwMsgID] = m_nVectorCurAllot;
		        nThisLoc = m_nVectorCurAllot;
	        }
			else if (!bAdd)
	        {
		        // 查找是否重复订阅
		        SVoteSinkList  pVoteSinkList = m_vectorVoteSink[nThisLoc];
		        List<SVoteSinkInfo> plistVoteSink  = pVoteSinkList.listVoteSink;
		        if(plistVoteSink != null)
		        {
			        for( int i=0;i<plistVoteSink.Count;i++)
			        {
				        SVoteSinkInfo  pVoteSinkInfo =plistVoteSink[i];
				        if(pVoteSinkInfo.pSink == pVoteSink)
				        {
					        return false;
				        }
			        }

			        SVoteSinkInfo votesinkinfo=new SVoteSinkInfo(pVoteSink, pszDesc);
			        plistVoteSink.Add(votesinkinfo);			
		        }
	        }	

	        return true;
        }

        /** 取消订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageVoteSink pVoteSink)
        {
            if (dwMsgID < 0 || dwMsgID >= m_nMaxMsgID || pVoteSink == null)
	        {
		        return false;
	        }

	        int nThisLoc = m_nMsgIDSinkTable[dwMsgID];
	        if(nThisLoc == -1)
	        {
		        return true;
	        }

			if (!m_vectorVoteSink.ContainsKey(nThisLoc))
			{
				//TRACE.ErrorLn("CMessageSlot::UnSubscibe 位置对应的SVoteSinkList为空 dwMsgID=" + dwMsgID);
				return false;
			}
				

	        SVoteSinkList  pVoteSinkList = m_vectorVoteSink[nThisLoc];
            List<SVoteSinkInfo> plistVoteSink = pVoteSinkList.listVoteSink;
	        if(plistVoteSink != null)
	        {
                for (int i = 0; i < plistVoteSink.Count; i++)
		        {
                    SVoteSinkInfo pVoteSinkInfo = plistVoteSink[i];
			        if(pVoteSinkInfo.pSink == pVoteSink)
			        {
						plistVoteSink.RemoveAt(i);
                        //pVoteSinkList.bRemoveFlag = true;
						if (plistVoteSink.Count == 0)
						{
							m_vectorVoteSink.Remove(nThisLoc);

							// 如果执行队列也为空
							if(!m_vectorExecuteSink.ContainsKey(nThisLoc))
							{
								m_nMsgIDSinkTable[dwMsgID] = -1;
							}
						}
				        return true;
			        }
		        }
	        }

	        return true;
        }

        /** 订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink, string pszDesc)
        {
	        if(dwMsgID < 0 || dwMsgID >= m_nMaxMsgID || pExecuteSink == null || pszDesc == null)
	        {
		        return false;
	        }

	        int nThisLoc = m_nMsgIDSinkTable[dwMsgID];
	        if(nThisLoc == -1)
	        {
		        m_nVectorCurAllot++;
		        if(m_nVectorCurAllot >= m_nVectorOpenSize)
		        {
			        ResizeVector();
		        }

		        m_nMsgIDSinkTable[dwMsgID] = m_nVectorCurAllot;
		        nThisLoc = m_nVectorCurAllot;

		        // 压入
		        SExecuteSinkInfo executesinkinfo=new SExecuteSinkInfo(pExecuteSink, pszDesc);
                m_vectorExecuteSink.Add(nThisLoc,executesinkinfo);
	        }
	        else
	        {
		        // 查找是否重复订阅
				if (!m_vectorExecuteSink.ContainsKey(nThisLoc))
				{
					m_vectorExecuteSink.Add(nThisLoc, new SExecuteSinkInfo(pExecuteSink, pszDesc));
				}
				else
				{
					SExecuteSinkInfo info = m_vectorExecuteSink[nThisLoc];
					TRACE.WarningLn("CMessageSlot::Subscibe 重复订阅执行消息，消息码 = " + dwMsgID + ", 描述 = " + pszDesc + ",原始描述=" + info.szDescs);
					return false;
				}

	        }	

	        return true;
        }

        /** 取消订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageExecuteSink pExecuteSink)
        {
            if (dwMsgID < 0 || dwMsgID >= m_nMaxMsgID || pExecuteSink == null)
	        {
		        return false;
	        }

	        int nThisLoc = m_nMsgIDSinkTable[dwMsgID];
	        if(nThisLoc == -1)
	        {
		        return true;
	        }

			SExecuteSinkInfo pExecuteSinkInfo = null;
	        // 查找是否重复订阅
            if(! m_vectorExecuteSink.TryGetValue(nThisLoc,out pExecuteSinkInfo))
            {
                return true;
            }

	        if(pExecuteSinkInfo.pSink != null)
	        {
				m_vectorExecuteSink.Remove(nThisLoc);

				//如果投票列表也为空就把m_nMsgIDSinkTable[dwMsgID] = -1
				if(!m_vectorVoteSink.ContainsKey(nThisLoc))
				{
					m_nMsgIDSinkTable[dwMsgID] = -1;
				}
	        }
            else
            {
                TRACE.ErrorLn(String.Format("MessageSlot:UnSubscibe_{0}", nThisLoc));
            }

	        return true;
        }

        /** 分发消息
        @param   
        @param   
        @return true：正常执行；false：被否决
        */
        public bool Fire(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
	        if(dwMsgID < 0 || dwMsgID >= m_nMaxMsgID)
	        {
		        return false;
	        }

	        // 如果没人订阅投票或者执行消息，直接返回
	        int nThisLoc = m_nMsgIDSinkTable[dwMsgID];
	        if(nThisLoc == -1)
	        {
		        return true;
	        }

            SExecuteSinkInfo pExecuteSinkInfo = null;
            
            // 如果同样的消息ID还没结束，不允许再次发同样的消息
            if (m_vectorExecuteSink.ContainsKey(nThisLoc))
            {
                pExecuteSinkInfo = m_vectorExecuteSink[nThisLoc];
                if (pExecuteSinkInfo == null || pExecuteSinkInfo.nEndFlag == 1)
                {
					//警告不会引起大家的注意，所有改成错误级别
					if (pExecuteSinkInfo == null)
					{
						TRACE.ErrorLn("CMessageSlot::Fire 本次消息尚未轮询完毕，不能再发同样消息，pExecuteSinkInfo == null 消息码 = " + dwMsgID);
					}
					else
					{
						TRACE.ErrorLn("CMessageSlot::Fire 本次消息尚未轮询完毕，不能再发同样消息，pExecuteSinkInfo.nEndFlag == 1 消息码 = " + dwMsgID);
					}

                    return false;
                }
                pExecuteSinkInfo.nEndFlag = 1;
            }
			else
			{
				return false;
			}

			SVoteSinkList pVoteSinkList = null;
	        // 先发送投票消息
            if(m_vectorVoteSink.TryGetValue(nThisLoc,out pVoteSinkList))
	        {
		        // 置在轮询过程中，有删除节点发生
		        pVoteSinkList.bRemoveFlag = false;

		        // 轮询
		        bool bResult = true;
		        bool bOldTimeRemove = false; // 有一些节点并不是轮询过程删除的，而是轮询前删除的
		        for( int i=0; i<pVoteSinkList.listVoteSink.Count; i++)
		        {
			        if((pVoteSinkList.listVoteSink[i]).pSink != null)
			        {
				        try
				        {
                            long nPos=pszMsg.GetPosition();

					        if(!(pVoteSinkList.listVoteSink[i]).pSink.OnVote(dwMsgID, pGameMsgHead, pszMsg))
					        {
						        bResult = false;

						        break;
					        }

                            pszMsg.SetPosition(nPos);
				        }
				        catch (Exception ex)
				        {
							pExecuteSinkInfo.nEndFlag = 0;
					        TRACE.ErrorLn("发送投票消息非法，消息码 = "+ dwMsgID+", 描述 = " + pVoteSinkList.listVoteSink[i].szDesc+",ex="+ex.Message);
								
					        return false;					
				        }				
			        }
			        else
			        {
				        bOldTimeRemove = true;
			        }
		        }

                
		        // 如果在轮询过程中，有删节点发生
		        if(pVoteSinkList.bRemoveFlag || bOldTimeRemove)
		        {
					List<SVoteSinkInfo> TempInfo = new List<SVoteSinkInfo>();

                    for (int Count = 0; Count < pVoteSinkList.listVoteSink.Count; Count++)
			        {
				        if(pVoteSinkList.listVoteSink[Count].pSink == null)
				        {
                            TempInfo.Add(pVoteSinkList.listVoteSink[Count]);
				        }	
				        else
				        {
                            Count++;
				        }
			        }

			        pVoteSinkList.bRemoveFlag = false;

					//删节点
					for (int i = 0; i < TempInfo.Count; i++)
					{
						pVoteSinkList.listVoteSink.Remove(TempInfo[i]);
					}
		        }

                // 如果有否决sink要截止
                if (!bResult)
                {
                    // 轮询结束
					pExecuteSinkInfo.nEndFlag = 0;
                    
                    return false;
                }
	        }

	        // 发送执行消息
            if (pExecuteSinkInfo.pSink != null)
	        {
		        try
		        {
			        pExecuteSinkInfo.pSink.OnExecute(dwMsgID, pGameMsgHead, pszMsg);
		        }
		        catch (Exception ex)
		        {
			        pExecuteSinkInfo.nEndFlag = 0;

			        TRACE.ErrorLn("发送执行消息非法，消息码 = "+dwMsgID+", 描述 = "+pExecuteSinkInfo.szDescs);
					TRACE.ErrorLn("异常信息:" + ex.ToString());

			        return true;					
		        }		
	        }

	        // 轮询结束
			pExecuteSinkInfo.nEndFlag = 0;
	        
	        return true;
        }

        /** 重设vector大小
        @param   
        @param   
        @return  
        */
        public void ResizeVector()
        {
	        m_nVectorOpenSize += 20;
	        if(m_nVectorOpenSize >=m_nMaxMsgID)
	        {
                m_nVectorOpenSize = m_nMaxMsgID;
	        }

	        // 设置投票订阅sink vector大小
	        //m_vectorVoteSink.resize(m_nVectorOpenSize);

	        // 设置否决订阅sink vector大小
	        //m_vectorExecuteSink.resize(m_nVectorOpenSize);
        }

        public int ByteSize()
        {
            int bytes = Marshal.SizeOf(m_nMsgIDSinkTable);		

	        if (m_vectorVoteSink.Count > 0)
	        {
		        bytes += m_vectorVoteSink[0].ByteSize() * m_vectorVoteSink.Count;
	        }

            SExecuteSinkInfo info = new SExecuteSinkInfo(null,"");

            bytes += m_vectorExecuteSink.Count * Marshal.SizeOf(info);

	        bytes += sizeof(int) * 2;

	        return bytes;
        }
    }
}
