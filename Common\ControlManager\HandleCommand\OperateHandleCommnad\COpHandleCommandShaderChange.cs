﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandShaderChange : IHandleCommand
    {
        const float STATE_MIN = 0.0001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private Material material;
        private string m_key;
        private float m_orgNum;
        private float m_targetNum;
        private float m_speed;
        private string m_shaderName;

        private float m_curNum;
        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;

        bool isAdd = true;
        public COpHandleCommandShaderChange(SOpHandleCommand_ShaderChange data)
        {
            if (data.target != null)
            {
                var mats = data.target.GetComponent<Renderer>().materials;
                if (string.IsNullOrEmpty(data.m_shaderName))
                    material = mats[0];
                else
                {
                    foreach (var mat in mats)
                    {
                        if (mat.name == data.m_shaderName || mat.name.Contains(data.m_shaderName))
                        {
                            material = mat;
                            break;
                        }
                    }
                }
            }
            runInstance = data.runInstance;
            m_key = data.m_shaderKey;
            m_orgNum = data.m_orgNum;
            m_targetNum = data.m_targetNum;
            m_speed = data.m_speed;
            m_isPlay = false;
            m_others = data.otherCommand;
            m_shaderName = data.m_shaderName;
            isAdd = m_targetNum > m_orgNum;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpShaderChange;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (material == null)
                {
                    Renderer t = OperateHandleCommandHelp.FindTarget<Renderer>(runInstance);
                    if (t != null)
                    {
                        material = t.sharedMaterial;
                    }
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                if(material == null)
                {
                    m_isEnd = true;
                    return false;
                }
                material.SetFloat(m_key, m_orgNum);
                m_curNum = m_orgNum;
            }
            float oneStepAdd = Math.Abs(Time.deltaTime * m_speed);
            if (isAdd)
            {
                m_curNum += oneStepAdd;
            }
            else
            {
                m_curNum -= oneStepAdd;
            }
            material.SetFloat(m_key, m_curNum);
            if (isAdd)
            {
                if (m_curNum >= m_targetNum)
                {
                    return true;
                }
            }
            else
            {
                if (m_curNum <= m_targetNum)
                {
                    return true;
                }
            }
            return false;
        }

        public void update()
        {
        }
    }
}
