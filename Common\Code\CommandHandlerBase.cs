﻿using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using GLib;
using GLib.Common;


public abstract class CommandHandlerBase<T> : MonoBehaviourEX<T>, IEventExecuteSink where T : MonoBehaviour
{
    public struct HandlerInfo
    {
        public byte srcType;
        public Delegate Handler;
        public Type type;
    }

    public Dictionary<ushort, HandlerInfo> commandHandlers;


    public override void Awake ()
    {
        base.Awake ();
        commandHandlers = new Dictionary<ushort, HandlerInfo>();
    }

    public void OnDestroy()
    {
        foreach (var item in commandHandlers) {
            GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)item.Key, item.Value.srcType, 0);
        }

        commandHandlers.Clear();
    }

    /// <summary>
    /// 注册监听事件
    /// </summary>
    /// <param name="wEventID">事件ID</param>
    /// <param name="bSrcType">发送源类型 EMSOURCE_TYPE</param>
    /// <param name="handler"></param>
    public void RegHandler(ushort wEventID, byte bSrcType, Delegate handler, int nMaxObjectCacheAmount = 0)
    {
        if (commandHandlers.ContainsKey(wEventID))
        {
            TRACE.ErrorLn("RegHandler failed! already exists. wEventID = " + wEventID);
        }
        commandHandlers.Add(wEventID, new HandlerInfo { srcType = bSrcType, Handler = handler, type = null});

        GlobalGame.Instance.EventEngine.Subscibe(this, wEventID, bSrcType, 0, "");
    }

    /// <summary>
    /// 注册监听事件(支持修改缓存数量)
    /// </summary>
    /// <typeparam name="TItem">上下文结构</typeparam>
    /// <param name="wEventID">事件ID</param>
    /// <param name="bSrcType">发送源类型 EMSOURCE_TYPE</param>
    /// <param name="handler"></param>
    /// <param name="nMaxObjectCacheAmount">缓存最大数量</param>
    public void RegHandler<TItem>(ushort wEventID, byte bSrcType, Delegate handler, int nMaxObjectCacheAmount = 0) where TItem : cmd_Base
    {
        if (commandHandlers.ContainsKey(wEventID))
        {
            TRACE.ErrorLn("RegHandler failed! already exists. wEventID = " + wEventID);
        }
        GSpawnPool.Instance.SetObjectItemMaxAmount<TItem>(nMaxObjectCacheAmount);
        commandHandlers.Add(wEventID, new HandlerInfo { srcType = bSrcType, Handler = handler, type = typeof(TItem) });        
        GlobalGame.Instance.EventEngine.Subscibe(this, wEventID, bSrcType, 0, "");
    }

    /// <summary>
    /// 注册监听事件(UI)
    /// </summary>
    /// <param name="wEventID">事件ID</param>
    /// <param name="bSrcType">发送源类型 EMSOURCE_TYPE</param>
    /// <param name="handler"></param>
    public void RegHandlerUI(ushort wEventID, byte bSrcType, Delegate handler, int nMaxObjectCacheAmount = 0)
    {
        if (commandHandlers.ContainsKey(wEventID))
        {
            TRACE.ErrorLn("RegHandlerUI failed! already exists. wEventID = " + wEventID);
        }

        commandHandlers.Add(wEventID, new HandlerInfo { srcType = bSrcType, Handler = handler, type = null });
        GlobalGame.Instance.EventEngine.Subscibe(this, wEventID, bSrcType, 0, "");
    }

    /// <summary>
    /// 注册监听事件(UI)(支持修改缓存数量)
    /// </summary>
    /// <typeparam name="TItem">上下文结构</typeparam>
    /// <param name="wEventID">事件ID</param>
    /// <param name="bSrcType">发送源类型 EMSOURCE_TYPE</param>
    /// <param name="handler"></param>
    /// <param name="nAmount">缓存最大数量</param>
    public void RegHandlerUI<TItem>(ushort wEventID, byte bSrcType, Delegate handler, int nMaxObjectCacheAmount = 0) where TItem : cmd_Base
    {
        if (commandHandlers.ContainsKey(wEventID))
        {
            TRACE.ErrorLn("RegHandlerUI failed! already exists. wEventID = " + wEventID);
        }

        GSpawnPool.Instance.SetObjectItemMaxAmount<TItem>(0);
        commandHandlers.Add(wEventID, new HandlerInfo { srcType = bSrcType, Handler = handler, type = null });

        GlobalGame.Instance.EventEngine.Subscibe(this, wEventID, bSrcType, 0, "");
    }

    public void UnRegHandler(ushort wEventID, byte bSrcType)
    {
        commandHandlers.Remove (wEventID);
        GlobalGame.Instance.EventEngine.UnSubscibe(this, wEventID, bSrcType, 0);
    }

    public void OnExecute (ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
    {      
        if (!commandHandlers.ContainsKey(wEventID)) 
        {
            EntityLogicDef enumDisplayStatus = (EntityLogicDef)wEventID;
            TRACE.TraceLn("Hander not reg for Event:" + enumDisplayStatus.ToString());
            return;
        }

        if (pContext == null)
        {
            EntityLogicDef enumDisplayStatus = (EntityLogicDef)wEventID;
            TRACE.ErrorLn("CommandHandlerBase OnExecute failed! pContext == null. wEventID = " + enumDisplayStatus.ToString()); // 打印调试信息
            return;
        }

        if (pContext is cmd_Base == false)
        {
            EntityLogicDef enumDisplayStatus = (EntityLogicDef)wEventID;
            TRACE.ErrorLn("CommandHandlerBase OnExecute failed! pContext is cmd_Base == false." + enumDisplayStatus.ToString());
            return;
        }
        cmd_Base info = (cmd_Base)pContext;

        // 引用计数加1
        info.IncRef();

        // 执行回调,执行成功引用计数减1
        if( onCommand(wEventID, info) )
        {
            RecycleObjectToCaches(wEventID, info);
        }
    }

    public void RecycleObjectToCaches(ushort wEventID, cmd_Base info)
    {
        // 引用计数减1
        info.DecRef();

        // 将对象回收到缓冲池
        if (info.GetRefCount() <= 0 && commandHandlers[wEventID].type != null)
        {
            //TRACE.TraceLn("--------Rec:" + info.GetType().ToString());
			/*
            if (Api.s_Dict.ContainsKey(info.GetType()))
            {
                int val = 0;
                Api.s_Dict.TryGetValue(info.GetType(), out val);
                val = val - 1;
                Api.s_Dict[info.GetType()] = val;    
            }
			*/
            //foreach (KeyValuePair<Type, int> pair in Api.s_Dict)
            //{
            //    TRACE.ErrorLn("Type: " + pair.Key.ToString() + "  Val: = " + pair.Value);
            //}
            info.ReSet();
            GSpawnPool.Instance.RecycleObjectItemToCaches(info.GetType(), info);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="wEventID"></param>
    /// <param name="info"></param>
    /// <returns>命令回调执行完成返回true，其他的情况返回false</returns>
    public abstract bool onCommand(ushort wEventID, cmd_Base info);

}

