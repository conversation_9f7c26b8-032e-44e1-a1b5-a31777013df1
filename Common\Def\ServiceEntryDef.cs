﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
/// <summary>
/// ServiceEntryDef
/// </summary>
/// <remarks>
/// 2021.10.21: 创建. 王康阳 <br/>
/// IP地址类
/// </remarks>
namespace GLib.Common
{
    [Serializable]
    public class ServiceEntryDef
    {
        public string serverWebUrl;
        public string vmServerWebUrl;
        public string sceneServerNodeName;
        public string vmServerNodeName;
        public string gateway_HostAddress;
        public int gateway_Port;
        public Properties properties;
    }

    [Serializable]
    public class Properties
    {
        public string fileServerWebUrl;
    }

    [Serializable]
    public class Navigations
    {
        public List<MineList> mys;
        public bool userIsLogin;
        public string auditVersion;
        public Dictionary<string, object> properties = new Dictionary<string, object>();
        public ServiceEntryDef serverEntry;
        public Dictionary<string, string> WebApiUrls = new Dictionary<string, string>();
        public string DefaultWebUrl;
    }
    [Serializable]
    public class MineList
    {
        public int sort;
        public int classlyId;
        public string iconId;
        public string listName;
        public string windowId;
        public bool authenticatedRequired;
        public bool enableChildrenProtect;
        public int buttonId;
    }
}
