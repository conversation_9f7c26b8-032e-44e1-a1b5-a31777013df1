﻿/// <summary>
/// AssistantNpcCenter
/// </summary>
/// <remarks>
/// 2023.1.12: 创建. 谌安 <br/>
/// 器械选择<br/>
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class AssistantNpcCenter : ISchemeNode, IAssistantNpcCenter
    {
        private const string Npc_INFO = "AssistantNpc";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, Dictionary<int,AssistantNpcDef>> m_npcByID;

        private List<AssistantNpcDef> assistantNpcDefs;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        private Action m_curLoadFinishCallback;

        private int courseId;

        public AssistantNpcCenter()
        {
            m_npcByID = new Dictionary<int, Dictionary<int, AssistantNpcDef>>();
            assistantNpcDefs = new List<AssistantNpcDef>();
        }

        ~AssistantNpcCenter()
        {
        }

        public bool Create()
        {
          
            return true;
        }

        public bool LoadScheme(int courseID, Action action)
        {
            courseId = courseID;
            m_curLoadFinishCallback = action;
            assistantNpcDefs.Clear();
            if (!m_npcByID.ContainsKey(courseID))
            {
                m_npcByID.Add(courseID, null);
                string strPath = "CourseCsv/"+courseID+"/"+ Npc_INFO;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent,courseID);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                if (m_curLoadFinishCallback != null)
                {
                    m_curLoadFinishCallback();
                }
                foreach (var item in GetAssistantNpcTaskInfos(courseID))
                {
                    assistantNpcDefs.Add(item.Value);
                }
            }
            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }
            Dictionary<int, AssistantNpcDef> curDef = null;
            m_npcByID.TryGetValue((int)pData, out curDef);
            if(curDef == null)
            {
                curDef = new Dictionary<int, AssistantNpcDef>();
                m_npcByID[(int)pData] = curDef;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                AssistantNpcDef map = new AssistantNpcDef();
                String tmpInfo = "";
                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.talk = pCSVReader.GetString(nRow, tmp_col++, "");
                //map.talk.Replace("\n","n");
              /*  tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.talkPos = new Vector3(-0.774f,1.017f,0);
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.talkPos = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }*/

                map.nexttalk = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.interval = pCSVReader.GetInt(nRow, tmp_col++, 0);
                

              /*  tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.targetPos = new Vector3(0, -500, 0);
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp =  tmpInfo.Split(';');
                    map.targetPos = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }*/

               /* tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.rotateAngle = new Vector3(0, -500, 0);
                if (!string.IsNullOrEmpty(tmpInfo)) {
                    string[] tmp = tmpInfo.Split(';');
                    map.rotateAngle = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }
*/

               /* tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.afterRotateAngle = new Vector3(0, -500, 0);
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.afterRotateAngle = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }*/

               /* map.moveKind = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.moveSpped = pCSVReader.GetFloat(nRow, tmp_col++, -1);*/
                map.assessmentIsShow = pCSVReader.GetInt(nRow, tmp_col++, 0);
               // map.playAnimationName = pCSVReader.GetString(nRow, tmp_col++, "");
               // map.endPlayAnimationName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.audioID = pCSVReader.GetInt(nRow, tmp_col++, -1);
                map.endAudioID = pCSVReader.GetInt(nRow, tmp_col++, -1);
                map.kind = pCSVReader.GetInt(nRow, tmp_col++, 0);
                //map.sendNode = pCSVReader.GetString(nRow, tmp_col++, "");
                map.textEffect = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.DoctorHighliht = pCSVReader.GetInt(nRow, tmp_col++, 0);
                assistantNpcDefs.Add(map);
                if (curDef.ContainsKey(map.Id))
                {
                    Debug.LogError("---策划注意--- AssistantNpc 重复ID = " + map.Id + " talk = " + map.talk);
                }
                curDef.Add(map.Id,map);
            }

            if (m_curLoadFinishCallback != null)
            {
                m_curLoadFinishCallback();
            }
            return true;
        }

        public void Release()
        {
            assistantNpcDefs.Clear();
            m_npcByID.Clear();
            m_npcByID = null;
        }

        /// <summary>
        /// 获得器械选择数据
        /// </summary>
        /// <param name="courseID"></param>
        /// <returns></returns>
        public Dictionary<int, AssistantNpcDef> GetAssistantNpcTaskInfos(int courseID)
        {
            Dictionary<int, AssistantNpcDef> curDef = null;
            m_npcByID.TryGetValue(courseID, out curDef);
            return curDef;
        }

        public List<AssistantNpcDef> GetAssistantNpcDefs()
        {
            return assistantNpcDefs;
        }
      
    }
}
