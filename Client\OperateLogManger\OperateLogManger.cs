﻿/// <summary>
/// OperateLogManger
/// </summary>
/// <remarks>
/// 2023/1/13 14:43:49: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GLib.Common;

namespace GLib.Client
{
    public class OperateLogManger : IOperateLogMgr, IEventExecuteSink
    {
        public string ModuleName { get; set; }
        public EMModuleLoadState ModuleLoadState { get; set; }
        public float Progress { get; set; }

        public List<OperateLogDataDisPose> m_alldatas { get; set; }


        public Dictionary<string, OperationLogInfo> m_OperateLog { get; set; }
        public Dictionary<int, OperateConditionInfo> m_OperateCondition { get; set; }


        public Dictionary<int, OperateLogLogicBase> m_OperateLogLogicBase;
        public Dictionary<int, OperateLogDataDisPose> SuccessDatas { get; set; }
        public Dictionary<int, OperateLogDataDisPose> ErrorDatas { get; set; }

        public Dictionary<int, OperateLogDataDisPose> allTaskDatas { get; set; }

        public bool Create()
        {
            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_UPDATE_OPERATELOG, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_UPDATE_DIRECTOPERATELOG, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
            }
            return true;
        }
        public void Release()
        {
            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_UPDATE_OPERATELOG, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.EVENT_UPDATE_DIRECTOPERATELOG, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            }
            for (int i = 0; i < m_OperateLogLogicBase.Count; i++)
            {
                if (m_OperateLogLogicBase.ContainsKey(i + 1))
                {
                    m_OperateLogLogicBase[i + 1].Release();
                }
            }
            SuccessDatas.Clear();
            m_OperateCondition.Clear();
            m_alldatas.Clear();
            m_OperateLog.Clear();
            ErrorDatas.Clear();
            allTaskDatas.Clear();         
            m_OperateLogLogicBase.Clear();
        }
        #region
        public void FixedUpdate()
        {

        }

        public void LateUpdate()
        {

        }

        public void Update()
        {

        }
        #endregion

        public void LoadDatable()
        {
            m_OperateLog = GlobalGame.Instance.CourseMgr.GetOperationLogInfo();
            m_OperateCondition = GlobalGame.Instance.CourseMgr.GetOperateConditionInfo();
            
            m_OperateLogLogicBase = new Dictionary<int, OperateLogLogicBase>();
            m_alldatas = new List<OperateLogDataDisPose>();
            SuccessDatas = new Dictionary<int, OperateLogDataDisPose>();
            ErrorDatas = new Dictionary<int, OperateLogDataDisPose>();
            allTaskDatas = new Dictionary<int, OperateLogDataDisPose>();
        }
       
        public void ReleaseDate()
        {
            m_OperateLogLogicBase.Clear();
            SuccessDatas.Clear();
            m_alldatas.Clear();
            ErrorDatas.Clear();
            allTaskDatas.Clear();
        }
        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (int)ViewLogicDef.EVENT_UPDATE_OPERATELOG:
                    UpdateOperateLogDate updateOperate = pContext as UpdateOperateLogDate;
                    UpdateOperate(updateOperate,false);
                    break;
                case (int)ViewLogicDef.EVENT_UPDATE_DIRECTOPERATELOG:
                    OperateLogDataDisPose DataLog = pContext as OperateLogDataDisPose;
                    DirectOperatLog(DataLog);
                    break;
            }
        }

        public OperationLogInfo GetOperateLogInfo(UpdateOperateLogDate data)
        {
         
            OperationLogInfo logInfo = FindOperationLog(data,3);

            if(logInfo == null)
            {
                TRACE.WarningLn("日志表格中条件不存在：");
                return null;
            }
            return logInfo;
        }

        void DirectOperatLog(OperateLogDataDisPose DataLog)
        {
            m_alldatas.Add(DataLog);
            OperateLogDate.Instance.SetDate(DataLog);
            OperateLogDate.Instance.SetOperateLogDate(DataLog);
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_UPTDATE_OPERATEWIN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");

            //if (!string.IsNullOrEmpty(DataLog.ScoreAlias))
            //{
            //    string[] allAlisName = DataLog.ScoreAlias.Split(';');
            //    foreach (var item in allAlisName)
            //    {
            //        UpdateScoreDate scoreDate = new UpdateScoreDate();
            //        scoreDate.AliasInt = DataLog.ScoreAlias;
            //        scoreDate.IsSucced = DataLog.Result == 1 ? true : false;
            //        GHelp.FireExecute((ushort)ViewLogicDef.EVENT_SCORE_UPDATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, scoreDate);
            //    }
            //}


        }

        private OperationLogInfo FindOperationLog(UpdateOperateLogDate data,int max)
        {

            if(max <= 0)
            {
                return null;   
            }
            OperationLogInfo logInfo = null;


            string data1 = data.OneOperateName;
            if (max < 3)
                data1 = "";
            string data2 = data.TwoOperatedName;
            if (max < 2)
                data2 = "";
            string data3 = data.TaskID;

            string dataID = string.Format("{0}:{1}:{2}", data1, data2, data3);//::10012
            if (m_OperateLog.TryGetValue(dataID, out logInfo))
            {
                return logInfo;
            }
            max = max - 1;
            return FindOperationLog(data, max);
        }






        /// <summary>
        /// 判断表格中是否存在对应日志
        /// </summary>
        /// <param name="decStr">操作描述</param>
        /// <param name="operateModel">操作物体名称</param>
        /// <param name="operatedModel">被操作物体名称</param>
        /// <returns></returns>
        public OperationLogInfo GetOperateLogInfo(string decStr,string operateModel,string operatedModel)
        {
            OperationLogInfo logInfo = null;
            List<OperationLogInfo> tempFinlist = new List<OperationLogInfo>(); 
            if (!string.IsNullOrEmpty(decStr))
            {
                foreach (var item in m_OperateLog.Values)
                {
                    if (item.ConditionDescription==decStr)
                    {
                        tempFinlist.Add(item);
                    }
                }
                if (tempFinlist.Count==0)
                {
                    //TRACE.ErrorLn("日志描述错误"+decStr);
                    return null;
                }
                if (!string.IsNullOrEmpty(operateModel) && !string.IsNullOrEmpty(operatedModel))
                {
                    List<OperationLogInfo> endList = new List<OperationLogInfo>();
                    foreach (var item in tempFinlist)
                    {
                        if (item.OperateModelName == operateModel && item.OperatedModelName == operatedModel)
                        {
                            endList.Add(item);
                        }
                    }
                    if (endList.Count == 0)
                    {
                        TRACE.ErrorLn("模型名称错误");
                        return null;
                    }
                    if (endList.Count > 1)
                    {
                        TRACE.ErrorLn("表格中存在多个满足条件的日志");
                        return null;
                    }
                    logInfo = endList[0];
                }
                else
                {
                    if (tempFinlist.Count>1)
                    {
                        TRACE.ErrorLn("表格中存在多个满足操作描述条件的日志");
                        return null;
                    }
                    if (tempFinlist.Count == 1)
                    {
                        logInfo = tempFinlist[0];
                    }
                }

            }
            else
            {
                if (!string.IsNullOrEmpty(operateModel) && !string.IsNullOrEmpty(operatedModel))
                {
                    foreach (var item in m_OperateLog.Values)
                    {
                        if (item.OperateModelName == operateModel && item.OperatedModelName == operatedModel)
                        {
                            tempFinlist.Add(item);
                        }
                    }
                    if (tempFinlist.Count==0)
                    {
                        TRACE.ErrorLn("无对应模型名称或被操作模型名称" + operateModel);
                        return null;
                    }
                    if (tempFinlist.Count>1)
                    {
                        TRACE.ErrorLn("有多个对应模型名称或被操作模型名称" + operateModel);
                        return null;
                    }
                    logInfo = tempFinlist[0];
                }
            }
            return logInfo;
        }
        public void UpdateOperate(UpdateOperateLogDate date,bool TEST)
        {
            OperationLogInfo logInfo = SetOperateLogInfo(date);
            if (logInfo == null)
            {
                TRACE.WarningLn("表格中未有此日志对应项");
                return;
            }
            string[] OperateDate = date.OperateDate.Split(';');
            string[] logInfoCondtionIDs = logInfo.ConditionDateType.Split(';');
            //if (logInfoCondtionIDs.Length != OperateDate.Length)
            //{
            //    TRACE.ErrorLn("操作参数和操作参数类型数量不匹配");
            //    return;
            //}
            //传过来的数据
            string[] conditionDatas = date.OperateDate.Split(';');
             
            if (string.IsNullOrEmpty(logInfo.ConditionID))
            {
               // OperateLogDate.Instance.SetDate(logInfo);
            }
            else
            {
                string[] conditionIDs = logInfo.ConditionID.Split(';');
                string[] logicType = logInfo.LogicType.Split(';');
                OperateLogDataDisPose logdispose = new OperateLogDataDisPose();
                for (int i = 0; i < logicType.Length; i++)
                {
                    int logicMun = (int.Parse(logicType[i]));
                    OperateLogLogicBase LogLogicBase = null;
                    logInfo.ConditionID = conditionIDs[i];
                    logInfo.LogicType= logicType[i];
                    logInfo.ConditionDateType= logInfoCondtionIDs[i];
                    date.OperateDate = OperateDate[i];
                    if (m_OperateLogLogicBase.TryGetValue(logicMun, out LogLogicBase))
                    {
                        LogLogicBase.Init(logInfo, date);
                        //LogLogicBase.DisUpdate();
                    }
                    else
                    {
                        switch ((LogicType)logicMun)
                        {
                            case LogicType.ValueTpye:
                                LogLogicBase = new ValueType();

                                break;
                            case LogicType.OperateEnd:
                                 LogLogicBase=new IsOperateEnd();
                                break;
                            case LogicType.GroupSend:
                                LogLogicBase = new GourdSend();
                                break;

                            default: break;

                        }
                        m_OperateLogLogicBase.Add(logicMun, LogLogicBase);
                       

                    }


                    LogLogicBase.Init(logInfo, date);
                    int restype = LogLogicBase.DisUpdate();
                  
                    logdispose.ID = logInfo.TaskID;
                    logdispose.OperateName = logInfo.OperateName;
                    logdispose.NecessaryOperation=logInfo.NecessaryOperation;
                    logdispose.Result = restype;
                    switch ((ResultType)restype)
                    {
                        case ResultType.Fail:
                            if (!allTaskDatas.ContainsKey(logdispose.ID))
                            {
                                ErrorDatas.Add(logdispose.ID, logdispose);
                            }
                            Debug.Log(logInfo.OperateName + "操作失败");
                            break;
                        case ResultType.Succeed:
                            if (!allTaskDatas.ContainsKey(logdispose.ID))
                                SuccessDatas.Add(logdispose.ID, logdispose);
                            Debug.Log(logInfo.OperateName + "操作成功");
                            break;
                        case ResultType.Repetition:
                            // SuccessDatas.Add(logInfo.ID, logInfo);
                            Debug.Log(logInfo.OperateName+"重复操作");
                            break;
                        default:
                            break;


                    }
                    
                }

                
                if (!allTaskDatas.ContainsKey(logdispose.ID))
                {
                    allTaskDatas.Add(logdispose.ID, logdispose);
                }
              
                //Debug.LogError(logdispose.OperateName+ "----------------"+ logdispose.Result);
                m_alldatas.Add(logdispose);
                OperateLogDate.Instance.SetDate(logdispose);
                OperateLogDate.Instance.SetOperateLogDate(logdispose);
                GHelp.FireExecute((ushort)ViewLogicDef.EVENT_UPTDATE_OPERATEWIN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
            }
        }
        public OperationLogInfo SetOperateLogInfo(UpdateOperateLogDate date)
        {
            OperationLogInfo returnInfo=  GetOperateLogInfo(date);
            if (returnInfo!=null)
            {
                OperationLogInfo temInfo = new OperationLogInfo();
                temInfo.ID = returnInfo.ID;
                temInfo.OperateName = returnInfo.OperateName;
                temInfo.ConditionDescription = returnInfo.ConditionDescription;
                temInfo.OperateModelName = returnInfo.OperateModelName;
                temInfo.OperatedModelName = returnInfo.OperatedModelName;
                temInfo.NecessaryOperation = returnInfo.NecessaryOperation;
                temInfo.ConditionID = returnInfo.ConditionID;
                temInfo.ConditionDateType = returnInfo.ConditionDateType;
                temInfo.LogicType = returnInfo.LogicType;
                temInfo.TaskID = returnInfo.TaskID;
                return temInfo;
            }
            return null;

        }
        /// <summary>
        /// string转三目运算
        /// </summary>
        /// <param name="tem"></param>
        /// <param name="conditionDate">数据类型</param>
        /// <param name="date"></param>
        /// <returns></returns>
        public bool StringToTernaryOperation(string tem, ConditionDateType conditionDate,string temDate,object date)
        {
            string[] temStrs = tem.Split('?');
            temStrs[1] = temStrs[1].Replace("(", "");
            temStrs[1] = temStrs[1].Replace(")", "");
            string[] bools = temStrs[1].Split(':');
            switch (conditionDate)
            {
                case ConditionDateType.None:
                    break;
                case ConditionDateType.Bool:
                    return StringToBool(temDate) ? StringToBool(bools[0]) : StringToBool(bools[1]);
                case ConditionDateType.Int:
                    if (date == null)
                    {
                        return false;
                    }
                    bool isSure = false;
                    int dateNumber = int.Parse(temDate);
                    int condNum = int.Parse((string)date);
                    switch (GetIntSymbol(temStrs[0]))
                    {
                        case 2:
                            isSure= dateNumber < condNum ? StringToBool(bools[0]) : StringToBool(bools[1]);
                            break;
                        case 4:
                            isSure = dateNumber == condNum ? StringToBool(bools[0]) : StringToBool(bools[1]);
                            break;
                        case 6:
                            isSure = dateNumber <= condNum ? StringToBool(bools[0]) : StringToBool(bools[1]);
                            break;
                        case 8:
                            isSure = dateNumber > condNum ? StringToBool(bools[0]) : StringToBool(bools[1]);
                            break;
                        case 12:
                            isSure = dateNumber >= condNum ? StringToBool(bools[0]) : StringToBool(bools[1]);
                            break;
                    }
                    return isSure;
                case ConditionDateType.String:
                    if (date==null)
                    {
                        return false;
                    }
                    string dateStr = (string)temDate;
                    return dateStr.Contains((string)date) ? StringToBool(bools[0]) : StringToBool(bools[1]);
            }
            return false;
        }
        public bool StringToBool(string tem)
        {
            int index = -1;
            int.TryParse(tem,out index);
            if (index == -1)
            {
                TRACE.ErrorLn("日志条件表格三目运算配置错误" + tem);
                return false;
            }
            return index == 1 ? true : false;
        }
        public int GetIntSymbol(string tem)
        {
            int index = 0;
            if (tem.Contains("<"))
            {
                index += 2;
            }
            if (tem.Contains("="))
            {
                index += 4;
            }
            if (tem.Contains(">"))
            {
                index += 8;
            }
            return index;
        }
    }
}