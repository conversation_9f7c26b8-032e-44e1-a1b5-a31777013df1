﻿/// <summary>
/// ButtonAudioCenter
/// </summary>
/// <remarks>
/// 2021/11/3 11:46:43: 创建. 王正勇 <br/>
/// 
/// </remarks>
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Client
{
    /// <summary>
    /// ButtonAudioCenter
    /// </summary>
    public class AudioConfigCenter : ISchemeNode, IAudioConfigCenter
    {
        private const string _INFO = "AudioConfig";
        private List<AudioConfig.Types.Item> m_AudioConfigList;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }

        public bool Create()
        {
            m_AudioConfigList = new List<AudioConfig.Types.Item>();
            if (!LoadScheme())
            {
                TRACE.ErrorLn("VideoConfig load fail!");
                return false;
            }
            return true;
        }
        public bool LoadScheme()
        {
            m_AudioConfigList.Clear();
            string strPath = _INFO;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Data);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);

                return false;
            }

            return true;
        }
        public bool OnSchemeLoad_Data(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                AudioConfig.Types.Item data = new AudioConfig.Types.Item();
                data.Id= pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.ModuleType = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.TriggerMode = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.AudioId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.Volume= pCSVReader.GetFloat(nRow, tmp_col++, 1);
                m_AudioConfigList.Add(data);
            }

            return true;
        }
        public AudioConfig.Types.Item GetButttonAudioId(ModuleType moduleType, ButtonTriggerMode buttonType)
        {
            if (m_AudioConfigList == null || m_AudioConfigList.Count <= 0)
            {
                return null;
            }
            AudioConfig.Types.Item item = m_AudioConfigList.Where(w => w.ModuleType == (int)moduleType && w.TriggerMode == (int)buttonType).FirstOrDefault();
            return item;
        }
        public AudioConfig.Types.Item GetButttonAudioId(int iAudioConfigId)
        {
            if (iAudioConfigId <= 0)
            {
                return null;
            }
            AudioConfig.Types.Item item = m_AudioConfigList.Where(w => w.Id == iAudioConfigId).FirstOrDefault();
            return item;
        }
        public List<AudioConfig.Types.Item> GetModuleAudio(ModuleType moduleType)
        {
            if (m_AudioConfigList == null || m_AudioConfigList.Count <= 0)
            {
                return null;
            }
            List<AudioConfig.Types.Item> listItem = m_AudioConfigList.Where(w => w.ModuleType == (int)moduleType).ToList();
            return listItem;
        }

        public void Release()
        {
            m_AudioConfigList.Clear();
        }


    }
}
