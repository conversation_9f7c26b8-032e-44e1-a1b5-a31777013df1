﻿using GLib.LitJson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public class COpHandleCommandEventSend : IHandleCommand
    {
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private string eventEnumString;
        private string eventTypeString;

        /// <summary>
		/// 直接透传
		/// </summary>
		public string param;
        /// <summary>
        /// 用于转换成class，暂不实现
        /// </summary>
        public string paramJson;
        /// <summary>
        /// 类名，与paramJson配合使用,暂未实现
        /// </summary>
        public string className;
        private List<IHandleCommand> m_others;
        public COpHandleCommandEventSend(SOpHandleCommand_EventSend data)
        {
            eventEnumString = data.eventEnumString;
            eventTypeString = data.eventTypeString;
            param = data.param;
            paramJson = data.paramJson;
            className = data.className;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpCreateEntity;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }
            if (string.IsNullOrEmpty(eventTypeString) || string.IsNullOrEmpty(eventEnumString))
            {
                TRACE.ErrorLn("ExecuteEventSend error eventTypeString=null || eventEnumString = null");
                return true;
            }
            ViewLogicDef keyID = (ViewLogicDef)(Enum.Parse(typeof(ViewLogicDef), eventEnumString));
            EMSOURCE_TYPE type = (EMSOURCE_TYPE)(Enum.Parse(typeof(EMSOURCE_TYPE), eventTypeString));
            try
            {
                if (!string.IsNullOrEmpty(param)&&(param.Equals("true") || param.Equals("false")))
                {
                    bool b = bool.Parse(param);
                    GHelp.FireExecute((ushort)keyID, (byte)type, 0, b);
                }
                else
                {

                    if (!string.IsNullOrEmpty(className))
                    {
                        Type objectType = (from asm in AppDomain.CurrentDomain.GetAssemblies()
                                           from t in asm.GetTypes()
                                           where t.IsClass && t.Name == className
                                           select t).Single();
                        object _obj = Activator.CreateInstance(objectType);
                        JsonData jsonObj = JsonMapper.ToObject(paramJson);
                        foreach (var Field in _obj.GetType().GetFields())//GetFields获取字段
                        {
                            for (int k = 0; k < jsonObj.Keys.Length; k++)
                            {
                                string key = jsonObj.Keys[k];
                                if (Field.Name.Equals(key))
                                {
                                    JsonData a = jsonObj[key];
                                    JsonType ty = a.GetJsonType();
                                    object jValue = null;
                                    switch (ty)
                                    {
                                        case JsonType.String:
                                            jValue = (string)a;
                                            break;
                                        case JsonType.Int:
                                            jValue = (int)a;
                                            break;
                                        case JsonType.Boolean:
                                            jValue = (bool)a;
                                            break;
                                        case JsonType.Double:
                                            jValue = (double)a;
                                            break;
                                        case JsonType.Long:
                                            jValue = (long)a;
                                            break;
                                        case JsonType.Object:
                                            jValue = (object)a;
                                            break;
                                    }
                                    Field.SetValue(_obj, jValue);
                                }
                            }
                        }
                        GHelp.FireExecute((ushort)keyID, (byte)type, 0, _obj);
                    }
                    else
                    {
                        GHelp.FireExecute((ushort)keyID, (byte)type, 0, param);
                    }
                }
            }
            catch (Exception e)
            {
                TRACE.ErrorLn(e.Message);
                m_isEnd = true;
            }

            return true;
        }

        public void update()
        {
        }
    }
}
