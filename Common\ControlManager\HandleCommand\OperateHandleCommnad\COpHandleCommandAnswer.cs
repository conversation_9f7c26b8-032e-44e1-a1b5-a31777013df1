﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandAnswer : IHandleCommand,IEventExecuteSink
    {
                                          // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private int m_answerID;

        private bool m_isOver;

        private List<IHandleCommand> m_others;
        public COpHandleCommandAnswer(SOpHandleCommand_Answer data)
        {
            if (data.answerID > 0)
            {
                m_answerID = data.answerID;
            }
            else
            {
                m_answerID = data.stepId;
            }
            m_isPlay = false;
            m_isOver = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpMove;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)ViewLogicDef.EVENT_ANSWER_TASKOVER:
                    {
                        GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)ViewLogicDef.EVENT_ANSWER_TASKOVER, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                        m_isOver = true;
                    }
                    break;
                default:
                    break;
            }
        }

        public void OnPause()
        {
            
        }

        public void release()
        {
            m_isPlay = false;
            m_isOver = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                m_isPlay = true;
                var answer = GlobalGame.Instance.SchemeCenter.GetAnswerUI().GetAllAnswere(m_answerID);
                if (answer == null || answer.Count == 0)
                {
                    TRACE.ErrorLn($"-----策划注意！！！问答表无此Id数据 answerID = {m_answerID}，请确认是否删除改此处问答或填充问答数据。如需删除，请告知程序");
                    return true;
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)ViewLogicDef.EVENT_ANSWER_TASKOVER, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
                cmd_AnswerWindow cmd_Help = new cmd_AnswerWindow();
                cmd_Help.wModel = WindowModel.AnswerUIWindow;
                cmd_Help.aCommandState = AsyncCommandState.CreateCommmand;
                cmd_Help.ID = m_answerID;
                GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CREATE_ANSWERWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Help);
            }

            if (m_isOver)
            {
                return true;
            }
            return false;
        }

        public void update()
        {
        }
    }
}
