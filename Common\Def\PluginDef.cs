﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    /// <summary>
    /// 识别返回
    /// </summary>
    /// <param name="code">0:失败  1:成攻</param>
    /// <param name="usefulText"></param>
    public delegate void RecognizeCallback(int code,string usefulText);

    public class StopVoiceDef
    {
        public byte[] data;
        public AudioClip audioClip;
    }
}
