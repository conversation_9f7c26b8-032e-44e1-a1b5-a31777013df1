﻿/// <summary>
/// EntityView
/// </summary>
/// <remarks>
/// 2019.7.4: 创建. 谌安 <br/>
/// 实体描述 <br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.Events;

namespace GLib.Common
{
    /// 实体标志
    public enum EntityFlags : uint
    {
        flagVisible = 0x1,			/// 可见（标识是否在屏幕范围内）
	    flagSelectable = 0x2,			/// 逻辑可选择
        // 类型
        flag2D = 0x4,			/// 2D类型
	    flag3D = 0x8,          /// 3D类型

        // 渲染标志
        flagDrawFilename = 0x10,
        flagDrawName = 0x20,		/// 绘制实体名字
	    flagDrawResId = 0x40,		/// 绘制实体资源Id
        flagHide = 0x100,	/// 隐藏标志(隐藏状态下，不进行update和draw)
        flagDrawHP = 0x400,		/// 绘制实体HP
    }

    /// 实体扩展标志
    public enum EntityFlagsEx
    {
        exflagDenseOptimize = 0x1,			/// 稠密优化，该标志将不渲染实体对象
	    exflagMove = 0x2,			/// 表示实体是否正在移动
	    exflagRapidMove = 0x4,			/// 瞬移标志
	    exflagPerfOptimize = 0x8,			/// 是否性能优化
	    exflagFilteHide = 0x10,			/// 是否筛选隐藏
        exflagDead = 0x20,          /// 死亡       
        exflagActiveTitle = 0x4000,         //激活标题
    };

    public class EntityViewCommand
    {
        public ushort cmdID;
        public int nParam;
        public string strParam;
        public object ptrParam;

        public void Set(ushort cmdID, int nParam, string strParam, object ptrParam)
        {
            this.cmdID = cmdID;
            this.nParam = nParam;
            this.strParam = strParam;
            this.ptrParam = ptrParam;
        }
    }

    public class EntityViewCommandEx
    {
        public EntityViewCommand CMD;
        public int hashCode;

    }

    public class EntityViewItem : cmd_Base
    {
        public Int64 UID;		    // 实体UID
        public uint EntityViewID;	// 实体ID(输出)
        public int ConfigID;        //配置ID
        public int EntityFactoryConifgID; //实体工厂配置ID（服务器刷怪表）
        public string szName;	    // 名字	
        public string prefabPath;   //路径
        public float Angle;          //实体角度
        public byte EntityType;     // 实体类型
        public byte byIsHero;		// 是否为主角,1=主角
        public int nSkinID;			// 皮肤ID
        //二种类型长度为1取id，>1直接使用颜色值
        public string nameColor;    //名字颜色  
        /// <summary>
        /// 预制体前缀  格式 "XXXX_"
        /// </summary>
        public string prefabPrefix = "";
        public bool bCreateImmediate = false; //立刻优先创建:剧情,UI界面的View使用
        public float fPosX;     // 模型X轴偏移
        public float fPosY;     // 模型Y轴偏移
        public float fPosZ;     // 模型Y轴偏移
        public bool bForceCreate = false;   // 主角未创建时强制创建

        public string strTargetID = "";//机器人id
        public uint nRobotMasterID = 0;//机器人归属角色ID

        public float fMoveSpeed = 0.0f; //移动速度

        public BulletItem bulletItem = null;
        public override void ReSet()
        {
            bCreateImmediate = false;
            EntityViewID = 0;
            strTargetID = "";
            nameColor = "";
            nRobotMasterID = 0;
            Angle = 0;
            nSkinID = 0;
            fMoveSpeed = 0.0f;
            EntityFactoryConifgID = 0;
            if (bulletItem != null)
            {
                bulletItem.ReSet();
            }
            bulletItem = null;
        }
    }

    public class BulletItem
    {
        /// <summary>
        /// 攻击者
        /// </summary>
        public Int64 nAttackGuid;
        /// <summary>
        /// 攻击者部件 id
        /// </summary>
        public Int64 nAttackPartGuid = 1;
        /// <summary>
        /// 目标
        /// </summary>
        public Int64 nTargetGuid;
        /// <summary>
        /// 目标点，当前target未找到时使用
        /// </summary>
        public Vector3 vTargetPosition;
        //技能id
        public int nSkillID;
        //发射的第几次
        public int nFireCount;

        public void CopyParam(BulletItem param)
        {
            nAttackGuid = param.nAttackGuid;
            nAttackPartGuid = param.nAttackPartGuid;
            nTargetGuid = param.nTargetGuid;
            vTargetPosition = param.vTargetPosition;
            nSkillID = param.nSkillID;
            nFireCount = param.nFireCount;
        }

        public void ReSet()
        {
            nAttackGuid = 0;
            nAttackPartGuid = 1;
            nTargetGuid = 0;
            vTargetPosition = Vector3.zero;
            nSkillID = 0;
            nFireCount = 0;
        }
    }

    public class cmd_creature_MoveTo : cmd_EntityView
    {
        public int nActorID;			// 角色ID
        public float fSpeed;
        public float fPosition_x;      // 角色位置
        public float fPosition_y;      // 角色位置
        public float fPosition_z;      // 角色位置
        public float fForward_x;	   // 角色朝向绕x轴旋转的角度
        public float fForward_y;      // 角色朝向绕y轴旋转的角度
        public float fForward_z;      // 角色朝向绕z轴旋转的角度
        public bool isJStop;

        public override void ReSet()
        {
            nActorID = 0;
            fSpeed = 0.0f;
            fPosition_x = 0.0f;
            fPosition_y = 0.0f;
            fPosition_z = 0.0f;
            fForward_x = 0.0f;
            fForward_y = 0.0f;
            fForward_z = 0.0f;
            isJStop = false;
        }
    };

    public class EntryView_Destroy : cmd_Base
    {
        public uint ENTITY_ID;
        public long ENTITY_UID;

        public override void ReSet()
        {
            ENTITY_ID = 0;
        }
    }
    public enum EntityLogicDef
    {
        INVALID_ENTITY_ID = (DGlobalGame.CMD_ENTITY+1),  // 无效实体

        // 设置实体是否可见
        ENTITY_TOVIEW_VISABLE,
        // 同步实体位置
        ENTITY_TOVIEW_MOVETO,

        // 同步实体位置
        ENTITY_TOVIEW_SYNC_POS,
        // 同步实体大小
        ENTITY_TOVIEW_SYNC_SCALE,
        //返回实体
        ENEITY_GAME,
        ENTITY_TWEENPLAYER_STOP,
        //移动
        ENTITY_TOVIEW_MOVE_POS,
        //实体动画播放
        ENTITY_TOVIEW_PLAY_ANIMATION,

        //头顶信息命令
        ENTITY_TOVIEW_TITLEPART_FORMAT,

        //头顶信息命令(批量处理)
        ENTITY_TOVIEW_TITLEPART_FORMAT_LIST,

        //单条头顶信息显示隐藏
        ENTITY_TOVIEW_TITLEPART_VISIABLE,

        //显示头顶符号
        ENTITY_TOVIEW_SHOWNPCSYMBOL,

        //隐藏头顶符号
        ENTITY_TOVIEW_HIDENPCSYMBOL,

        // 实体头顶显示说话内容
        ENTITY_TOVIEW_SHOW_CHAT,

        // 实体头顶隐藏说话内容
        ENTITY_TOVIEW_HIDE_CHAT,
        // 添加光效  
        ENTITY_TOVIEW_ADD_LIGHTING,

        // 移除光效
        ENTITY_TOVIEW_REMOVE_LIGHTING,
        //改變皮膚
        ENTITY_TOVIEW_CHANGE_SKIN,
        //上载具
        ENTITY_TOVIEW_ENTER_TANK,
        //下载具
        ENTITY_TOVIEW_LEAVE_TANK,
        //实体死亡动作
        ENTITY_TOVIEW_ENTITY_DEAD,
        //实体头顶信息变化
        ENTITY_TOPINFO_CHANGE,
        //实体旋转
        ENTITY_ROTATION,
        //实体旋转通过Euler
        ENTITY_ROTATION_EULERANGLES,
        //实体跳跃
        ENTITY_JUMP,
        //实体缩放
        ENTITY_ZOOM,
        //设置颜色
        ENTITY_SETCOLOR,
        //播放指定动画动作
        ENTITY_PLAYANIMATED,
        //变形
        ENTITY_DEFORMED,
        //邀请乘客
        ENTITY_INVITE,
        //与指定对象对话
        ENTITY_TALK,
        //设置推力
        ENTITY_SETTHRUST,
        //装载指定目标
        ENTITY_LOADING,

        // 转向方向
        ENTITY_TOVIEW_TURN_TO_DIRECTION,
        // 中断正在播放的攻击动作
        ENTITY_TOVIEW_BREAK_ATTACK_ANIMATION,

        //根据朝向移动
        ENTITY_TOVIEW_MOVE_BY_ANGLE,
        //朝向的移动停止
        ENTITY_MOVE_END,
        //只设置一次的实体转体
        ENTITY_ROTATION_BY_ONE,
        //子弹打断
        ENTITY_BULLET_BREAK,
        //设置摄像机跟随
        ENTITY_SET_CAMERA_FOLLOW,
        //注水
        ENTITY_WATER_FLOODING,
        //显示文字
        ENTITY_SHOW_TEXT_IN_LED,
        //实体停止积木块移动
        ENTITY_STOP_MOVE_OPCODE,
        //timeline的信息
        ENTITY_TIMELINE_INFO,
        //销毁timeline
        ENTITY_TIMELINE_DESTORY,
        //timeline播放完毕
        ENTITY_TIMELINE_STOPED,
        //设置UI摄像机显示与否
        ENTITY_SET_UI_CAMERA_SHOW,
        //装卸-安装
        ENTITY_STEVEDOINGS_INSTALL,
        //装卸-卸载
        ENTITY_STEVEDOINGS_REMOVE,
    }

    /// <summary>
    /// 静态实体定义，已知实体名字
    /// </summary>
    public enum StaticEntityDef
    {
        NONE = 0,
    }

    #region 实体视图相关
    public class cmd_EntityView : cmd_Base
    {
        public uint nViewID;

        public override void ReSet()
        {

        }
    }

    //实体可见信息
    public class cmd_creature_Visible : cmd_EntityView
    {
        public bool bVisable;			// 可见
    }

    public class IntroductionData : cmd_EntityView
    {
        public string Title;
        public string TexturePath;
        public Transform Parent;
    }

    public class cmd_creature_rigidbody_sync : cmd_EntityView
    {
        public bool bNotGround = false;   //是否贴地
        public uint nEntityID;			// 实体ID
        public float fPosition_x;      // 实体位置
        public float fPosition_y;      // 实体位置
        public float fPosition_z;      // 实体位置
        public Transform nParent;      //父节点

        public float fRot_x;
        public float fRot_y;
        public float fRot_z;

        public override void ReSet()
        {
            nParent = null;
            nEntityID = 0;
            fPosition_x = 0.0f;
            fPosition_y = 0.0f;
            fPosition_z = 0.0f;
            fRot_x = 0.0f;
            fRot_y = 0.0f;
            fRot_z = 0.0f;
        }
    }

    public class cmd_creature_scale_sync : cmd_EntityView
    {
        public float fScale_x;
        public float fScale_y;
        public float fScale_z;

        public override void ReSet()
        {
            fScale_x = 0.0f;
            fScale_y = 0.0f;
            fScale_z = 0.0f;
        }
    }
    //返回实体对象和ID
    public class cmd_creature_game : cmd_EntityView
    {
        public int EntityId;
        public GameObject obj;
    }
    /// <summary>
    /// 主事件系统通用
    /// </summary>
    public class cmd_creature_mainsystem_currency : cmd_EntityView
    {
        public MainEventGeneralState nState;
    }
    #endregion

    //寻路
    //ENTITY_TOVIEW_MOVE_POS
    public class cmd_MovePos : cmd_EntityView
    {
        public List<Vector3> listPath = null;					// 移动路径 避免频繁的内存拷贝所以使用list
        public bool bIsOption = false;
        public byte bFlag;								// 参考EM3DMoveFlag枚举
        public float fSendCmdTime;                     //发送移动命令的时间
        public float fMovedTime;                       //服务器已经走过的时间

        public cmd_MovePos()
        {
            fSendCmdTime = Time.realtimeSinceStartup;
            fMovedTime = 0.0f;
        }

        public override void ReSet()
        {
            listPath = null;
            bIsOption = false;
            bFlag = 0;
            fSendCmdTime = 0.0f;
            fMovedTime = 0.0f;
        }
    };

    public class cmd_MoveByAngle : cmd_EntityView
    {
        public float fSpeed;
        public float fAngle;
    }

    public class cmd_RotateAngle : cmd_EntityView
    {
        public float targetAngle;
        public float Angle;
        public float timeCount;
        //
        public float fRot_x;
        public float fRot_y;
        public float fRot_z;

    }


    /// 进去载具的上下文
    public class EnterTankContext : cmd_EntityView
    {
        public uint TankID;
        public uint EnterID;

        public override void ReSet()
        {
            TankID = 0;
            EnterID = 0;
        }
    }

    public class cmd_Jump : cmd_EntityView
    {
        public float Height;
        public Vector3 Position;
        public float JumpDuration;
    }

    public class cmd_Zoom : cmd_EntityView
    {
        public float Scale;
    }

    public class cmd_SetColor : cmd_EntityView
    {
        public Color Color;
    }

    public class cmd_Show : cmd_EntityView
    {
        public bool Visibility;
    }

    public class cmd_Deformed : cmd_EntityView
    {
        public string ModelId;
    }

    public class cmd_Invite : cmd_EntityView
    {
        public string PlayerNickName;
    }

    public class cmd_Talk : cmd_EntityView
    {
        public string TalkContent;
        public string TalkId;
        public IEntity TargetEntity;
    }

    public class cmd_SetThrust : cmd_EntityView
    {
        public float ThrustValue;
        public bool IsEffective;
    }

    public class cmd_Loading : cmd_EntityView
    {
        public bool LoadAction;
        public bool ShowObjectStateWhenLoaded;
        public Vector3 ScopeArea;
    }

    public class cmd_UseSkill : cmd_EntityView
    {
        public uint AttackGuid;
        public uint TargetGuid;
        public float Angle;
        public uint SkillID;
    }

    public class cmd_SetCameraFollow : cmd_EntityView
    {
        public uint EntityId;
        public string Paths;
    }

    public class cmd_WaterFlooding : cmd_EntityView
    {
        public uint EntityId;
        public bool IsFlooding;
    }

    public class cmd_ShowTextInLED : cmd_EntityView
    {
        public uint EntityId;
        public string Message;
    }

    public class cmd_SetTimeline : cmd_EntityView
    {
        public string Path;
        public int BackTime;
    }

    public class opcode_timelineDef
    {
        public GameObject Obj;
        public bool IsStopScript;
        public string Path;
    }

    public class cmd_SetUICameraShow : cmd_EntityView
    {
        public bool IsShow;
    }

    //装卸
    public class cmd_Stevedorings_install : cmd_EntityView
    {
        public uint EntityID;
        public EntityAppearance appearance;
    }
    public class cmd_Stevedorings_remove : cmd_EntityView
    {
        public uint EntityID;
        public EntityAppearance appearance;
        //移除到目标点
        public bool isRemvoeToTarget;

        public Vector3 pos;
        public Vector3 rot;
        public Vector3 scale;
    }

    public class cmd_laEx : cmd_EntityView
    {
        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, string> property = new Dictionary<string, string>();
    }

    // 通用效果
    public class PlayAnimationContext : cmd_EntityView
    {
        public string name;			        // 动作名
        public bool bhide;                  // 是隐藏
        public bool bTeleport;		        // 是否瞬移(瞬移到ptTarget位置)
        public Vector3 ptTarget;		    // 目标点

        public float transitionDuration;    //融合时间 normalizeTime(0-1)

        public long nRobotPartUID;         //攻击部件uid
        public bool isRobot;
        public float fAllowBreakTime;      //可允许打断时间

        // 模型静止

        public override void ReSet()
        {
            name = string.Empty;
            ptTarget = Vector3.zero;
            bhide = false;
            bTeleport = false;
            transitionDuration = 0;
        }
    };

    //头顶称号数据
    //ENTITY_TITLEPART_FORMAT
    public class cmd_titlepart : cmd_EntityView
    {
        public TitleIndexType index = TitleIndexType.TitleIndexType_Name;
        public string titleInfo;

        public void setTextWithDefaultStyle(string text)
        {
            titleInfo = @"<label text='" + text + @"' font='1' fgcolor='FFFFFFFF' size='20'>";
        }

        public override void ReSet()
        {
            index = TitleIndexType.TitleIndexType_Name;
            titleInfo = null;
        }
    }

    //头顶称号数据 单挑数据用 cmd_titlepart
    //ENTITY_TITLEPART_FORMAT
    public class cmd_titlepartlist : cmd_EntityView
    {
        public string[] titleInfos = new string[(int)TitleIndexType.TitleIndexType_Max];
        public bool[] visiables = new bool[(int)TitleIndexType.TitleIndexType_Max];

        public override void ReSet()
        {
            titleInfos = new string[(int)TitleIndexType.TitleIndexType_Max];
            visiables = new bool[(int)TitleIndexType.TitleIndexType_Max];
        }
    }

    public class cmd_titlepart_visiable : cmd_EntityView
    {
        public TitleIndexType index = TitleIndexType.TitleIndexType_Name;
        public bool visiable = true;

        public override void ReSet()
        {
            index = TitleIndexType.TitleIndexType_Name;
            visiable = true;
        }
    }

    // 说话内容结构体
    public class cmd_entity_TitleChatText : cmd_EntityView
    {
        public uint pdbid;			// pdbid
        public Int64 UID;          // UID
        public string strText;      // 文本
        public string strFunc;      // 回调函数
        public int nTime;			// 显示时长
        public bool bMultyDiaplay;  // 能否多级显示

        public override void ReSet()
        {
            pdbid = 0;
            UID = 0;
            bMultyDiaplay = false;
            strText = null;
            strFunc = null;
            nTime = 0;
        }
    }

    public class ChangePartContextEx : cmd_EntityView
    {
        public EntityParts partID;
        public int resId;
        public int fallBackResID;
        public float scale = 1.0f;
        public bool applyImmediately = false;
        public string strTargetID = "";
        public UnityAction<GameObject> unityEvent;

        //临时，向前兼容 不报错
        //public string texturePath;

        public override void ReSet()
        {
            partID = EntityParts.EntityPart_Body;
            resId = 0;
            scale = 1;
            applyImmediately = false;
        }
    }

    // 转向方向			
    // ENTITY_TOVIEW_TURN_TO_DIRECTION
    public class cmd_creature_turn_to_direction : cmd_EntityView
    {
        public float[] ptLoc = new float[6];            // 转向方向点

        public override void ReSet()
        {
            for (int i = 0; i < 6; i++)
            {
                ptLoc[i] = 0;
            }
        }
    };

    #region 实体标示变化(血条，称号其它)

    public class cmd_entity_TopInfoChange : cmd_EntityView
    {
        /// <summary>
        /// 血量条颜色显示风格
        /// </summary>
        public uint HPFlag = 0;

        /// <summary>
        /// false时只修改 不控制显示
        /// </summary>
        public bool IsShow;

        public override void ReSet()
        {
            HPFlag = 0;
        }
    }

    #endregion
}
