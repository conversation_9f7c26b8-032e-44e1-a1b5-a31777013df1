﻿#define OpenDebugInfo_GameStateLogin
using GLib.Common;
using UnityEngine;

namespace GLib.Boot
{
	// 游戏客户端状态-登录 GameStateLogin
	public class GameStateLogin : IGameState
	{
		// 版本检查 内部流程状态
		private enum EMCheckVerState
		{
			None = 0,
			Create,         // 初始化
			Enter,          // 进入
			Wait,           // 等待
			Exit,           // 退出
			Release,        // 关闭
			Error,          // 错误

			MAX,
		}
		////// 模块方法 //////////////////////////////////////////

		private bool m_IsEnd = false;
		// 流程是否结束
		public bool IsEnd
		{
			set { m_IsEnd = value; }
			get { return m_IsEnd; }
		}

		// 请求切换状态
		public bool IsBack = false;

		/// <summary>
		/// 状态创建.
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
			//return CGame.Instance.LoginModule.Create();
			IsBack = false;
			return true;
		}

		/// <summary>
		/// 状态释放
		/// </summary>
		public void Release()
		{
			//CGame.Instance.LoginModule.Release();
		}

		/// <summary>
		/// 当游戏流程进入
		/// </summary>
		/// <param name="nState">新状态</param>
		/// <param name="nOldState">旧状态</param>
		public void OnEnter(GameState nState, GameState nOldState)
		{
			/*
			 * 账号：g417,g411
			 * 密码：g
			 */
			CGame.Instance.LoginModule.OnEnter(nState, nOldState);
			// 逻辑测试时用
			//CGame.Instance.LoginModule.SetGatewayAddress("***********",9000);
			//CGame.Instance.LoginModule.StartLogin("g417", "g");

		}

		/// <summary>
		/// 当游戏流程退出
		/// </summary>
		/// <param name="nState">旧状态</param>
		/// <param name="nNewState">新状态</param>
		public void OnExit(GameState nState, GameState nNewState)
		{
			CGame.Instance.LoginModule.OnExit(nState, nNewState);
		}

		/// <summary>
		/// 每逻辑帧
		/// </summary>
		public void OnFixedUpdate()
		{
			IsFinish();
			if (IsEnd) return;
			IsGoBack();

		}

		/// <summary>
		/// 模块运行是否完成
		/// </summary>
		private void IsFinish()
		{
			IsEnd = CGame.Instance.LoginModule.IsLoginFinished();
			if (IsEnd)
			{
				//todo
			}
		}

		/// <summary>
		/// 是否切换状态
		/// </summary>
		public void IsGoBack()
		{
			// 判断模块是否需要改变状态
			//todo
			//IsBack = CGame.Instance.LoginModule.IsBack;

		}
	}
}



