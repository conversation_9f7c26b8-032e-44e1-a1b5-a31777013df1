﻿/// <summary>
/// RayEventCallBackParameter
/// </summary>
/// <remarks>
/// 2022.5.18: 创建. 谌安 <br/>
/// 射线处理逻辑 <br/>
/// </remarks>
using GLib.Common;
using UnityEngine;

namespace GLib.Common
{
    public enum RayEventType
    {
        /// <summary>
        /// 射线进入
        /// </summary>
        EventEnter = 0,
        /// <summary>
        /// 射线离开
        /// </summary>
        EventExit,
        /// <summary>
        /// 射线移动
        /// </summary>
        EventHover,
        /// <summary>
        /// 事件按下
        /// </summary>
        EventPress,
        /// <summary>
        /// 事件释放
        /// </summary>
        EventRelease,
        EventDragStart, /// 手柄拖拽事件开始
        EventDrag,   /// 手柄拖拽中
        EventDragEnd,  /// 手柄拖拽事件结束
        EventDrop,  /// 手柄拖放事件，在EventDragEnd事件之后
        EventGrap,   /// 抓取
        EventGrapRelease,  /// 抓取丢弃
    }
    /// <summary>
    /// 接口IEventcallback的事件参数
    /// </summary>
    public class RayEventCallBackParameter : cmd_Base
    {
        //当前射线指到物体上的位置
        public Vector3 CurrentPoint;
        public Quaternion objectRotation;
        //当前点位置相对上一帧的偏移量
        public Vector3 Detaly;
        public bool isLeftHand;
    }
   
}
