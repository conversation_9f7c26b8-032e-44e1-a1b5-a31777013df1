﻿/// <summary>
/// MonsterCenter
/// </summary>
/// <remarks>
/// 2021.3.30: 创建. 谌安 <br/>
/// 信息處理中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;

namespace GLib.Client
{
    public class MonsterCenter : ISchemeNode, IMonsterCenter
    {
        private const string _INFO = "Monster";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, Monster.Types.Item> m_dataInfoByID;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public MonsterCenter()
        {
            m_dataInfoByID = new Dictionary<int, Monster.Types.Item>();
        }

        ~MonsterCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = _INFO;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Data);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Data(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                Monster.Types.Item data = new Monster.Types.Item();

                data.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.ModelId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.IconId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.Name = pCSVReader.GetString(nRow, tmp_col++, "");
                data.HP = pCSVReader.GetInt(nRow, tmp_col++, 0);
                string AIInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                string[] ais = AIInfo.Split(';');
                if (ais.Length > 0)
                {
                    for (int i = 0; i < ais.Length; i++)
                    {
                        if(!string.IsNullOrEmpty(ais[i]))
                            data.AI.Add(int.Parse(ais[i]));
                    }
                }
                data.DieAnimation = pCSVReader.GetString(nRow, tmp_col++, "");
                data.DieLuaFunction = pCSVReader.GetString(nRow, tmp_col++, "");
                data.PrizeId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.CampId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.ShowName = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.View = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                data.MaxDistance = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                data.Speed = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                data.BehitSound = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.DieSound = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.BehitAnimation = pCSVReader.GetString(nRow, tmp_col++, "");
                data.MonsterType = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.Attack = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.Defense = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.ShowHP = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.NameColor = pCSVReader.GetString(nRow, tmp_col++, "");
                data.Radius = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                m_dataInfoByID.Add(data.Id, data);
            }

            return true;
        }

        public void Release()
        {
            m_dataInfoByID.Clear();
            m_dataInfoByID = null;
        }

        public Monster.Types.Item GetMonsterInfoByID(int tID)
        {
            Monster.Types.Item info = null;

            m_dataInfoByID.TryGetValue(tID, out info);

            return info;
        }
    }
}


