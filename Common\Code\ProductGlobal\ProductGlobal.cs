﻿/// <summary>
/// ProductGlobal
/// </summary>
/// <remarks>
/// 2021/11/29 14:27:48: 创建. 王正勇 <br/>
/// 项目全局管理
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// ProductGlobal
    /// </summary>
    public class ProductGlobal
    {
        private static ProductGlobal _Instance;
        /// <summary>
        /// 当前项目模块
        /// </summary>
        private ProductModel m_productModel;
        /// <summary>
        /// 是否在前台
        /// </summary>
        private bool m_IsInFrontDesk = true;
        /// <summary>
        /// 进入后台运行多长时间发出通知
        /// </summary>
        private int m_InBackgrounderNum = 20;
        /// <summary>
        /// 进入到后台时间
        /// </summary>
        private DateTime? m_InBackGrounderTime;


        public static ProductGlobal Instance
        {
            get
            {
                if (_Instance == null)
                {
                    _Instance = new ProductGlobal();
                    Initialize();
                }
                return _Instance;
            }
        }
        /// <summary>
        /// 初始化
        /// </summary>
        private static void Initialize()
        {

        }
        /// <summary>
        /// 设置当前所属模块
        /// </summary>
        /// <param name="productModel"></param>
        public void SetProductModel(ProductModel productModel)
        {
            TRACE.TraceLn(productModel.ToString());
            m_productModel = productModel;
        }
        /// <summary>
        /// 设置项目是否在前台
        /// </summary>
        /// <param name="bState"></param>
        public void SetIsForontDesk(bool bState)
        {
            m_IsInFrontDesk = bState;

#if !UNITY_EDITOR && (UNITY_ANDROID || UNITY_IOS || UNITY_IPHONE)
            //当前在前台
            if (bState)
            {
                //发出通知进入到前台
                GHelp.FireExecute((int)DGlobalEvent.PRODUCT_IN_FRONT_DESK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                if (m_InBackGrounderTime != null)
                {
                    int iTimeNum = GHelp.GetTwoDateTimeInterval(m_InBackGrounderTime ?? DateTime.Now, DateTime.Now, 1);
                    if (iTimeNum >= m_InBackgrounderNum)
                    {
                        GHelp.FireExecute((int)DGlobalEvent.PRODUCT_EXCEED_BACKSTAGE_TIME, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, iTimeNum);
                    }
                }
                
                m_InBackGrounderTime = null;
                AppMessage.Instance.SendAppStart();
            }
            else
            {
                //发出通知进入到后台
                m_InBackGrounderTime = DateTime.Now;
                AppMessage.Instance.SendAppEnd();
            }
            
#endif
        }
        /// <summary>
        /// 检查进入后台时长
        /// </summary>
        public void CheckIntoBackStage()
        {
#if !UNITY_EDITOR && (UNITY_ANDROID || UNITY_IOS || UNITY_IPHONE)
            //如果不是在后台，那么自己退出
            if (m_IsInFrontDesk)
            {
                return;
            }
            int iTimeNum = GHelp.GetTwoDateTimeInterval(m_InBackGrounderTime ?? DateTime.Now, DateTime.Now, 1);
            if (iTimeNum >= 5)
            {
                GHelp.FireExecute((int)DGlobalEvent.PRODUCT_IN_BACKSTAGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
            }
#endif
        }
        /// <summary>
        /// 获取当前所属模块
        /// </summary>
        /// <returns></returns>
        public ProductModel GetProductGlobal()
        {
            return m_productModel;
        }
        /// <summary>
        /// 获取当前是否在前台
        /// </summary>
        /// <returns></returns>
        public bool GetIsForontDeskState()
        {
            return m_IsInFrontDesk;
        }

    }
}
