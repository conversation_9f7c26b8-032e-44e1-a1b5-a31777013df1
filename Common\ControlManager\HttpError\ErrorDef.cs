﻿/// <summary>
/// ErrorDef
/// </summary>
/// <remarks>
/// 2021/5/12 18:00:05: 创建. 王正勇 <br/>
/// 用于处理错误消息
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    [Serializable]
    /// <summary>
    /// ErrorDef
    /// </summary>
    public class HttpError
    {
        public string code;
        public string message;
        public void PromptErrorMessage()
        {
            GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, Api.NTR(message));
        }
    }
    [Serializable]
    public class ErrorSerial
    {
        public HttpError error;
    }
}
