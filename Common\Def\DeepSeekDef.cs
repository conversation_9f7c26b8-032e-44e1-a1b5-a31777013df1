﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    
    [Serializable]
    public class DeepSeekRequest
    {
        public string model;
    
        public List<RequestMessages> messages;
    
        public bool stream;
    }

    [Serializable]
    public class RequestMessages
    {
        /// <summary>
        /// 
        /// </summary>
        public string role;

        /// <summary>
        /// 
        /// </summary>
        public string content;
    }

    [Serializable]
    public class DeepSeekResponse
    {
        public string id ;
        //public string object ;
        public int created ;
        public string model ;
        public List<Choice> choices ;
        public Usage usage ;
        public string system_fingerprint ;
    }

    [Serializable]
    public class Choice
    {
        public int index ;
        public ResponseMessages message;
        public string logprobs;
        public string finish_reason;
    }

    [Serializable]
    public class ResponseMessages
    {
        public string role;

        public string content;

        public string reasoning_content;
    }

    [Serializable]
    public class Usage
    {
        public int prompt_tokens ;
        public int completion_tokens ;
        public int total_tokens ;
        public PromptTokensDetails prompt_tokens_details ;
        public CompletionTokensDetails completion_tokens_details ;
        public int prompt_cache_hit_tokens ;
        public int prompt_cache_miss_tokens ;
    }

    [Serializable]
    public class PromptTokensDetails
    {
        public int cached_tokens ;
    }

    [Serializable]
    public class CompletionTokensDetails
    {
        public int reasoning_tokens ;
    }

}