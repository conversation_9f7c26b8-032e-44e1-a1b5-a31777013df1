﻿/// <summary>
/// Tank
/// </summary>
/// <remarks>
/// 2021.3.26: 创建. 谌安 <br/>
/// 载具<br/>
/// </remarks>
using GLib.Common;
using System;
using System.Runtime.InteropServices;
using System.Text;
using UnityEngine;
using GLib;
using Google.Protobuf;
using game.common;
using game.scene;
using System.Collections.Generic;
using static Game.Entity.Entity_CreateEntity.Types;
using Game.Entity;

namespace GLib.Client.Entity
{
	public class CTank : ITank
    {
        // UID
	    Int64					    m_uid;

        string                      m_guid;
        //机器人目标id
        string                      m_tragetID;
        //归属角色guid
        uint                      m_robotMasterId;

	    // 数值属性
	    int[]						m_nNumProp;

	    // 怪物称号
	    string						m_szTitle;

	    // 实体类型
	    CEntityClass				m_EntityClass;

	    // 信号槽
	    CMessageSlot				m_MessageSlot;

	    // 实体部件
	    IEntityPart[] 				m_pEntityPart;


        // 实体技能部件
        Dictionary<long,IEntityPart> m_pSkillPart;

        // 实体视图ID
        UInt32                      m_nEntityViewID;

	    // 死亡标志
	    bool						m_bDieFlag;

        // 模型id
        int                         m_nSkinID;

        // 是否为虚拟怪物
        bool						m_bIsDeceitFlag;

	    // 本载具配置信息
	    //STankSchemeInfo				m_pTankSchemeInfo;

	    // 怪物等级
	    Int32						m_lLevel;

		//3D坐标位置
		Vector3						m_Position;

		//3D朝向
		Vector3						m_Forward;
        // 实体类型
        EMEntityType m_entitiyType;
        //实体ID
        int m_entitiyID = 0;
        /// <summary>
        /// 3D移动管理器
        /// </summary>
        protected C3DMoveManager m_3DMoveManager;
        //实体工厂配置ID（服务器刷怪表ID）
        int m_entityFactoryConfigID = 0;
        //称号格式化
        //TitleFormat m_titleFormat;


        /** 
        @param   
        @param   
        @return  
        */
        public CTank()
        {
	        // UID
	        m_uid =DGlobalGame.INVALID_UID;

            m_guid = "";

            // 实体视图ID
            m_nEntityViewID = 0;

	        // 死亡标志
	        m_bDieFlag = false;

	        // 怪物称号
	        m_szTitle="";

	        // 是否为虚拟怪物
	        m_bIsDeceitFlag = false;

	        // 本载具配置信息
	        //m_pTankSchemeInfo = null;

	        // 怪物等级
	        m_lLevel = 0;

			m_Position = Vector3.zero;

			m_Forward = Vector3.zero;

        
        }

        public void Init()
        {
            m_EntityClass = new CEntityClass();

            // 信号槽
            m_MessageSlot = new  CMessageSlot(DGlobalMessage.MSG_ACTION_MAXID);

            // 数值属性
            m_nNumProp = new int[(int)eEntityProp.EEntityMax];
			for (int i = 0; i < m_nNumProp.Length; i++ )
			{
				m_nNumProp[i] = 0;
			}

			// 实体部件
			m_pEntityPart = new IEntityPart[(int)EMENTITYPART.ENTITYPART_ENTITY_MAXID];
			for (int i = 0; i < m_pEntityPart.Length; i++ )
			{
				m_pEntityPart[i] = null;
			}

            m_pSkillPart = new Dictionary<long, IEntityPart>();

            // 信号槽
            m_MessageSlot.Init(40);

            //移动管理器
            m_3DMoveManager = new C3DMoveManager();
            m_3DMoveManager.Create(this);

			//m_titleFormat = new TitleFormat();
			//m_titleFormat.Create(this);

        }

		/// <summary>
		/// 获取3D移动实体管理器
		/// </summary>
		/// <returns></returns>
		public I3DMoveManager Get3DMoveManager()
		{
            return m_3DMoveManager;
		}

        /** 释放,会释放内存
        @param   
        @param   
        @return  
        */
        public void Release()
        {
			//if (m_titleFormat != null)
			//	m_titleFormat.Release();
	        if(m_bIsDeceitFlag)
	        {
		        // 从实体世界中移除
				CEntityWorld entityWorld = (CEntityWorld)GHelp.GetEntityClient().GetEntityWorld();
				entityWorld.RemoveDeceitEntity(this);

		        // 是否为虚拟人物
		        m_bIsDeceitFlag = false;

		        // 消息槽
		        m_MessageSlot.Close();
			}
	        else
	        {
		        ///////////////////////////////////////////////////////////////////
		        // 发送事件
		        /*SEventEntityDestroryEntity_C eventdestroryentity;
		        eventdestroryentity.uidEntity = m_uid;	
		        byte bSrcType = GetEventSourceType();
                GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
			        eventdestroryentity);*/

		        ///////////////////////////////////////////////////////////////////
		        // 释放部件
		        for(int i = 0; i <(int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; i++)
		        {
			        if(m_pEntityPart[i] != null)
			        {
						m_pEntityPart[i].Release();
				        m_pEntityPart[i]=null;
			        }
		        }

                foreach (KeyValuePair<long, IEntityPart> skillPart in m_pSkillPart)
                {
                    skillPart.Value.Release();
                }
                m_pSkillPart.Clear();

		        ///////////////////////////////////////////////////////////////////
		        // 从实体世界中移除
		        ((CEntityClient)GHelp.GetEntityClient()).Remove(this);

                EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
                Destroy.ENTITY_ID = GetEntityViewID();
                Destroy.ENTITY_UID = m_uid;

                // 移除实体视图
                GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY,0, "", Destroy);
                
                // 消息槽
                m_MessageSlot.Close();

				//ITankClient pTankClient = GlobalGame.Instance.TankClient;
				// 删除动态怪物的相关配置
				/*if (pTankClient != null && m_pTankSchemeInfo != null && m_pTankSchemeInfo.lCustomType > 0)
				{
					pTankClient.DelTankInfoFromBornMap(m_pTankSchemeInfo.lTankID, m_pTankSchemeInfo.lLevel);
				}*/

		        // 因为在载具死亡的时候，是在本指针里的“公共部件”里处理，如果
		        // 直接在此部件里直放释放，会导致非法，因为m_MessageSlot还在循环，
		        // 所以必须等其循环完，才能释放本身.
		       // CPointerReleaseTimer  pReleaseTimer = new CPointerReleaseTimer();
		       // pReleaseTimer.Create(this);
	        }
        }

		/// <summary>
		/// 还原,不释放对象，只将状态还原到创建时状态
		/// </summary>
		public void Restore()
		{
			TRACE.ErrorLn("CTank::Restore 未实现");
		}

        /** 创建
        @param   
        @param   
        @return  
        */
        public bool Create()
        {

            // 实体类型
            m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Tank);

			// 创建实体视图 
			if (!CreateView())
			{
				return false;
			}

            // 取得配置中心
            //ITankClient pTankClient = GameHelp.GetTankClient();
           // if (pTankClient == null)
            {
          //      return false;
            }

            // 取得载具配置	
            /*UInt32 nTankID = (UInt32)GetNumProp((uint)EMCREATURE_PROP.CREATURE_TANK_ID);

            m_pTankSchemeInfo = (STankSchemeInfo)(pTankClient.GetTankSchemeInfo(nTankID));
            if (m_pTankSchemeInfo == null)
            {
                return false;
            }

            if (m_pTankSchemeInfo.lCustomType > 0)
            {
                STankSchemeInfo tankSchemeInfo = new STankSchemeInfo();
                if (pTankClient.GetTankSchemeInfo((int)nTankID, (int)m_lLevel,ref tankSchemeInfo))
                {
                    m_pTankSchemeInfo = tankSchemeInfo;
                }
                else
                {
                    m_pTankSchemeInfo = null;// 此处需要清除，否则Release时，会调用DelMonsterInfoFromBornMap
                    return false;
                }

                m_pTankSchemeInfo = (STankSchemeInfo)(pTankClient.AddTankInfoToBornMap(m_pTankSchemeInfo));
                if (m_pTankSchemeInfo == null)
                {
                    return false;
                }
            }*/

            // 添加到实体世界中
            if (!((CEntityClient)GHelp.GetEntityClient()).Add(this))
			{
				return false;
			}

            // 公共汽车,坐骑不显示血条

			/*IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory != null)
			{
				if (m_pTankSchemeInfo.nCarryType == (int)EMTANK_CARRYTYPE.TANK_CARRYTYPE_BUS || m_pTankSchemeInfo.nCarryType == (int)EMTANK_CARRYTYPE.TANK_CARRYTYPE_BMW)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName | (uint)EntityFlags.flagDrawHP);
				}
				else
				{
					pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName | (uint)EntityFlags.flagDrawHP);
				}

				if ((m_pTankSchemeInfo.nOpinionFlag & (int)ETankOpinionFlag.TANK_OPINION_NO_SELECT) > 0)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagSelectable);
				}
			}*/

			// 移动速度
			ChangeMoveStyle();

            // 设置怪物名字
            //ShowEntityName();

			// 创建默认出生光效
			/*if (m_pTankSchemeInfo.lInitFlashID > 0)
			{
                SkillEffectContext context = GHelp.GetObjectItem<SkillEffectContext>();
				context.id = (uint)m_pTankSchemeInfo.lInitFlashID;
				context.target = context.src = m_nEntityViewID;
				GHelp.sendEntityCommand(m_nEntityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_ADD_LIGHTING, m_pTankSchemeInfo.lInitFlashID, "", context);
			}*/

			// 插旗
			//GameHelp.sendEntityCommand(m_nEntityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_CHANGE_FLAG, GetNumProp((UInt32)EMCREATURE_PROP.CREATURE_PROP_NATION), "", null);

            /*int nResID= GetNumProp((UInt32)EMCREATURE_PROP.CREATURE_PROP_NATION);
			if (nResID > 0 && nResID <= nNationFlag.Length)
			{
				GHelp.GetSkinView().ChangeFlag(m_nEntityViewID, nNationFlag[nResID - 1]);
			}*/
	        return true;
        }

		/// <summary>
		/// 正在飞行
		/// </summary>
		/// <returns></returns>
		public bool IsFlying()
		{
			//UInt32 dwMask = (UInt32)GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_DEGREEMASK);	// 身份标识
			//if (((int)EMtDegreeMask.tDegreeMask_Flying & dwMask) != 0)
			//{
			//	return true;
			//}
			return false;
		}

        /** 构建虚拟载具
        @param   
        @param   
        @return  
        */
        public bool Deceit(Int64 uid)
        {
	        if(uid ==DGlobalGame.INVALID_UID)
	        {
		        return false;
	        }

	        // UID
	        m_uid = uid;

	        // 实体类型
	        m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Tank);

	        // 添加到实体世界中
			//CEntityWorld entityWorld = (CEntityWorld)GHelp.GetEntityClient().GetEntityWorld();
			//if (!entityWorld.AddDeceitEntity(this))
	        {
		   //     return false;
	        }

	        // 是否为虚拟人物
	        m_bIsDeceitFlag = true;

	        return true;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public string GetName()
        {
            /*if(m_pTankSchemeInfo == null)
	        {
		        return "";
	        }

	        return m_pTankSchemeInfo.szName;*/
            //暂时获取第一个机器人的名字
            return "";
        }

        /** 
        @param   
        @param   
        @return  
        */
        public void ShowEntityName()
        {	
            
        }

        /** 调整怪物速度
        @param   
        @param   
        @return  
        */
        public void ChangeMoveStyle()
        {
			//获取移动速度
			//float fSpeed = GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_CUR_MOVE_SPEED) / DGlobalGame.FLOAT_SCALE_SIZE;
			//设置移动速度
			//GHelp.sendEntityCommand(m_nEntityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_SET_SPEED, (int)(fSpeed * 100), "", null);
        }

        /** 取得实体类型
        @param   
        @param   
        @return  
        */
        public IEntityClass  GetEntityClass()
        {
	        return m_EntityClass;
        }

        public EMEntityType GetEntityType()
        {
            return m_entitiyType;
        }

        /** 取得UID
        @param   
        @param   
        @return  
        */
        public Int64  GetUID()
        {
	        return m_uid;
        }

        public string GetStrGUID()
        {
            return m_guid;
        }

		/// <summary>
		/// 获取3D坐标
		/// </summary>
		/// <returns></returns>
		public Vector3 GetPosition()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
			Vector3 pos = pEntityFactory.GetPosition(m_nEntityViewID);
			return pos;
		}

        /// <summary>
        /// 设置3D坐标
        /// </summary>
        public void SetPosition(Vector3 vPos)
		{
            m_Position = vPos;
		}

        /// <summary>
        /// 获取是否正在跳跃
        /// </summary>
        /// <returns></returns>
        public EntityJumpDef GetJumpState()
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return EntityJumpDef.None;
            EntityJumpDef isJump = pEntityFactory.GetJumpState(m_nEntityViewID);
            return isJump;
        }

        /// <summary>
		/// 设置跳跃状态
		/// </summary>
		/// <param name="state"></param>
		/// <returns></returns>
		public bool SetJumpState(EntityJumpDef state)
        {
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
            if (pEntityFactory == null)
                return false;
            pEntityFactory.SetJumpState(m_nEntityViewID, state);
            return true;
        }

        /// <summary>
        /// 获取朝向
        /// </summary>
        /// <returns></returns>
        public Vector3 GetForward()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
			Vector3 forward = pEntityFactory.GetForward(m_nEntityViewID);
			return forward;
		}

		/// <summary>
		/// 设置朝向
		/// </summary>
		/// <param name="vForward"></param>
		public void SetForward(Vector3 vForward)
		{
            m_Forward = vForward;
		}

		/// <summary>
		/// 获取移动速度
		/// </summary>
		/// <returns></returns>
		public float GetMoveSpeed()
		{
			return GetNumProp((uint)eEntityProp.EEntityMoveSpeed) / DGlobalGame.FLOAT_SCALE_SIZE;
		}

        /** 设置数值属性
        @param   nValue ：属性值
        @param   
        @return  
        */
        public bool SetNumProp(uint dwPropID, int nValue)
        {
	        // 要转成内部tankpropid
	       
	        return true;
        }

        /** 取得数值属性
        @param   
        @param   
        @return  
        */
        public int GetNumProp(uint dwPropID)
        {
            return m_nNumProp[dwPropID];

        }

        /** 设置字符属性
        @param   pszValue ：属性值
        @param   
        @return  
        */
        public bool SetStrProp(uint dwPropID, string pszValue)
        {
            int _value = 0;
            GHelp.ConvertEntityPropInt((eEntityProp)dwPropID, pszValue, ref _value);
            m_nNumProp[dwPropID] = _value;
            return true;
        }
        public int GetEntityFactoryConfigID()
        {
            return m_entityFactoryConfigID;
        }
        /** 批量更新属性
        @param   
        @param   
        @return  
        */
        public bool BatchUpdateProp(IMessage pszProp, int nLen)
        {

            int nInLen = nLen;
            EntityInfo other = pszProp as EntityInfo;
            m_uid = Api.GuidCInt(other.Guid);
            m_guid = other.Guid;
            m_tragetID = other.Guid;
            m_Position = new UnityEngine.Vector3(other.Position.X, other.Position.Y, other.Position.Z);
            // m_szName = other.Name;
            m_entitiyType = (EMEntityType)other.EntityType;
            m_entitiyID = other.ConfigId;
            m_entityFactoryConfigID = other.EntityFactoryConfigID;
            if (!string.IsNullOrEmpty(other.RobotMasterId))
            {
                m_robotMasterId = Api.GuidCInt(other.RobotMasterId);
            }
            else
            {
                m_robotMasterId = 0;
            }

            foreach (ProItem item in other.Props)
            {
                SetStrProp((uint)item.PropType, item.PropValue);
            }
            return true;
        }

        /// <summary>
        /// 检查是否有主人
        /// </summary>
        public void CheckRobotMaster()
        {
            if (m_robotMasterId > 0)
            {
                ICreature entity = GHelp.GetEntityByUID(m_robotMasterId) as ICreature;
                //上载具
                if (entity != null)
                {
                    IPersonTankPart pPersonTankPart = (IPersonTankPart)entity.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
                    if (pPersonTankPart != null)
                    {
                        pPersonTankPart.EnterTank(entity, this);
                    }
                }
                else
                {
                    TRACE.ErrorLn("RobotMaster error:" + m_robotMasterId);
                }
            }
        }

        public IMessage GetConfigInfo()
        {
            return GHelp.GetConfigItem(m_entitiyType, 0);
        }

        /** 消息
        @param   
        @param   
        @return  true：正常执行；false：被否决  
        */
        public bool OnMessage(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
            return  m_MessageSlot.Fire(dwMsgID, pGameMsgHead, pszMsg);
        }

        /** 订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageVoteSink  pVoteSink, string pszDesc)
        {
            return m_MessageSlot.Subscibe(dwMsgID, pVoteSink, pszDesc);
        }

        /** 取消订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageVoteSink  pVoteSink)
        {
            return m_MessageSlot.UnSubscibe(dwMsgID, pVoteSink);
        }

        /** 订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink, string pszDesc)
        {
            return m_MessageSlot.Subscibe(dwMsgID, pExecuteSink, pszDesc);
        }

        /** 取消订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink)
        {
            return m_MessageSlot.UnSubscibe(dwMsgID, pExecuteSink);
        }

        /** 增加实体部件
        @param   
        @param    
        @return  
        */
        public bool AddEntityPart(IEntityPart  pEntityPart)
        {
	        if(pEntityPart == null)
	        {
		        return false;
	        }

	        uint nPartID = pEntityPart.GetPartID();
	        if(nPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || nPartID >=(uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
	        {
		        return false;
	        }

	        if(m_pEntityPart[nPartID] != null)
	        {
		        return false;
	        }

	        m_pEntityPart[nPartID] = pEntityPart;

	        return true;
        }

        /** 移除实体部件
        @param   
        @param   
        @return  
        */
        public bool RemoveEntityPart(uint dwPartID)
        {
	        if(dwPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || dwPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
	        {
		        return false;
	        }

	        m_pEntityPart[dwPartID] = null;

	        return true;
        }

        /** 取得实体部件
        @param   
        @param   
        @return  
        */
        public IEntityPart  GetEntityPart(uint dwPartID)
        {
	        if(dwPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || dwPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
	        {
		        return null;
	        }

	        return m_pEntityPart[dwPartID];
        }

        public IEntityPart GetSkillPart(long uid = 1)
        {
            IEntityPart part = null;

            m_pSkillPart.TryGetValue(uid, out part);

            return part;
        }

        /// <summary>
		/// 获得全部的技能部件
		/// </summary>
		/// <returns></returns>
		public Dictionary<long, IEntityPart> GetAllSkillParts()
        {
            return m_pSkillPart;
        }

        /// <summary>
		/// 获得或者创建技能部件
		/// </summary>
		/// <param name="dwPartID"></param>
		/// <param name="uid"></param>
		/// <returns></returns>
		public IEntityPart GetOrCreateSkillPart(long skillID = 1)
        {
            IEntityPart part = null;

            return part;
        }

        public bool AddSkillPart(IEntityPart pEntityPart, long skillID = 1)
        {
            if (pEntityPart as ISkillPart == null)
            {
                return false;
            }

            if (m_pSkillPart.ContainsKey(skillID))
            {
                return false;
            }

            m_pSkillPart.Add(skillID, pEntityPart);

            return true;
        }

        /** 是否正在移动
        @param   
        @param   
        @return  
        */
        public bool IsMoving()
        {
	        if(m_nEntityViewID == 0)
	        {
		        return false;
	        }
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return false;
			return pEntityFactory.isMoving(m_nEntityViewID);
        }

        /** 取得实体视图ID接口
        @param   
        @param   
        @return  
        */
        public UInt32 GetEntityViewID()
        {
	        return m_nEntityViewID;
        }

        /** 取得事件源类型,SOURCE_TYPE_PERSON, SOURCE_TYPE_MONSTER ... ...
        @param   
        @param   
        @return  
        */
        public byte GetEventSourceType()
        {
	        return (byte)EMSOURCE_TYPE.SOURCE_TYPE_TANK;
        }

        /** 属性是否变化
        @param   
        @param   
        @return  
        */
        public bool IsNumPropChanged(uint dwPropID)
        {
	        return false;
        }


        /** 属性是否变化处理结束,清理标识
        @param   
        @param   
        @return  
        */
        public bool ClsNumPropChanged()
        {
	        return false;
        }

        /** 设置死亡标志
        @param   
        @param   
        @return  
        */
        public void SetDieFlag()
        {
	        m_bDieFlag = true;
        }

        public void LockVisitEnable(bool lock_visit)
        {

        }

        // 创建显示层实体
        public bool CreateView()
        {
            EntityViewItem item = GHelp.GetObjectItemEx<EntityViewItem>();
            if (!GetBasicViewInfo(ref item))
            {
                TRACE.ErrorLn("createView getBasicViewInfo failed!");
                return false;
            }
			
			//角度
			//item.Angle = GetNumProp((uint)EMCREATURE_PROP.CREATURE_TANK_ANGLE);
           
            // 创建EntityView
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, 0, "", item);
            m_nEntityViewID = item.EntityViewID;
            if (m_nEntityViewID == 0)
            {
                TRACE.ErrorLn("Tank::CreateView invalid viewID=" + m_uid);
                return false;
            }
            Vector3 pos = m_Position;
            if (item.nRobotMasterID > 0)
            {//让机器人的坐标等于归属者
              // IEntity masterEntity = GHelp.GetEntityByUID(item.nRobotMasterID);
               // pos = masterEntity.GetPosition();
            }
            // 设置出生坐标
            cmd_creature_rigidbody_sync data = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
            data.bNotGround = false;
            data.nEntityID =GetEntityViewID();
            data.fPosition_x = pos.x;
            data.fPosition_y = pos.y;
            data.fPosition_z = pos.z;
            GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", data);
            m_nSkinID = item.nSkinID;
            GHelp.ChangeEntitiyPart(GetEntityViewID(), EntityParts.EntityPart_Body, item.nSkinID,item.strTargetID);
            
            TRACE.TraceLn("createView Tank id=" + m_uid);
            GHelp.RecycleObjectItemEx<EntityViewItem>(item);
            return true;
        }

        //获得阵营
        public int GetCamp()
        {
            return GetNumProp((uint)eEntityProp.EEntityCamp);
        }

        public bool GetBasicViewInfo(ref EntityViewItem item)
        {
            item.EntityViewID = 0;
            item.UID = m_uid;
            // 只需要传递实体类型、默认皮肤ID、名字即可
           
            item.byIsHero = 0;
            
			item.szName = "";
            item.strTargetID = m_tragetID;
            item.EntityType = (byte)EMEntityType.typeRobot;
            item.nRobotMasterID = m_robotMasterId;
            item.bCreateImmediate = true;
            item.Angle = GetNumProp((int)eEntityProp.EEntityAngle);
            m_nSkinID = item.nSkinID = 1002;//暂时载具使用1002进行显示处理
            item.fMoveSpeed = GetMoveSpeed();
            item.EntityFactoryConifgID = m_entityFactoryConfigID;
            //item.prefabPath = GHelp.GetModelPathByType(m_entitiyType, m_entitiyID);
            return true;
        }

        public int GetModelID()
        {
            return m_nSkinID;
        }
        public int GetConfigID()
        {
            return 0;
        }

        public void sendCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
        {
            // 部件接收命令
            for (int id = 0; id < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; ++id)
            {
                if (m_pEntityPart[id] != null)
                {
                    if (m_pEntityPart[id].onCommand(cmdid, nParam, strParam, ptrParam))
                    {
                        return;
                    }
                }
            }
        }
    }
}