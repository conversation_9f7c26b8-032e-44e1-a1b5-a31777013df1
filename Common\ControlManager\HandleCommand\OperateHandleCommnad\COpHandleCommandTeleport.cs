﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandTeleport : IHandleCommand
    {
        private GameObject m_target;
        private Vector3 m_orgPoint;
        private Vector3 m_orgRotate;
        private Vector3 m_orgScale;
        bool m_isEnd = false; // 是否不正确的执行完指令
        private bool m_isPlay = false;
        private bool m_isLocal = false;
        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;
        public COpHandleCommandTeleport(SOpHandleCommand_Teleport data)
        {
            m_target = data.target;
            m_orgPoint = data.orgPoint;
            m_orgRotate = data.orgRotate;
            m_orgScale = data.orgScale;
            m_isLocal = data.isLocal;
            runInstance = data.runInstance;
            m_isPlay = false;
            m_isEnd = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpPlayAnimation;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isEnd = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }

            if (m_target == null)
            {
                m_isEnd = true;
                return false;
            }
        
            if (m_orgPoint.x > -9999)
            {
                if (m_isLocal)
                {
                    m_target.transform.localPosition = m_orgPoint;
                }
                else
                {
                    m_target.transform.position = m_orgPoint;
                }
            }
            if (m_orgRotate.x > -9999)
            {
                m_target.transform.localEulerAngles = m_orgRotate;
            }
            if (m_orgScale.x > -9999)
            {
                m_target.transform.localScale = m_orgScale;
            }

            return true;
        }

        public void update()
        {
        }
    }
}
