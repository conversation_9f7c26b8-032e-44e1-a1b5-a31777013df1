﻿using game.schemes;
using System;
using System.Collections.Generic;

namespace GLib.Common
{
    public class EffectViewItem_LightingEffect : IEffectViewItem
    {

        public void PlayEffect(UInt32 viewID, EffectView.Types.Item effectData, EffectViewExtraInfo extraInfo)
        {
            // 效果列表
            if (effectData.DoEffectList != null)
            {
                for (int i = 0; i < effectData.DoEffectList.Count; i++)
                {
                    // 取出的结构存在多次应用，需要使用引用计数
                    SkillEffectContext context = GHelp.GetObjectItem<SkillEffectContext>();
                    context.id = (uint)effectData.DoEffectList[i];
                    context.src = extraInfo.srcViewID;
                    context.target = extraInfo.targetViewID;
                    context.feedbackID = extraInfo.feedbackID;
                    //context.coordSpace = (CoordinateSpace)effectData.lightingCoordinateSpace;
                    GHelp.sendEntityCommand(viewID, (int)EntityLogicDef.ENTITY_TOVIEW_ADD_LIGHTING, effectData.DoEffectList[i], "", context);
                }
            }
        }

        public void StopEffect(UInt32 viewID, EffectView.Types.Item effectData, EffectViewExtraInfo extraInfo)
        {
            if (effectData.DoEffectList != null)
            {
                for (int i = 0; i < effectData.DoEffectList.Count; i++)
                {
                    SkillEffectContext context = GHelp.GetObjectItem<SkillEffectContext>();
                    context.id = (uint)effectData.DoEffectList[i];  // 效果Id;
                    context.feedbackID = extraInfo.feedbackID;
                    GHelp.sendEntityCommand(viewID, (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, effectData.DoEffectList[i], "", context);
                }
            }
        }
    }
}
