﻿/// <summary>
/// EffectViewItem_Animation
/// </summary>
/// <remarks>
/// 2021.5.21: 创建. 谌安 <br/>
/// 效果表现改变动作<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using game.schemes;
using GLib.Common;

namespace GLib.Common
{
    public class EffectViewItem_Animation : IEffectViewItem
    {

        public void PlayEffect(UInt32 viewID, EffectView.Types.Item effectData, EffectViewExtraInfo extraInfo)
        {

            // 播放动作
            PlayAnimationContext anicontext = GHelp.GetObjectItem<PlayAnimationContext>();
            anicontext.name = effectData.DoActionId;
            /*if (effectData.fNormalizedStopTime != -1)
            {
                anicontext.bActionStop = true;
                anicontext.fNormoalizedStopTime = effectData.fNormalizedStopTime;
                anicontext.canbreak = false;
            }
            else
            {
                anicontext.bhide = effectData.bhide;
            }*/
            GHelp.sendEntityCommand(viewID, (int)EntityLogicDef.ENTITY_TOVIEW_PLAY_ANIMATION, 0, "", anicontext);

        }

        public void StopEffect(UInt32 viewID, EffectView.Types.Item effectData, EffectViewExtraInfo extraInfo)
        {
            if (!string.IsNullOrEmpty(effectData.DoActionId))
                GHelp.sendEntityCommand(viewID, (int)EntityLogicDef.ENTITY_TOVIEW_BREAK_ATTACK_ANIMATION, 0, effectData.DoActionId, null);
        }
    }
}
