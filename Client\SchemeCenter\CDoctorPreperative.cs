﻿/// <summary>
/// CourseHomepage
/// </summary>
/// <remarks>
/// 2023/02/07 15：00：00: 创建. 郭才志 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CDoctorPreperativecs : ISchemeNode, IDoctorPreperative
    {
        public const string Task_Info = "DoctorPreperative";
        private Dictionary<int, List<DoctorPreperativeInfo>> m_TaskInfoById;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Action<List<DoctorPreperativeInfo>> m_action;
        private List<DoctorPreperativeInfo> m_doctorPreperativeInfos;
        public CDoctorPreperativecs()
        {
            m_TaskInfoById = new Dictionary<int, List<DoctorPreperativeInfo>>();
            m_doctorPreperativeInfos = new List<DoctorPreperativeInfo>();
        }
        ~CDoctorPreperativecs()
        {
        }
        public bool Create()
        {
            //if (!LoadScheme())
            //{
            //    return false;
            //}
            return true;
        }
        public bool LoadScheme(int CourseId, Action<List<DoctorPreperativeInfo>> action = null)
        {
            m_action = action;
            if (!m_TaskInfoById.ContainsKey(CourseId))
            {
                string strPath = "CourseCsv/" + CourseId + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                List<DoctorPreperativeInfo> datas;
                m_TaskInfoById.TryGetValue(CourseId, out datas);
                for (int i = 0; i < datas.Count; i++)
                {
                    if(!m_doctorPreperativeInfos.Contains(datas[i]))
                    {
                        m_doctorPreperativeInfos.Add(datas[i]);
                    }
                }
                m_action?.Invoke(datas);
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            List<DoctorPreperativeInfo> datas = null;
            m_TaskInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new List<DoctorPreperativeInfo>();
                m_TaskInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                DoctorPreperativeInfo preperativeInfo = new DoctorPreperativeInfo();
                preperativeInfo.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.StepID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.ChooseName = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.IsAnswer = pCSVReader.GetInt(nRow, tmp_col++, 0);
                datas.Add(preperativeInfo);
            }
            m_action?.Invoke(datas);
            for (int i = 0; i < datas.Count; i++)
            {
                if (!m_doctorPreperativeInfos.Contains(datas[i]))
                {
                    m_doctorPreperativeInfos.Add(datas[i]);
                }
            }
            return true;
        }


        public void Release()
        {
            m_doctorPreperativeInfos.Clear();
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
        }

        //public List<DoctorPreperativeInfo> GetCourseHomepageInfoByID(int couseID,int tID)
        //{
        //    DoctorPreperativeInfo info = null;
        //    Dictionary<int, DoctorPreperativeInfo> datas = new Dictionary<int, DoctorPreperativeInfo>();
        //    m_TaskInfoById.TryGetValue(couseID, out datas);
        //    datas.TryGetValue(tID, out info);
        //    return info;
        //}
        public List<DoctorPreperativeInfo> GetAllDoctorPreperative(int couseID)
        {
            List<DoctorPreperativeInfo> datas;
            m_TaskInfoById.TryGetValue(couseID, out datas);
            return datas;
        }
    }
}
