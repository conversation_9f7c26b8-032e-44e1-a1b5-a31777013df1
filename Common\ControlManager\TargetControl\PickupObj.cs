﻿/// <summary>
/// CPickupObj
/// </summary>
/// <remarks>
/// 2021.4.21: 创建. 谌安 <br/>
/// PickupObj<br/>
/// </remarks>
using System;
using System.Collections;
using UnityEngine;
namespace GLib.Common
{
    public class CPickupObj : IPickupObj, IEventExecuteSink
    {
        //-------------------------------------------------
        //拖动模块注册
        //-------------------------------------------------
        const int BOX_OPENSETTLE_TIME = 60;

        //PickupObj__ = {};

        //bool state = false;
	    Int64 uid	   	= 0;         //宝箱UID
	    int startRequestTime	= 0; //请求打开开始时间
	    int boxOpenStartTime	= 0;
	    int skepId		= 0;          //宝箱栏id	
	    //int timerId	 = 200;           //时钟Id
	    bool autoPick	= false;
	    bool dataRecived	= false	;
	    
        void create()
        {
            //GlobalGame.Instance.TimerManager.AddTimer(this,2008,100, "CPickupObj");

            //GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, (byte)EMSOURCE_TYPE.SOURCE_TYPE_BOX,0 , "PickupObj_OnBoxDestroy");
        }

        //销毁
        void destroy()
        {
            //时钟删除
           // GlobalGame.Instance.TimerManager.RemoveTimer(this, 2008);

            //GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, (byte)EMSOURCE_TYPE.SOURCE_TYPE_BOX, 0);
        }

        //打开宝箱
        //@uid 宝箱uid
        void openBox(Int64 uid,bool autoPick) 
        {
	        //Info("autoPock: "..autoPick)  
	        //bool isAuto = false;
	        //打开箱子
	        //没有请求过,直接通知打开
	        //Info("enter open box")
	        if 	(autoPick)
            {
		       // isAuto = true;
            }


            if (uid == DGlobalGame.INVALID_UID)
            {
                IEntity boxEntity = GHelp.GetEntityByUID(uid);
                if (CommonApi.StartOpenTreasureBox(GHelp.GetHero().GetStrGUID(), boxEntity.GetStrGUID()))
                {
			        this.uid = uid;
                    this.autoPick = autoPick;
                    this.boxOpenStartTime = 0;
                    this.dataRecived = false;
                    this.startRequestTime = Api.GetTickCount();
			      
			        TRACE.DebugLog("request open box");
		        }
		        return;
            }
            //如果是同一个箱子,那么看看请求时间,不能太长了
            /*int slapse = Api.GetTickCount() - startRequestTime;
           //如果在自动拾取的情况下，如果已经有打开的    
           //那么这个打开的必须要关闭了才能够继续请求

           if (autoPick  && skepId != 0)
           {
               //不过还是增加一个时间限制，否则如果搞死掉了怎么办
               //就是16秒内请求我将忽略这次请求
               //足够长的时间

               if (slapse <16)
               {
                   return;
               }
           }

          if (this.uid == uid)
           {
               return;
           }

            //如果请求太频繁,同样不能受理
            if (slapse < 1)
            {
		        TRACE.DebugLog("openBox error,4秒限制 slapse = "+slapse);
		        return;
            }*/

            closeBox();
            //通知打开
            IEntity boxEntity1 = GHelp.GetEntityByUID(uid);
            if (CommonApi.StartOpenTreasureBox(GHelp.GetHero().GetStrGUID(), boxEntity1.GetStrGUID()))
            {
		        this.uid = uid;
                this.autoPick = autoPick;
                this.boxOpenStartTime = 0;
                this.dataRecived = false;
                this.startRequestTime = Api.GetTickCount();
	        }
        }

        //设置掉落物品栏id
        public void setSkepId(int skepId)
        {
	        this.skepId = skepId;
        }

        //取得掉落物品栏id
        int getSkepId()
        {
	        return skepId;
        }

        //通知start open skep
        void startOpen()
        {
	        boxOpenStartTime = Api.GetTickCount();
	        //取消这次打开操作
	        if (uid ==DGlobalGame.INVALID_UID)
            {
		        closeBox();
	        }
        }

        //数据到来通知
        void dataReceived()	
        {
	        if (dataRecived == false)
            {
		        dataRecived	= true;
		        TRACE.DebugLog("dataReceived");
	        }
        }

        //关闭宝箱
        //记得关闭相关的物品栏
        void closeBox()
        {
            if (skepId == 0  && uid != 0)
            {
    	        return;
            }
    
	        TRACE.DebugLog("closeBox enter into");
	        
	        //关闭箱子		
	        if (uid != 0)
            {
	            TRACE.DebugLog("closeBox,box uid = "+uid);
	            //DelayExecute(CloseBox,uid)
		       // CommonApi.CloseBox(uid);		
	        }
	        //恢复变量到无效状态
	        skepId = 0;
	        uid = 0;
	        autoPick = false;
	        dataRecived	= false	;
	        startRequestTime = 0;
	        boxOpenStartTime = 0;
        }

        //关闭宝箱
        //记得关闭相关的物品栏
        void closeBoxOnly()
        {
    
	        TRACE.DebugLog("closeBox enter into");
	
	        //关闭箱子		
	        if (uid != 0 )
            {
	            TRACE.DebugLog("closeBox,box uid = "+uid);
               // CommonApi.CloseBox(uid);		
	        }
	        uid = 0;
	        autoPick = false;
	        dataRecived	= false	;
	        startRequestTime = 0;
	        boxOpenStartTime = 0;
        }

        //宝箱删除事件
        void onBoxDestroy(object pContext)
        {
           /* if((pContext is SEventEntityCreateEntity_C)==false)
            {
                return;
            }

            SEventEntityCreateEntity_C data = (SEventEntityCreateEntity_C)pContext;
	        if (data.uidEntity == uid)
            {
			    //关闭宝箱
		        closeBox();
	        }*/
        }

        public void __OpenBox__(Int64 uid,bool isPick)
        {
	 	    openBox(uid,isPick);
        }

        void PickupObj_Show()
        {

        }

        void PickupObj_Hide()
        {

        }

        //加载,创建对象
        public bool PickupObj_Load()
        {
            create();
            
            return true;
        }

        //卸载,删除对象
        public void PickupObj_Unload()
        {
            destroy();
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
          //  if (wEventID==(ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY&&bSrcType== (byte)EMSOURCE_TYPE.SOURCE_TYPE_BOX)
            {
                //onBoxDestroy(pContext);
            }
        }

        public bool PickupItem()
        {
            return true;
        }
    }
}