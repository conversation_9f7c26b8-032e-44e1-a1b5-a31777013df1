﻿/// <summary>
/// Buff
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// BUFF 客户端<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using game.schemes;
using Google.Protobuf.Collections;
using GLib.Common;

namespace GLib.Client
{

    public class CBuff : IB<PERSON>, ITimerHandler
    {
	    enum ETimerEventID
	    {
		    ETimerEventID_Life = 0,	// buff生命周期
	    };

        // buff index
	    UInt32					m_dwBuffIndex;

	    // buff id
	    UInt32					m_dwBuffID;

	    // 等级
	    UInt32					m_dwLevel;

	    // 总时间
	    UInt32					m_dwTime;

	    // 启动tick
	    UInt32					m_dwStartTick;	

	    // 图标ID
	    int						m_nIconID;	

	    // buff名字 
	    string					m_szName;

	    // 描述
	    string					m_szDesc;

	    // 随机效果描述
	    string					m_szRandDesc;

	    // 光效ID
	    List<UInt32>			m_listFlashID;

        // 光效类型
        List<byte>               m_listFlashType;

	    // 随机光效ID
	    UInt32					m_dwRandFlashID;

	    ///////////////////////////////////////////
	    // BUFF部件
	    CBuffPart				m_pBuffPart;

		// 所有效果
		List<IEffect> m_listEffect;

		string m_strGuid;
		uint m_uid;
        /** 
        @param   
        @param   
        @return  
        */
        public CBuff()
        {
	        // buff index
	        m_dwBuffIndex = 0;

	        // buff id
	        m_dwBuffID = DGlobalGame.INVALID_BUFF_ID;

	        // 等级
	        m_dwLevel = 0;

	        // 总时间
	        m_dwTime = 0;

	        // 启动tick
	        m_dwStartTick = 0;

	        // 图标id
	        m_nIconID = 0;

	        // buff名字 
	        m_szName = "";	

	        // 描述
	        m_szDesc = "";	

	        // 随机效果描述
	        m_szRandDesc = "";	

	        // BUFF部件
	        m_pBuffPart = null;

	        // 所有效果
	        m_listEffect = new List<IEffect>();

	        // 光效ID
			m_listFlashID = new List<uint>();
			m_listFlashType = new List<byte>();

	        // 随机光效ID
	        m_dwRandFlashID = 0;

			m_strGuid = "";
			m_uid = 0;
        }

        /** 
        @param   
        @param   
        @return  
        */
        ~CBuff()
        {

        }

        /** 释放所有资源，并且销毁此对像
        @param   
        @param   
        @return    	
        */
        public void Release()
        {
	        // buff index
	        m_dwBuffIndex = 0;

	        // buff id
	        m_dwBuffID = DGlobalGame.INVALID_BUFF_ID;

	        // 等级
	        m_dwLevel = 0;

	        // 总时间
	        m_dwTime = 0;

	        // 启动tick
	        m_dwStartTick = 0;

	        // 图标id
	        m_nIconID = 0;

	        // buff名字 
	        m_szName = "";	

	        // 描述
	        m_szDesc = "";	

	        // 随机效果描述
	        m_szRandDesc = "";	

	        // BUFF部件
	        m_pBuffPart = null;

	        // 光效ID
			m_listFlashID = null;
			m_listFlashType = null;

	        // 随机光效ID
	        m_dwRandFlashID = 0;

			m_strGuid = "";
			m_uid = 0;

			// 结束效果
			foreach (IEffect effect in m_listEffect)
			{
				effect.Release();
			}

	        // 所有效果
	        m_listEffect.Clear();
        }

        /** 
        @param   
        @param   
        @return  
        */
        public bool Create(CBuffPart pBuffPart, UInt32 dwBuffID, RepeatedField<int> effectIDs)
        {
	        if(pBuffPart == null)
	        {
				TRACE.ErrorLn("Buff::Create pBuffPart == null");
		        return false;
	        }

			IEffectClient pEffectClient = GHelp.GetEffectClient();
	        if(pEffectClient == null)
	        {
		        return false;
	        }

            CBuffClient pBuffClient = GHelp.GetBuffClient() as CBuffClient;
            if (pBuffClient == null)
            {
                return false;
            }

			Buff.Types.Item pSchemeInfo = pBuffClient.GetBuffSchemeInfo(dwBuffID);
	        if(pSchemeInfo == null)
	        {
		        return false;
	        }

	        // buff index
	        m_dwBuffIndex = (uint)pSchemeInfo.Id;

	        // buff id
	        m_dwBuffID = (uint)pSchemeInfo.BuffId;

	        // 等级
	        m_dwLevel = (uint)pSchemeInfo.Level;

	        // 启动tick
	        m_dwStartTick = 0;

	        // BUFF部件
	        m_pBuffPart = pBuffPart;

	        // 图标id
	        m_nIconID = pSchemeInfo.IconId;

	        // buff名字 
	        m_szName = pSchemeInfo.Name;	

	        // 描述
	        m_szDesc = pSchemeInfo.Description;	

	        // 光效ID	
           /* if (pLevelInfo.dwFlashIDEx != 0)
            {
                // 参数作为女性角色BUFF外观；不填则默认男女一致，否则女角色优先取此值
		        IEntity pEntity = m_pBuffPart.GetMaster();
		        if(pEntity != null && pEntity.GetEntityClass().IsPerson())
		        {
			        int nSex = pEntity.GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_SEX);
			        if(nSex == (int)EMPERSON_SEX.PERSON_SEX_FEMALE)
			        {
				        m_listFlashID.Add(pLevelInfo.dwFlashIDEx);
			        }
		        }
            }*/

	        // 创建效果
            foreach (UInt32 effectId in effectIDs)
            {
                // 没创建成功不要紧的，有些效果客户端本来就不需要创建
                BuffEffect.Types.Item buffEffectinfo = GlobalGame.Instance.SchemeCenter.GetSchemeEffect().GetEffectSchemeInfo(effectId);
                m_listFlashID.Add((uint)buffEffectinfo.SpecialEffect);
                IEffect pEffect = pEffectClient.Build(effectId, null, 0);
		        if(pEffect != null)
		        {
			        m_listEffect.Add(pEffect);
		        }
            }
			
			return true;
        }

        /** buff id
        @param   
        @param   
        @return  
        */
        public UInt32 GetBuffID()
        {
	        return m_dwBuffID;
        }

        /** 等级
        @param   
        @param   
        @return  
        */
        public UInt32 GetLevel()
        {
	        return m_dwLevel;
        }

        /** 剩余时间，外面要自已处理时间倒数,如果返回0,表示永久状态
        @param   
        @param   
        @return  
        */
        public UInt32 GetLeftTime()
        {
	        if(m_dwTime > 0)
	        {		
		         int nLeftTime = (int)m_dwTime - (Api.GetTickCount() - (int)m_dwStartTick);
				 if (nLeftTime <= 0)
					 nLeftTime = 1;

		         return (uint)nLeftTime;
	        }

	        return m_dwTime;
        }

        /** 图标ID
        @param   
        @param   
        @return  
        */
        public int GetIconID()
        {
	        return m_nIconID;
        }

        /** buff名字
        @param   
        @param   
        @return  
        */
        public string GetBuffName()
        {
	        return m_szName;
        }

        /** buff描述
        @param   
        @param   
        @return  
        */
        public string GetDesc()
        {
	        return m_szDesc;
        }

        /** buff随机描述
        @param   
        @param   
        @return  
        */
        public string GetRandDesc()
        {
	        return m_szRandDesc;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public UInt32 GetIndex()
        {
	        return m_dwBuffIndex;
        }

        public string GetGUID()
        {
            return m_strGuid;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public List<UInt32> GetFlashIDList()
        {
	        return m_listFlashID;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public List<byte> GetFlashTypeList()
        {
            return m_listFlashType;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public UInt32 GetRandFlashID()
        {
	        return m_dwRandFlashID;
        }

        /**
        @purpose          : 定时器触发后回调,你可以在这里编写处理代码
        @param	 dwTimerID: 定时器ID,用于区分是哪个定时器
        @return		      : empty
        */
        public void OnTimer(TimerInfo ti)
        {
	        // 移除状态
            m_pBuffPart.RemoveBuff(this);
        }

        /** 启动效果
        @param   
        @param   
        @return  
        */
        public bool Start()
        {
	        // 启动效果
            foreach (IEffect effect in m_listEffect)
            {
                effect.Start(m_pBuffPart.GetMaster().GetUID());
            }

	        // 启动定时器
	        if(m_dwTime > 0)
	        {	
		        // 启动tick
                m_dwStartTick = (uint)Api.GetTickCount();

		        GHelp.SetTimer((int)ETimerEventID.ETimerEventID_Life,
								          (int)m_dwTime,
								          this, -1,"CBuff");
	        }

	        return true;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public bool Stop()
        {
	        // 结束效果
            foreach (IEffect effect in m_listEffect)
            {
                effect.End();
            }

	        // kill定时器
	        GHelp.KillTimer((int)ETimerEventID.ETimerEventID_Life, this);

	        return true;
        }

        /** 取得总时间
        @param   
        @param   
        @return  
        */
        public int	 GetTotalTime()
        {
	        return (int)m_dwTime;
        }

        public int GetEffectCount()
        {
	        return m_listEffect.Count;
        }

        public int GetEffectIDByIndex( int index )
        {
	        if (index < 0 || index >= m_listEffect.Count)
	        {
		        return 0;
	        }

	        return (int)m_listEffect[index].GetEffectID();
        }

		public void SetBuffGUID(string guid)
		{
			m_strGuid = guid;
			m_uid = Api.GuidCInt(m_strGuid);
		}
    };
}
