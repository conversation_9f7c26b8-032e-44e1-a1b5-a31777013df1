﻿/// <summary>
/// UBB
/// </summary>
/// <remarks>
/// 2019.6.24: 创建. 谌安 <br/>
/// 解析字串中的关键字<br/>
/// </remarks>
using System;
using UnityEngine;
using System.Collections;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using game.schemes;

namespace GLib.Common
{
    public class UBB
    {

        /*
        [[
          关键字的解析类似UBB语法格式，UBB语法一般格式为:
          [命令名]命令参数[/命令名]

          本系统具体支持的命令有:
          [b]  : 字体加粗
          [i]  : 斜体
          [u]  : 字体下划线
          [red]   : 字体变红
          [blue]  : 字体变蓝
          [yellow]: 字体变黄
          [green] : 字体变绿
          [img]   : 图片,参数为客户端图片名
          [url]   : 加入链接,参数为 (requestSmelt?1=aaa)请求精炼 
          [npc]   : 加入npc链接,点击后直接与NPC对话,参数为NPCID或名称
          [monster]:怪物链接,点击后直接去打怪,参数为怪物ID或名称
          [item]  : 物品链接,参数为物品ID或名称
          [itemEx]: 物品链接,带物品图片,参数为物品ID或名称
          [prize] : 显示奖励,参数为奖励ID或名称
          [prizeEx] : 显示奖励图片,参数为奖励ID或名称
          [actor] : 玩家链接,点击后显示玩家tooltip,参数为玩家ID ** 这个其实没什么用,用后面的[hero] **
          [move]  : 移动到指定点,参数为( 1,100,100)华夏城
          [automove]:移动到指定的具体地点
          [mustmove]:必须移动到具体指定的地图，支持跨国寻路链接，镜像地图不分流
          [task]  : 任务链接,参数为任务ID或任务名
          [hero]  : 玩家[hero]id,name[/hero]
          [heroinfo]:要显示当前玩家的信息，目前只支持任务任务对话框和任务列表的任务信息窗口。 add by tanjr
                     格式：[heroinfo]name[/heroinfo] 名字；[heroinfo]vocation[/heroinfo] 职业；[heroinfo]level[/heroinfo] 最高魂等级
          [movetoAutoCollect]  : 移动到指定点自动采集,参数为( 1,100,100)华夏城
          [movetoAutoKill]  : 移动到指定点自动打怪,参数为( 1,100,100)华夏城
          [monsterAutoKill]	：根据monsterID找到位置移动到指定地点开始自动挂机
          [monsterAutoAdvanceTask]	：移动到指定地点推进任务
          [s]   : 自定义字符串列表      [s]a[/s]->#a
          [t]   : 扩展自定义字符串列表  [t]a[/t]->a
          [exp] : 经验图标加数量
         */

        // 品阶对应该的颜色名字
        static string[] gItemColor = { "c0", "c0", "c1", "c2", "c3", "c4", "c5", "c6", "c7", "c8", "c8" };

        // UBB命令列表
        static Dictionary<string, UBBDelegate> UBB_COMMANDS = new Dictionary<string, UBBDelegate>();
        delegate string UBBDelegate(string str);

        static MatchEvaluator m_MatchEvaluator;

        static bool m_bMsg = false; // 是否正在格式化聊天频道消息

        /// <summary>
        /// 设置是否在格式化聊天频道消息
        /// <param name="bMsg">是否正在格式化聊天频道消息</param>
        /// </summary>
        /// <returns>返回老的设置</returns>
        public static bool SetMsg(bool bMsg)
        {
            bool oldMsg = m_bMsg;
            m_bMsg = bMsg;
            return oldMsg;
        }

        public static void Init()
        {

            UBB_COMMANDS.Add("b", UBBCommand_Bold);
            UBB_COMMANDS.Add("i", UBBCommand_Incline);
            UBB_COMMANDS.Add("u", UBBCommand_Underline);
            UBB_COMMANDS.Add("red", UBBCommand_Red);
            UBB_COMMANDS.Add("blue", UBBCommand_Blue);
            UBB_COMMANDS.Add("yellow", UBBCommand_Yellow);
            UBB_COMMANDS.Add("green", UBBCommand_Green);
            UBB_COMMANDS.Add("orange", UBBCommand_Orange);
            UBB_COMMANDS.Add("purple", UBBCommand_Purple);
            UBB_COMMANDS.Add("white", UBBCommand_White);
            UBB_COMMANDS.Add("codese", UBBCommand_Codese);
            UBB_COMMANDS.Add("building", UBBCommand_Building);
            UBB_COMMANDS.Add("move", UBBCommand_Move);
            UBB_COMMANDS.Add("video", UBBCommand_Video);
            UBB_COMMANDS.Add("face", UBBCommand_Face);
            UBB_COMMANDS.Add("function", UBBCommand_Function);
            UBB_COMMANDS.Add("colour", UBBCommand_Colour);
            UBB_COMMANDS.Add("img", UBBCommand_Image);
            UBB_COMMANDS.Add("url", UBBCommand_Url);
            UBB_COMMANDS.Add("size", UBBCommand_Size);
            UBB_COMMANDS.Add("regFunction", UBBCommand_regFunction);


            m_MatchEvaluator = new MatchEvaluator(UBBReplace);
        }
        // 传入一个包含UBB信息的字串,返回一个html信息的字串
        private static Regex r = new Regex("\\[([a-zA-Z]+)\\]([^\\[]+)\\[\\/[a-zA-Z]+\\]|(<br>)|(&nbsp)", RegexOptions.IgnoreCase | RegexOptions.Multiline);
        //string re = "<a href='client::VoiceLink(.*)>(.*)<\\/a>";
        private static Regex r2 = new Regex("<a href='client::VoiceLink(.*)>(.*)<\\/a>", RegexOptions.IgnoreCase | RegexOptions.Singleline);
        private static Regex r3 = new Regex("\\[([a-zA-Z]+_+[a-zA-Z]+)\\]([^\\[]+)\\[\\/([a-zA-Z]+_+[a-zA-Z]+)\\]", RegexOptions.IgnoreCase | RegexOptions.Multiline);
        //32 普通的英文半角空格 160 普通的英文半角空格但不换行 12288 中文全角空格 （一个中文宽度） 8194 （半个中文宽度） 8195 空格 （一个中文宽度） 8197 空格 （四分之一中文宽度）
        private static string spacestr = ((char)8197).ToString();
        public static string UBBStr(string str)
        {
            if (UBB_COMMANDS.Count <= 0)
            {
                Init();
            }

            if (str == string.Empty)
            {
                return "";
            }
            // [yellow]魔尊洞府:[/yellow]
            // [npc]15001[/npc]
            //string re = "\\[([a-zA-Z]+)\\](([\u4E00-\u9FA5]|[\uFE30-\uFFA0]|\\w)+)\\[\\/[a-zA-Z]+\\]|(<br>)|(&nbsp)";
            //Regex r = new Regex(re, RegexOptions.IgnoreCase | RegexOptions.Singleline);

            //空格和换行替换特殊处理下，大部分ubb调用都是这两，调用正则表达式比各种字串替换消耗大10倍以上
            string temp = str;
            //换行
            if (temp.Contains("<br>"))
            {
                temp = temp.Replace("<br>", "\n");
            }
            //换行
            if (temp.Contains("</br>"))
            {
                temp = temp.Replace("</br>", "\n");
            }
            //空格
            if (temp.Contains("&nbsp;"))
            {
                temp = temp.Replace("&nbsp;", spacestr);
            }

            bool ret = temp.Contains("[") && temp.Contains("]");
            if (ret)
            {
                temp = r.Replace(temp, m_MatchEvaluator);
                bool boIndex = false;
                if (temp.IndexOf("[") > -1)
                {
                    boIndex = true;
                }
                int number = 0;
                while (boIndex)
                {
                    temp = r.Replace(temp, m_MatchEvaluator);
                    if (temp.IndexOf("[/") <= -1)
                    {
                        boIndex = false;
                    }
                    #region 超过循环50次强制退出
                    number++;
                    if (number > 50)
                    {
                        boIndex = false;
                    }
                    #endregion
                }

                return temp;
            }
            else
            {
                return temp;
            }
        }

        public static string Textr2Replace(string chatContext)
        {
            if (chatContext == null)
            {
                return chatContext;
            }
            if (chatContext.Contains("<a href='client::VoiceLink"))
                chatContext = r2.Replace(chatContext, "");
            return chatContext;
        }

        // 替换关键字
        private static string UBBReplace(Match match)
        {
            //if (match.Groups[4].Value == "&nbsp")
            //{
            //    return UBB_COMMANDS["&nbsp"]("");
            //}
            //else if (match.Groups[3].Value == "<br>")
            //{

            //    return UBB_COMMANDS["<br>"]("");
            //}

            string szDunctionName = match.Groups[1].Value;
            if (szDunctionName.StartsWith("funciton_"))
            {
                szDunctionName = "funciton_";
            }
            string szPar = match.Groups[2].Value;
            if (!UBB_COMMANDS.ContainsKey(szDunctionName))
            {
                return "";
            }

            if (UBB_COMMANDS[szDunctionName] == null)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + szDunctionName);
                return "";
            }

            return UBB_COMMANDS[szDunctionName](szPar);

        }

        // 字体加粗
        private static string UBBCommand_Bold(string str)
        {
            return string.Format("<b>{0}</b>", UBBStr(str));
        }

        // 斜体
        private static string UBBCommand_Incline(string str)
        {
            return string.Format("<i>{0}</i>", UBBStr(str));
        }

        // 下划线
        private static string UBBCommand_Underline(string str)
        {
            return UBBStr(str);
        }

        // 红色字体
        private static string UBBCommand_Red(string str)
        {
            if (m_bMsg)
            {
                return string.Format("<color=#FF0000>{0}</color>", UBBStr(str));
            }
            return string.Format("<color=#FF0000>{0}</color>", UBBStr(str));
        }

        // 蓝色字体
        private static string UBBCommand_Blue(string str)
        {
            if (m_bMsg)
            {
                return string.Format("<color=#0000FF>{0}</color>", UBBStr(str));
            }
            return string.Format("<color=#0000FF>{0}</color>", UBBStr(str));
        }

        // 黄色字体
        private static string UBBCommand_Yellow(string str)
        {
            if (m_bMsg)
            {
                return string.Format("<color=#FFFF00>{0}</color>", UBBStr(str));
            }
            return string.Format("<color=#FFFF00>{0}</color>", UBBStr(str));
        }

        // 绿色字体
        private static string UBBCommand_Green(string str)
        {
            if (m_bMsg)
            {
                return string.Format("<color=#008000>{0}</color>", UBBStr(str));
            }
            return string.Format("<color=#008000>{0}</color>", UBBStr(str));
        }

        // 橙色字体
        private static string UBBCommand_Orange(string str)
        {
            if (m_bMsg)
            {
                return string.Format("<color=#FFA500>{0}</color>", UBBStr(str));
            }
            return string.Format("<color=#FFA500>{0}</color>", UBBStr(str));
        }

        // 紫色字体
        private static string UBBCommand_Purple(string str)
        {
            if (m_bMsg)
            {
                return string.Format("<color=#800080>{0}</color>", UBBStr(str));
            }
            return string.Format("<color=#800080>{0}</color>", UBBStr(str));
        }
        /// <summary>
        /// 白色
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private static string UBBCommand_White(string str)
        {
            return string.Format("<color=#FFFFFF>{0}</color>", UBBStr(str));
        }
        public static string UBBCommand_Colour(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
                return "";
            }
            string loc = str.Substring(1, pos - 1);
            string txt = str.Substring(pos + 1, str.Length - pos - 1);
            return string.Format("<color=#{0}>{1}</color>", loc, txt);
        }
        // 图片
        private static string UBBCommand_Image(string str)
        {
            return string.Format("<img=1,{0}/>", str);
        }

        // URL
        private static string UBBCommand_Url(string str)
        {
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
            }

            return string.Format("<a href='{0}'+>{1}</a>", str.Substring(2, pos - 1), str.Substring(pos + 1, str.Length));
        }
        /// <summary>
        /// 设置字体大小
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private static string UBBCommand_Size(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
                return "";
            }
            string loc = str.Substring(1, pos - 1);
            string txt = str.Substring(pos + 1, str.Length - pos - 1);
            return string.Format("<size={0}>{1}</size>", loc, txt);
        }
        // 移动到指定点
        // 会将地图ID转换成属于自己的国家地图
        private static string UBBCommand_Move(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
                return "";
            }

            string loc = str.Substring(0, pos + 1);
            string txt = str.Substring(pos + 1, str.Length - pos - 1);

            return string.Format("<a href='client::move{0}'>{1}</a>", loc, txt);
        }
        private static string UBBCommand_Codese(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
                return "";
            }

            string loc = str.Substring(0, pos + 1);
            string txt = str.Substring(pos + 1, str.Length - pos - 1);
            return string.Format("<a href='client::codese{0}'>{1}</a>", loc, txt);
        }
        public static string UBBCommand_Building(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
                return "";
            }

            string loc = str.Substring(0, pos + 1);
            string txt = str.Substring(pos + 1, str.Length - pos - 1);
            return string.Format("<a href='client::building{0}'>{1}</a>", loc, txt);
        }
        public static string UBBCommand_Video(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + str);
                return "";
            }

            string loc = str.Substring(0, pos + 1);
            string txt = str.Substring(pos + 1, str.Length - pos - 1);
            return string.Format("<a href='client::playVideo{0}'>{1}</a>", loc, txt);
        }
        public static string UBBCommand_Function(string str)
        {
            str = str.Replace('：', ':');
            str = str.Replace('，', ',');
            string[] arrstr = str.Split(':');
            int pos = arrstr[1].IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + arrstr[1]);
                return "";
            }

            string loc = arrstr[1].Substring(0, pos + 1);
            string txt = arrstr[1].Substring(pos + 1, arrstr[1].Length - pos - 1);
            return string.Format("<a href='client::{0}{1}'>{2}</a>", arrstr[0], loc, txt);
        }
        public static string UBBCommand_regFunction(string str)
        {
            str = str.Replace('：', ':');
            str = str.Replace('，', ',');
            string[] arrstr = str.Split(':');
            int pos = arrstr[1].IndexOf(")");
            if (pos < 0)
            {
                TRACE.ErrorLn("invalid UBB syntax!" + arrstr[1]);
                return "";
            }

            string loc = arrstr[1].Substring(0, pos + 1);
            string txt = arrstr[1].Substring(pos + 1, arrstr[1].Length - pos - 1);
            return string.Format("<a href='reg_client::{0}{1}'>{2}</a>", arrstr[0], loc, txt);
        }


        public static string UBBCommand_Face(string str)
        {
            return string.Format("<img=1,{0}>", str);
        }
        // 钱币格式
        //现在统一改成单一的元宝
        private static string UBBCommand_Money(string str)
        {
            int val = 0;
            if (!int.TryParse(str, out val))
            {
                str = str.Replace(",", "");// 以免数字在其他地方被格式化
                if (!int.TryParse(str, out val))
                {
                    TRACE.ErrorLn("invalid UBB syntax!" + str);
                    return "";
                }
            }

            if (m_bMsg)
            {
                return string.Format(Api.NTR("{0}<a href='' class=\"c8\">元宝</a>"), (int)Mathf.Floor(val));
            }
            else
            {
                return string.Format("{0}<quad class=\"money_gold\">", (int)Mathf.Floor(val));
            }
        }

        //-----------------------------------------------------------------------
        // 移动自动去采集
        //-----------------------------------------------------------------------
        private static string UBBCommand_MoveToCollect(string str)
        {
            str = str.Replace('，', ',');
            int pos = str.IndexOf(")");
            if (pos < 0)
            {
                TRACE.WarningLn("invalid UBB syntax!" + str);
                return "";
            }

            string loc = str.Substring(1, pos);
            string txt = str.Substring(pos + 1, str.Length);

            return string.Format("<a href='client::movetoAutoCollect{0}'>{1}</a>", loc, txt);
        }



        private static readonly Regex s_EmojiRegex = new Regex("\\[EMO[0-9_-]{1,3}\\]",
            RegexOptions.Singleline | RegexOptions.IgnoreCase
        );
        public static List<string> UBBAnalysisClick(string strContent)
        {
            List<string> listClick = new List<string>();
            //<a href='client::ProcessTaskDialog(1,1,1)'>xxxxx</a><a href='client::ProcessTaskDialog(1,1,1)'>xxxxx</a>
            string strReplContent = strContent.Replace("</a>", "|");
            string[] arryContent = strReplContent.Split('|');
            if (arryContent != null && arryContent.Length > 0)
            {
                foreach (string content in arryContent)
                {
                    if (!string.IsNullOrEmpty(content))
                    {
                        int istartdex = content.IndexOf("href='");
                        if (istartdex > 0)
                        {
                            string strClick = string.Empty;
                            int ienddex = content.IndexOf("'>");
                            if (istartdex < ienddex)
                            {
                                strClick = content.Substring(istartdex + 6, ienddex - istartdex - 6);
                                //判断是否是直接运行UBB
                                if (strClick.IndexOf("reg_") > -1)
                                {
                                    GHelp.HtmlItemClick(strClick);
                                }
                                else
                                {
                                    listClick.Add(strClick);
                                }
                            }
                        }
                    }
                }
            }
            return listClick;
        }
    }

}
