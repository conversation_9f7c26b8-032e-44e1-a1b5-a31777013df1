﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandShowTips : IHandleCommand
    {
        bool m_isEnd = false; // 是否不正确的执行完指令
        private bool m_isPlay = false;
        private List<IHandleCommand> m_others;
        private string tips;
        private Color color;
        public COpHandleCommandShowTips(SOpHandleCommand_ShowTips data)
        {
            m_isPlay = false;
            m_isEnd = false;
            m_others = data.otherCommand;
            tips = data.tips;
            color = data.color;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpShowTips;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isEnd = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, Api.NTR(tips), color, true);
                m_isPlay = true;
            }

            return true;
        }

        public void update()
        {
        }
    }
}
