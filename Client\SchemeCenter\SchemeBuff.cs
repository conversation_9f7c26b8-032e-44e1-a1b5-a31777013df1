﻿/// <summary>
/// SchemeBuff
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// buff配置脚本<br/>
/// </remarks>
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;

namespace GLib.Client
{
	// buff配置
	public class CSchemeBuff : ISchemeNode,ISchemeBuff
	{
		// Effect配置脚本名字
		public const string BUFF_SCHEME_FILENAME = "Buff";

		// 所有BUFF配置属性	
		private Dictionary<uint, Buff.Types.Item> m_buffInfoByID;

		FastReader m_BuffReader;
		/// <summary>
		/// 脚本名称(模块实现者不用设置)
		/// </summary>
		public string SchemeName { get; set; }

		/// <summary>
		/// 异步脚本的加载状态(注释:异步模块专用)
		/// </summary>
		public EMSchemeState SchemeLoadState { get; set; }

		/// <summary>
		/// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
		/// </summary>
		public float Progress { get; set; }

 
		/** 
		@param   
		@param   
		@return  
		*/
		public CSchemeBuff()
		{
			m_buffInfoByID = new Dictionary<uint, Buff.Types.Item>();
		}


		/// <summary>
		/// 初始化
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
			// 加载脚本
			string stringPath = BUFF_SCHEME_FILENAME;
			bool bResult = FastReader.FastRead(stringPath, out m_BuffReader, OnBuffReadFinish);
			if (!bResult)
			{
				TRACE.ErrorLn("加载配置文件失败。文件名 = " + stringPath);
				return false;
			}
			return true;
		}

		/// <summary>
		/// 释放资源
		/// </summary>
		public void Release()
		{
			// 所有地图配置属性	
			m_buffInfoByID.Clear();
		}


		/** 取得对应buff及级别数据
		@param   
		@param   
		@return  
		*/
		public Buff.Types.Item GetBuffSchemeInfo(UInt32 dwBuffID, UInt32 dwLevel = 1)
		{
			UInt32 dwBuffKey = dwBuffID * 10000 + dwLevel;

			Buff.Types.Item sInfo = null;
			m_buffInfoByID.TryGetValue(dwBuffKey, out sInfo);
			if (sInfo != null)
			{
				return sInfo;
			}

			sInfo = ReadBuff((Int32)dwBuffKey);
			if (sInfo == null)
			{
				return null;
			}
			m_buffInfoByID.Add(dwBuffKey, sInfo);
			return sInfo;
		}

		/** 取得对应buff及级别数据
		@param   
		@param   
		@return  
		*/
		public Buff.Types.Item GetBuffSchemeInfo(UInt32 dwBuffID)
		{
			UInt32 dwBuffKey = dwBuffID;

			Buff.Types.Item sInfo = null;
			m_buffInfoByID.TryGetValue(dwBuffKey, out sInfo);
			if (sInfo != null)
			{
				return sInfo;
			}

			sInfo = ReadBuff((Int32)dwBuffKey);
			if (sInfo == null)
			{
				return null;
			}
			m_buffInfoByID.Add(dwBuffKey, sInfo);
			return sInfo;
		}

		/// <summary>
		/// 加载Buff表回调
		/// </summary>
		/// <param name="szFileName"></param>
		/// <returns></returns>
		bool OnBuffReadFinish(string szFileName)
		{
#if UNITY_EDITOR
			Dictionary<Int32, Int32> allData = m_BuffReader.AllData();
			var iter = allData.GetEnumerator();
			Int32 nError = 0;
			while (iter.MoveNext())
			{
				Buff.Types.Item sInfo = ReadBuff(iter.Current.Key);
				if (sInfo == null)
				{
					nError++;
					continue;
				}
				m_buffInfoByID.Add((UInt32)iter.Current.Key, sInfo);
			}
			iter.Dispose();
			return nError == 0;
#else
			return true;
#endif
		}

		/// <summary>
		/// 读取一行数据
		/// </summary>
		/// <param name="nFormatID"></param>
		/// <returns></returns>
		Buff.Types.Item ReadBuff(Int32 nFormatID)
		{
			FastLine pCSVReader = m_BuffReader.StartLine(nFormatID);
			if (pCSVReader == null)
			{
				return null;
			}
			int nRow = 0;

			Int32 nCol = 0;
			Buff.Types.Item info = new Buff.Types.Item();

			if (!m_buffInfoByID.ContainsKey((UInt32)nFormatID))
			{
				// idx
				info.Id = nFormatID;

				// buffID
				info.BuffId = (int)pCSVReader.GetInt(nRow, nCol++, 0);
				if (info.BuffId <= DGlobalGame.INVALID_BUFF_ID)
				{
					TRACE.ErrorLn("buffid不能小于等于0! buffid = " + info.BuffId);
					return null;
				}
				// BUFF ID不能大于0xffff，否则存数据库会出问题
				if (info.BuffId >= 0xFFFF)
				{
					TRACE.ErrorLn("buffid不能大于等于0xFFFF! buffid = " + info.BuffId);
					return null;
				}

				info.Level = (int)pCSVReader.GetInt(nRow, nCol++, 0);

				// buff名字
				info.Name = pCSVReader.GetString(nRow, nCol++, "");

				info.Description = pCSVReader.GetString(nRow, nCol++, "");

				// 叠加规则
				info.TimingType = pCSVReader.GetInt(nRow, nCol++, 0);

				// 叠加数量
				info.Duration = pCSVReader.GetInt(nRow, nCol++, 0);
				
				// 标志
				info.DispearWhenReset = pCSVReader.GetInt(nRow, nCol++, 0);
				info.DispearWhenQuit = pCSVReader.GetInt(nRow, nCol++, 0);
				string effectIds = pCSVReader.GetString(nRow, nCol++, "");
				string[] es = effectIds.Split(';');
				if(es.Length > 0)
                {
					for (int xx = 0; xx < es.Length; xx++)
					{
						info.BuffEffectIds.Add(int.Parse(es[xx]));
					}
                }
				info.Addable = pCSVReader.GetInt(nRow, nCol++, 0);
				info.AddMax = pCSVReader.GetInt(nRow, nCol++, 0);

				info.Replaced = pCSVReader.GetInt(nRow, nCol++, 0);
				info.IconId = pCSVReader.GetInt(nRow, nCol++, 0);
				info.AddSoundId = pCSVReader.GetInt(nRow, nCol++, 0);
				info.ExistSoundId = pCSVReader.GetInt(nRow, nCol++, 0);
				info.DispearSoundId = pCSVReader.GetInt(nRow, nCol++, 0);
			}
			return info;
		}
	};
}
