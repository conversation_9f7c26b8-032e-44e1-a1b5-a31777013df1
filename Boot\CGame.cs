﻿/// <summary>
/// CBoot
/// </summary>
/// <remarks>
/// 2019.6.24: 创建. 谌安 <br/>
/// 游戏初始化管理<br/>
/// </remarks>
using System;
using System.Collections;
using GLib.Common;
using UnityEngine;
using GLib.Client;
using GLib.Render;
using GLib.Client.Scheme;
using GLib.UI;
using GLib.Client.Entity;
using GLib.Lua;
using GLib.Haptic;

namespace GLib.Boot
{
    public class CGame : MonoBehaviourEX<CGame>, IGame
    {

        private float m_LastTickFixedUpdate = 0;
        private float m_LastTickUpdate = 0;
        private float m_LastTickLateUpdate = 0;

        private Transform m_transform = null;
        private TimeSpan? m_IntoTimeSpan;
        public bool IsInit { get; private set; }

        /// <summary>
        /// 点击退出次数
        /// </summary>
        private int m_ClickEscape = 0;
        private DateTime m_ClickEscapeDtetime;

        #region<<各系统模块声明>>
        GameClient m_GameClient = null;

        //模块管理器
        ModuleDispatch m_moduleDispatch = null;

        //模块加载器
        CGameLoader m_gameLoader = null;

        //网络模块
        INetManager m_netManager = null;

        //定时器管理器
        ITimerManager m_timeMgr = null;

        //事件引擎
        IEventEngine m_eventEngine = null;

        // 游戏模块
        IProductModule m_GameModule = null;
        //状态管理模块
        IStateManagerModule m_StateManagerModule = null;
        // Render 加载器
        IRenderLoader m_RenderLoader = null;
        //渲染代理
        IRenderViewProxy m_renderViewProxy = null;
        // 实体客户端
        IEntityClient m_entityClient = null;
        //日志模块
        CTraceModule m_trace = null;
        // 脚本管理
        ISchemeCenter m_schemeCenter = null;

        IResManager m_ResManager = null;

        // Buff客户端模块
        IBuffClient m_buffClient = null;
        // Buff客户端模块
        IEffectClient m_effctClient = null;

        // UI系统模块
        IUILoader m_UILoader = null;
        //登录管理
        ILoginManager m_LoginManager = null;
        //登录模块
        ILoginModule m_LoginModule = null;
        IGameSDK m_GameSDK = null;
        //Lua模块
        ILuaManager m_luaManager = null;
        // 移动控制模块
        IMoveController m_moveController = null;
        /// <summary>
        /// 任务控制模块
        /// </summary>
        ITaskControler m_taskControler = null;
        IButtonControl m_buttonControl = null;
        ILoadingControler m_loadingControl = null;
        ISceneInfoClient m_sceneInfoClient = null;
        IHapticClient m_HapticClient = null;
        //////////////////////////////////////以上为基础系统,注意分开方便后期移植整合///////////////////////////////

        //控制器管理器
        IControlManager m_ControlManager = null;
        //组装管理
        // IAssemblingClient m_AssemblingClient = null;
        // 导航网格
        INavigationManager m_NavigationManager = null;
        // 效果管理
        IEffectViewManager m_EffectViewManager = null;
        // 载具系统客户端
        ITankClient m_TankClient = null;
        /// <summary>
        /// 执行模块
        /// </summary>
        IExecuteModelClient m_executeModelClient = null;
        /// <summary>
        /// 场景客户端
        /// </summary>
        ISceneClient m_sceneClient = null;
        /// <summary>
        /// 消息回调
        /// </summary>
        IMessageBoxCallBack m_messageBoxCallBack = null;
        /// <summary>
        /// la层Http请求信息
        /// </summary>
        ILaHttpClient m_laHttpClient = null;
        /// <summary>
        /// timeline信息
        /// </summary>
        ITimelineClient m_timelineClient = null;
        /// <summary>
        /// 工具管理信息
        /// </summary>
        IToolMgr m_toolMgr = null;

        /// <summary>
        /// 课程模块管理
        /// </summary>
        ICourseMgr m_courseMgr = null;

        IOperateManger m_operateManger = null;

        IHighlightEffect m_highlightManger = null;
        /// <summary>
        /// 任务管理信息
        /// </summary>
        ITaskMgr m_taskMgr = null;

        /// <summary>
        /// 任务管理信息
        /// </summary>
        IDepartment m_Department = null;


        ISceneModelClient m_sceneModelClient = null;
        /// <summary>
        /// 日志信息管理
        /// </summary>
        IOperateLogMgr m_OperateLogMgr = null;
        /// <summary>
        /// 成绩信息管理
        /// </summary>
        IScoreMgr m_ScoreMgr;
        /// <summary>
        /// 个人资料管理
        /// </summary>
        IPersonalDataMgr m_PersonalDataMgr = null;
        #endregion

        #region<<取得各模块的接口(属性)>>
        //全局游戏Transform对象
        public Transform GlobalTransform { get { return m_transform; } }
        //游戏流程
        public IGameClient GameClient { get { return m_GameClient; } }

        // 定时器管理器
        public ITimerManager TimerManager { get { return m_timeMgr; } }

        // 获得事件引擎
        public IEventEngine EventEngine { get { return m_eventEngine; } }

        //获取模块加载器
        public CGameLoader GameLoader { get { return m_gameLoader; } }

        //获取网络模块
        public INetManager NetManager { get { return m_netManager; } }

        //获取游戏模块
        public IProductModule GameModule { get { return m_GameModule; } }

        //状态管理模块
        public IStateManagerModule StateManagerModule { get { return m_StateManagerModule; } }

        // Render 加载器
        public IRenderLoader RenderLoader { get { return m_RenderLoader; } }
        // 渲染代理
        public IRenderViewProxy RenderViewProxy { get { return m_renderViewProxy; } }
        //实体客户端
        public IEntityClient EntityClient { get { return m_entityClient; } }
        //日志模块
        public CTraceModule TraceModule { get { return m_trace; } }
        // 脚本管理
        public ISchemeCenter SchemeCenter { get { return m_schemeCenter; } }
        //资源管理
        public IResManager ResManager { get { return m_ResManager; } }

        // Buff客户端模块
        public IBuffClient BuffClient { get { return m_buffClient; } }

        public IEffectClient EffectClient
        {
            get { return m_effctClient; }
        }
        //UI管理模块
        public IUILoader UILoader { get { return m_UILoader; } }
        //获取登录管理模块
        public ILoginManager LoginManager { get { return m_LoginManager; } }
        //获取登录模块
        public ILoginModule LoginModule { get { return m_LoginModule; } }
        public IGameSDK GameSDK { get { return m_GameSDK; } }

        //获取Lua模块
        public ILuaManager LuaManager { get { return m_luaManager; } }
        //移动控制模块
        public IMoveController MoveController { get { return m_moveController; } }
        public ITaskControler TaskControler { get { return m_taskControler; } }
        /// <summary>
        /// 获取按钮控制
        /// </summary>
        public IButtonControl ButtonControl { get { return m_buttonControl; } }

        public ILoadingControler LoadingControler { get { return m_loadingControl; } }
        //////////////////////////////////////以上为基础系统,注意分开方便后期移植整合///////////////////////////////
        //实体管理
        public IEntityFactory EntityFactory { get { if (m_RenderLoader != null) { return m_RenderLoader.GetEntityFactory(); } return null; } }

        //控制器管理器
        public IControlManager ControlManager { get { return m_ControlManager; } }

        //执行模块管理
        public IExecuteModelClient ExecuteModelClient { get { return m_executeModelClient; } }

        public ISceneClient SceneClient { get { return m_sceneClient; } }

        // 导航网格
        public INavigationManager NavigationManager { get { return m_NavigationManager; } }

        //效果表现
        public IEffectViewManager EffectViewManager { get { return m_EffectViewManager; } }

        // 消息回调
        public IMessageBoxCallBack MessageBoxCallBack { get { return m_messageBoxCallBack; } }
        /// <summary>
        /// la层Http请求
        /// </summary>
        public ILaHttpClient LaHttpClient { get { return m_laHttpClient; } }
        /// <summary>
        /// timeline模块
        /// </summary>
        public ITimelineClient TimelineClient { get { return m_timelineClient; } }
        /// <summary>
        /// 工具管理模块
        /// </summary>
        public IToolMgr  ToolMgr { get { return m_toolMgr; } }

        public ICourseMgr CourseMgr { get { return m_courseMgr; } }
        public IOperateManger OperateManger { get { return m_operateManger; } }


        public ITaskMgr TaskMgr { get { return m_taskMgr; } }
        public IHapticClient HapticClient { get { return m_HapticClient; } }
        public ISceneModelClient SceneModelClient { get { return m_sceneModelClient; } }

        public IDepartment iDepartment { get { return m_Department; } }

        public IOperateLogMgr OperaLogMagr { get { return m_OperateLogMgr; } }

        public IScoreMgr ScoreMgr { get { return m_ScoreMgr; } }

        public IPersonalDataMgr PersonalDataMgr { get { return m_PersonalDataMgr; } }

        public IHighlightEffect HighlightManger { get { return m_highlightManger; } }

        #endregion

        public override void Awake()
        {
            LoadBootManager.Instance.m_CGameDateTime = DateTime.Now;
            base.Awake();
            //拼装的分辨率设置 不花时间去改分辨率适配了 之后的拼装没有这东西
            //新拼装做好 这代码我会删掉
            //Screen.SetResolution(1920, 1080, false);
#if UNITY_EDITOR
            GameViewUtils.SetWidescreen(string.Format("{0}x{1}", GHelp.GetVerticalResolution().x, GHelp.GetVerticalResolution().y));
#endif

            Screen.fullScreen = true;
            //横屏不显示，竖屏显示
            PluginPlatform.Instance.Plugin().SetStatusBar(true);
            GlobalGame.SetGame(this);
            IsInit = false;
            m_transform = transform;
            try
            {
                //初始化
                Init();
            }
            catch (Exception ex)
            {
                Debug.LogError("Init error ex.Source=" + ex.Source);
                Debug.LogError("Init error ex.message=" + ex.Message);
                Debug.LogError("Init error ex stack=" + ex.StackTrace);
            }

            GHelp.SetEventEngineStatus(true);
        }

        private void Init()
        {
            m_trace = new CTraceModule();

            m_moduleDispatch = new ModuleDispatch();

            m_timeMgr = new CTimerManager();

            m_eventEngine = new CEventEngine();

            m_gameLoader = new CGameLoader();

            m_netManager = new CNetManager();

            m_LoginManager = new LoginManager();

            m_LoginModule = new CLoginModule();

            m_GameClient = new GameClient();

            m_GameModule = new CProductModule();

            m_StateManagerModule = new StateManagerModule();

            m_renderViewProxy = new CRenderViewProxy();

            m_entityClient = new CEntityClient();

            m_RenderLoader = new RenderLoader();

            m_NavigationManager = new NavigationManager();

            m_schemeCenter = new CSchemeCenter();

            m_ResManager = new GResManager();

            m_buffClient = new CBuffClient();

            m_effctClient = new CEffectClient();

            m_UILoader = new CUILoader();

            m_moveController = new CMoveController();

            m_EffectViewManager = new CEffectViewManager();

            m_luaManager = new LuaManager();

            m_taskControler = new CTaskControler();
            m_buttonControl = new CButtonControl();
            m_loadingControl = new LoadingControler();
            //////////////////////////上方为最基础系统/////////////////
            m_sceneInfoClient = new SceneInfoClient();
            m_GameSDK = new GameSDK();
            m_ControlManager = new CControlManager();
            m_TankClient = new CTankClient();
            m_executeModelClient = new ExecuteModelClient();           
            m_sceneClient = new SceneClient();
            m_messageBoxCallBack = gameObject.AddComponent<CMessageBoxCallBack>();
            m_laHttpClient = new LaHttpClient();
            m_timelineClient = new TimelineClient();
            m_toolMgr = new ToolManager();
            m_courseMgr = new  CourseManger();
            m_operateManger = new OperateManger();
            m_taskMgr = new TaskManager();
            m_HapticClient = new HapticClient();
            m_sceneModelClient = new SceneModelClient();
            m_OperateLogMgr = new OperateLogManger();
            m_ScoreMgr = new ScoreManger();
            m_PersonalDataMgr = new PersonalDataManager();
            m_highlightManger = new HighlightEffectManager();
            //////在初始化之后需要用到的模块在这里显示创建加载//////////
            m_trace.Create();

            m_moduleDispatch.Create();

            m_timeMgr.Create();

            m_eventEngine.Create();

            m_gameLoader.Create();

            m_messageBoxCallBack.Create();
            IsInit = true;

            Create();
        }

        public void Create()
        {

            // 在【阶段： Create 创建初始资源】 时要用的系统模块在下面注册 ///////////////////////////////////// 
            Reg("NetManager", m_netManager, GameState.Create, 3.0f);				// 网络模块
            // 在【阶段： Init 初始化】 时要用的系统模块在下面注册 ///////////////////////////////////// 
            Reg("RenderViewProxy", m_renderViewProxy, GameState.Init, 3.0f);		// 渲染代理
            Reg("StateManagerModule", m_StateManagerModule, GameState.Init, 3.0f);      // 状态管理模块
            Reg("RenderLoader", m_RenderLoader, GameState.Init, 3.0f, true);        // Render 加载器
            Reg("UILoader", m_UILoader, GameState.Init, 3.0f);
            Reg("SchemeCenter", m_schemeCenter, GameState.Init, 5.0f, true);        // 脚本管理
            Reg("ResManager", m_ResManager, GameState.Init, 3.0f, true);        // 脚本管理
            Reg("LuaManager", m_luaManager, GameState.Init, 3.0f, true);            // Lua模块
            Reg("ControlManager", m_ControlManager, GameState.Init, 3.0f);          // 控制器管理器
            Reg("HapticClient", m_HapticClient, GameState.Init, 3.0f);          // Haptic触觉
            Reg("GameSDK", m_GameSDK, GameState.Init, 3.0f);
            // 在【阶段： 登录 】 时要用的系统模块在下面注册 ///////////////////////////////////// 
            Reg("LoginModule", m_LoginModule, GameState.Login, 3.0f);				// 登录模块
            // 在【阶段： Game 】 时要用的系统模块在下面注册 ///////////////////////////////////// 
            Reg("m_SceneInfoClient", m_sceneInfoClient, GameState.Game, 3.0f);     //场景信息管理
            Reg("m_sceneModelClient", m_sceneModelClient, GameState.Game, 3.0f);     //场景信息管理
            Reg("m_sceneClient", m_sceneClient, GameState.Game, 3.0f);//场景
            Reg("LoginManager", m_LoginManager, GameState.Game, 3.0f);       // 登录管理
            Reg("MoveController", m_moveController, GameState.Game, 3.0f);          // 移动控制模块
            Reg("EffectViewManager", m_EffectViewManager, GameState.Game, 3.0f);    // 效果管理
            Reg("EntityClient", m_entityClient, GameState.Game, 3.0f);              // 实体客户端(暂时放这里)
            Reg("BuffClient", m_buffClient, GameState.Game, 3.0f);                  // Buff客户端模块
            Reg("BuffEffectClient", m_effctClient, GameState.Game, 3.0f);                  // Buff客户端模块
            Reg("NavigationManager", m_NavigationManager, GameState.Game, 3.0f);    // 导航网格
            //Reg("AssemblingClient", m_AssemblingClient, GameState.Game, 3.0f);//组装管理器
            Reg("AssemblingClient", m_executeModelClient, GameState.Game, 3.0f);//执行模块            
            Reg("TaskControler", m_taskControler, GameState.Game, 3.0f);  //任务管理
            Reg("LaHttpClient", m_laHttpClient, GameState.Game, 3.0f);  //la层Http信息管理
            Reg("TimelineClient", m_timelineClient, GameState.Game, 3.0f);  //timeline信息管理
            Reg("ButtonControler", m_buttonControl, GameState.Game, 3.0f);  //按钮控制
            Reg("LoadingControler", m_loadingControl, GameState.Game, 3.0f);  //按钮控制        
            Reg("m_toolMgr", m_toolMgr, GameState.Game, 3.0f);  //工具管理 
            Reg("m_courseMgr", m_courseMgr, GameState.Game, 3.0f);  //工序管理 
            Reg("m_operateManger", m_operateManger, GameState.Game, 3.0f);     //操作物体
            Reg("m_taskMgr", m_taskMgr, GameState.Game, 3.0f);  //任务管理
            Reg("m_OperateLogMgr", m_OperateLogMgr, GameState.Game, 3.0f);  //任务管理
            Reg("m_ScoreInfo", m_ScoreMgr, GameState.Game, 3.0f);  //分数管理
            Reg("m_PersonalDataMgr", m_PersonalDataMgr, GameState.Game, 3.0f);  //个人资料
            Reg("m_highlightManger", m_highlightManger, GameState.Game, 3.0f);  //高亮管理

            m_GameClient.Create();
        }

        public void RegisterModuleEvent(IModule module, uint eventFlag = 7)
        {
            m_moduleDispatch.RegisterModuleEvent(module, eventFlag);
        }



        public void UnRegisterModuleEvent(IModule module)
        {
            m_moduleDispatch.UnRegisterModuleEvent(module);
        }

        /// <summary>
        /// 注册加载模块配置信息  为了提高加载时的性能，请在加载配置时 先预先人工排好序
        /// </summary>
        /// <param name="strModuleName">模块中文名称</param>
        /// <param name="module">需要加载的模块</param>
        /// <param name="nLoadState">加载模块时的初始状态</param>
        /// <param name="fTimeOut">模块加载的大致时间，当超出这个时间系统会提示加载超时</param>
        /// <param name="bAsyn">设置模块是否需要异步加载，默认false为同步加载</param>
        /// <returns></returns>
        private bool Reg(string strModuleName, IModule module, GameState nLoadState = GameState.Init, float fTimeOut = 3.0f, bool bAsyn = false)
        {
            return m_gameLoader.Reg(strModuleName, module, nLoadState, fTimeOut);
        }

        //销毁
        public void Release()
        {
            // 停止所有的协程
            StopAllCoroutinesEx();

            if (m_trace != null)
                m_trace.Release();

            if (m_moduleDispatch != null)
                m_moduleDispatch.Release();
            //if (m_thinkingController != null)
            //    m_thinkingController.Release();
        }


        //每帧渲染时调用
        public void Update()
        {
            // 降低刷新频率 
            float fTime = Time.realtimeSinceStartup;
            if (fTime - m_LastTickUpdate < 0.05f)
            {
                return;
            }
            m_LastTickUpdate = fTime;


#if UNITY_EDITOR
            if (Input.GetKey(KeyCode.L))
            {
                bool flag = TRACE.GetStopLogFlag();
                TRACE.SetStopLogFlag(!flag);
            }
#endif

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("ModuleDispatch.DispatchUpdate()");
#endif
            m_moduleDispatch.DispatchUpdate();
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

#if !UNITY_EDITOR

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                m_ClickEscape++;
                if (m_ClickEscape >= 2)
                {
                    int clickNum = GHelp.GetTwoDateTimeInterval(m_ClickEscapeDtetime, DateTime.Now, 1);
                    if (clickNum <= 10)
                    {
                        Application.Quit();
                    }
                    else
                    {
                        m_ClickEscape = 1;
                        m_ClickEscapeDtetime = DateTime.Now;
                        GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, Api.NTR("再次进行该操作后退出APP"));
                    }
                }
                else
                {
                    m_ClickEscapeDtetime = DateTime.Now;
                    GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, Api.NTR("再次进行该操作后退出APP"));
                }

            }
#endif

        }



        //最后更新
        public void LateUpdate()
        {
            // 降低刷新频率 
            float fTime = Time.realtimeSinceStartup;
            if (fTime - m_LastTickLateUpdate < 0.05f)
            {
                return;
            }
            m_LastTickLateUpdate = fTime;

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("ModuleDispatch.DispatchLateUpdate()");
#endif
            m_moduleDispatch.DispatchLateUpdate();
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif
        }



        //固定时间调用，与图像渲染分离
        public void FixedUpdate()
        {

            m_netManager.FixedUpdate();

            // 降低刷新频率 
            float fTime = Time.realtimeSinceStartup;
            if (fTime - m_LastTickFixedUpdate < 0.05f)
            {
                return;
            }
            m_LastTickFixedUpdate = fTime;

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("TimeMgr.Dispatch()");
#endif
            m_timeMgr.Dispatch(20);
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("ModuleDispatch.DispatchFixedUpdate()");
#endif
            m_moduleDispatch.DispatchFixedUpdate();
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif
        }

        //程序暂停
        public void OnApplicationPause(bool pause)
        {
            //切换到后台
            if (pause)
            {
                SetCurrentSessionTime();
                m_IntoTimeSpan = null;
                SetForontDesk(false);
            }
            //切换到前台
            else
            {
                SetForontDesk(true);
                m_IntoTimeSpan = new TimeSpan(DateTime.Now.Ticks);
                if (GlobalGame.Instance != null)
                {

                    SetSwitchForeground();
                }

            }
        }
        private void SetForontDesk(bool bState)
        {
            ProductGlobal.Instance.SetIsForontDesk(bState);
            if (!bState)
            {
                Invoke("ChecBackStage", 5);
            }
        }
        private void ChecBackStage()
        {
            ProductGlobal.Instance.CheckIntoBackStage();
        }
        private static void SetSwitchForeground()
        {
            if (UIHelper.Instance != null)
            {
#if !UNITY_EDITOR
                if (GHelp.GetLayoutModel() == LayoutModel.AcrossModel)
                {
                 //   UIHelper.Instance.SetUICanvasSomehowScreen(new Vector2(1920, 1080), true, null);
                }
                else
                {
                //    UIHelper.Instance.SetUICanvasSomehowScreen(new Vector2(1080, 1920), false, null);
                }
#endif
            }
        }

        public void OnApplicationFocus(bool focus)
        {
            //切换到前台
            if (focus)
            {
                SetForontDesk(true);
                m_IntoTimeSpan = new TimeSpan(DateTime.Now.Ticks);
                if (GlobalGame.Instance != null)
                {
                    SetSwitchForeground();
                }

                if (m_netManager != null && !m_netManager.Connected)
                {//网络联接断开
                    //TRACE.TraceLn("回到前台，网络断开，准备重连");
                    //网络连接中断，并且当前是登录完成状态，才会重连
                    if (m_LoginManager != null && m_LoginManager.GetNowLoginState() == LoginState.LoginFinish)
                    {//登录完成
                        m_netManager.NotifyReconnection();
                    }
                }
            }
            //切换到后台
            else
            {
                SetForontDesk(false);
                SetCurrentSessionTime();
                m_IntoTimeSpan = null;
            }
        }

        private void SetCurrentSessionTime()
        {
            if (m_IntoTimeSpan != null)
            {
                TimeSpan endTimeSpan = new TimeSpan(DateTime.Now.Ticks);
                TimeSpan timeSpan = endTimeSpan.Subtract((m_IntoTimeSpan ?? endTimeSpan)).Duration();
            }
        }

        //程序退出
        public void OnApplicationQuit()
        {
            SetCurrentSessionTime();
            m_netManager.Release();
            m_trace.Release();
        }

        // 协程相关接口

        public Coroutine StartCoroutineEx(IEnumerator routine)
        {
            return StartCoroutine(routine);
        }
        public Coroutine StartCoroutineEx(string methodName)
        {
            return StartCoroutine(methodName);
        }
        public Coroutine StartCoroutineEx(string methodName, object value)
        {
            return StartCoroutine(methodName, value);
        }
        public Coroutine StartCoroutine_AutoEx(IEnumerator routine)
        {
            return StartCoroutine(routine);
        }
        public void StopAllCoroutinesEx()
        {
            StopAllCoroutines();
        }
        public void StopCoroutineEx(string methodName)
        {
            StopCoroutine(methodName);
        }

        public void StopCoroutineEx(Coroutine routine)
        {
            StopCoroutine(routine);
        }

    }
}
