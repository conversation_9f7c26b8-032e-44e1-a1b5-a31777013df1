﻿using System;
using System.Collections.Generic;

using GLib.Common;

namespace GLib.Client.Entity
{
    public class CEntityClass : IEntityClass
    {
        // 标志位
        uint m_dwFlag;

        /** 
        @param   
        @param   
        @return  
        */
        public CEntityClass()
        {
            // 标志位
            m_dwFlag = 0;
        }

        /** 还原,不释放内存，只将状态还原到创建时状态
        @param   未来继续使用
        @param   
        @return  
        */
        public void Restore()
        {
            // 标志位
            m_dwFlag = 0;
        }

        /** 
        @param   
        @param   
        @return  
        */
        public void SetClass(uint dwClass)
        {
            // 标志位
            m_dwFlag = dwClass;

            if (dwClass == (uint)EMtEntity_Class.tEntity_Class_Person || dwClass == (uint)EMtEntity_Class.tEntity_Class_Monster)
            {
                m_dwFlag |= (uint)EMtEntity_Class.tEntity_IsClass_Creature;
            }
        }

        /** 是否为人物
        @param   
        @param   
        @return  
        */
        public bool IsPerson()
        {
            return ((m_dwFlag & 0xff) == (uint)EMtEntity_Class.tEntity_Class_Person) ? true : false;
        }

        /** 是否为怪物
        @param   
        @param   
        @return  
        */
        public bool IsMonster()
        {
            return ((m_dwFlag & 0xff) == (uint)EMtEntity_Class.tEntity_Class_Monster) ? true : false;
        }
        
        /** 是否为生物
        @param   
        @param   
        @return  
        */
        public bool IsCreature()
        {
            return ((m_dwFlag & (uint)EMtEntity_Class.tEntity_IsClass_Creature) == (uint)EMtEntity_Class.tEntity_IsClass_Creature) ? true : false;
        }

        public bool IsBox()
        {
            return ((m_dwFlag & 0xff) == (uint)EMtEntity_Class.tEntity_Class_Box) ? true : false;
        }

        public bool IsTank()
        {
            return ((m_dwFlag & 0xff) == (uint)EMtEntity_Class.tEntity_Class_Tank) ? true : false;
        }

        public bool IsTrap()
        {
            return ((m_dwFlag & 0xff) == (uint)EMtEntity_Class.tEntity_Class_Trap) ? true : false;
        }

        /** 取得游戏类型（tEntity_Class_Person， tEntity_Class_Monster，tEntity_Class_Equipment，tEntity_Class_Leechdom）
        @param   
        @param   
        @return  
        */
        public uint Class()
        {
            return m_dwFlag & 0xff;
        }
    }
}