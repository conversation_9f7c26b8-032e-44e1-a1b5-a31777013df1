﻿/// <summary>
/// MapInfoCenter
/// </summary>
/// <remarks>
/// 2019.7.19: 创建. 谌安 <br/>
/// 地图信息中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class DepartmentCenter : ISchemeNode, IDepartment
    {
        private const string ENTITY_CONFIG = "Department";

        List<Department> departmentList ;

        private Dictionary<int, Department> m_dicDepartments;

        private Department m_Department;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public DepartmentCenter()
        {
            departmentList = new List<Department>();
            m_Department = new Department();
            m_dicDepartments = new Dictionary<int, Department>();
        }

        ~DepartmentCenter() { }

        public bool Create()
        {
            if (!LoadScheme())
            {
                TRACE.ErrorLn("Department load fail!");
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = ENTITY_CONFIG;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Entity);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Entity(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }
            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                 Department mdepartment = new Department();

                mdepartment.id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                mdepartment.DepartmentName = pCSVReader.GetString(nRow, tmp_col++, "");

                departmentList.Add(mdepartment);
                m_dicDepartments.Add(mdepartment.id, mdepartment);
            }
            return true;
        }

        public void Release()
        {
            m_dicDepartments.Clear();
            m_dicDepartments = null;
            departmentList.Clear();
            departmentList = null;
        }

        public Department GetDepartmentById(int id)
        {
            Department info = null;

            m_dicDepartments.TryGetValue(id, out info);

            return info;
        }

        public List<Department> GetAllDepartmentList()
        {
            return departmentList;
        }
    }
}
