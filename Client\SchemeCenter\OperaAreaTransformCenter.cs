﻿/// <summary>
/// MapInfoCenter
/// </summary>
/// <remarks>
/// 2019.7.19: 创建. 谌安 <br/>
/// 地图信息中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class OperaAreaTransformCenter : ISchemeNode, IOperaAreaTransformCenter
    {
        private const string MAP_INFO = "OperaAreaTransform";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, Dictionary<int, CameraTransformDef>> m_InfoByID;

        private List<CameraTransformDef> m_TransInfos;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        private Action<Dictionary<int, CameraTransformDef>> m_action;


        private int m_courseId;

        int moveID;

        public OperaAreaTransformCenter()
        {
            m_InfoByID = new Dictionary<int, Dictionary<int, CameraTransformDef>>();
            m_TransInfos = new List<CameraTransformDef>();
        }

        ~OperaAreaTransformCenter()
        {
        }

        public bool Create()
        {
            //if (!LoadScheme(m_courseId))
            //{
            //    return false;
            //}
            return true;
        }

        public bool LoadScheme(int CourseId, Action<Dictionary<int, CameraTransformDef>> action = null)
        {
            m_action = action;
            m_courseId = CourseId;

            
            if (!m_InfoByID.ContainsKey(CourseId))
            {
                string strPath = "CourseCsv/" + CourseId + "/" + MAP_INFO;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
           
            else
            {
                Dictionary<int, CameraTransformDef> datas = new Dictionary<int, CameraTransformDef>();
                m_InfoByID.TryGetValue(CourseId, out datas);
                m_TransInfos.Clear();
                foreach (var item in datas)
                {
                    m_TransInfos.Add(item.Value);
                }
                m_action?.Invoke(datas);
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, CameraTransformDef> datas = null;
            m_InfoByID.TryGetValue((int)pData, out datas);
            m_TransInfos.Clear();
            if (datas == null)
            {
                datas = new Dictionary<int, CameraTransformDef>();
                m_InfoByID[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                CameraTransformDef map = new CameraTransformDef();

                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.Pos = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Rorate = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Scale = pCSVReader.GetString(nRow, tmp_col++, "");
                map.DurationTime = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                map.ImageName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Desc = pCSVReader.GetString(nRow, tmp_col++, "");
                m_TransInfos.Add(map);
                datas.Add(map.Id, map);
            }
            m_action?.Invoke(datas);
            return true;
        }

        public void Release()
        {
            m_InfoByID.Clear();
            m_InfoByID = null;
        }

        public void GetHeroMoveInfoByID(int tID)
        {
            if (tID<1)
                return;
            if (!m_TransInfos.Contains(m_TransInfos[tID-1]))
                return;
            CameraTransformDef info = m_TransInfos[tID - 1];
            moveID = tID;
            SetNowHeroMovePos(tID);
            if (info == null)
            {
                return;
            }
            SEventHeroMove heroMove = new SEventHeroMove();
            heroMove.vTragetPos = GHelp.StringToVec3(info.Pos);
            heroMove.vTragetEul = GHelp.StringToVec3(info.Rorate);
            heroMove.fAnimationDuration = info.DurationTime;
            if (info.DurationTime == 0)
            {
                heroMove.bAnimation = false;
            }
            else
            {
                heroMove.bAnimation = true;
            }
            GHelp.FireExecute(DGlobalEvent.EVENT_HERO_MOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, heroMove);
        }


        public void GetTouchMoveinfoByID(int tID)
        {
            if (tID < 1)
                return;
            if (!m_TransInfos.Contains(m_TransInfos[tID - 1]))
                return;
            CameraTransformDef info = m_TransInfos[tID - 1];
            moveID = tID;
            SetNowHeroMovePos(tID);
            if (info == null)
            {
                return;
            }
            HapticDevice_Transform touchMove = new HapticDevice_Transform();
            touchMove.pos = GHelp.StringToVec3(info.Pos);
            touchMove.rotation = GHelp.StringToVec3(info.Rorate);
            touchMove.scale = GHelp.StringToVec3(info.Scale);
            touchMove.handType = EMHandType.All;
           
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_CHANGE_HAPTICDEVICE_TRANSFORM, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, touchMove);
        }

        public int GetNowHeroMovePos()
        {
            return moveID;
        }

        public void SetNowHeroMovePos(int tID)
        {

        }

        public CameraTransformDef GetCameraTransformInfoByID(int tID)
        {
            return m_TransInfos[tID];
        }

        public int GetPosCount() {
            return m_TransInfos.Count;
        }
    }
}
