﻿/// <summary>
/// ButtonAudioDef
/// </summary>
/// <remarks>
/// 2021/11/3 11:26:10: 创建. 王正勇 <br/>
/// 按钮音效数据类
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// 模块类型
    /// </summary>
    public enum ModuleType
    {
        /// <summary>
        /// 通用
        /// </summary>
        General = 0,
        /// <summary>
        /// 组装机器人
        /// </summary>
        AssemblyRobot=1,
        /// <summary>
        /// 编程
        /// </summary>
        Programme=2,
        /// <summary>
        /// 小游戏
        /// </summary>
        MiniGames=3,
        /// <summary>
        /// 擂台赛
        /// </summary>
        Challenge=4,
        /// <summary>
        /// 答题
        /// </summary>
        Answer=5,
        /// <summary>
        /// 作业
        /// </summary>
        HomeWork=6,
        /// <summary>
        /// 任务
        /// </summary>
        Task=7,
        /// <summary>
        /// 运行积木块
        /// </summary>
        RunBlock=8,
        /// <summary>
        /// 无
        /// </summary>
        None,
    }
    /// <summary>
    /// 按钮类型
    /// </summary>
    public enum ButtonTriggerMode
    {
        /// <summary>
        /// 默认按钮
        /// </summary>
        DefaultBtn = 0,
        /// <summary>
        /// 确定/取消按钮
        /// </summary>
        ConfirmOrCancelBtn = 1,
        /// <summary>
        /// 返回
        /// </summary>
        GoBackBtn = 2,
        /// <summary>
        /// 触发弹窗
        /// </summary>
        TriggerPopupBtn = 3,
        /// <summary>
        /// 切换按钮
        /// </summary>
        CutBtn = 4,
        /// <summary>
        /// 打开/关闭按钮
        /// </summary>
        OpenOrCloseBtn = 5,
        /// <summary>
        /// 截图按钮
        /// </summary>
        ScreenshotBtn = 6,
        /// <summary>
        /// 运行编程按钮
        /// </summary>
        RunProgrammeBtn = 7,
        /// <summary>
        /// 停止编程按钮
        /// </summary>
        StopProgrammeBtn = 8,
        /// <summary>
        /// 撤销按钮
        /// </summary>
        ProgrammeRevocationBtn = 9,
        /// <summary>
        /// AR拍照按钮
        /// </summary>
        ARPhotographBtn = 10,
        /// <summary>
        /// 正确答题
        /// </summary>
        CorrectAnswer = 11,
        /// <summary>
        /// 错误答题
        /// </summary>
        MistakeAnswer = 12,
        /// <summary>
        /// 答题奖励【答完题给星星】
        /// </summary>
        AnswerAward = 13,
        /// <summary>
        /// 拖出零部件
        /// </summary>
        FishUpParts = 14,
        /// <summary>
        /// 零部件飞回部件栏
        /// </summary>
        PartsBackOrigin = 15,
        /// <summary>
        /// 正确拼搭
        /// </summary>
        CorrectAssemb = 16,
        /// <summary>
        /// 子目标搭建完成
        /// </summary>
        SubgoalSetUpOver = 17,
        /// <summary>
        /// 拖出积木块
        /// </summary>
        FishUpBlcok = 18,
        /// <summary>
        /// 积木块拼接
        /// </summary>
        BlockJoint = 19,
        /// <summary>
        /// 代码块放置
        /// </summary>
        BlockPut = 20,
        /// <summary>
        /// 代码块删除
        /// </summary>
        BlockDel = 21,
        /// <summary>
        /// 展开代码块
        /// </summary>
        OpenBlockClassly = 22,
        /// <summary>
        /// 弹出结算
        /// </summary>
        PopupSettlement = 23,
        /// <summary>
        /// 金币结算
        /// </summary>
        GoldSettlement = 24,
        /// <summary>
        /// 点击宝箱
        /// </summary>
        ClickTreasureChest = 25,
        /// <summary>
        /// 倒计时
        /// </summary>
        CountDown = 26,
        /// <summary>
        /// 释放技能
        /// </summary>
        ReleaseSkills = 27,
        /// <summary>
        /// 移动按钮
        /// </summary>
        MoveBtn = 28,
        /// <summary>
        /// 小地图
        /// </summary>
        MineMap=29,
        /// <summary>
        /// 源码
        /// </summary>
        SourceCode=30,
        /// <summary>
        /// 播放视频
        /// </summary>
        PlayVideo=31,
        /// <summary>
        /// 任务引导
        /// </summary>
        TaskIssue=32,
        /// <summary>
        /// 无
        /// </summary>
        None,
    }
}
