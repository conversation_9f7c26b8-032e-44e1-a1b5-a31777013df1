﻿/// <summary>
/// PersonalDataMenuCenter
/// </summary>
/// <remarks>
/// 2023.7.12: 创建. YSH <br/>
/// 个人中心功能入口<br/>
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace GLib.Client
{
    public class PersonalDataMenuCenter : ISchemeNode, IPersonalDataMenuCenter
    {
        private const string PERSONALDATAMENU_INFO = "PersonalDataMenu";

        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }

        private float m_progress = 0.0f;
        public float Progress
        {
            get { return m_progress; }
            set { m_progress = value; }
        }

        private Dictionary<int, PersonalDataMenuDef> m_PersonalDataMenuByID;

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public void Release()
        {
            m_PersonalDataMenuByID.Clear();
            m_PersonalDataMenuByID = null;
        }

        public PersonalDataMenuCenter()
        {
            m_PersonalDataMenuByID = new Dictionary<int, PersonalDataMenuDef>();
        }

        public bool LoadScheme()
        {
            string strPath = PERSONALDATAMENU_INFO;
            Progress = 0.0f;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                PersonalDataMenuDef map = new PersonalDataMenuDef();
                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.Name = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Icon = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Type = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.FunctionType = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.WebUrl = pCSVReader.GetString(nRow, tmp_col++, "");
                map.WebUrlParam = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Sort = pCSVReader.GetInt(nRow, tmp_col++, 0);
                m_PersonalDataMenuByID.Add(map.Id, map);
            }
            // 按sort字段排序
            m_PersonalDataMenuByID = m_PersonalDataMenuByID.OrderBy(x => x.Value.Sort).ToDictionary(x => x.Key, x => x.Value);
            Progress = 1.0f;
            return true;
        }

        /// <summary>
        /// 获取所有表格数据
        /// </summary>
        public Dictionary<int, PersonalDataMenuDef> GetAllMenu()
        {
            return m_PersonalDataMenuByID;
        }
    }
}
