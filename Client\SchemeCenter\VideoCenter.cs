﻿using game.schemes;
using GLib.Common;
/// <summary>
/// VideoCenter
/// </summary>
/// <remarks>
/// 2021.7.26: 创建. 吴航 <br/>
/// 视频 <br/>
/// </remarks>
using System;
using System.Collections.Generic;

namespace GLib.Client
{
    public class VideoCenter : ISchemeNode, IVideoCenter
    {
        private List<VideoConfig.Types.Item> m_Videos;

        private const string ENTITY_CONFIG = "Video";


        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public VideoCenter()
        {
            m_Videos = new List<VideoConfig.Types.Item>();
        }

        ~VideoCenter() { }

        public bool Create()
        {
            if (!LoadScheme())
            {
                TRACE.ErrorLn("VideoConfig load fail!");
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = ENTITY_CONFIG;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Entity);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Entity(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }
            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                VideoConfig.Types.Item mVideoConfig = new VideoConfig.Types.Item();

                mVideoConfig.VideoId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                mVideoConfig.Path = pCSVReader.GetString(nRow, tmp_col++, "");
                mVideoConfig.Content = pCSVReader.GetString(nRow, tmp_col++, "");
                m_Videos.Add(mVideoConfig);
            }
          
            return true;
        }

        public void Release()
        {
            m_Videos.Clear();
            m_Videos = null;
        }

        public VideoConfig.Types.Item GetVideoInfoByID(int VideoID)
        {
            for (int i = 0; i < m_Videos.Count; i++)
            {
                if (m_Videos[i].VideoId == VideoID)
                {
                    return m_Videos[i];
                }
            }
            TRACE.ErrorLn(string.Format("未找到配置:{0}的视频", VideoID));
            return null;
        }

        public List<VideoConfig.Types.Item> GetAllVideo()
        {
            return m_Videos;
        }
    }
}
