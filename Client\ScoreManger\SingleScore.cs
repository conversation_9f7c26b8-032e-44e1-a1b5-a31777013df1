﻿/// <summary>
/// SingleScore
/// </summary>
/// <remarks>
/// 2023/2/17 13:39:06: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using GLib.LitJson;

namespace GLib.Client
{
    public class SingleScore : HTTP_Response_Handler
    {
        /// <summary>
        /// 网页下载数据
        /// </summary>
        public List<CreateTrainRoot> m_TrainRoot;
        public CourseData m_CurrentDate;

        /// <summary>
        /// 项目的步骤信息，数据用于显示任务完成度，所以会剔除大步骤里面没有得分点的情况
        /// </summary>
        public List<Step> m_ProjectInfo;
        
        /// <summary>
        /// 项目的步骤信息，数据用于显示结算页的结果显示，不会剔除大步骤里面没有得分点的情况
        /// </summary>
        public List<Step> m_ProjectInfoAll;

        /// <summary>
        /// 分数数据
        /// </summary>
        private Dictionary<string, ScoreInfo> m_ScoreInfoDic;
        public Dictionary<string, ScoreInfo> ScoreInfo
        {
            get { return m_ScoreInfoDic; }
        }

        /// <summary>
        /// 分数条件
        /// </summary>
        private Dictionary<int, ScoreConditionInfo> m_ScoreConditoion;
        public Dictionary<int, ScoreConditionInfo> ScoreConditoionInfo
        {
            get { return m_ScoreConditoion; }
        }
        //网页上的得分数据
        private Dictionary<string, ScoreInfo> m_TempInfos;
        
        private Dictionary<string, ScoreInfo> m_TempInfosShowScoreWindow;


        private List<ScoreInfo> m_TeminfoList;
        //已经判断的得分ID
        private List<int> m_CurCaulculateScore;
        //得分细节
        public List<ScoreInfo> m_FinishAllDetail;

        private List<ScoreInfo> m_FinishDetail;
        private List<ScoreInfo> m_FinishDetailAll;

        private List<ScoreStepInfo> m_ScoreStepInfo;
        private List<ScoreStepInfo> m_ScoreStepInfoAll;

        private List<ScoreInfo> m_AllScoreInfo;
        private List<ScoreInfo> m_YesFinishDetail;
        public List<MistakeInfo> m_MistakeInfos;
        //总得分
        private float m_TotleScore;

        public float TotleScore
        {
            set { m_TotleScore = value; }
            get { return m_TotleScore; } 
        }
        //总分
        private float m_TotalPoints;
        public float TotalPoints 
        {
            get { return m_TotalPoints; }
        }

        //可得分任务数量
        public int m_TotalPointsNumber;

        public int TotalPointsNumber
        {
            get { return m_TotalPointsNumber; }
        }
        public void Init(CourseData course)
        {
            //m_CurrentDate = course;
            m_CurrentDate = course;
            m_TrainRoot = new List<CreateTrainRoot>();
            m_TempInfos = new Dictionary<string, ScoreInfo>();
            m_TempInfosShowScoreWindow = new Dictionary<string, ScoreInfo>();
            m_CurCaulculateScore = new List<int>();
            m_FinishDetail = new List<ScoreInfo>();
            m_FinishDetailAll = new List<ScoreInfo>();
            m_FinishAllDetail = new List<ScoreInfo>();
            m_ScoreStepInfo = new List<ScoreStepInfo>();
            m_ScoreStepInfoAll = new List<ScoreStepInfo>();
            m_ProjectInfo = new List<Step>();
            m_ProjectInfoAll = new List<Step>();
            m_AllScoreInfo= new List<ScoreInfo>();
            m_YesFinishDetail = new List<ScoreInfo>();
            m_TeminfoList = new List<ScoreInfo>();
            m_MistakeInfos = new List<MistakeInfo>();
            m_FinishDetail.Clear();
            m_FinishDetailAll.Clear();
            m_FinishAllDetail.Clear();
            m_TempInfos.Clear();
            m_TempInfosShowScoreWindow.Clear();
            m_CurCaulculateScore.Clear();
            m_ProjectInfo.Clear();
            m_ProjectInfoAll.Clear();
            m_TrainRoot.Clear();
            m_YesFinishDetail.Clear();
            m_MistakeInfos.Clear();
            m_AllScoreInfo.Clear();
            m_TotleScore = 0;
            m_TotalPoints = 0;
            m_TotalPointsNumber = 0;
            GetScoreStepInfo();
            GetScoreList();
        }

        public void LoadScoreData(int patientId)
        {
            int courseCfgId = GlobalGame.Instance.CourseMgr.GetCurCoursePatientId(m_CurrentDate.Id, patientId);
            GlobalGame.Instance.SchemeCenter.GetScoreCondition().LoadScheme(courseCfgId, (date) =>
            {
                m_ScoreConditoion = date;
            });
       
            GlobalGame.Instance.SchemeCenter.GetScoreInfo().LoadScheme(courseCfgId, (date) =>
            {
                m_ScoreInfoDic = date;
                GetCheckPoint();
            });
        }

        public void Realse()
        {
            m_FinishDetail.Clear();
            m_FinishDetailAll.Clear();
            m_FinishAllDetail.Clear();
            m_CurCaulculateScore.Clear();
            m_TempInfos.Clear();
            m_TempInfosShowScoreWindow.Clear();
            m_TrainRoot.Clear();
            m_TeminfoList.Clear();
        }

        public void GetScoreStepInfo()
        {
            m_ProjectInfo.Clear();
            string webPath = string.Format("{0}{1}", WebURL.ProjectStep, m_CurrentDate.Id);
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this, null, "");
        }
        public void GetScoreList()
        {
            string webPath = string.Format("{0}{1}", WebURL.CheckPoints, m_CurrentDate.Id);
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this, null, "");
        }

        public void GetCheckPoint()
        {
            m_TempInfos.Clear();
            m_TeminfoList.Clear();
            SetScore();
            SetStageInfo();
        }
        public void OnResponse(byte[] pContent, uint dwLen, string url)
        {
            string data = Encoding.UTF8.GetString(pContent);
        }

        public void OnError(uint dwError, string url)
        {
            Debug.Log("====== : ScoreOnError ");
        }

        public void OnLocation(string new_url, string url)
        {
            Debug.Log("====== :ScoreOnLocation ");
        }

        public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
        {
            string data = Encoding.UTF8.GetString(pData);
            if (url.Contains(WebURL.CheckPoints))
            {
                if (data != null)
                {
                    TRACE.WarningLn("从" + url + "获取评分点训练数据   " + data);
                    m_TrainRoot = JsonMapper.ToObject<List<CreateTrainRoot>>(data);
                }
            }
            if (url.Contains(WebURL.ProjectStep))
            {
                TRACE.WarningLn("从" + url + "获取评分点训练数据m_ProjectInfo++  " + data);
                List<Step> projectInfo = JsonMapper.ToObject<List<Step>>(data);
                foreach (var item in projectInfo)
                {
                    bool canAdd = false;
                    foreach (var temp in item.checkPoints)
                    {
                        if (temp.point!=-9999)
                        {
                            canAdd = true;
                        }
                    }
                    if (canAdd)
                    {
                        m_ProjectInfo.Add(item);
                    }
                }
                foreach (var item in projectInfo)
                {
                    bool canAdd = false;
                    foreach (var temp in item.checkPoints)
                    {
                        canAdd = true;
                    }
                    if (canAdd)
                    {
                        m_ProjectInfoAll.Add(item);
                    }
                }
            }

            return true;

        }

        public EMHTTP_METHOD GetCurrentHttpMethod()
        {
            return EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB;
        }
        /// <summary>
        /// 设置分数
        /// </summary>
        public void SetScore()
        {
            m_TotalPoints = 0;
            m_TotalPointsNumber = 0;
            foreach (var item in m_TrainRoot)
            {
                if (m_ScoreInfoDic.ContainsKey(item.alias))
                {
                    ScoreInfo info=m_ScoreInfoDic[item.alias];
                    info.name = item.name;
                    info.AddScore = (float)item.point;
                    info.ConditionName = item.name;
                    info.StepID = item.step.id;
                    info.FinalScore = 0;
   
                    if (m_ScoreInfoDic[item.alias].Deduction!=0)
                    {
                        m_ScoreInfoDic[item.alias].FinalScore = m_ScoreInfoDic[item.alias].Deduction;
                    }
                    if (m_ScoreInfoDic[item.alias].AddScore != -9999)
                    {
                        m_TotalPoints += (float)item.point;
                        m_TotalPointsNumber++;
                    }
                    else
                    {
                        info.FinalScore = 0;
                    }
                    m_TempInfos.Add(item.alias, info);
                    m_TempInfosShowScoreWindow.Add(item.alias, info);
                    m_TeminfoList.Add(info);
                }
            }
        }
        /// <summary>
        /// 是否是跳步骤
        /// </summary>
        /// <param name="isJump"></param>
        /// <param name="temAlias"></param>
        /// <param name="conditionDate"></param>
        public void CalculateScore(bool isJump,string temAlias,string conditionDate,bool succed)
        {
            if (!m_TempInfos.ContainsKey(temAlias))
            {
                
            }
            else
            {
                ScoreInfo temScore = m_TempInfos[temAlias];
      
                if (m_CurCaulculateScore.Contains(temScore.ID))
                {

                }
                else
                {
                    if (!succed)
                    {
                        temScore.FinalScore = -temScore.Deduction;
                    }
                    else if (isJump)
                    {

                        temScore.FinalScore = -temScore.Deduction;
           
                    }
                    else if (!string.IsNullOrEmpty(temScore.ConditionType))
                    {
                        bool CanAdd = true;
                        string[] temCondition = temScore.ConditionType.Split(';');
                        foreach (var item in temCondition)
                        {
                            int condId = int.Parse(item);
                            ScoreConditionInfo condition = null;
                            m_ScoreConditoion.TryGetValue(condId, out condition);
                            if (condition == null)
                            {
                                TRACE.ErrorLn("当前得分条件ID配置错误,ID：" + temScore.ID);
                            }
                            switch ((ScoreConditionType)condition.ConditionType)
                            {
                                case ScoreConditionType.None:
                                    break;
                                case ScoreConditionType.WithOutNext:
                                    {
                                        foreach (var temp in m_CurCaulculateScore)
                                        {
                                            if (temp > temScore.ID)
                                            {
                                                CanAdd = false;
                                            }
                                        }
                                    }
                                    break;
                                case ScoreConditionType.WithOutLast:
                                    {
                                        List<int> temNeedFinish = new List<int>();
                                        foreach (var temItem in m_TempInfos.Values)
                                        {
                                            if (temItem.ID < temScore.ID)
                                            {
                                                temNeedFinish.Add(temItem.ID);
                                            }
                                        }
                                        CanAdd = CompareListEquals(temNeedFinish, m_CurCaulculateScore);
                                    }
                                    break;
                                case ScoreConditionType.SpecialSteps:
                                    {
                                        string[] temSpecial = condition.ConditionDate.Split(';');
                                        for (int i = 0; i < temSpecial.Length; i++)
                                        {
                                            int special = int.Parse(temSpecial[i]);
                                            if (!m_CurCaulculateScore.Contains(special))
                                            {
                                                CanAdd = false;
                                            }
                                        }
                                    }
                                    break;
                                case ScoreConditionType.IncompleteInterval:
                                    {
                                        string[] temSpecial = condition.ConditionDate.Split(';');
                                        List<int> scoreID = new List<int>();
                                        for (int i = 0; i < temSpecial.Length; i++)
                                        {
                                            int temID = int.Parse(temSpecial[i]);
                                            if (i==0)
                                            {
                                                if (temID < 1)
                                                {
                                                    temID = 1;
                                                }
                                            }
                                            if (i==1)
                                            {
                                                if (temID > m_TeminfoList[m_TeminfoList.Count-1].ID)
                                                {
                                                    temID = m_TeminfoList[m_TeminfoList.Count - 1].ID;
                                                }
                                            }
                                            scoreID.Add(temID);
                                        }
                                        for (int i = scoreID[0]; i <= scoreID[1]; i++)
                                        {
                                            if (m_CurCaulculateScore.Contains(i))
                                            {
                                                CanAdd = false;
                                            }
                                        }
                                    }
                                    break;
                                case ScoreConditionType.FinishInterval:
                                    {
                                        string[] temSpecial = condition.ConditionDate.Split(';');
                                        List<int> scoreID = new List<int>();
                                        for (int i = 0; i < temSpecial.Length; i++)
                                        {
                                            int temID = int.Parse(temSpecial[i]);
                                            if (i == 0)
                                            {
                                                if (temID < 1)
                                                {
                                                    temID = 1;
                                                }
                                            }
                                            if (i == 1)
                                            {
                                                if (temID > m_TeminfoList[m_TeminfoList.Count - 1].ID)
                                                {
                                                    temID = m_TeminfoList[m_TeminfoList.Count - 1].ID;
                                                }
                                            }
                                            scoreID.Add(temID);
                                        }
                                        for (int i = scoreID[0]; i <= scoreID[1]; i++)
                                        {
                                            if (!m_CurCaulculateScore.Contains(i))
                                            {
                                                CanAdd = false;
                                            }
                                        }
                                    }
                                    break;
                                case ScoreConditionType.SpecialTaskSteps:
                                    {
                                        if (!string.IsNullOrEmpty(conditionDate))
                                        {
                                            if (!conditionDate.Contains(condition.ConditionDate))
                                            {
                                                CanAdd = false;
                                            }
                                        }
                                    }
                                    break;
                            }
      
                        }
                        if (CanAdd)
                        {
                            //增加判断，避免多个得分条件重复为分数赋值
                            if (temScore.FinalScore <= 0)
                            {
                                temScore.FinalScore += temScore.AddScore;
                            }
                        }
                        else
                        {
                            //ScoreInfo存在多个加分条件时，减分条件为0不对FinalScore做处理，解决bug
                            if (temScore.Deduction == 0)
                            {

                            }
                            else
                            {
                                temScore.FinalScore = -temScore.Deduction;
                            }
                        }
                    }
                    else
                    {
                        temScore.FinalScore += temScore.AddScore;
                    }
                    m_CurCaulculateScore.Add(temScore.ID);
                    if (temScore.AddScore != -9999)
                    {
                        m_TotleScore += temScore.FinalScore;
                    }
                    if (!m_FinishDetail.Contains(temScore))
                    {                   
                        m_FinishDetail.Add(temScore);
          
                    }
                    if (!m_FinishDetailAll.Contains(temScore))
                    {                   
                        m_FinishDetailAll.Add(temScore);
          
                    }
                    GetExamData(temScore);
                }

            }
        }

        /// <summary>
        /// 判断两个集合是否相等
        /// </summary>
        /// <param name="eventTypes1"></param>
        /// <param name="eventTypes2"></param>
        /// <returns></returns>
        public bool CompareListEquals(List<int> eventTypes1, List<int> eventTypes2)
        {
            return eventTypes1.Count <= eventTypes2.Count && eventTypes1.Count(t => !eventTypes2.Contains(t)) == 0;
        }

        public void GetExamData(ScoreInfo temScore)
        {
            if (GlobalGame.Instance.CourseMgr.GetCourseState() == CourseState.MockTest)
            {
                //if (GlobalGame.Instance.CourseMgr.GetCourseState() == CourseState.MockTest)
                //{
                AddMockExamData addMockExamData = new AddMockExamData();
                //addMockExamData.student = new NEntity();
                addMockExamData.student = GlobalGame.Instance.GameSDK.GetUserInfo().userID;
                addMockExamData.checkpointEntity = temScore.ID.ToString();
                addMockExamData.stepName = temScore.TaskDec;
                addMockExamData.name = temScore.ConditionName;
                //addMockExamData.trainInfo = new NEntity();
                addMockExamData.trainInfo = GlobalGame.Instance.CourseMgr.GetTrainID().ToString();
                addMockExamData.point = temScore.FinalScore;
                addMockExamData.alias = temScore.Alias;
                addMockExamData.IsDone = "true";
                addMockExamData.stepId = temScore.StepID;
                string dataBody = string.Empty;
                //dataBody =JsonMapper.ToJson(addMockExamData);
                dataBody = JsonUtility.ToJson(addMockExamData);
                byte[] bodyRaw = Encoding.UTF8.GetBytes(dataBody);
                CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, WebURL.AddMockExam, this, null, "", bodyRaw);
            }
            if (GlobalGame.Instance.CourseMgr.GetCourseState() == CourseState.SimulationTraining)
            {
                TaskStepUpdate taskStepUpdate = new TaskStepUpdate();
                taskStepUpdate.userId = GlobalGame.Instance.GameSDK.GetUserInfo().userID;
                taskStepUpdate.trainInfo = GlobalGame.Instance.CourseMgr.GetTrainID().ToString();
                taskStepUpdate.taskId = int.Parse(temScore.Alias);
                taskStepUpdate.feedId = temScore.StepID;
                int count = GetScoreStepID(temScore.StepID);
                foreach (var item in m_ProjectInfo)
                {
                    if (item.id == temScore.StepID)
                    {
                        taskStepUpdate.feedDetail = item.name;
                    }
                }
                taskStepUpdate.feedTotalNumber = count;
                taskStepUpdate.taskName = temScore.ConditionName;  //
                taskStepUpdate.isDone = "1";
                if (temScore.FinalScore != temScore.AddScore)
                {
                    taskStepUpdate.isDone = "0";
                }
                string dataBody = string.Empty;
                dataBody = JsonUtility.ToJson(taskStepUpdate);
                byte[] bodyRaw = Encoding.UTF8.GetBytes(dataBody);
                CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, WebURL.UpdateTask, this, null, "", bodyRaw);
            }
        }

        /// <summary>
        /// 获取细节数据
        /// </summary>
        public List<ScoreStepInfo> GetFinishDetail()
        {
            m_ScoreStepInfo.Clear();
            m_YesFinishDetail.Clear();
            Dictionary<int, int> temAllDic = new Dictionary<int, int>();
            Dictionary<int, int> temFinDic = new Dictionary<int, int>();
            List<int> temAllBigStepID = new List<int>();
            foreach (var item in m_TempInfos.Values)
            {
                if (item.AddScore == -9999)
                {
                   
                }
                else
                {
                    if (temAllDic.ContainsKey(item.StepID))
                    {
                        temAllDic[item.StepID] += 1;
                    }
                    else
                    {
                        temAllDic.Add(item.StepID, 1);
                    }
                }

            }
            foreach (var item in m_FinishDetail)
            {
                if (item.AddScore == -9999)
                {

                }
                else
                {
                    if (item.FinalScore != 0)
                    {
                        m_YesFinishDetail.Add(item);
                    }
                }
            }
            foreach (var item in m_YesFinishDetail)
            {
                if (temFinDic.ContainsKey(item.StepID))
                {
                    if (item.FinalScore != 0)
                    {
                        temFinDic[item.StepID] += 1;
                    }
             
                }
                else
                {
                    temFinDic.Add(item.StepID, 1);
                }
            }
            foreach (var item in temAllDic)
            {
                ScoreStepInfo scoreStep = new ScoreStepInfo();
                scoreStep.ScoreStepID = item.Key;
                scoreStep.TotalNum = item.Value;
                scoreStep.Num = 0;
                scoreStep.Order = GetIsOrder(item.Key);
                if (temFinDic.ContainsKey(item.Key))
                {
                    scoreStep.Num = temFinDic[item.Key];
                }
                m_ScoreStepInfo.Add(scoreStep);
            }
            m_ScoreStepInfo.Sort((a, b) => a.Order.CompareTo(b.Order));
            return m_ScoreStepInfo;
        }
        
        public List<ScoreStepInfo> GetFinishDetailShowScoreWindow()
        {
            m_ScoreStepInfoAll.Clear();
            m_YesFinishDetail.Clear();
            Dictionary<int, int> temAllDic = new Dictionary<int, int>();
            Dictionary<int, int> temFinDic = new Dictionary<int, int>();
            List<int> temAllBigStepID = new List<int>();
            foreach (var item in m_TempInfosShowScoreWindow.Values)
            {
                if (temAllBigStepID.Contains(item.StepID))
                {
                    
                }
                else
                {
                    temAllBigStepID.Add(item.StepID);
                }
                
                if (temAllDic.ContainsKey(item.StepID))
                {
                    temAllDic[item.StepID] += 1;
                }
                else
                {
                    temAllDic.Add(item.StepID, 1);
                }
            }
            foreach (var item in m_FinishDetail)
            {
                if (item.AddScore == -9999)
                {

                }
                else
                {
                    if (item.FinalScore != 0)
                    {
                        m_YesFinishDetail.Add(item);
                    }
                }
            }
            foreach (var item in m_YesFinishDetail)
            {
                if (temFinDic.ContainsKey(item.StepID))
                {
                    if (item.FinalScore != 0)
                    {
                        temFinDic[item.StepID] += 1;
                    }
             
                }
                else
                {
                    temFinDic.Add(item.StepID, 1);
                }
            }
            foreach (var item in temAllDic)
            {
                ScoreStepInfo scoreStep = new ScoreStepInfo();
                scoreStep.ScoreStepID = item.Key;
                scoreStep.TotalNum = item.Value;
                scoreStep.Num = 0;
                scoreStep.Order = GetIsOrder(item.Key);
                if (temFinDic.ContainsKey(item.Key))
                {
                    scoreStep.Num = temFinDic[item.Key];
                }
                m_ScoreStepInfoAll.Add(scoreStep);
            }
            m_ScoreStepInfoAll.Sort((a, b) => a.ScoreStepID.CompareTo(b.ScoreStepID));
            return m_ScoreStepInfoAll;
        }

        /// <summary>
        /// 获取分支支线信息
        /// </summary>
        /// <returns></returns>
        public List<ScoreInfo> GetLineFinish()
        {
            List<string> Specital = GlobalGame.Instance.CourseMgr.GetSpecialID();
            List<ScoreInfo> temp = new List<ScoreInfo>();
            foreach (var item in m_ScoreInfoDic.Values)
            {
                bool isCanOpen = true;
                if (string.IsNullOrEmpty(item.Method))
                {
                    
                }
                else
                {
                    if (!Specital.Contains(item.Method))
                    {
                        isCanOpen = false;
                    }
                }
                if (isCanOpen)
                {
                    temp.Add(item);
                }
            }
            return temp;
        }
        /// <summary>
        /// 获取分支完成信息
        /// </summary>
        /// <returns></returns>
        public List<ScoreStepInfo> GetLineFinishDetail()
        {
            m_ScoreStepInfo.Clear();
            m_YesFinishDetail.Clear();
            Dictionary<int, int> temAllDic = new Dictionary<int, int>();
            Dictionary<int, int> temFinDic = new Dictionary<int, int>();
            foreach (var item in GetLineFinish())
            {
                if (item.AddScore == -9999)
                {

                }
                else
                {
                    if (temAllDic.ContainsKey(item.StepID))
                    {
                        temAllDic[item.StepID] += 1;
                    }
                    else
                    {
                        temAllDic.Add(item.StepID, 1);
                    }
                }
            }
            foreach (var item in m_FinishDetail)
            {
                if (item.AddScore == -9999)
                {

                }
                else
                {
                    if (item.FinalScore != 0)
                    {
                        m_YesFinishDetail.Add(item);
                    }
                }
            }
            foreach (var item in m_YesFinishDetail)
            {
                if (temFinDic.ContainsKey(item.StepID))
                {
                    if (item.FinalScore != 0)
                    {
                        temFinDic[item.StepID] += 1;
                    }

                }
                else
                {
                    temFinDic.Add(item.StepID, 1);
                }
            }
            foreach (var item in temAllDic)
            {
                ScoreStepInfo scoreStep = new ScoreStepInfo();
                scoreStep.ScoreStepID = item.Key;
                scoreStep.TotalNum = item.Value;
                scoreStep.Num = 0;
                scoreStep.Order = GetIsOrder(item.Key);
                if (temFinDic.ContainsKey(item.Key))
                {
                    scoreStep.Num = temFinDic[item.Key];
                }
                m_ScoreStepInfo.Add(scoreStep);
            }
            m_ScoreStepInfo.Sort((a, b) => a.Order.CompareTo(b.Order));
            return m_ScoreStepInfo;
        }
        public int GetScoreStepID(int stepId)
        {
            int count = 0;
            foreach (var item in m_TeminfoList)
            {
                if (item.StepID==stepId)
                {
                    count++;
                }
            }
            return count;
        }
        public int GetIsOrder(int projectID)
        {
            foreach (var item in m_ProjectInfo)
            {
                if (item.id == projectID)
                {
                    return item.stepOrder;
                }
            }
            return -1;
        }
        public Step GetProjectStep(int projectID)
        {
            foreach (var item in m_ProjectInfo)
            {
                if (item.id == projectID)
                {
                    return item;
                }
            }
            return null;
        }
        
        public Step GetProjectStepAll(int projectID)
        {
            foreach (var item in m_ProjectInfoAll)
            {
                if (item.id == projectID)
                {
                    return item;
                }
            }
            return null;
        }
        
        public string GetProjectStepByID(int ID)
        {
            return m_ProjectInfoAll[ID].name;
        }

        public List<ScoreInfo> GetAddMockExamDatas()
        {
            return SetAllRes();     
        }

        public List<ScoreInfo> SetAllRes() {
            m_FinishAllDetail = new List<ScoreInfo>();
            m_FinishAllDetail.Clear();
            foreach (var item in m_FinishDetail)
            {
                m_FinishAllDetail.Add(item);
            }
            foreach (var item in m_TempInfos.Values)
            {
                //-9999项不显示
                //if (item.AddScore != -9999)
                //{
                //    if (!m_FinishAllDetail.Contains(item))
                //    {
                //        m_FinishAllDetail.Add(item);
                //    }
                //} 
                if (!m_FinishAllDetail.Contains(item))
                {
                    m_FinishAllDetail.Add(item);
                }

            }

            return m_FinishAllDetail;
        }

        public List<ScoreInfo> GetAllScoreInfo()
        {
            return GetAllRes();
        }
        public List<ScoreInfo> GetAllRes()
        {           
            foreach (var item in m_TempInfos.Values)
            {
                if (!m_AllScoreInfo.Contains(item))
                {
                    m_AllScoreInfo.Add(item);
                }
            }
            return m_AllScoreInfo;
        }

        /// <summary>
        /// 根据别名获取每步的满分值
        /// </summary>
        /// <param name="alias"></param>
        /// <returns></returns>
        public double GetStepPoint(string alias) {
            for (int i = 0; i < m_TrainRoot.Count; i++)
            {
                if (alias== m_TrainRoot[i].alias)
                {
                    return m_TrainRoot[i].point;
                }
            }
            return 0;
        }
        public int GetFinfishCount()
        {
            int count = 0;
            foreach (var item in m_FinishDetail)
            {
                if (item.AddScore != -9999&& item.FinalScore != 0)
                {
                    count++;
                }
            }
            return count;
        }
        public ScoreInfo GetScoreInfoByID(string alias)
        {
            ScoreInfo temScore = null;
            m_TempInfos.TryGetValue(alias,out temScore);
            return temScore;
        }
        public List<ScoreInfo> GetAllScoreByStepID(string alias)
        {
            ScoreInfo temScore = null;
            m_TempInfos.TryGetValue(alias, out temScore);
            List<ScoreInfo> allScore = new List<ScoreInfo>();
            if (temScore!=null)
            {
                foreach (var item in m_TempInfos.Values)
                {
                    if (temScore.StepID == item.StepID)
                    {
                        allScore.Add(item);
                    }
                }
            }
            return allScore;
        }
        #region 跳多步

        public void SetStageInfo()
        {
            // 根据后台网页配置的数据来寻找对应的任务/问答数据(跳步显示的任务列表数据来源于此)
            // 1.根据网页配置的别名在任务表中找到对应的任务
            // 2.如果在任务表中找不到，再到问答表中根据别名找到对应的问答
            int paintid = int.Parse(GlobalGame.Instance.CourseMgr.GetNowCaseInfo().id);
            List<TaskStageInfoDef> taskStageInfoDefs = new List<TaskStageInfoDef>();
            for (int i = 0; i < m_TrainRoot.Count; i++)
            {
                if (m_TrainRoot[i].medId == paintid)
                {
                    TaskStageInfoDef taskStageInfoDef = new TaskStageInfoDef();
                    // 1.TaskInfo.csv
                    OperateInfo operateInfo = GlobalGame.Instance.SchemeCenter.GetTaskInfo().GetZhuXianTaskByAliasName(m_TrainRoot[i].alias);
                    if (operateInfo != null)
                    {
                        taskStageInfoDef.Order = m_TrainRoot[i].step.stepOrder;
                        taskStageInfoDef.Name = m_TrainRoot[i].step.name;
                        taskStageInfoDef.StageId = m_TrainRoot[i].id;
                        taskStageInfoDef.TaskId = operateInfo.Id;
                        taskStageInfoDef.createTrainRoot = m_TrainRoot[i];
                        if (taskStageInfoDefs.Find(item => (item.StageId == taskStageInfoDef.StageId)) == null)
                        {
                            taskStageInfoDefs.Add(taskStageInfoDef);
                        }
                        continue;
                    }
                    // 2.AnswerUIInfo.csv
                    operateInfo = GlobalGame.Instance.SchemeCenter.GetAnswerUI().GetZhuXianTaskByAliasName(m_TrainRoot[i].alias);
                    if (operateInfo != null)
                    {
                        taskStageInfoDef.Order = m_TrainRoot[i].step.stepOrder;
                        taskStageInfoDef.Name = m_TrainRoot[i].step.name;
                        taskStageInfoDef.StageId = m_TrainRoot[i].id;
                        taskStageInfoDef.TaskId = operateInfo.Id;
                        taskStageInfoDef.createTrainRoot = m_TrainRoot[i];
                        if (taskStageInfoDefs.Find(item => (item.StageId == taskStageInfoDef.StageId)) == null)
                        {
                            taskStageInfoDefs.Add(taskStageInfoDef);
                        }
                        continue;
                    }
                }
            }
            //排序
            taskStageInfoDefs.Sort((a, b) => { if (a.Order != b.Order) { return a.Order.CompareTo(b.Order); } else { return a.StageId.CompareTo(b.StageId); } ; });
            //taskStageInfoDefs.Sort((a, b) => a.StageId.CompareTo(b.StageId));
            //taskStageInfoDefs.Sort((a, b) => a.TaskId.CompareTo(b.TaskId));
            //查找后台配置和本地TaskInfo表任务没对应的任务
            List<OperateInfo> TaskInfos = GlobalGame.Instance.SchemeCenter.GetTaskInfo().GetTaskInfos();
            for (int i = 0; i < TaskInfos.Count; i++)
            {
                bool isHave = false;
                for (int j = 0; j < taskStageInfoDefs.Count; j++)
                {
                    if (TaskInfos[i].Id == taskStageInfoDefs[j].TaskId)
                    {
                        isHave = true;
                        break;
                    }
                }
                if (!isHave)
                {
                    TRACE.TraceLn(string.Format("未在后台配置找到主线任务:{0},请在后台或TaskInfo字段AliasName配置", TaskInfos[i].Id));
                }
            }
            GlobalGame.Instance.CourseMgr.SetTaskStageInfoDefs(taskStageInfoDefs);
        }
        #endregion
        #region 错题处理
        public void SetMistake(List<MistakeInfo> tempMis)
        {
            m_MistakeInfos.Clear();
            foreach (var item in tempMis)
            {
                m_MistakeInfos.Add(item);
            }
        }
        public List<MistakeInfo> GetMistake()
        {
            if (m_MistakeInfos.Count!=0)
            {
                return GetAllMistake();
            }
            else
            {
                return m_MistakeInfos;
            }
        }

        public List<MistakeInfo> GetAllMistake()
        {
            List<MistakeInfo> tempMis = new List<MistakeInfo>();
            foreach (var item in m_MistakeInfos)
            {
                if (item.done == 0&& item.point != -9999)
                {
                    tempMis.Add(item);
                }
            }
            if (m_TrainRoot.Count!=0)
            {
                List<int> temAlias = new List<int>();
                foreach (var item in m_MistakeInfos)
                {
                    temAlias.Add(item.alias);
                }
                //m_TrainRoot.Sort((a, b) => a.step.stepOrder.CompareTo(b.step.stepOrder));
                m_TrainRoot.OrderBy(a => a.step.stepOrder).ThenBy(a=>a.id);
                List<CreateTrainRoot> tempList = new List<CreateTrainRoot>();
                foreach (var item in m_TrainRoot)
                {
                    if (item.point!=-9999)
                    {
                        int temAlia = 0;
                        int.TryParse(item.alias, out temAlia);
                        if (temAlia != 0 && !temAlias.Contains(temAlia))
                        {
                            tempList.Add(item);
                        }
                    }
                }
                foreach (var item in tempList)
                {
                    MistakeInfo mistake = new MistakeInfo();
                    mistake.stepName = item.step.name;
                    mistake.checkpointName = item.name;
                    mistake.point = item.point;
                    mistake.done = 0;
                    mistake.medId = item.medId;
                    mistake.alias = int.Parse(item.alias);
                    tempMis.Add(mistake);
                }
            }
            return tempMis;
        }


        #endregion
        /// <summary>
        /// 结算上传的detail数据
        /// </summary>
        public Dictionary<int, int> GetUpdateDetail()
        {
            List<string> temMisInfo = new List<string>();
            List<ScoreInfo> temFinish = new List<ScoreInfo>();
            Dictionary<int, int> temFinDic = new Dictionary<int, int>();
            List<MistakeInfo> tem= GetAllMistake();
            //错题数据
            foreach (var item in tem)
            {
                ScoreInfo tempInfo=GetScoreInfoByID(item.alias.ToString());
                if (tempInfo!=null&&tempInfo.AddScore!=-9999)
                {
                    temMisInfo.Add(item.alias.ToString());
                }
            }
            //判断错题中的任务是否完成
            foreach (var item in m_FinishDetail)
            {
                if (item.AddScore == -9999)
                {

                }
                else
                {
                    bool canAdd = false;
                    if (temMisInfo.Contains(item.Alias))
                    {
                        canAdd = true;
                    }
                    if (canAdd && item.FinalScore>0)
                    {
                        temFinish.Add(item);
                    }
                }
            }
            foreach (var item in temFinish)
            {
                if (temFinDic.ContainsKey(item.StepID))
                {
                    if (item.FinalScore != 0)
                    {
                        temFinDic[item.StepID] += 1;
                    }

                }
                else
                {
                    temFinDic.Add(item.StepID, 1);
                }
            }
            return temFinDic;
        } 

    }
}