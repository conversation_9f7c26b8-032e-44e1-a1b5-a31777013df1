﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandTweenMove : IHandleCommand
    {
        private GameObject m_target;
        private Vector3 m_orgRotate;
        private Vector3 m_targetRotate;
        private Vector3 m_orgScale;
        private Vector3 m_orgPoint;
        private Vector3 m_pTargetPos;
        private bool m_isLocal = false;
        private Quaternion m_targetQua;
        private float time;
        private float rotateSpeed = 10f;

        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;

        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;
        private float startTime;
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;   //0.001f
        public COpHandleCommandTweenMove(SOpHandleCommand_TweenMove data)
        {
            m_target = data.target;
            runInstance = data.runInstance;
            m_others = data.otherCommand;
            m_pTargetPos = data.targetPos;
            m_orgRotate = data.orgRotate;
            m_targetRotate = data.targetRotate;
            m_orgScale = data.orgScale;
            m_orgPoint = data.orgPoint;
            time = data.time;
            m_isLocal = data.isLocal;
            m_targetQua = Quaternion.Euler(m_targetRotate);
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpTweenMove;
        }

        public void OnPause()
        {

        }

        public void release()
        {

        }
        Vector3 ptSource = Vector3.zero;
        Quaternion rotateSource= Quaternion.identity;
        public bool run()
        {
            if (!m_isPlay)
            {
                startTime = Time.time;
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
            }
            if (m_target == null)
            {
                m_isEnd = true;
                return false;
            }
            if (!m_isPlay)
            {//开始时重新设置默认坐标
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                if (m_orgPoint.x > -9999)
                {
                    if (m_isLocal)
                    {
                        m_target.transform.localPosition = m_orgPoint;
                    }
                    else
                    {
                        m_target.transform.position = m_orgPoint;
                    }
                }
                if (m_orgRotate.x > -9999)
                {
                    m_target.transform.localEulerAngles = m_orgRotate;
                }
                if (m_orgScale.x > -9999)
                {
                    m_target.transform.localScale = m_orgScale;
                }

                ptSource = m_target.transform.position;
                if (m_isLocal)
                {
                    ptSource = m_target.transform.localPosition;
                }
                rotateSource = m_target.transform.rotation;
                if (m_isLocal)
                {
                    rotateSource = m_target.transform.localRotation;
                }
            }
            float curTime = Time.time - startTime;
            Vector3 pos;
          
            float transRate = curTime / time;
            if (m_isLocal)
            {
                pos = Vector3.LerpUnclamped(ptSource, m_pTargetPos, transRate);
                SetPos(pos);
            }
            {
                pos = Vector3.LerpUnclamped(ptSource, m_pTargetPos, transRate);
                SetPos(pos);
            }

            if (m_targetRotate != null && m_targetRotate.x > -9999)
            {
                if (m_isLocal)
                    m_target.transform.localRotation = Quaternion.SlerpUnclamped(rotateSource, m_targetQua, transRate);
                else
                    m_target.transform.rotation = Quaternion.SlerpUnclamped(rotateSource, m_targetQua, transRate);
            }

            if (curTime >= time)
            {
                SetPos(m_pTargetPos);
                SetFinalRotate();
                return true;
            }
            return false;
        }

        void SetPos(Vector3 pos)
        {
            if (m_isLocal)
            {
                m_target.transform.localPosition = pos;
            }
            else
            {
                m_target.transform.position = pos;
            }
        }

        void SetFinalRotate()
        {
            if (m_targetRotate != null && m_targetRotate.x > -9999)
            {
                if (m_isLocal)
                    m_target.transform.localRotation = m_targetQua;
                else
                    m_target.transform.rotation = m_targetQua;
            }
        }

        public void update()
        {
        }
    }
}
