﻿/// <summary>
/// UniWebViewDef
/// </summary>
/// <remarks>
/// 2021/10/6 11:18:47: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// UniWebViewDef
    /// </summary>
    public class UniWebViewDef
    {
        /// <summary>
        /// 绑定到此有效负载的标识符。 它将在内部用于标识回调。  
        /// </summary>
        public string identifier;
        /// <summary>
        /// 此有效负载中包含的结果代码。 一般来说，“0”表示操作没有完成问题，而非零值意味着有问题。  
        /// </summary>
        public string resultCode;
        /// <summary>
        /// 从本机返回值或数据。 你应该看看  
        ///相应的api来知道到底包含了什么 
        /// </summary>
        public string data;
    }
    public enum LayoutType
    {
        /// <summary>
        /// 自定义
        /// </summary>
        UserDefined,
        /// <summary>
        /// 全屏
        /// </summary>
        FullScreen,
        /// <summary>
        /// 顶部缩进显示导航
        /// </summary>
        TopRetractShowNavig,
        /// <summary>
        /// 顶部缩进
        /// </summary>
        TopRetract,
        /// <summary>
        /// 底部缩进
        /// </summary>
        BottomRetract,
        /// <summary>
        /// 移走
        /// </summary>
        MoveTo,

    }
    public enum WebViewBackType
    {
        /// <summary>
        /// 无
        /// </summary>
        None,
        /// <summary>
        /// 课程返回
        /// </summary>
        CourseBack,
        /// <summary>
        /// 我的返回
        /// </summary>
        MineBack,
        /// <summary>
        /// 登陆返回
        /// </summary>
        LoginBack,
        /// <summary>
        /// AI训练返回
        /// </summary>
        AIDrillBack,

    }
}
