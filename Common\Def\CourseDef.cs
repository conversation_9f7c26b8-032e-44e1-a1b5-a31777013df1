﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public enum CourseIDDef
    {
        //换药、拆线
        huanyao = 34,
        //胃管置入
        weiguan = 61,
        //三腔二囊管
        sanqiangerrang = 62,
        //腰椎穿刺术
        yaozhui = 63,
        //胸腔穿刺术
        XiongQiang = 64,
        //动脉穿刺术
        DongMai = 65,
        /// 腹腔穿刺术
        FuChuan = 68,
        //静脉穿刺术
        JingMai = 70,
        /// <summary>
        /// 导尿术
        /// </summary>
        DaoNiaoShu=72,
        /// <summary>
        /// 吸痰术
        /// </summary>
        XiTan=73,
        /// <summary>
        /// 骨髓穿刺术
        /// </summary>
        GuSui=74,
        /// <summary>
        /// 清创术
        /// </summary>
        QingChuang = 77,

        //吸氧术//
        XiYang=78,

        /// <summary>
        /// 开放伤口止血包扎
        /// </summary>
        KaiFangShangKouZhiXueBaoZa = 79,

        /// <summary>
        /// 手术基本操作
        /// </summary>
        ShouShuJiBenCaoZuo = 83,
        /// <summary>
        /// 消毒与铺巾 
        /// </summary>
        XiaoDuYuPuJin = 84,

        /// <summary>
        /// 四肢骨折
        /// </summary>
        SiZhiGuZhe = 85,

        /// <summary>
        /// 对开式手术衣
        /// </summary>
        ChuanTuoShouShuYi = 81,

        /// <summary>
        /// 穿脱隔离服
        /// </summary>
        ChuanTuoGeLiFu = 86,
        /// <summary>
        /// 手术刷手法
        /// </summary>
        ShuaShouFa = 87,
        /// <summary>
        /// 脓肿切开术
        /// </summary>
        NongZhongQieKai = 90,
        /// <summary>
        /// 心肺复苏
        /// </summary>
        XinFeiFuSu = 91,
    }

    public enum CoursePatientIDDef
    {
        /// <summary>
        /// 四肢骨折前臂
        /// </summary>
        SiZhiGuZhe_QianBi = 8501,
        /// <summary>
        /// 四肢骨折上臂
        /// </summary>
        SiZhiGuZhe_ShangBi = 8502,
        
    }

    /// <summary>
    /// 获得课程数据v2  请求数据
    /// </summary>
    [Serializable]
    public class ReqCourseData {
        /// <summary>
        /// PC、VR、Android
        /// </summary>
        public string platform;
        /// <summary>
        /// 1:执医24项
        /// </summary>
        public string projectKind;
        /// <summary>
        /// 关键字（与医院有关、与项目配置的关键id）
        /// </summary>
        //public string projectKey;
    }

    /// <summary>
    /// 精品课程信息
    /// </summary>
    [Serializable]
    public class QualityCourseInfo
    {
        public int id;
        public int departmentID;
        public string departmentName;
        public string operaName;
        public byte operaImg;
    }

    /// <summary>
    /// 近期训练信息
    /// </summary>
    [Serializable]
    public class LastTrainInfo
    {
        public int departmentID;
        public string departmentName;
        public string operaName;
        //public byte operaImg;
    }

    [Serializable]
    public class Department {
        /// <summary>
        /// csv文件中科室id
        /// </summary>
        public int id;
        /// <summary>
        /// csv文件中科室名字
        /// </summary>
        public string DepartmentName;
    }

    /// <summary>
    /// <para name="id">科室id</para>
    /// <para name="name">科室名</para>
    /// <para name="departCode"></para>
    /// </summary>
    [Serializable]
    public class CourseInfoAndDepartment
    {
        /// <summary>
        /// 科室id
        /// </summary>
        public int id;
        /// <summary>
        /// 手术名字
        /// </summary>
        public string name;
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool avaliable;
        /// <summary>
        /// 科室名字
        /// </summary>
        public string departmentName;
        /// <summary>
        /// PC、VR、Android
        /// </summary>
        public string platform;
        /// <summary>
        /// 项目种类
        /// </summary>
        public int projectKind;
        /// <summary>
        /// 是否是精品课程
        /// </summary>
        public bool isElite;
    }

    /// <summary>
    ///<para name="id">数据库id</para>
    ///<para name="name">中文缩写</para>
    ///<para name="parent">中文全称</para>
    ///<para name="englishName">手术类型的英文名称</para>
    ///<para name="description">说明</para>
    ///<para name="available">是否可以点击，0代表没有，1代表有</para>
    ///<para name="department"></para>
    ///</summary>
    [Serializable]
    public class OperaListByID
    {
        /// <summary>
        /// id
        /// </summary>
        public int id;
        /// <summary>
        /// 中文缩写
        /// </summary>
        public string name;
        /// <summary>
        /// 中文全称
        /// </summary>
        //public string parent;
        /// <summary>
        /// 手术类型的英文名称
        /// </summary>
        //public string englishName;
        /// <summary>
        /// 说明
        /// </summary>
        //public string description;
        /// <summary>
        /// 是否开放选择，0代表不开放，1代表开放
        /// </summary>
        public bool avaliable;
        /// <summary>
        /// 
        /// </summary>
        public string departmentName;


        public string platform;
    }
    /// <summary>
    /// 当前课程中，场景数据管理
    /// </summary>
    public class CourseSceneInfo
    {
        /// <summary>
        /// 场景点位表格id
        /// </summary>
        public int ScenePointID;
        /// <summary>
        /// 实体id
        /// </summary>
        public uint EntityID;

        public string key;
    }

    public class TaskOpInfo
    {
        public int step;
        public int taskId;
        public string name;
    }

    [Serializable]
    public class TopCourseInfo
    {
        public List<TopCourseData> data;
        public string message;
        public string status;
    }

    [Serializable]
    public class TopCourseData
    {
        public int id;
        public string name;
        public string createTime;
        public string updateTime;
        public int createBy;
        public int updateBy;
        public string content;
        public string departID;
        public bool hasPic;

    }
}