﻿using game.schemes;
using GLib.Common;
/// <summary>
/// SchemeEntity
/// </summary>
/// <remarks>
/// 2019.7.3: 创建. 谌安 <br/>
/// 模型 <br/>
/// </remarks>
using System;
using System.Collections.Generic;

namespace GLib.Client
{
    public class SchemeEntity : ISchemeNode,ISchemeEntity
    {
        private Dictionary<int, Model.Types.Item> m_dictEntitiys;

        private const string ENTITY_CONFIG = "Model";

        private Model m_model;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public SchemeEntity()
        {
            m_model = new Model();
            m_dictEntitiys = new Dictionary<int, Model.Types.Item>();
        }

        ~SchemeEntity() { }

        public bool Create()
        {
            if (!LoadScheme())
            {
                TRACE.ErrorLn("Entity load fail!");
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = ENTITY_CONFIG;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Entity);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Entity(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }
            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                Model.Types.Item mEntity = new Model.Types.Item();

                mEntity.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                mEntity.PrefabPath = pCSVReader.GetString(nRow, tmp_col++, "");
                mEntity.Scale = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                mEntity.ModelName = pCSVReader.GetString(nRow, tmp_col++, "");
                mEntity.TinyMapID = pCSVReader.GetInt(nRow, tmp_col++, 0);

#if UNITY_EDITOR
                if (m_dictEntitiys.ContainsKey(mEntity.Id))
                {
                    TRACE.ErrorLn("Model Id Error:" + mEntity.Id);
                }
#endif
                m_dictEntitiys.Add(mEntity.Id, mEntity);
                m_model.Items.Add(mEntity);
            }
            GlobalGame.Instance.RenderLoader.SkinInit();
            return true;
        }

        public void Release()
        {
            m_dictEntitiys.Clear();
            m_dictEntitiys = null;
        }

        public Model.Types.Item GetEntityInfoByID(int sEntityID)
        {
            Model.Types.Item info = null;

            m_dictEntitiys.TryGetValue(sEntityID, out info);
            if (info == null)
            {
                TRACE.ErrorLn(Api.NTR(string.Format("未找到模型Id:{0}", sEntityID)));
            }
            return info;
        }

        public Model GetAllModel()
        {
            return m_model;
        }
    }
}
