using GLib;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;

public class GVoiceManager : Singleton<GVoiceManager>
{
    private readonly string voiceDicPath = "Growlib/wav/";
    private readonly string cashPath = Application.persistentDataPath + "/";

    private byte[] data;
    private AudioClip recordClip;
    public GVoiceManager()
    {
        CheckCashPath();
    }

    ~GVoiceManager()
    {

    }

    private void CheckCashPath()
    {
        if (!Directory.Exists(Path.Combine(cashPath, voiceDicPath)))
        {
            Directory.CreateDirectory(Path.Combine(cashPath, voiceDicPath));
        }
    }
    #region 录音相关
    /// <summary>
    /// 获取录音设备
    /// </summary>
    /// <returns></returns>
    public string[] GetDevices()
    {
        return Microphone.devices;
    }
    //开始录音
    public bool StartRecording()
    {
        
        if (!Microphone.IsRecording(null))
        {
            recordClip = Microphone.Start(null, false, 60, 16000);
            if(recordClip==null)
            {
                TRACE.WarningLn("HM_录音失败，录音设备未成功调起");
            }
            return true;
        }
        else
        {
            TRACE.WarningLn("HM_录音中！");
            if (recordClip == null)
            {
                TRACE.WarningLn("HM_录音失败，录音设备未成功调起");
            }
        }
        return false;
    }
    /// <summary>
    /// 停止录音
    /// </summary>
    /// <returns></returns>
    public StopVoiceDef StopRecording()
    {
        if (recordClip != null)
        {
            data = GetRealAudio(ref recordClip);
        }
        Microphone.End(null);
        StopVoiceDef stopVoiceDef = new StopVoiceDef();
        stopVoiceDef.data = data;
        stopVoiceDef.audioClip = recordClip;
        return stopVoiceDef;
    }

    /// <summary>
    /// 检测是否是空语音
    /// </summary>
    /// <param name="clip">AudioClip文件</param>
    /// <param name="threshold">
    /// 阈值调整建议：
    /// 完全静音（理想环境）	0.001f      无底噪，仅浮点误差
    /// 普通室内环境	        0.01f-0.03f	包含微弱底噪
    /// 嘈杂环境	            0.05f+	    需根据实际噪音调整</param>
    /// <returns>是否是空语音</returns>
    public bool CheckEmptyVoice(AudioClip clip, float threshold = 0.02f)
    {
        // 获取样本
        float[] samples = new float[clip.samples * clip.channels];
        clip.GetData(samples, 0);
        // 判断是否静音
        if (IsSilence(samples, threshold))
        {
            return true;
        }
        return false;
    }

    // 计算RMS能量
    public bool IsSilence(float[] samples, float threshold)
    {
        float sum = 0f;
        foreach (float sample in samples)
        {
            sum += sample * sample;
        }
        float rms = Mathf.Sqrt(sum / samples.Length);
        return rms < threshold;
    }
    #endregion

    #region 保存录音文件
    /// <summary>
    /// 保存录音  返回guid
    /// </summary>
    public string Save()
    {
        if (!Microphone.IsRecording(null))
        {
            return SaveVoice(data, GetVoiceGuid());
        }
        else
        {
            TRACE.WarningLn("HM_正在录音中，请先停止录音！");
        }
        return "";
    }
    /// <summary>
    /// 保存音频文件
    /// </summary>
    /// <param name="dataDef"></param>
    /// <returns>返回音频文件的名称</returns>
    public string SaveVoice(byte[] dataDef, string voiceGuid)
    {
        if (dataDef == null)
        {
            TRACE.WarningLn("HM_没有任何录音数据!");
            return voiceGuid;
        }
        string fileName = voiceGuid;
        if (!fileName.ToLower().EndsWith(".wav"))
        {//如果不是“.wav”格式的，加上后缀
            fileName += ".wav";
        }
        string path = Path.Combine(cashPath, voiceDicPath);//录音保存路径
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
        path = Path.Combine(path, fileName);//录音保存路径
        TRACE.WarningLn("HM_保存路径:" + path);//输出路径
        using (FileStream fs = CreateEmpty(path))
        {
            fs.Write(dataDef, 0, dataDef.Length);
            WriteHeader(fs, recordClip); //wav文件头
        }
        return fileName;
    }
    #endregion

    /// <summary>
    /// 判断音频文件是否存在,如果存在返回音频文件的全路径
    /// </summary>
    /// <param name="voiceGuid">音频文件GUID</param>
    /// <returns></returns>
    public string FindHaveVoice(string voiceGuid)
    {
        string fileName = voiceGuid;
        if (!fileName.ToLower().EndsWith(".wav"))
        {//如果不是“.wav”格式的，加上后缀
            fileName += ".wav";
        }
        string path = Path.Combine(cashPath,voiceDicPath, fileName);//录音保存路径
        if (File.Exists(path))
        {
            return path;
        }
        return "";
    }
    /// <summary>
    /// 获取音频文件存放路径
    /// </summary>
    /// <returns></returns>
    public string GetVoiceDirPath()
    {
        return Path.Combine(cashPath, voiceDicPath);
    }

    #region 支持功能
    /// <summary>
    /// 获取音频翻译内容
    /// </summary>
    /// <param name="audioDef">音频二进制数据</param>
    /// <param name="callback">回调</param>
    public void AudioTranslate(byte[] audioDef, RecognizeCallback callback)
    {
    }
    public byte[] GetFileData(string strFileName)
    {
       
        string strPath = FindHaveVoice(strFileName);
        TRACE.TraceLn(string.Format("HM_获取文件二进制数据路径为:", strFileName));
        if (string.IsNullOrEmpty(strPath))
        {
            return null;
        }
        return File.ReadAllBytes(strPath);
    }
    /// <summary>
    /// 写文件头
    /// </summary>
    /// <param name="stream"></param>
    /// <param name="clip"></param>
    private void WriteHeader(FileStream stream, AudioClip clip)
    {
        int hz = clip.frequency;
        int channels = clip.channels;
        int samples = clip.samples;

        stream.Seek(0, SeekOrigin.Begin);

        Byte[] riff = System.Text.Encoding.UTF8.GetBytes("RIFF");
        stream.Write(riff, 0, 4);

        Byte[] chunkSize = BitConverter.GetBytes(stream.Length - 8);
        stream.Write(chunkSize, 0, 4);

        Byte[] wave = System.Text.Encoding.UTF8.GetBytes("WAVE");
        stream.Write(wave, 0, 4);

        Byte[] fmt = System.Text.Encoding.UTF8.GetBytes("fmt ");
        stream.Write(fmt, 0, 4);

        Byte[] subChunk1 = BitConverter.GetBytes(16);
        stream.Write(subChunk1, 0, 4);

        UInt16 one = 1;

        Byte[] audioFormat = BitConverter.GetBytes(one);
        stream.Write(audioFormat, 0, 2);

        Byte[] numChannels = BitConverter.GetBytes(channels);
        stream.Write(numChannels, 0, 2);

        Byte[] sampleRate = BitConverter.GetBytes(hz);
        stream.Write(sampleRate, 0, 4);

        Byte[] byteRate = BitConverter.GetBytes(hz * channels * 2);
        stream.Write(byteRate, 0, 4);

        UInt16 blockAlign = (ushort)(channels * 2);
        stream.Write(BitConverter.GetBytes(blockAlign), 0, 2);

        UInt16 bps = 16;
        Byte[] bitsPerSample = BitConverter.GetBytes(bps);
        stream.Write(bitsPerSample, 0, 2);

        Byte[] datastring = System.Text.Encoding.UTF8.GetBytes("data");
        stream.Write(datastring, 0, 4);

        Byte[] subChunk2 = BitConverter.GetBytes(samples * channels * 2);
        stream.Write(subChunk2, 0, 4);
    }
    /// <summary>
    /// 创建wav格式文件头
    /// </summary>
    /// <param name="filepath"></param>
    /// <returns></returns>
    private FileStream CreateEmpty(string filepath)
    {
        FileStream fileStream = new FileStream(filepath, FileMode.Create);
        byte emptyByte = new byte();

        for (int i = 0; i < 44; i++) //为wav文件头留出空间
        {
            fileStream.WriteByte(emptyByte);
        }

        return fileStream;
    }
    /// <summary>
    /// 获取真正大小的录音
    /// </summary>
    /// <param name="recordedClip"></param>
    /// <returns></returns>
    public byte[] GetRealAudio(ref AudioClip recordedClip)
    {
        int position = Microphone.GetPosition(null);
        if (position <= 0 || position > recordedClip.samples)
        {
            position = recordedClip.samples;
        }
        return GetAudioSize(ref recordedClip, position);
    }
    /// <summary>
    /// 获取音频文件的大小
    /// </summary>
    /// <param name="recordedClip"></param>
    /// <param name="position"></param>
    /// <returns></returns>
    private static byte[] GetAudioSize(ref AudioClip recordedClip, int position)
    {
        float[] soundata = new float[position * recordedClip.channels];
        recordedClip.GetData(soundata, 0);
        recordedClip = AudioClip.Create(recordedClip.name, position, recordedClip.channels, recordedClip.frequency, false);
        recordedClip.SetData(soundata, 0);
        int rescaleFactor = 32767;
        byte[] outData = new byte[soundata.Length * 2];
        for (int i = 0; i < soundata.Length; i++)
        {
            short temshort = (short)(soundata[i] * rescaleFactor);
            byte[] temdata = BitConverter.GetBytes(temshort);
            outData[i * 2] = temdata[0];
            outData[i * 2 + 1] = temdata[1];
        }
        TRACE.DebugLog("HM_position=" + position + "  outData.leng=" + outData.Length);
        return outData;
    }
    /// <summary>
    /// 获取本地音频的真实大小
    /// </summary>
    /// <param name="recordedClip"></param>
    /// <returns></returns>
    public byte[] GetAudioSize(ref AudioClip recordedClip)
    {
        int position = recordedClip.samples;
        return GetAudioSize(ref recordedClip, position);
    }
    public string GetVoiceGuid()
    {
        return Guid.NewGuid().ToString();
    }
    #endregion

    #region 百度短语音识别
    const string BAIDU_TOKEN_KEY = "BAIDU_TOKEN_KEY";           // 保存本地的token
    const string BAIDU_TOKEN_EXPIRE_TIME_KEY = "BAIDU_TOKEN_EXPIRE_TIME_KEY";   // 保存本地token的过期时间

    private string baiduAccessToken;

    public string GetAccessTokenUrl()
    {
        string baseUrl = WebURL.RequestBaiduToken;
        Dictionary<string, string> queryParams = new Dictionary<string, string> {
            { "grant_type", "client_credentials" },
            { "client_id", GHelp.GetBaiduAPIKey() },
            { "client_secret", GHelp.GetBaiduSecretKey() }
        };
        string urlWithQuery = BuildUrlWithQuery(baseUrl, queryParams);
        return urlWithQuery;
    }

    public void SetBaiduAccessToken(S2C_BaiduVoiceToken token)
    {
        baiduAccessToken = token.access_token;
        var expireTime = DateTime.Now.AddSeconds(token.expires_in);
        TRACE.TraceLn($"---------百度语音token已更新 = {baiduAccessToken}， 有效期至 = {expireTime}");
        PlayerPrefs.SetString(BAIDU_TOKEN_KEY, baiduAccessToken);
        PlayerPrefs.SetString(BAIDU_TOKEN_EXPIRE_TIME_KEY, expireTime.ToString());
    }

    /// <summary>
    /// 检测百度token是否有效（是否有，且未过期）
    /// </summary>
    /// <returns></returns>
    public bool IsBaiduTokenValid()
    {
        if (!PlayerPrefs.HasKey(BAIDU_TOKEN_KEY) || !PlayerPrefs.HasKey(BAIDU_TOKEN_EXPIRE_TIME_KEY))
        {
            return false;
        }
        var expireTime = DateTime.Parse(PlayerPrefs.GetString(BAIDU_TOKEN_EXPIRE_TIME_KEY));
        return (expireTime - DateTime.Now).TotalSeconds > 0;
    }

    public string GetBaiduAccessToken()
    {
        if (string.IsNullOrEmpty(baiduAccessToken))
        {
            baiduAccessToken = PlayerPrefs.GetString(BAIDU_TOKEN_KEY);
        }
        return baiduAccessToken;
    }

    // 构建带Query参数的URL
    private string BuildUrlWithQuery(string baseUrl, Dictionary<string, string> parameters)
    {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (parameters.Count > 0)
        {
            urlBuilder.Append("?");
            bool isFirst = true;
            foreach (var param in parameters)
            {
                if (!isFirst) urlBuilder.Append("&");
                urlBuilder.Append($"{Uri.EscapeDataString(param.Key)}={Uri.EscapeDataString(param.Value)}");
                isFirst = false;
            }
        }
        return urlBuilder.ToString();
    }
    #endregion
}
