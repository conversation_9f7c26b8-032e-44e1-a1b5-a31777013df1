﻿/// <summary>
/// LogicDef
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 逻辑层->显示层 <br/>
/// </remarks>

using game.scene;
using game.schemes;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Scripting;
[assembly: Preserve]

namespace GLib.Common
{
    public class DGlobalGame
    {

        // 无效uid
        public const Int64 INVALID_UID = 0;

        // 无效地图id
        public const int INVALID_MAPID = 0;

        // 无效场景id
        public const int INVALID_ZONEID = 0;

        // 无效的BUFF ID
        public const int INVALID_BUFF_ID = 0;

        public const int CMD_ENTITY = 1000; //实体层逻辑
                                            //整形化倍数因子
        public const float FLOAT_SCALE_SIZE = 10000.0f;

        public const int CMD_OFFSET = 16000;
        public const int GAME_VIEW_CMD_OFFSET = CMD_OFFSET + 1000; // 客户端逻辑到显示层命令偏移
    }

    /// <summary>
    /// 逻辑层->显示层
    /// </summary>
    public enum ViewLogicDef
    {
        #region 初始
        GVIEWCMD_LOAD_LOGIN = (DGlobalGame.GAME_VIEW_CMD_OFFSET + 1),

        // 场景加载
        GVIEWCMD_MATCH_LOAD_SCENE,

        // 场景摧毁
        GVIEWCMD_MATCH_DESTROY_SCENE,

        // 资源清理
        GVIEWCMD_MATCH_CLEAR_RESOURCE,

        //地图系统初始化
        GVIEWCMD_MAIN_SYSTEM_INIT,

        //创建实体
        GVIEWCMD_CREATE_ENTRY,

        //销毁实体
        GVIEWCMD_DESTROY_ENTRY,
        EVENT_RETURN_ENTITYOBJ,

        ////场景跳转后实例物体
        GVIEWCMD_SCENE_LOAD_PREFAB,

        //辅助场景加载
        GVIEWCMD_SCENE_LOAD_AIDSCENE,

        //切换到其它场景，主场景需要特殊处理
        GVIEWCMD_MAIN_SYSTEM_CHANGEOTHERSCENE,

        //从其它场景，切回主场景需要特殊处理
        GVIEWCMD_MAIN_SYSTEM_GOTOMAINSCENE,

        //添加光效
        GVIEWCMD_ADD_LIGHTING,

        //移除光效
        GVIEWCMD_REMOVE_LIGHTING,

        //////////////////////////////////////////////// 音乐播放 ////////////////////////////////////
        GVIEWCMD_PLAY_MUSIC, // 播放音乐   gamelogic_PlayMusic
        GVIEWCMD_STOP_MUSIC, // 停止音乐   gamelogic_StopMusic
        GVIEWCMD_PAUSE_MUSIC, // 暂停音乐   gamelogic_PauseMusic
        GVIEWCMD_RESUME_MUSIC, // 恢复音乐   gamelogic_ResumeMusic
        GVIEWCMD_SET_MUSIC_VOLUME, // 设置音量   gamelogic_SetMusicVolume
        GVIEWCMD_SET_AUDIO_STATE, // 设置声音的状态 用于控制声音音效的开启关闭 gamelogic_SetAudioState
        GVIEWCMD_AUDIO_ISPLAY, //音效是否播放/
        //播放场景音乐
        GVIEWCMD_PLAY_SCENE_MUSIC,

        // 通知显示层显示SystemTips
        GVIEWCMD_ADD_SYSTEMTIPS,
        //通知显示层显示等待窗口
        GVIEWCMD_WAITING_SHOW,
        GVIEWCMD_WAITING_HIDE,

        // 道具高亮
        GVIEWCMD_HIGHLIGHT_Enable,
        //json模型点击功能
        COPHANDLE_MODELCLICK_EVENT,
        /// <summary>
        /// 完成点击
        /// </summary>
        COPHANDLE_OVERMODELCLICK_EVENT,
        /// <summary>
        /// 跳步
        /// </summary>
        SKIP_OPERATE_EVENT,

        /// <summary>
        /// 通知显示IP列表
        /// </summary>
        GVIEWCMD_SHOWIP,

        /// <summary>
        /// 模型添加抓取和点击功能
        /// </summary>
        EVENT_MODE_ADD_GRAPCLICK,

        // 手动连接服务器事件
        EVENT_MANUAL_CONNECT_SERVER,
        // 资源预加载
        GVIEWCMD_MATCH_PRELOAD_RESOURCE,

        // loading窗口显示
        GVIEWCMD_LOADINGWIN_SHOW,
        // loading窗口隐藏
        GVIEWCMD_LOADINGWIN_HIDE,
        // loading更新进度
        GVIEWCMD_LOADINGWIN_UPDATE,
        // loading窗口创建
        GVIEWCMD_LOADINGWIN_CREATE,
        #endregion

        #region 教育窗口
        EVENT_EDUC_ENTER,
        /// <summary>
        /// 教育端单元被触发
        /// </summary>
        EVENT_EDUC_UNIT_TRIGGER,
        /// <summary>
        /// 教育端游戏窗口进入
        /// </summary>
        EVENT_EDUC_GAMEWINDOW_ENTER,
        /// <summary>
        /// 教育端游戏窗口进入
        /// </summary>
        EVENT_EDUC_GAMEWINDOW_LOADFINISH,
        /// <summary>
        /// 点击工具栏 隐藏右侧点击的图片 視頻等
        /// </summary>
        EVENT_CLICKTOOL_HIDE,
        /// <summary>
        /// 点击部件類別(基礎 輔助等)切換部件
        /// </summary>
        EVENT_CLICK_CHANGE_PART,
        /// <summary>
        /// 组装保存机器人数据
        /// </summary>
        EVENT_SAVE_ASSEMBLING_ROBOT,
        /// <summary>
        /// 所有数据(课程信息)获取完成 开始加载UI
        /// </summary>
        EVENT_ALL_COURSE_DATA_FINISH,
        /// <summary>
        /// 是否显示组装编程的子节点窗口
        /// </summary>
        EVENT_ISSHOW_ASSEMBLYPROGRAM_SUBNODE,
        /// <summary>
        /// 点击了课程资源之后 还原组装 编程的Toggle值  否则进入场景之后 再点击组装会return掉
        /// </summary>
        EVENT_RESET_ASSEMBLY_TOGGLE_VALUE,
        /// <summary>
        /// 教育端游戏窗口UI隐藏
        /// </summary>
        EVENT_EDUC_UI_HIDE,
        /// <summary>
        /// 教育端游戏窗口UI显示
        /// </summary>
        EVENT_EDUC_UI_SHOW,
        /// <summary>
        /// 登录窗口隐藏
        /// </summary>
        EVENT_LOGIN_HIDE,
        EVENT_EDUC_MAX = EVENT_EDUC_ENTER + 500,
        #endregion

        #region 模型组装
        EVENT_ASSEMBLY_ENTER,
        /// <summary>
        /// 组装显示/关闭
        /// </summary>
        EVENT_MA_SHOW_DISABLE,
        /// <summary>
        /// 新组装显示/关闭
        /// </summary>
        NEW_EVENT_MA_SHOW_DISABLE,
        /// <summary>
        /// 组装部件创建
        /// </summary>
        EVENT_MA_PART_CREATE,
        /// <summary>
        /// 组装部件继续创建(点击部件图片 不用在继续点击)
        /// </summary>
        EVENT_MA_PART_CREATE_CONTINUE,
        /// <summary>
        /// 组装部件列表拖动
        /// </summary>
        EVENT_MA_PART_SCROLLDRAG,
        /// <summary>
        /// 组装部件列表拖动结束
        /// </summary>
        EVENT_MA_PART_SCROLLENDDRAG,
        /// <summary>
        /// 组装数据为空(第一次和把之前的数据删空)
        /// </summary>
        EVENT_ASSEMBLY_EMPTYDATA,
        /// <summary>
        /// 组装拖拽变色
        /// </summary>
        EVENT_ASSEMBLY_DRAG_OVER,
        /// <summary>
        /// 组装数据发生改变 增加或者移除了组件
        /// </summary>
        EVENT_ASSEMBLY_CHANGE,
        /// <summary>
        /// 点击组装部件
        /// </summary>
        EVENT_ASSEMBLY_PARTCLICK,
        /// <summary>
        /// 点击吸附创建之后 没有吸附 应该要回收
        /// </summary>
        EVENT_ASSEMBLY_PARTCLICK_RECOVER,
        /// <summary>
        /// 点击组件模型显示模型上的几个按钮
        /// </summary>
        EVENT_ASSEMBLY_CLICKMODEL_SHOWBTN,
        /// <summary>
        /// 组件图片点击或者拖拽 隐藏模型上的旋转删除按钮
        /// </summary>
        EVENT_ASSEMBLY_TEXTURE_CLICKDRAG,
        /// <summary>
        ///模型按钮 拉进或拉远摄像机
        /// </summary>
        EVENT_ASSEMBLY_MOVE_CAMERA,
        /// <summary>
        ///旋转无变化或者旋转碰到了其他物体 继续下一次旋转
        /// </summary>
        EVENT_ASSEMBLY_CONTINUE_RORATE,
        /// <summary>
        ///点击了旋转按钮 判断自己旋转了 其他物体还有没有连接面 没有则PartRealse
        /// </summary>
        EVENT_ASSEMBLY_CLICK_RORATE,
        /// <summary>
        ///拖拽旋转碰到了其他物体 继续下一次旋转
        /// </summary>
        EVENT_ASSEMBLY_CONTINUE_DRAG_RORATE,
        /// <summary>
        /// 组装可拖动数量的变化事件
        /// </summary>
        EVENT_ASSEMBLY_PARTNUM_CHANGE,
        /// <summary>
        /// 鼠标点击了空白处
        /// </summary>
        EVENT_ASSEMBLY_CLICK_EMPTY,
        /// <summary>
        ///组装工具配置表加载完成 开始加载组装UI
        /// </summary>
        EVENT_PARTTOOL_CONFIG_FINISH,
        #endregion
        #region 新拼装
        /// 获取机器人列表数据
        /// </summary>
        GET_ROBOTS_SC,
        /// 新建机器人
        /// </summary>
        CREAT_ROBOT_SC,
        /// 新建机器人本地数据获取完成
        /// </summary>
        CREAT_ROBOT_SC_LOCAL_FINISH,
        /// <summary>
        /// 增加子目标的默认零部件
        /// </summary>
        ADDLEGO_CS,
        /// <summary>
        /// 增加零部件服务器回包
        /// </summary>
        ADDLEGO_SC,
        /// <summary>
        /// 保存机器人服务器回包
        /// </summary>
        UPDATEROBOT_SC,
        /// <summary>
        /// 删除机器人零部件
        /// </summary>
        DELETELEGO_CS,
        /// <summary>
        /// 删除机器人零部件
        /// </summary>
        DELETELEGO_SC,
        /// <summary>
        /// 机器人改变数据(增加零部件或者删除零部件,成功或者失败)
        /// </summary>
        ROBOTS_CHANGE_DATA,
        /// <summary>
        /// 清除机器人
        /// </summary>
        ROBOTS_CLEAN_CS,
        /// <summary>
        /// 清除机器人
        /// </summary>
        ROBOTS_CLEAN_SC,
        /// <summary>
        /// 机器人数据获取完成
        /// </summary>
        ROBOTS_DATA_FINISH,
        /// <summary>
        /// 选择拼装机器人
        /// </summary>
        CHANGE_ROBOT,
        /// <summary>
        ///选择机器人目标(子模型)配置表数据
        /// </summary>
        CHANGE_BUILD_TARGET_CONFIG,
        /// <summary>
        /// 子目标零部件全部完成,继续下一个子目标的零部件
        /// </summary>
        TARGET_PART_FINISH_NEXT,
        /// <summary>
        ///机器人零部件配置表加载完成
        /// </summary>
        EVENT_BUILD_TARGET_CONFIG_FINISH,
        /// <summary>
        /// 吸附成功
        /// </summary>
        ASSEMBLY_SNAP_SUCCESS,
        /// <summary>
        /// 吸附失败
        /// </summary>
        ASSEMBLY_SNAP_FAIL,
        /// <summary>
        /// 鼠标拖动使主模型开始旋转
        /// </summary>
        ASSEMBLY_START_RORATE,
        /// <summary>
        /// 鼠标拖动使主模型旋转完成
        /// </summary>
        ASSEMBLY_RORATE_FINISH,
        /// <summary>
        /// 点击机器人头像 显示机器人
        /// </summary>
        ASSEMBLY_CLICK_SHOW_ROBOT,
        /// <summary>
        ///显示机器人模型
        /// </summary>
        ASSEMBLY_SHOW_ROBOT,
        /// <summary>
        /// 选择机器人toggle点击
        /// </summary>
        ASSEMBLY_CHOOSEROBOT_TOGGLECLICK,
        /// <summary>
        /// 选择机器人是否到达上限
        /// </summary>
        ASSEMBLY_CHOOSEROBOT_TOGGLEMAX,
        /// <summary>
        /// 部件是否拖拽中
        /// </summary>
        ASSEMBLY_PART_ISDRAG,
        /// <summary>
        /// 回收用来做左侧移动的图片PartCell
        /// </summary>
        ASSEMBLY_PARTCELL_RECYCLE,
        /// <summary>
        /// 摄像机已经移到5000的位置,开始生成机器人模型
        /// </summary>
        ASSEMBLY_SHOW_FINISH,
        /// <summary>
        /// 左侧零部件全都加载完成
        /// </summary>
        ASSEMBLY_ALLPART_LOADFINISH,
        /// <summary>
        /// 本地机器人增加部件完成
        /// </summary>
        ASSEMBLY_LOCAL_ROBOT_ADDLEGO_FINISH,
        /// <summary>
        /// targetcell加载完成
        /// 当所有targetcell加载完成 在开始加载PartCell
        /// </summary>
        ASSEMBLY_TARGETCELL_LOAD_FINISH,
        /// <summary>
        /// 组装隐藏和组装删除
        /// 需要区别开 组装隐藏要在调用一次 TargetCell Freshen
        /// </summary>
        ASSEMBLY_TARGETCELL_FRESHEN,
        /// <summary>
        /// 组装撤回
        /// </summary>
        ASSEMBLY_REVOKE,
        /// <summary>
        /// 按钮是否显示机器人全景
        /// </summary>
        ASSEMBLY_SHOWROBOT_BTN,
        /// <summary>
        /// 子目标点击
        /// </summary>
        ASSEMBLY_TARGET_BTNCLICK,
        /// <summary>
        /// 机器人选择
        /// </summary>
        ASSEMBLY_ROBOTCELL_BTNCLICK,
        /// <summary>
        /// 组装打开
        /// </summary>
        ASSEMBLY_OPEN,
        /// <summary>
        /// 组装关闭
        /// </summary>
        ASSEMBLY_CLOSE,
        /// <summary>
        /// 组装完成零部件点击
        /// </summary>
        ASSEMBLY_PARTFINISH_CELLCLICK,
        /// <summary>
        /// 开始重新组装机器人
        /// </summary>
        ASSEMBLY_REFRESH_ROBOT,
        /// <summary>
        /// 开始重新组装机器人确认
        /// </summary>
        ASSEMBLY_REFRESH_ROBOT_SURE,
        /// <summary>
        /// 组装Widget是否显示
        /// </summary>
        ASSEMBLY_WIDGET_SHOW,
        /// <summary>
        /// 选择机器人和场景里显示机器人模型
        /// </summary>
        ASSEMBLY_SCENE_SHOW_ROBOT,
        /// <summary>
        /// 选择机器人界面展示机器人
        /// </summary>
        CHOOSEROBOT_SHOWROBOT,
        /// <summary>
        /// 智能提示的零部件id
        /// </summary>
        ASSEMBLY_HELPORDER_CONFIGID,
        /// <summary>
        /// 智能提示图片是否显示
        /// </summary>
        ASSEMBLY_TIPIMAGE_ISSHOW,
        /// <summary>
        /// 创建机器人模型完成
        /// </summary>
        ASSEMBLY_CREAT_ROBOT_FINISH,
        /// <summary>
        /// 组装完成视频window
        /// </summary>
        ASSEMBLY_CREAT_FINISHVIDEO_WINDOW,
        /// <summary>
        /// 组装教育window
        /// </summary>
        ASSEMBLY_CREAT_EDUCATION_WINDOW,
        /// <summary>
        /// 机器人信息window
        /// </summary>
        ASSEMBLY_CREAT_ROBOTINFO_WINDOW,
        /// <summary>
        /// 选择机器人window
        /// </summary>
        ASSEMBLY_CREAT_CHOOSEROBOT_WINDOW,
        /// <summary>
        /// 选择机器人window创建完成
        /// </summary>
        ASSEMBLY_CREAT_CHOOSEROBOT_WINDOW_FINISH,
        /// <summary>
        /// 关闭选择机器人界面
        /// </summary>
        ASSEMBLY_CLOSE_CHOOSEROBOT_WINDOW,
        /// <summary>
        /// 组装UI机器人加载完成
        /// </summary>
        ASSEMBLY_UI_ROBOT_LOADFINISH,
        /// <summary>
        /// 组装UI机器人卸载完成
        /// </summary>
        ASSEMBLY_UI_ROBOT_UNLOADFINISH,
        /// <summary>
        /// 组装子目标任务
        /// </summary>
        ASSEMBLY_TARGET_TASK,
        /// <summary>
        /// AR拍照进入退出
        /// </summary>
        ARPHOTO,
        /// <summary>
        /// 搭建背景音效
        /// </summary>
        ASSEMBLY_BGMUSIC,
        /// <summary>
        /// 拼装视频提示
        /// </summary>
        ASSEMBLY_VIDEOTIP,
        /// <summary>
        /// 完成界面摄像机旋转
        /// </summary>
        FINISH_CAMERA_RORATE,
        /// <summary>
        /// 打开帮我拼
        /// </summary>
        ASSEMBLY_OPEN_HELPME,
        /// <summary>
        /// 帮我拼
        /// </summary>
        ASSEMBLY_HELPME,
        /// <summary>
        /// 撒花动画
        /// </summary>
        ASSEMBLY_ANIMATION,
        EVENT_ASSEMBLY_MAX = EVENT_ASSEMBLY_ENTER + 500,
        #endregion

        #region 杂项
        //杂项
        EVENT_DONTKNOW_ENTER,
        //点击鼠标特效
        GVIEWCMD_ADD_POINTDYNAMIC_UI,
        //场景切换控制
        GVIEWCMD_SCENECHANGE_CONTROL,
        //场景切换控制-创建完成
        GVIEWCMD_SCENECHANGE_CONTROL_CREATEFINISH,
        //开启宝箱进度
        GVIEWCMD_OPENBOX_PROPRESS,
        ///
        /// 场景切换类型 1：销毁 2：刷新
        /// </summary>
        CHANGESCENE_TYPE,
        #region 主界面窗口
        /// <summary>
        /// 进入主界面
        /// </summary>
        EVENT_MAIN_IN,
        /// <summary>
        /// 主界面切换
        /// </summary>
        EVENT_MAIN_SCENECHANGE,
        /// <summary>
        /// 隐藏主界面
        /// </summary>
        EVENT_MAIN_HIDE,
        #endregion
        /// <summary>
        /// 进入选择关卡界面
        /// </summary>
        EVENT_GAMELEVLEWINDOW_IN,
        #region 消息框窗口
        //通知显示层显示消息框窗口
        GVIEWCMD_MESSAGEBOX_OPEN,
        #endregion

        // login窗口显示
        GVIEWCMD_LOGINWIN_SHOW,
        // login窗口的登录按钮点击
        GVIEWCMD_LOGINWIN_ONLOGIN_CLICK,
        // login窗口隐藏
        GVIEWCMD_LOGINWIN_HIDE,

        //场景界面是否隐藏
        SCENE_UI_ISSHOW,
        /// <summary>
        /// 告知积木块运行状态
        /// </summary>
        INFORM_BLOCK_RUN_STATE,
        /// <summary>
        /// 打开对话窗口
        /// </summary>
        SHOW_TALKWINDOW,
        /// <summary>
        /// 打开执行命令错误信息窗口
        /// </summary>
        SHOW_ERRWINDOW,
        /// <summary>
        /// 视频是否播放
        /// </summary>
        VIDEO_ISPLAY,
        /// <summary>
        /// 视频滑动条是否点击
        /// </summary>
        VIDER_SLIDER_ISCLICK,
        /// <summary>
        /// 销毁主界面并推出场景
        /// </summary>
        DESTROY_MAIN_INTERFACE_CHANGE_SCENE,
        /// <summary>
        /// 其他模块发送事件去关闭智能提示window(和下面的这个事件 区分开来)
        /// </summary>
        CLOSEING_SMARTTIP_WINDOW,
        /// <summary>
        /// 智能提示window关闭时发出关闭智能提示window的事件(其他模块可能在关闭了窗口需要做什么,所以加这个事件)
        /// </summary>
        CLOSE_SMARTTIP_WINDOW,
        /// <summary>
        /// 重新播放
        /// </summary>
        VIDEO_AGAIN_PLAY,
        /// <summary>
        /// 点击视频window关闭按钮
        /// </summary>
        VIDEO_CLOSE,
        /// <summary>
        /// 点击视频window返回按钮
        /// </summary>
        VIDEO_RETURN,
        /// <summary>
        /// 视频开始播放
        /// </summary>
        VIDER_START_PLAY,
        /// <summary>
        /// 视频是否是双击
        /// </summary>
        IS_DOUBLE_CLICK,
        /// <summary>
        /// 设置主动画视频进度
        /// </summary>
        SET_MAIN_ANIMATION_VIDER_PROCESS,
        /// <summary>
        /// 设置动画视频进度
        /// </summary>
        SET_VIDER_PROCESS,
        /// <summary>
        /// 重置场景通知
        /// </summary>
        UI_RESET_SCENE_INFROM,
        /// <summary>
        /// 创建视频window
        /// </summary>
        CREAT_VIDEO_WINDOW,
        /// <summary>
        /// 创建能调整UI显示和是否全屏的视频window
        /// </summary>
        CREAT_VIDEO_CODE_WINDOW,
        ///<summary>
        /// 创建智能提示window
        /// </summary>
        CREAT_SMARTTIP_WINDOW,
        /// <summary>
        /// 打开答题窗口
        /// </summary>
        SHOW_ANSWER_WINDOW,
        /// <summary>
        /// 答题奖励窗口
        /// </summary>
        SHOW_ANSWER_AWARD_WINDOW,
        /// <summary>
        /// 关闭编程界面UI
        /// </summary>
        CLOSE_PROGRAM_WINDOW,
        /// <summary>
        /// 打开小游戏窗口
        /// </summary>
        SHOW_MINIGAME_WINDOW,
        /// <summary>
        /// 打开小游戏窗口完毕
        /// </summary>
        SHOW_MINIGAME_WINDOW_FINISH,
        /// <summary>
        /// 关闭小游戏窗口
        /// </summary>
        HIDE_MINIGAME_WINDOW,
        /// <summary>
        /// 创建小游戏视频window
        /// </summary>
        CREAT_MIGIGAME_VIDEO_WINDOW,
        /// <summary>
        /// 打开倒计时消息window
        /// </summary>
        SHOW_COUNTTIME_MESSAGE_WINDOW,
        /// <summary>
        /// 打开倒计时消息window完毕
        /// </summary>
        SHOW_COUNTTIME_MESSAGE_WINDOW_FINISH,
        /// <summary>
        /// 倒计时消息结束
        /// </summary>
        EVENT_COUNTTIME_WINDOW_END,
        /// <summary>
        /// 倒计时消息强制结束
        /// </summary>
        EVENT_COUNTTIME_WINDOW_FORCE_END,
        /// <summary>
        /// 擂台赛重置
        /// </summary>
        EVENT_CHALLENGE_RESET,
        /// <summary>
        /// 擂台赛提交代码
        /// </summary>
        EVENT_CHALLENGE_SUBMIT,
        /// <summary>
        /// 创建问题反馈Window
        /// </summary>
        COURCE_QUESTFEEDBACK_WINDOWM,
        /// <summary>
        /// 打开手指提示窗口
        /// </summary>
        SHOW_TIPSFINGER_WINDOW,
        /// <summary>
        /// 打开手指提示窗口完毕
        /// </summary>
        SHOW_TIPSFINGER_WINDOW_FINISH,
        /// <summary>
        /// 关闭手指提示窗口
        /// </summary>
        CLOSE_TIPSFINGER_WINDOW,
        /// <summary>
        /// 拖拽改变UI大小的系数
        /// </summary>
        UI_DRAG_SIZE_COEFFICIENT,
        /// <summary>
        ///主界面场景点击返回
        /// </summary>
        SCENE_MAIN_CLICK_BLACK,
        /// <summary>
        /// 渲染层初始化结束
        /// </summary>
        RENDER_LOADER_FINISH,
        /// <summary>
        /// 回退回登录界面
        /// </summary>
        BACK_IN_LOGIN_WINDOW,
        /// <summary>
        /// 打开建议反馈window
        /// </summary>
        OPEN_ADVICEWINDOW,
        /// <summary>
        /// 获取系统相册返回的图片路径
        /// </summary>
        GET_SYSTEMPHOTO_PATH,
        /// <summary>
        /// 获取系统相册返回的图片Texture
        /// </summary>
        GET_SYSTEMPHOTO_TEXTURE,
        /// <summary>
        /// 视频是否锁定
        /// </summary>
        VIDEO_LOCK,
        /// <summary>
        /// 竖屏登录窗口创建完成
        /// </summary>
        CREATE_PORTAITLOGINWINDOW_FINISH,
        /// <summary>
        /// 通知窗口模式改变
        /// </summary>
        INFORM_WINDOW_MODEL_CHANAGE,
        /// <summary>
        /// 打开摄像机拍照保存至系统相册
        /// </summary>
        EVENT_OPENCAMERA_SAVETEXTURE,
        /// <summary>
        /// 打开竖屏获取用户手机相册窗口
        /// </summary>
        SHOW_PORTRAIT_PHONE_PHOTO_WINDOW,
        /// <summary>
        /// 打开竖屏创建角色窗口
        /// </summary>
        SHOW_PORTRAIT_CREATE_ROLE_WINDOW,
        /// <summary>
        /// 打开摄像机并保存相册窗口
        /// </summary>
        SHOW_CAMERA_ADN_SAVETEXTURE_WINDOW,
        /// <summary>
        /// 展示竖屏登录窗口
        /// </summary>
        SHOW_PORTRAIT_LOGIN_WINDOW,
        /// <summary>
        /// 通知load加载完成
        /// </summary>
        INFORM_LOAD_OVER,
        /// <summary>
        /// 通知强制登出
        /// </summary>
        INFORM_FORCED_LOGIN_OUT,
        /// <summary>
        /// 所有uibuttonscale是否能点击
        /// </summary>
        BUTTON_ISCLICK,
        /// <summary>
        /// 重新设置我的数据
        /// </summary>
        INFORM_RESET_MYS,
        /// <summary>
        /// 注册任务视频播放完成
        /// </summary>
        SUB_TASK_VIDEO_OVER,
        /// <summary>
        /// 展示竖屏登录窗口(不显示)
        /// </summary>
        SHOW_PORTRAIT_LOGIN_WINDOW_NO_SHOW,
        /// <summary>
        /// 等比缩放改变大小
        /// </summary>
        UI_DRAG_SCALE_COEFFICIENT,
        /// <summary>
        /// 获取拍照返回的图片Texture
        /// </summary>
        GET_TAKEPHOTO_TEXTURE,
        /// <summary>
        /// 关闭拍照窗口
        /// </summary>
        EXIT_TAKEPHOTO_WINDOW,
        /// <summary>
        /// 视频加载失败
        /// </summary>
        VIDEO_LOAD_FAIL,
        /// <summary>
        /// 碰撞和触发发到lua层
        /// </summary>
        TRIGGER_TYPE,
        EVENT_DONTKNOW_MAX = EVENT_DONTKNOW_ENTER + 500,
        #endregion

        #region 手动控制器
        EVENT_MANUAL_CONTROLLER_ENTER,
        /// <summary>
        /// 显示机器人手动控制器
        /// </summary>
        MANUAL_CONTROLLER_SHOW,
        /// <summary>
        /// 隐藏机器人手动控制器
        /// </summary>
        MANUAL_CONTROLLER_HIDE,
        /// <summary>
        /// 设置手动控制器可控机器人
        /// </summary>
        MANUAL_CONTROLLER_SET_ROBOT,
        /// <summary>
        /// 移除手动控制器可控机器人
        /// </summary>
        MANUAL_CONTROLLER_REMOVE_ROBOT,
        /// <summary>
        /// 设置技能图标
        /// </summary>
        MANUAL_CONTROLLER_SET_SKILL_ICON,
        /// <summary>
        /// 获取当前TargetID
        /// </summary>
        PROGRAME_GET_TARGETID,
        /// <summary>
        /// 获取当前TargetID
        /// </summary>
        PROGRAME_REQUEST_TARGETID,
        /// <summary>
        /// 获取移动指令结束
        /// </summary>
        MANUAL_MOVETO_END,
        /// <summary>
        /// 向手动控制器请求其是否正在显示
        /// </summary>
        MANUAL_REQUEST_IS_SHOW,
        /// <summary>
        /// 返回手动控制器是否正在显示
        /// </summary>
        MANUAL_RETURN_IS_SHOW,
        /// <summary>
        /// 显示语音控制器
        /// </summary>
        MANUAL_VOICE_CONTROLLER_SHOW,
        /// <summary>
        /// 隐藏语音控制器
        /// </summary>
        MANUAL_VOICE_CONTROLLER_HIDE,
        /// <summary>
        /// 技能高亮
        /// </summary>
        MANUAL_SKILL_HIGH_LIGHT,
        EVENT_MANUAL_CONTROLLER_MAX = EVENT_MANUAL_CONTROLLER_ENTER + 500,
        #endregion

        #region 系列课程
        EVENT_COURSE_ENTER,
        /// <summary>
        /// 练习标识服务器回包
        /// </summary>
        EXERCISEIDENT_SC,
        /// <summary>
        /// 开始请求系列数据
        /// </summary>
        START_SERISE_REQUEST,
        /// <summary>
        /// 系列数据回调
        /// </summary>
        START_SERISE_CALLBACK,
        /// <summary>
        /// 课程开始
        /// </summary>
        COURCE_START,
        /// <summary>
        /// 事件未完成点击返回 回到环节界面
        /// </summary>
        COURCE_RETURN,
        /// <summary>
        /// 各自模块事件逻辑结束(分为2个事件结束的原因是 各自模块的事件结束 不代表驱动模块该事件结束)
        /// </summary>
        COURCE_EVENT_LOGIC_END,
        /// <summary>
        /// 事件真正结束
        /// </summary>
        COURCE_EVENT_END,
        /// <summary>
        /// 该事件全部结束
        /// </summary>
        COURCE_END,
        /// <summary>
        /// 课程视频子事件时间节点
        /// </summary>
        COURCE_VIDEO_SON_EVENT_NODE,
        /// <summary>
        /// 时间环节中的子事件,事件开始(是需要主动画视频控制类根据子事件时间节点控制什么时间点开始事件)
        /// </summary>
        COURCE_TIMELINK_EVENT_START,
        /// <summary>
        /// 移除时间环节子事件节点
        /// </summary>
        COURCE_REMOVE_SONEVENT_NODE,
        /// <summary>
        /// 时间环节最后一个事件结束
        /// </summary>
        COURCE_TIME_LINK_END,
        /// <summary>
        /// 主动画视频播放完成,直接下一个事件
        /// </summary>
        COURCE_ANIMATIONVIDEO_FINISH,
        /// <summary>
        /// 主动画环节本地保存的事件开始执行
        /// </summary>
        COURCE_ANIMATIONVIDEO_LOACTION_STARTEVENT,
        /// <summary>
        /// 拖动slider改变当前事件索引
        /// 否则事件index不一致 事件会结束不了
        /// </summary>
        COURCE_SLIDER_CHANGE_EVENT_INDEX,
        /// <summary>
        /// 更新环节状态
        /// </summary>
        COURCE_UPDATE_LINK_STATE,
        /// <summary>
        /// 更新课程状态
        /// </summary>
        COURCE_UPDATE_COURCE_STATE,
        /// <summary>
        /// 更新课程星级数据
        /// </summary>
        COURCE_UPDATE_STAR_VALUE,
        /// <summary>
        /// 设置事件完成window的事件index
        /// </summary>
        COURCE_SET_EVENT_FINISH_INDEX,
        /// <summary>
        /// 设置各个事件逻辑的事件index
        /// </summary>
        COURCE_SET_EACH_EVENT_INDEX,
        /// <summary>
        /// 事件完成windows返回按钮事件
        /// </summary>
        COURCE_EVENT_FINISH_BACKBTN_CLICK,
        /// <summary>
        /// 是否hide事件完成window
        /// </summary>
        COURCE_EVENT_FINISH_WINDOW_HIDE,
        /// <summary>
        /// 增加有效操作次数
        /// </summary>
        COURCE_ADD_EFFECTIVEOPNUM,
        /// <summary>
        /// 清除有效操作次数
        /// </summary>
        COURCE_CLEAN_EFFECTIVEOPNUM,
        /// <summary>
        /// 是否移动系列 环节窗口
        /// </summary>
        COURCE_CHANGE_WINDOW_POS,
        /// <summary>
        /// 课程创建window
        /// </summary>
        COURCE_CREATE_WINDOWM,
        /// <summary>
        /// 课程创建window完成
        /// </summary>
        COURCE_CREATE_WINDOWM_FINISH,
        /// <summary>
        /// 关闭课程主动画window
        /// </summary>
        COURSE_CLOSE_ANIMATIONWINDOW,
        /// <summary>
        /// 关闭课程组装window
        /// </summary>
        COURSE_CLOSE_COURSEASSEMBLY_WINDOW,
        /// <summary>
        /// 主动画是否播放
        /// </summary>
        COURSE_ANIMATIONWINDOW_ISSHOW,
        /// <summary>
        /// 主动画的子事件执行 
        /// 是否移走主动画UI
        /// </summary>
        COURSE_ANIMATIONWINDOW_ISMOVE,
        /// <summary>
        /// 事件完成window
        /// </summary>
        COURCE_CREAT_EVENTFINISH_WINDOW,
        /// <summary>
        /// 系列window
        /// </summary>
        COURCE_CREAT_SERISEWINDOW,
        /// <summary>
        /// 课程window
        /// </summary>
        COURCE_CREAT_COURSEWINDOW,
        /// <summary>
        /// 环节window
        /// </summary>
        COURCE_CREAT_LINKWINDOW,
        /// <summary>
        /// 打开讲解视频
        /// </summary>
        PROGRAM_OPEN_EXPLAIN_WINDOW,
        /// <summary>
        /// 有某些事件 第一个事件触发了 会连续触发几个事件(例如答题) 所以做个事件事件完成 应该发最后一个的Index
        /// 而不是第一个 并且需要把CourseConfigCsv数据增加到EventMgr里 否则判断进不去 这种事件特殊处理
        /// </summary>
        COURCE_ADD_COURSECONFIGCSV_EVENTMGR,
        /// <summary>
        /// 限制选项区ScrollRect移动
        /// </summary>
        COURCE_SCROLLRECT_CHOOSEAREA_NO_MOVE,
        /// <summary>
        /// 限制空位区ScrollRect移动
        /// </summary>
        COURCE_SCROLLRECT_EMPTYAREA_NO_MOVE,
        /// <summary>
        /// 关闭点击交互window
        /// </summary>
        COURSE_CLICK_CLOSE_WINDOW,
        /// <summary>
        /// 注册组装任务Id
        /// </summary>
        ROBOTS_SUBSCRIBE_UIACTION_SC,
        /// <summary>
        /// 通知编程驱动点击确认按钮
        /// </summary>
        INFORM_DIVE_BLOCK_CONFIRM,
        /// <summary>
        /// 通知编程驱动点击取消按钮
        /// </summary>
        INFORM_DIVE_BLOCK_CLOSE,
        /// <summary>
        /// 当前选中系列索引
        /// </summary>
        COURSE_SERIES_INDEX,
        /// <summary>
        /// 在时间环节中 没有子时间节点的 不能直接隐藏 这样会导致UI断层 应该等下个window创建成功在隐藏
        /// </summary>
        COURSE_REMOVE_EVENEWINDOW,
        /// <summary>
        /// 点击交互 已经点击了 需要重置些状态
        /// </summary>
        COURSE_CLICK_CELL_CLCIK,
        /// <summary>
        /// 下个课程状态解锁
        /// </summary>
        COURSE_NEXTCOURSE_LOCK,
        /// <summary>
        /// 发到lua 获取选择题的语音id
        /// </summary>
        COURSE_GET_CHOSSE_VOICE,
        /// <summary>
        /// 停止选择题的语音id
        /// </summary>
        COURSE_STOP_CHOSSE_VOICE,
        /// <summary>
        /// 开始生成课程cell
        /// </summary>
        COURSE_START_CREATE_COURSECELL,
        /// <summary>
        /// 设置linkwindow的位置归零
        /// </summary>
        COURCE_LINKWINDOW_RESETPOS,
        /// <summary>
        /// 刷新coursecell数据
        /// </summary>
        COURCE_REFRESH_COURSECELLDATA,
        /// <summary>
        /// 请求课程总表数据关闭load
        /// </summary>
        COURCE_START_SERISE_REQUEST_ERROR,
        /// <summary>
        /// 答题创建cell完成
        /// </summary>
        COURSE_ANSWER_CREATE_CELL_FINISH,
        /// <summary>
        ///点击了答题语音
        /// </summary>
        COURSE_ANSWER_CLICK_AUDIO,
        /// <summary>
        /// 重置环节
        /// </summary>
        COURSE_LINK_RESET,
        /// <summary>
        /// 购买的是否是系统课
        /// </summary>
        COURSE_BUY_SYSTEMCOURSE,
        EVENT_COURSE_MAX = EVENT_COURSE_ENTER + 500,
        #endregion

        #region 编程模块相关事件
        EVENT_PROGRAM_ENTER,
        /// <summary>
        /// 获取编程区的积木块
        /// </summary>
        ACQUIRE_PROGRAM_AREA_BLOCK,
        /// <summary>
        /// 更新编程区积木块
        /// </summary>
        UPDATE_PROGRAM_AREA_BLOCK,
        /// 编程窗口移走
        /// </summary>
        PROGRAME_WINDOWS_MOVE_AWAY,
        /// <summary>
        /// 编程窗口移回
        /// </summary>
        PROGRAME_WINDOWS_MOVE_BACK,
        /// <summary>
        ///通知编程区加载积木块
        /// </summary>
        INFORM_PROGRAME_LOAD_BLOCK,
        /// <summary>
        /// 编程区积木块同步结果通知
        /// </summary>
        BLOCK_SYNC_RESULT_INFORM,
        /// <summary>
        /// 通知矫正机器人积木块信息
        /// </summary>
        CORRECT_ROBOT_BLOCK_INOF,
        /// <summary>
        /// 接受服务器准备运行准备结果
        /// </summary>
        BLOCK_LOAD_SCRIPT_RESPONSE,
        /// <summary>
        /// 开始运行
        /// </summary>
        BLOCK_START_SCRIPT_RESPONSE,
        /// <summary>
        /// 开始运行编程
        /// </summary>
        START_SCRIPT_RESPONSE,
        /// <summary>
        /// 停止编程
        /// </summary>
        STOP_SCRIPT_RESPONSE,
        /// <summary>
        /// 通知运行区开始运行
        /// </summary>
        INFORM_RUN_BLOCK_START,
        /// <summary>
        /// 开始运行状态报告
        /// </summary>
        CODE_BLOCK_BEGIN_RUN_REPORT,
        /// <summary>
        /// 结束运行状态报告
        /// </summary>
        CODE_BLOCK_END_RUN_REPORT,
        OTHER_INFORM_RUN,
        /// <summary>
        /// 自动同步编程区高度
        /// </summary>
        SYNC_PROGRAM_AUTO_DRAW,
        /// <summary>
        /// 通知编程区设置Pin
        /// </summary>
        INFORM_PROGRAME_SET_PIN,
        /// <summary>
        /// 编程资源加载完成。
        /// </summary>
        PROGRAM_BLOCK_LOAD_OVER,
        /// <summary>
        /// 其他区域通知积木块编程界面准备运行。
        /// </summary>
        OTHER_INFORM_PROGRAM_RUN,
        /// <summary>
        /// 加载变量积木块数据
        /// </summary>
        LOADING_VARIATE_BLOCK_INFO,
        /// <summary>
        /// 删除变量
        /// </summary>
        DELETE_VARIABLE_ITEM,
        /// <summary>
        /// 保存变量信息
        /// </summary>
        EDIT_VARIABLE_MESSAGE,
        /// <summary>
        /// 积木块增加变量列
        /// </summary>
        ADD_BLOCK_VARIABLE_ITEM,
        /// <summary>
        /// 积木块修改变量列
        /// </summary>
        DELETE_BLOCK_VARIABLE_ITEM,
        /// <summary>
        /// 通知自定义变量承载积木块更新数据
        /// </summary>
        INFORM_VARIABLE_CARRY_UPDATE,
        /// <summary>
        /// 指定更新积木块的变量数据
        /// </summary>
        ASSIGN_UPDATE_BLOCK_VARIABLE,
        /// <summary>
        /// 刷新变量布局
        /// </summary>
        REFRESH_LAYOUT_GROUP,
        /// <summary>
        /// 通知清除积木块
        /// </summary>
        INFORM_PROGRAM_CLOSE_BLOCK,
        /// <summary>
        /// 模板编程
        /// </summary>
        OPERATION_TEMPLATE_BLOCK,
        /// <summary>
        /// 试运行积木块
        /// </summary>
        TRIAL_OPERATION_BLOCK,
        /// <summary>
        /// 正常积木块操作
        /// </summary>
        OPERATION_NORMAL_BLOCK,
        /// <summary>
        /// 通知试运行
        /// </summary>
        INFORM_TRIAL_OPERATION_RUN,
        /// <summary>
        /// 重新加载积木块信息
        /// </summary>
        INFORM_ANEW_LOAD_BLOCK_INFO,
        /// <summary>
        /// 触发初始化按钮
        /// </summary>
        TRIGGER_INITIALIZE_BLOCK_BUT,
        /// <summary>
        /// 触发课程模块初始化积木块功能
        /// </summary>
        TRIGGER_COURSE_INITIALIZE_BLOCK_BUT,
        /// <summary>
        /// 进入到初始化积木块界面
        /// </summary>
        INTO_INITIALIZE_BLOCK_WINDOWS,
        /// <summary>
        /// 进入到编程积木块
        /// </summary>
        INTO_PROGRAMME_BLOCK_WINDOWS,
        /// <summary>
        /// 订阅积木块添加到编程区
        /// </summary>
        BLOCK_ADD_TO_WORKSPACE,
        /// <summary>
        /// 取消积木块操作订阅
        /// </summary>
        BLOCK_CLOSE_OPERATION,
        /// <summary>
        /// 积木块运行失败
        /// </summary>
        BLOCK_TRIAL_OPERATION_FAIL,
        /// <summary>
        /// 驱动运行积木块情况
        /// </summary>
        DRIVE_RUN_BLOCK_CONDITION,
        /// <summary>
        /// 打开试运行界面
        /// </summary>
        OPEN_TRIAL_OPERATION_WINDOW,
        /// <summary>
        /// 通知编程区积木块关闭
        /// </summary>
        INFORM_BLOCK_PROGRAMME_CLOSE,
        /// <summary>
        /// 通知运行积木块返回
        /// </summary>
        INFORM_BLOCK_RUN_BACK,
        /// <summary>
        /// 通知运行积木块关闭
        /// </summary>
        INFORM_BLOCK_RUN_CLOSE,
        /// <summary>
        ///设置运行UI状态
        /// </summary>
        SET_BLOCK_RUN_UI_STATE,
        /// <summary>
        /// 设置更改编程区位置
        /// </summary>
        SYNC_PROGRAM_CHANGE_DRAW,
        /// <summary>
        /// 发送积木块可用加载
        /// </summary>
        INFORM_BLOCK_CAN_LOAD,
        /// <summary>
        /// 运行完成返回编程界面
        /// </summary>
        RUN_OVER_BACK_PROGRAME,
        /// <summary>
        /// 编程界面外的地方通知保存
        /// </summary>
        INFORM_OTHER_SAVE_XML,
        /// <summary>
        /// 通知编程界面外的保存完成
        /// </summary>
        INFORM_OTHER_SAVE_OVER,
        /// <summary>
        /// 通知打开编程
        /// </summary>
        INFORM_OPEN_BLOCK,
        /// <summary>
        /// 通知积木块通知驱动返回
        /// </summary>
        INFORM_BLOCK_BACK,
        /// <summary>
        /// 运行替换积木块
        /// </summary>
        INFORM_CAN_REPLACE_BLOCK,
        /// <summary>
        /// 通知替换积木块
        /// </summary>
        INFORM_REPLACE_BLCOK,
        /// <summary>
        /// 通知显示编程提示按钮
        /// </summary>
        INFORM_SHOW_PROGRAM_BTN,
        /// <summary>
        /// 编程界面点击退出按钮
        /// </summary>
        INFORM_PROGRAM_CLICK_CLOSE,
        /// <summary>
        /// 通知积木块停止运行
        /// </summary>
        INFORM_STOP_RUN_BLOCK,
        EVENT_PROGRAM_MAX = EVENT_PROGRAM_ENTER + 500,
        #endregion

        #region 服务端场景模块相关事件
        EVENT_SCENE_MODE_ENTER,
        /// <summary>
        /// 更改角色信息
        /// </summary>
        ENTITY_TOVIEW_UPDATE_PLAYER_INFO,
        EVENT_SCENE_MODE_MAX = EVENT_SCENE_MODE_ENTER + 500,
        #endregion
        //场景重置完成事件
        EVENT_SCENE_RESETFINISH,
        /// <summary>
        /// 主角移动
        /// </summary>
        GVIEWCMD_TOVIEW_MOVE_POS,

        #region 任务模块
        EVENT_TASK_ENTER,
        /// <summary>
        /// 更新任务消息
        /// </summary>
        TASK_UPDATE_TRACK,
        /// <summary>
        /// 服务器通知客户端更新任务
        /// </summary>
        TASK_INSTANCE_LIST_UPDTE,
        /// <summary>
        /// 执行一段脚本
        /// </summary>
        TASK_EXECUTE_SCRIPT,
        /// <summary>
        /// 任务结算消息
        /// </summary>
        TASK_DETAILED_MESSAGE,
        /// <summary>
        /// 通知任务开始
        /// </summary>
        INFORM_MODEL_TASK_START,
        /// <summary>
        /// 通知任务结束
        /// </summary>
        INFORM_MODEL_TASK_OVER,
        /// <summary>
        /// 通知模块任务启动结算
        /// </summary>
        INFORM_MODEL_TASK_DETAILED,
        /// <summary>
        /// 通知模块任务启动结算_结束
        /// </summary>
        INFORM_MODEL_TASK_DETAILED_OVER,
        /// <summary>
        /// 创建任务窗口
        /// </summary>
        EVENT_CREATE_TASK_WINDOW,
        /// <summary>
        /// 销毁任务窗口
        /// </summary>
        EVENT_DESTROY_TASK_WINDOW,
        /// <summary>
        /// 任务模块VM运行超时
        /// </summary>
        TASK_VM_RUN_TIME_OUT,
        /// <summary>
        /// 任务完成通知
        /// </summary>
        TASK_OVER_INFORM,
        /// <summary>
        /// 通知当前任务完成
        /// </summary>
        INFORM_NOW_TASK_OVER,
        /// <summary>
        /// 通知任务窗口设置
        /// </summary>
        INFORM_TASK_WINDOW_CONFIG,
        /// <summary>
        /// 改变任务窗口位置
        /// </summary>
        INFORM_SHOW_TASK_WINDOW,
        /// <summary>
        /// 点击提交按钮结束
        /// </summary>
        TASK_SUBMIT_FINISH,
        /// <summary>
        /// 改变任务窗口坐标
        /// </summary>
        CHANGE_TASK_WINDOW_POS,
        /// <summary>
        /// 通知任务内容显示状态
        /// </summary>
        INFORM_TASK_CONTENT_SHOW_STATE,
        /// <summary>
        /// 子任务完成
        /// </summary>
        TASK_CHILD_FINISHED,
        /// <summary>
        /// 通知重新获取积木块
        /// </summary>
        TASK_INFORM_REST_GET_BLOCK,
        /// <summary>
        /// 通知显示任务提示
        /// </summary>
        INFORM_TASK_SHOW_TIPS_BTN,
        /// <summary>
        /// 通知继续探索
        /// </summary>
        INFORM_TASK_CONTINUE_EXPLORE,
        /// <summary>
        /// 通知运行积木块停止运行
        /// </summary>
        INFORM_RUN_BLOCK_STOP,
        /// <summary>
        /// 服务端下发对应任务的追踪目标数量
        /// </summary>
        INFORM_TASK_AIM_NUM,
        /// <summary>
        /// 服务端下发的任务失败消息
        /// </summary>
        TASK_FAIL_INFO,
        /// <summary>
        /// 停止所有播放
        /// </summary>
        TASK_STOP_ALL_PLAY,
        /// <summary>
        /// 提前显示任务完成图片
        /// </summary>
        TASK_ADVANCED_SHOW_OVER_IMG,
        /// <summary>
        /// 创建引导模块
        /// </summary>
        TASK_LOAD_GUIDE_ITEM,
        /// <summary>
        ///显示任务引导
        /// </summary>
        TASK_GUIDE_SHOW,
        /// <summary>
        /// 退出任务引导
        /// </summary>
        TASK_GUIDE_EXIT,
        /// <summary>
        /// 关闭所有任务引导
        /// </summary>
        CLOSE_ALL_TASK_GUIDE,
        /// <summary>
        /// 通知任务模块弹出任务引导
        /// </summary>
        INFORM_TASK_OPEN_TIPS,
        /// <summary>
        /// 通知任务设置数据
        /// </summary>
        INFORM_LOAD_ISSUE,
        /// <summary>
        /// 退出任务发布
        /// </summary>
        TASK_ISSUE_EXIT,
        /// <summary>
        /// 创建任务发布
        /// </summary>
        TASK_CREATE_ISSUE,
        /// <summary>
        /// 通知驱动触发确认功能
        /// </summary>
        INFORM_DRIVE_TRIGGER_CONFIRM,
        /// <summary>
        /// 通知任务发布是否可以关闭
        /// </summary>
        INFORM_ISSUE_CAN_CLOSE,
        /// <summary>
        /// 通知启动定时器
        /// </summary>
        INFORM_START_GUIDE_TIME,
        EVENT_TASK_MAX = EVENT_TASK_ENTER + 500,
        #endregion

        #region 答题
        ENTER_ANSWER,
        /// <summary>
        /// 答题ID更新
        /// </summary>
        ANSWER_QUESTID,
        /// <summary>
        /// 关闭答题window
        /// </summary>
        ANSWER_CLOSE_WINDOW,
        /// <summary>
        /// 下一题
        /// </summary>
        ANSWER_NEXT_QUEST,
        /// <summary>
        /// 题目选项全部都答对了
        /// </summary>
        ANSWER_ALL_RIGHT,
        /// <summary>
        /// 答题所有选项摇晃
        /// </summary>
        ANSWER_SHAKE,
        /// <summary>
        /// 答题所有选项停止摇晃(有交互了 关掉定时器)
        /// </summary>
        ANSWER_STOP_SHAKE,
        /// <summary>
        /// 填空题拖拽结束 重新开始计时是否抖动
        /// </summary>
        ANSWER_ENDDRAG_START_CHECK_SHAKE,
        /// <summary>
        /// 题目答对了 回收图片里生成的文字
        /// </summary>
        ANSWER_RECYCLE_THOUGHTTEXT,
        /// <summary>
        /// 答题音频按钮的位置
        /// </summary>
        ANSWER_AUDIO_POS,
        /// <summary>
        /// 已选择
        /// </summary>
        ANSWER_XUANZHE,
        /// <summary>
        /// 显示提示图片
        /// </summary>
        ANSWER_SHOW_TIP_IMAGE,
        /// <summary>
        /// 答题完成
        /// </summary>
        ANSWER_OVER,
        ENTER_ANSWER_MAX = ENTER_ANSWER + 500,
        #endregion
        #region 执行模块
        EVENT_EXECUTE_ENTER,
        /// <summary>
        /// 执行脚本结束
        /// </summary>
        EXECUTE_SCRIPT_END,
        /// <summary>
        /// 创建执行模块的拍照窗口
        /// </summary>
        EXECUTE_TAKEPHOTO_WINDOW,
        /// <summary>
        /// 创建执行模块的UI指引窗口
        /// </summary>
        EXECUTE_UI_GUIDE_WINDOW,
        /// <summary>
        /// UI指引窗口的初始化
        /// </summary>
        EXECUTE_UI_GUIDE_INIT,
        EVENT_EXECUTE_MAX = EVENT_EXECUTE_ENTER + 500,
        #endregion
        #region 作业模块
        EVENT_HOME_WROK_ENTER,
        /// <summary>
        /// 通知作业提示点击确定按钮
        /// </summary>
        INFORM_HOME_WROK_CONFIRM,
        /// <summary>
        /// 通知作业提示点击取消按钮
        /// </summary>
        INFORM_HOME_WROK_CLOSE,
        EVENT_HOME_WROK_MAX = EVENT_HOME_WROK_ENTER + 500,
        #endregion
        #region 竖屏菜单模块
        EVENT_PORTRAIT_SCREEN_ENTER,
        /// <summary>
        /// 通知创建菜单窗口
        /// </summary>
        EVENT_CREATE_MEUN_WINDOW,
        /// <summary>
        /// 通知创建研发中提示窗口
        /// </summary>
        EVENT_CREATE_UNFINISHED_WINDOW,
        /// <summary>
        /// 通知竖屏状态
        /// </summary>
        INFORM_PORTRAIT_SCREEN_STATE,
        /// <summary>
        /// 通知窗口打开完成
        /// </summary>
        INFORM_OPEN_WINDOW_OVER,
        /// <summary>
        /// 重刷主界面的显示
        /// </summary>
        HEASVY_BRUSH_MEUN_SHOW,
        /// <summary>
        /// 竖屏菜单点击了计划按钮
        /// </summary>
        CLICK_SERIESWINDOW,
        /// <summary>
        /// 通知首页检查URL
        /// </summary>
        INFORM_MAIN_CHECK_URL,
        /// <summary>
        /// 通知菜单模块点击
        /// </summary>
        INFORM_MEUN_ITEM_CLICK,
        /// <summary>
        /// 设置首页状态
        /// </summary>
        INFORM_MAIN_STATE,
        /// <summary>
        ///通知切换模块
        /// </summary>
        INFORM_MEUN_NAVIGATION_MODULE,
        EVENT_PORTRAIT_SCREEN_MAX = EVENT_PORTRAIT_SCREEN_ENTER + 500,
        #endregion
        #region 我的模块
        EVENT_MINE_ENIER,
        /// <summary>
        /// 通知创建我的窗口
        /// </summary>
        EVENT_CREATE_MINE_WINDOW,
        /// <summary>
        /// 角色信息修改完成
        /// </summary>
        EVENT_ROLE_MODIFY_FINISH,
        /// <summary>
        /// 通知关闭子窗口
        /// </summary>
        INFORM_CLOSE_CHILD_WINDOW,
        EVENT_MINE_MAX = EVENT_MINE_ENIER + 500,
        #endregion
        #region 擂台赛模块
        EVENT_CHALLENGE_ENTER,
        /// <summary>
        /// 擂台赛开始
        /// </summary>
        EVENT_CHALLENGE_START,
        /// <summary>
        /// 擂台赛结束
        /// </summary>
        EVENT_CHALLENGE_END,
        /// <summary>
        /// 收集金币
        /// </summary>
        EVENT_CHALLENGE_COLLECT_COIN,
        /// <summary>
        /// 擂台赛结算数据
        /// </summary>
        EVENT_CHALLENGE_RESULT_DATA,
        EVENT_CHALLENGE_MAX = EVENT_CHALLENGE_ENTER + 500,
        #endregion
        #region 小地图模块
        EVENT_TINYMAP_ENTER,
        /// <summary>
        /// 隐藏小地图
        /// </summary>
        HIDE_TINY_MAP,
        /// <summary>
        /// 显示小地图
        /// </summary>
        SHOW_TINY_MAP,
        /// <summary>
        /// 创建并设置小地图信息
        /// </summary>
        SHOW_SET_TINY_MAP_CONFIG,
        /// <summary>
        /// 摧毁小地图
        /// </summary>
        DESTROY_TINY_MAP,
        /// <summary>
        /// 设置小地图配置
        /// </summary>
        SET_TINY_MAP_CONFIG,
        /// <summary>
        /// 移除小地图缩放按钮事件
        /// </summary>
        REMOVE_TINY_MAP_BUTTON,
        /// <summary>
        /// 请求小地图属性
        /// </summary>
        REQUEST_TINY_MAP_PROPERTY,
        /// <summary>
        /// 返回小地图属性
        /// </summary>
        RETURN_TINY_MAP_PROPERTY,
        /// <summary>
        /// 设置摄像机坐标
        /// </summary>
        EVENT_TINY_MAP_SET_POSITION,
        /// <summary>
        /// 禁用小地图
        /// </summary>
        DISABLE_TINY_MAP,
        /// <summary>
        /// 启动小地图
        /// </summary>
        ENABLE_TINY_MAP,
        EVENT_TINYMAP_MAX = EVENT_TINYMAP_ENTER + 500,
        #endregion
        #region 内置浏览器
        EVENT_WEBVIEW_ENTER,
        /// <summary>
        /// 创建内置浏览器窗口
        /// </summary>
        EVENT_CREATE_WEBVIEW_WINDOW,
        /// <summary>
        /// 内嵌网页配置
        /// </summary>
        INFORM_WEBVIEW_CONFIG,
        /// <summary>
        /// 通知内嵌浏览器关闭窗口
        /// </summary>
        INFORM_WEBVIEW_CLOSE_WINDOW,
        /// <summary>
        /// 内嵌网页关闭发出通知
        /// </summary>
        WEBVIEW_CLOSE_INFORM,
        /// <summary>
        /// 发送购课成功
        /// </summary>
        INFORM_WEB_PAY_SUCCESS,
        /// <summary>
        /// 通知Web执行完成
        /// </summary>
        INFORM_WEB_EXECUTE_OVER,
        EVENT_WEBVIEW_MAX = EVENT_WEBVIEW_ENTER + 500,
        #endregion
        #region 分享模块
        EVENT_SHARE_ENTER,
        /// <summary>
        /// 创建分享窗口
        /// </summary>
        EVENT_CREATE_SHARE_WINDOW,
        EVENT_SHARE_MAX = EVENT_SHARE_ENTER + 500,
        #endregion
        #region 热更新模块
        EVENT_HOTUPDATE_ENTER,
        /// <summary>
        /// 下载公共资源(点击计划或我的)
        /// </summary>
        EVENT_UPDATE_COMMON,
        /// <summary>
        /// 一次热更新流程完成
        /// </summary>Q
        HOT_UPDATE_FINISH,
        HOT_UPDATE_CREATE_WAITWINDOW,
        HOT_UPDATE_CLOSE_WAITWINDOW,
        EVENT_HOTUPDATE_MAX = EVENT_HOTUPDATE_ENTER + 500,
        #endregion

        #region 小游戏模块
        EVENT_MINIGAME_ENTER,
        /// <summary>
        /// 小游戏初始化
        /// </summary>
        EVENT_MINIGAME_INIT,
        /// <summary>
        /// 小游戏准备阶段
        /// </summary>
        EVENT_MINIGAME_READY,
        /// <summary>
        /// 小游戏倒计时阶段
        /// </summary>
        EVENT_MINIGAME_COUNTDOWN,
        /// <summary>
        /// 小游戏游戏阶段
        /// </summary>
        EVENT_MINIGAME_GAME,
        /// <summary>
        /// 小游戏结束阶段
        /// </summary>
        EVENT_MINIGAME_END,
        /// <summary>
        /// 小游戏结算阶段
        /// </summary>
        EVENT_MINIGAME_SETTLEMENT,
        /// <summary>
        /// 小游戏暂停阶段
        /// </summary>
        EVENT_MINIGAME_PAUSE,
        /// <summary>
        /// 检查小游戏是否结束了
        /// </summary>
        EVENT_MINIGAME_CHECHK_FINISH,
        /// <summary>
        /// 小游戏结算窗口初始化
        /// </summary>
        EVENT_MINIGAME_SETTLEMENT_INIT,
        /// <summary>
        /// 小游戏设置状态
        /// </summary>
        EVENT_MINIGAME_SETGAMESTATE,
        /// <summary>
        /// 小游戏结束了
        /// </summary>
        EVENT_MINIGAME_FINISH,
        /// <summary>
        /// 舒尔特确认重新开始
        /// </summary>
        EVENT_MINIGAME_SCHULT_AGAIN_CONFIM,
        /// <summary>
        /// 舒尔特取消重新开始
        /// </summary>
        EVENT_MINIGAME_SCHULT_AGAIN_CANCEL,
        /// <summary>
        /// 舒尔特当局最佳时间
        /// </summary>
        EVENT_MINIGAME_SCHULT_BEST_TIME,
        /// <summary>
        /// 舒尔特报告
        /// </summary>
        EVENT_MINIGAME_SCHULT_REPORT,
        /// <summary>
        /// 舒尔特请求跳级消息结果
        /// </summary>
        EVENT_MINIGAME_SCHULT_DIFFICULTY,
        /// <summary>
        /// 训练报告创建成功
        /// </summary>
        EVENT_MINIGAME_PRATICE_CREATE_OVER,
        EVENT_MINIGAME_MAX = EVENT_MINIGAME_ENTER + 500,
        #endregion
        #region 儿童锁
        EVENT_CHILDLOCK_ENTER,
        /// <summary>
        /// 通知儿童锁验证成功
        /// </summary>
        INFORM_CHILD_LOCK_SUCCESS_VERIFY,
        EVENT_CHILDLOCK_MAX = EVENT_CHILDLOCK_ENTER + 500,
        #endregion

        #region 初始界面
        EVENT_MAIN_ENTER,
        /// <summary>
        /// 创建初始界面
        /// </summary>
        MAIN_SUCCESS_VERIFY,

        /// <summary>
        /// 隐藏初始界面
        /// </summary>
        MAIN_HIDE,
        EVENT_MAIN_MAX = EVENT_MAIN_ENTER + 500,
        #endregion

        #region 计算机运行与构成
        EVENT_COMPUTERCONSTITUTION_ENTER,
        /// <summary>
        /// 安装下一步
        /// </summary>
        INSTALL_NEXT_STEP,
        /// <summary>
        /// 滑入介绍模型
        /// </summary>
        SHOW_MODEL_INTRO,
        /// <summary>
        /// 模拟电脑部分步骤通知
        /// </summary>
        STEP_MODEL_PU,
        /// <summary>
        /// 开始接入特殊步骤
        /// </summary>
        SPECIAL_STEP,
        EVENT_COMPUTERCONSTITUTION_MAX = EVENT_COMPUTERCONSTITUTION_ENTER + 500,
        #endregion

        #region 计算机布线基础认知界面
        EVENT_BUXIAN_ENTER,
        /// <summary>
        /// 创建综合布线基础认知界面
        /// </summary>
        BUXIANCO,
        /// <summary>
        /// 创建综合布线训练操作界面
        /// </summary>
        BUXIANOP,
        /// <summary>
        /// 滑入介绍模型
        /// </summary>
        SHOW_BX_MODEL_INTRO,
        /// <summary>
        /// 选择工具模型
        /// </summary>
        SELECT_BX_TOOL,
        /// <summary>
        /// 播放timeline
        /// </summary>
        PLAY_TINELINE,
        EVENT_BUXIANCO_MAX = EVENT_BUXIAN_ENTER + 500,
        #endregion

        #region 计算机上架
        EVENT_SHANGJIA_ENTER,
        /// <summary>
        /// 该步骤触发一个 其他模型也触发
        /// </summary>
        COMMON_MODEL,
        /// <summary>
        /// 步骤完成
        /// </summary>
        STEP_FINISH,
        /// <summary>
        /// 提交考试
        /// </summary>
        EXAM_SUB,
        /// <summary>
        /// 显示考试通用界面
        /// </summary>
        EXAM_SHOW,
        /// <summary>
        /// 关闭考试通用界面
        /// </summary>
        EXAM_CLOSE,
        /// <summary>
        /// 点击了提示
        /// </summary>
        CLICK_TIP,
        EVENT_SHANGJIA_MAX = EVENT_SHANGJIA_ENTER + 500,
        #endregion

        #region UI控制
        /// <summary>
        /// 登陆界面
        /// </summary>
        EVENT_CREATE_LOGINWINDOW,
        /// <summary>
        /// 主界面
        /// </summary>
        EVENT_CREATE_MAINWINDOW,
        /// <summary>
        /// 关闭主界面
        /// </summary>
        EVENT_CLOSE_MAINWINDOW,
        /// <summary>
        /// 主界面底部界面
        /// </summary>
        EVENT_CRATE_MAINBOTTOMWINDOW,
        /// <summary>
        /// 关闭主界面底部界面
        /// </summary>
        EVENT_CLOSE_MAINBOTTOMWINDOW,
        /// <summary>
        /// 打开消毒界面
        /// </summary>
        EVENT_CREATE_XIAODUWINDOW,
        /// <summary>
        /// 关闭消毒界面
        /// </summary>
        EVENT_CLOSE_XIAODUWINDOW,
        /// <summary>
        /// 打开新消毒界面
        /// </summary>
        EVENT_CREATE_NEWXIAODUWINDOW,
        /// <summary>
        /// 关闭新消毒界面
        /// </summary>
        EVENT_CLOSE_NEWXIAODUWINDOW,
        EVENT_CREATE_LONGPRESSWINDOW,
        EVENT_CLOSE_LONGPRESSWINDOW,
        EVENT_CREATE_SLIDERWINDOW,
        EVENT_CLOSE_SLIDERWINDOW,
        EVENT_CREATE_OPERATEBTNWINDOW,
        EVENT_CLOSE_OPERATEBTNWINDOW,
        EVENT_CREATE_TIMEGOWINDOW,
        EVENT_CLOSE_TIMEGOWINDOW,

        /// <summary>
        /// 清创术洗刷界面
        /// </summary>
        EVENT_CREATE_XISHUAWINDOW,
        EVENT_CLOSE_XISHUAWINDOW,

        /// <summary>
        /// 搜索界面
        /// </summary>
        EVENT_CREATE_MAINSEARCHWINDOW,
        /// <summary>
        /// 关闭搜索界面
        /// </summary>
        EVENT_CLOSE_MAINSEARCHWINDOW,
        /// <summary>
        /// 主页界面
        /// </summary>
        EVENT_CREATE_MAINPAGEWINDOW,
        /// <summary>
        /// 关闭主页界面
        /// </summary>
        EVENT_CLOSE_MAINPAGEWINDOW,
        /// <summary>
        /// 课程界面
        /// </summary>
        EVENT_CREATE_COURSEWINDOW,
        /// <summary>
        /// 关闭课程界面
        /// </summary>
        EVENT_CLOSE_COURSEWINDOW,
        /// <summary>
        /// 订阅界面
        /// </summary>
        EVENT_CREATE_SUBSCRIBEWINDOW,
        /// <summary>
        /// 关闭订阅界面
        /// </summary>
        EVENT_CLOSE_SUBSCRIBEWINDOW,
        /// <summary>
        /// 个人中心界面
        /// </summary>
        EVENT_CREATE_PERSONALCENTERWINDOW,
        /// <summary>
        /// 关闭个人中心界面
        /// </summary>
        EVENT_CLOSE_PERSONALCENTERWINDOW,
        /// <summary>
        /// 单个课程显示
        /// </summary>
        EVENT_CREATE_SINGELECOURSEWINDOW,
        /// <summary>
        /// 关闭课程搜索界面
        /// </summary>
        EVENT_CLOSE_COURSESEARCHRESULTWINDOW,
        /// <summary>
        /// 开启课程搜索界面
        /// </summary>
        EVENT_CREATE_COURSESEARCHRESULTWINDOW,
        /// <summary>
        /// 课程首页界面
        /// </summary>
        EVENT_CREATE_COURSEHOMEPAGEWINDOW,
        /// <summary>
        /// 关闭课程首页界面
        /// </summary>
        EVENT_CLOSE_COURSEHOMEPAGEWINDOW,
        /// <summary>
        /// 开启术后问答界面
        /// </summary>
        EVENT_CREATE_QUESTIONWINDOW,
        /// <summary>
        /// 关闭术后问答界面
        /// </summary>
        EVENT_CLOSE_QUESTIONWINDOW,
        /// <summary>
        /// 分数界面
        /// </summary>
        EVENT_CREATE_SCOREWINDOW,
        /// <summary>
        /// 关闭分数界面
        /// </summary>
        EVENT_CLOSE_SCOREWINDOW,

        /// <summary>
        /// 搜索结果为空
        /// </summary>
        EVENT_SEARCH_NORESULT,

        /// <summary>
        /// 更新近期训练记录
        /// </summary>
        EVENT_UPDATE_RECENTTRAIN,
        /// <summary>
        /// 更新精品课程记录
        /// </summary>
        EVENT_UPDATE_QUALITYCOURSE,

        /// <summary>
        /// 医生对话
        /// </summary>
        EVENT_CREATE_DOCTORDIALOGUEWINDOW,
        EVENT_CLOSE_DOCTORDIALOGUEWINDOW,
        /// <summary>
        /// 答题
        /// </summary>
        EVENT_CREATE_ANSWERWINDOW,
        EVENT_CLOSE_ANSWERWINDOW,
        /// <summary>
        /// NPC对话
        /// </summary>
        EVENT_CREATE_NCPDIAOGUEWINDOW,
        /// <summary>
        /// 开启信息中心
        /// </summary>
        EVENT_CREATE_MESSAGECENTERWINDOW,
        /// <summary>
        /// 关闭信息中心
        /// </summary>
        EVENT_CLOSE_MESSAGECENTERWINDOW,


        /// <summary>
        /// 开启账号及安全
        /// </summary>
        EVENT_CREATE_ACCOUNTANDSECURITYWINDOW,
        /// <summary>
        /// 关闭账号及安全
        /// </summary>
        EVENT_CLOSE_ACCOUNTANDSECURITYWINDOW,
        /// <summary>
        /// 修改密码回调
        /// </summary>
        EVENT_CHANGE_PASSWARD_CB,

        /// <summary>
        /// 开启意见反馈窗口
        /// </summary>
        EVENT_CREATE_OPINIONFEEDBACKWINDOW,
        /// <summary>
        /// 关闭意见反馈窗口
        /// </summary>
        EVENT_CLOSE_OPINIONFEEDBACKWINDOW,
        /// <summary>
        /// 提交意见反馈成功返回
        /// </summary>
        EVENT_RECV_OPINIONFEEDBACK,

        /// <summary>
        /// 开启通用webUI窗口
        /// </summary>
        EVENT_CREATE_COMMONWEBUIWINDOW,
        /// <summary>
        /// 关闭通用webUI窗口
        /// </summary>
        EVENT_CLOSE_COMMONWEBUIWINDOW,

        /// <summary>
        /// 开启个人资料页面
        /// </summary>
        EVENT_CREATE_PERSONALDATA,
        /// <summary>
        /// 关闭个人资料页面
        /// </summary>
        EVENT_CLOSE_PERSONALDATA,

        /// <summary>
        /// 开启帮助中心页面
        /// </summary>
        EVENT_CREATE_HELPCENTER,
        /// <summary>
        /// 关闭帮助中心页面
        /// </summary>
        EVENT_CLOSE_HELPCENTER,

        /// <summary>
        /// 开启修改资料页面
        /// </summary>
        EVENT_CREATE_MODIFYDATAWINDOW,
        /// <summary>
        /// 关闭修改资料页面
        /// </summary>
        EVENT_CLOSE_MODIFYDATAWINDOW,

        /// <summary>
        /// 开启模考排行榜界面
        /// </summary>
        EVENT_CREATE_MOCKRANKWINDOW,
        /// <summary>
        /// 关闭模考排行榜界面
        /// </summary>
        EVENT_CLOSE_MOCKRANKWINDOW,
        /// <summary>
        /// 获取模考排行榜数据
        /// </summary>
        EVENT_GET_MOCKRANK_DATA,

        /// <summary>
        /// 开启公告界面
        /// </summary>
        EVENT_CREATE_NOTICEWINDOW,
        /// <summary>
        /// 关闭公告界面
        /// </summary>
        EVENT_CLOSE_NOTICEWINDOW,
        /// <summary>
        /// 获取公告数据
        /// </summary>
        EVENT_GET_NOTICE_DATA,
        /// <summary>
        /// 更新主页公告
        /// </summary>
        EVETN_UPDATE_MAINPAGE_NOTICE,
        /// <summary>
        /// 开启提交分数界面
        /// </summary>
        EVENT_CREATE_SUBMITSCOREWINDOW,
        /// <summary>
        /// 关闭提交分数界面
        /// </summary>
        EVENT_CLOSE_SUBMITSCOREWINDOW,

        /// <summary>
        /// 开启提交分数界面
        /// </summary>
        EVENT_CREATE_SIMULATHELPINFOWINDOW,
        /// <summary>
        /// 关闭提交分数界面
        /// </summary>
        EVENT_CLOSE_SIMULATHELPINFOWINDOW,

        /// <summary>
        /// 开启考核视角选择页面
        /// </summary>
        EVENT_CREATE_OPERATRANSWINDOW,
        /// <summary>
        /// 关闭考核视角选择页面
        /// </summary>
        EVENT_CLOSE_OPERATRANSWINDOW,
        /// <summary>
        /// 开启考核提示页面
        /// </summary>
        EVENT_CREATE_TESTTIPSWINDOW,
        /// <summary>
        /// 关闭考核提示页面
        /// </summary>
        EVENT_CLOSE_TESTTIPSWINDOW,
        #endregion

        /// <summary>
        /// 标本送检
        /// </summary>
        EVENT_CREATE_BIAOBENSONGJIAN,

        /// <summary>
        /// 人文关怀
        /// </summary>
        EVENT_CREATE_RENWENGUANHUAIWINDOW,
        EVENT_CLOSE_RENWENGUANHUAIWINDOW,

        /// <summary>
        /// 搜索关闭
        /// </summary>
        EVENT_OPEN_SERACHCLOSE,
        /// <summary>
        /// 搜索取消
        /// </summary>
        EVENT_OPEN_SERACHCANCLE,
        /// <summary>
        /// 显示下一步按钮
        /// </summary>
        EVENT_CHANGE_XIAODUNEXTON,
        /// <summary>
        /// 隐藏下一步按钮
        /// </summary>
        EVENT_CHANGE_XIAODUNEXTOFF,
        /// <summary>
        /// 显示消毒完成按钮
        /// </summary>
        EVENT_CHANGE_XIAODUENDON,
        /// <summary>
        /// 隐藏消毒完成按钮
        /// </summary>
        EVENT_CHANGE_XIAODUENDOFF,
        /// <summary>
        /// 消毒下一步
        /// </summary>
        EVENT_CHANGE_XIAODUNEXT,
        /// <summary>
        /// 医生对话完成
        /// </summary>
        EVENT_DOCTORDIALOGUE_OVER,
        /// <summary>
        /// 人文关怀答题完成
        /// </summary>
        EVENT_RWGH_ANSWER_OVER,
        /// <summary>
        /// 人文关怀完成
        /// </summary>
        EVENT_RWGH_OVER,
        /// <summary>
        /// 医生高亮关闭
        /// </summary>
        EVENT_DOCTOR_HIGHLIGHT_NO,
        /// <summary>
        /// 时间显示窗口
        /// </summary>
        EVENT_CREATE_TIMESHOW,
        /// <summary>
        /// 关闭时间窗口
        /// </summary>
        EVENT_CLOSE_TIMESHOW,
        /// <summary>
        /// 打开点位确定界面
        /// </summary>
        EVENT_CREATE_DIANWEIWINDOW,
        /// <summary>
        /// 关闭点位界面
        /// </summary>
        EVENT_CLOSE_DIANWEIWINDOW,
        /// <summary>
        /// 打开铺巾滑动条界面
        /// </summary>
        EVENT_OPEN_PUJINSLIDER,
        /// <summary>
        /// 关闭铺巾滑动条界面
        /// </summary>
        EVENT_CLOSE_PUJINSLIDER,

        /// <summary>
        /// 打开吸氧滑动条界面
        /// </summary>
        EVENT_OPEN_XIYANGSLIDER,
        /// <summary>
        /// 关闭吸氧滑动条界面
        /// </summary>
        EVENT_CLOSE_XIYANGSLIDER,

        /// <summary>
        /// 打开/关闭电除颤能量选择界面
        /// </summary>
        EVENT_OPEN_DCCENERGYWINDOW,
        EVENT_CLOSE_DCCENERGYWINDOW,

        #region 穿刺
        EVENT_CREATE_PUNCTUREWINDOW,
        EVENT_CLOSE_PUNCTUREWINDOW,
        /// <summary>
        /// 增加角度
        /// </summary>
        EVENT_INCREASE_ANGLE,
        /// <summary>
        /// 减少角度
        /// </summary>
        EVENT_REDUCE_ANGLE,
        /// <summary>
        /// 进针
        /// </summary>
        EVENT_INSERT_NEEDLE,
        /// <summary>
        /// 退针
        /// </summary>
        EVENT_WITHDRAW_NEEDLE,
        /// <summary>
        /// 刺入皮肤
        /// </summary>
        EVENT_PUNCTURE_ENTER,
        /// <summary>
        /// 穿刺
        /// </summary>
        EVENT_PUNCTURE_OPERATEBASE,
        /// <summary>
        /// 穿刺进针角度
        /// </summary>
        EVENT_PUNCTURE_JINZHEN_JIAODU,
        /// <summary>
        /// 穿刺步骤类型
        /// </summary>
        EVENT_PUNCTURE_STEP_TYPE,
        /// <summary>
        /// 穿刺抽血
        /// </summary>
        EVENT_PUNCTURE_CHOUXUE,
        /// <summary>
        /// 穿刺抽血刻度
        /// </summary>
        EVENT_PUNCTURE_CHOUXUE_HEIGHT,
        /// <summary>
        /// 拔出
        /// </summary>
        EVENT_PUNCTURE_EXIT,
        /// <summary>
        /// 穿刺检查样本结束
        /// </summary>
        EVENT_PUNCTURE_CHECK_YANGBEN_FINISH,
        /// <summary>
        /// 控制动脉脉搏跳动开关
        /// </summary>
        EVENT_CRTL_ARTERIOPUNCTURE,
        EVENT_PUNCTURE_END = EVENT_PUNCTURE_ENTER + 500,
        #endregion
        #region 力反馈传输数据
        EVENT_LIFANKUI_INPUTDADA_ENTER,
        /// <summary>
        /// 创建力反馈工具
        /// </summary>
        EVENT_CREATE_HAPTICTOOL,
        /// <summary>
        /// 移除力反馈工具
        /// </summary>
        EVENT_REMOVE_HAPTICTOOL,
        /// <summary>
        /// 模拟创建力反馈工具
        /// </summary>
        EVENT_TEST_CREATE_HAPTICTOOL,
        /// <summary>
        /// 发送到Bin层
        /// </summary>
        EVENT_TEST_BIN_CREATE_HAPTICTOOL,
        /// <summary>
        /// 力反馈HapticEffect_Hand数据
        /// </summary>
        EVENT_HAPTIC_EFFECT_HAND_DATA,
        /// <summary>
        /// 更改左右手HapticDevice的TRANSFORM
        /// 其实就是更改效应器操作范围(比如机械臂动一下,unity对应动很大位置,就把这个调小)
        /// </summary>
        EVENT_CHANGE_HAPTICDEVICE_TRANSFORM,
        EVENT_LIFANKUI_INPUTDADA = EVENT_LIFANKUI_INPUTDADA_ENTER + 500,
        #endregion
        /// <summary>
        /// 设置表面力
        /// </summary>
        EVENT_TOUCH_SETSHAP,

        /// <summary>
        ///  消毒完成
        /// </summary>
        EVENT_DISINFECT_OVER,

        EVENT_MODELDIRTY_CHANGE,

        /// <summary>
        /// 开启日期确认窗口
        /// </summary>
        EVENT_OPEN_QUALITYWINDOW,
        EVENT_CLOSE_QUALITYWINDOW,
        EVENT_OPEN_QUALITYSTYLEWINDOW,
        EVENT_CLOSE_QUALITYSTYLEWINDOW,

        /// <summary>
        /// 日期确认完成
        /// </summary>
        EVENT_SURE_QUALITY,

        /// <summary>
        /// 所有器械日期确认完成
        /// </summary>
        EVENT_ALLSURE_QUALITY,
        /// <summary>
        /// 打开镜头切换窗口
        /// </summary>
        EVENT_OPEN_LENSWITCH,
        EVENT_CLOSE_LENSWITCH,
        /// <summary>
        /// 模型加载完成
        /// </summary>
        EVENT_MODEL_FIN,

        /// <summary>
        /// 回收完成
        /// </summary>
        EVENT_RECYCLE_FINISH,
        /// <summary>
        /// 添加回收物品事件
        /// </summary>
        EVENT_ADD_RECYCLEGOODS,

        /// <summary>
        /// 回收步骤结束，开始题目
        /// </summary>
        EVENT_RECYCLE_OPERATE_END,

        /// <summary>
        /// 关闭器械选择
        /// </summary>
        EVENT_CLOSE_APPARATUSSELECT,

        #region 辅助NPC
        /// <summary>
        /// Npc操作通过任务激活
        /// </summary>
        EVENT_ANPC_OPERATEBYID,
        /// <summary>
        /// 直接点护士头像的操作
        /// </summary>
        EVENT_ANPC_CLICKNURSEBTN,
        /// <summary>
        /// Npc对话框被隐藏
        /// </summary>
        EVENT_ANPC_HIDE,
        /// <summary>
        /// NCP语言关闭
        /// </summary>
        EVENT_NPCMUSIC_OFF,
        /// <summary>
        /// SendNpc物品节点  使用者注册监听
        /// </summary>
        EVENT_ANPC_SEND_GOODSNODE,

        /// <summary>
        /// 更新NPC对话内容
        /// </summary>
        EVENT_UPDATE_DIALOGUE,

        #endregion
        /// <summary>
        /// 上传日志
        /// </summary>
        EVENT_UPDATE_OPERATELOG,

        /// <summary>
        ///
        /// 直接发日志
        ///
        /// </summary>

        EVENT_UPDATE_DIRECTOPERATELOG,
        /// <summary>
        /// 取消表面力
        /// </summary>
        EVENT_TOUCH_REMSHAP,

        /// <summary>
        /// 分数UI
        /// </summary>
        EVENT_OPEN_SCORE,
        /// <summary>
        /// 戴手套
        /// </summary>
        EVENT_TOOLFUN_ONGLOVE,

        /// <summary>
        /// 铺洞巾
        /// </summary>
        EVENT_PUDONGJIN_OVER,
        /// <summary>
        /// 播放洞巾动画
        /// </summary>
        EVENT_PUDONGJIN_PLAY,
        /// <summary>
        /// haptic道具和道具功能初始化完成
        /// </summary>
        EVENT_HAPTICTOOL_FUNCTION_FINISH,

        /// <summary>
        /// 温度枪
        /// </summary>
        EVENT_OPEN_WENDUQIANG,
        EVENT_CLOSE_WENDUQIANG,

        /// <summary>
        /// 丢棉签
        /// </summary>
        EVENT_TOOLFUN_THROWMIANQIAN,

        /// <summary>
        /// 打开GIF
        /// </summary>
        EVENT_OPEN_GIFPLAY,

        /// <summary>
        /// 镜头切换
        /// </summary>
        EVENT_LENSITCH_BEGIN,

        /// <summary>
        /// 镜头切换完成
        /// </summary>
        EVENT_LENSWITCH_FIN,

        /// 器械选择完成
        /// </summary>
        EVENT_OVRER_APPARATUSSELECT,

        /// 器械选择完成
        /// </summary>
        EVENT_XIYANGSHU_APPARATUSSELECT,

        /// <summary>
        /// 患者动画
        /// </summary>
        EVENT_PATIATEN_AINIMATION,

        /// <summary>
        /// 患者位置
        /// </summary>
        EVENT_PATIANT_HANDPOS,

        /// <summary>
        /// 洞巾初始化
        /// </summary>
        EVENT_START_PUDONGJIN,
        /// <summary>
        /// 手的姿势
        /// </summary>
        EVENT_HANDPOSE,
        /// 洞巾任务结束
        /// </summary>
        EVENT_OVER_DATI,
        /// <summary>
        /// 人文关怀
        /// </summary>
        EVENT_RENWENGUANHAI_OVER,
        /// <summary>
        /// 体位答题结束
        /// </summary>
        EVENT_TIWEIANSWER_OVER,

        /// <summary>
        /// 穿刺完成
        /// </summary>
        EVENT_CHUANTI_FINISH,
        /// <summary>
        /// 检查肝素完成
        /// </summary>
        EVENT_GANSU_FINISH,
        /// <summary>
        /// 检查针管完成
        /// </summary>
        EVENT_ZHENGUANCHECK_FINISH,

        /// <summary>
        /// 抽血完成
        /// </summary>
        EVENT_CHOUXUE_FINISH,
        /// <summary>
        ///答题完播动画
        /// </summary>
        EVENT_ANSWER_OVER_PLAYAINM,
        /// <summary>
        /// 病人播放动画
        /// </summary>
        EVENT_BINGREN_PLAYANI,
        EVENT_CHANGE_DONGJIN,
        EVTNT_BINGREN_SHABU,
        EVTNT_BINGREN_DONGJIN,
        EVTNT_BINGREN_DONGJINCOLLIDER,
        EVTNT_BINGREN_HAND,
        EVTNT_BINGREN_ZQTYS,
        EVTNT_BINGREN_XQCC_YIFU,
        /// <summary>
        /// 动脉穿待机摇头动画
        /// </summary>
        EVTNT_BINGREN_DMCC_IDLE,
        /// <summary>
        /// 动脉穿洞巾2动画
        /// </summary>
        EVTNT_BINGREN_DMCC_DONGJIN2,
        /// <summary>
        /// 动脉穿测血压
        /// </summary>
        EVTNT_BINGREN_DMCC_CEXUEYA,
        /// <summary>
        /// NPC对话结束
        /// </summary>
        EVENT_NPCDIALOGUE_OVER,
        #region 
        /// <summary>
        /// 病例选择完成
        /// </summary>
        EVENT_CASESELECT_FINISH,
        #endregion
        #region 任务模块
        /// <summary>
        /// 任务开始
        /// </summary>
        EVENT_TASK_START,
        /// <summary>
        /// 任务结束
        /// </summary>
        EVENT_TASK_FINISH,
        /// <summary>
        /// 任务更新
        /// </summary>
        EVENT_TASK_STARTUPDATE,
        /// <summary>
        /// 单个任务发布
        /// </summary>
        EVENT_TASK_STARTSINGELE,
        /// <summary>
        /// 任务发布完成
        /// </summary>
        EVENT_TASK_START_FINISH,
        /// <summary>
        /// 单个任务状态
        /// </summary>
        EVENT_TASK_SINGSTATE,
        /// <summary>
        /// 单个任务失败后刷新
        /// </summary>
        EVENT_TASK_SINGLEUPDATE,
        /// <summary>
        /// 多人任务需要房间的所有人都达到条件
        /// </summary>
        EVENT_TASKTYPE,
        /// <summary>
        /// UI界面更新
        /// </summary>
        EVENT_PREPERATION_UPDATE,
        /// <summary>
        /// 更新下一步骤ID
        /// </summary>
        EVENT_UPDATETASK_NEXTTASKID,
        #endregion
        /// <summary>
        /// 退出场景
        /// </summary>
        EVENT_EXITROOM,

        /// <summary>
        /// 当前训练场景
        /// </summary>
        EVENT_TRAINSCNEN,


        #region 器械选择
        EVENT_OPEN_APPARATUSSELECT,
        /// <summary>
        /// 加载器械到手术车
        /// </summary>
        EVENT_LOADAPPARATUS_SURGICALVEHICLE,
        #endregion
        /// <summary>
        /// 创建切换体位窗口
        /// </summary>
        EVENT_CREATE_CHANGETIWEIWINDOW,
        /// <summary>
        /// 关闭切换体位窗口
        /// </summary>
        EVENT_CLOSE_CHANGETIWEIWINDOW,
        #region 道具功能
        /// <summary>
        /// 蘸碘伏
        /// </summary>
        EVENT_TOOLFUN_OnIodine,

        /// <summary>
        /// 垃圾桶
        /// </summary>
        EVENT_TOOLFUN_TrashCan,

        #endregion
        #region 对话Ui
        /// <summary>
        /// 开启对话操作
        /// </summary>
        EVENT_OPERATEDIGLOUE_OPEN,
        /// <summary>
        /// 对话操作完成
        /// </summary>
        EVENT_OPERATEFORNT_FINISH,
        /// <summary>
        /// 创建对话UI
        /// </summary>
        EVENT_CREATE_DIAGLUEWINDOW,
        /// <summary>
        /// 关闭对话UI
        /// </summary>
        EVENT_CLOSE_DIAGLUEWINDOW,
        #endregion
        /// <summary>
        /// 清除paint绘画内容
        /// </summary>
        CLEAN_PAINT,
        /// <summary>
        /// 改变Paint颜色
        /// </summary>
        CLEAN_COlOR,
        /// <summary>
        /// 保存Paint
        /// </summary>
        SAVE_PAINT,
        /// <summary>
        /// 加载Paint
        /// </summary>
        LOAD_PAINT,
        /// <summary>
        /// 导尿消毒保存
        /// </summary>
        DAO_SAVEPAINT,
        /// <summary>
        /// 导尿加载Paint
        /// </summary>
        DAO_LOADPAINT,
        /// <summary>
        /// 设置Paintable的开启状态
        /// </summary>
        SET_PAINTABLE_ENABLE,
        /// <summary>
        /// 清创术消毒保存
        /// </summary>
        QCS_XDSAVE,
        /// <summary>
        /// 清创术消毒加载
        /// </summary>
        QCS_XDLOAD,

        #region 
        /// <summary>
        /// 回收窗口
        /// </summary>
        EVENT_CREATE_RECYCLEWINDOW,
        EVENT_CLOSE_RECYCLEWINDOW,
        #endregion
        /// <summary>
        /// 关闭顶部菜单
        /// </summary>
        EVENT_CREAT_MAINTOPWINDOW,
        /// <summary>
        /// 关闭顶部菜单
        /// </summary>
        EVENT_CLOSE_MAINTOPWINDOW,
        /// <summary>
        /// 默认双手状态
        /// </summary>
        EVENT_DEFAULT_HAND,
        /// <summary>
        /// 戴手套状态
        /// </summary>
        Event_GLOVE_HAND,
        /// <summary>
        /// 左手无器械状态
        /// </summary>
        EVENT_NONE_LEFTHAND,
        /// <summary>
        /// 左手有器械状态
        /// </summary>
        EVENT_TOOL_LEFTHAND,
        /// <summary>
        /// 右手无器械状态
        /// </summary>
        EVENT_NONE_RIGHTHAND,
        /// <summary>
        /// 右手有器械状态
        /// </summary>
        EVENT_TOOL_RIGHTHAND,
        /// <summary>
        /// 管理UI手套高亮
        /// </summary>
        EVENT_GLOVE_HIGHTMODEL,
        #region 场景加载预制体
        EVENT_SCENEMODEL_ENTER,
        EVENT_ENTITY_LOAD_FINISH,
        EVENT_SCENEMODEL_END = EVENT_SCENEMODEL_ENTER + 500,
        #endregion
        /// <summary>
        /// 护士气泡
        /// </summary>
        EVENT_BUBBLE_NURSE,
        /// <summary>
        /// 创建患者信息页面
        /// </summary>
        EVENT_CREAT_PATIENTINFOWINDOW,
        /// <summary>
        /// 关闭患者信息页面
        /// </summary>
        EVENT_CLOSE_PATIENTINFOWINDOW,
        /// <summary>
        /// 创建底部菜单栏
        /// </summary>
        EVENT_CREAT_CONTROLSTRIPTWINDOW,
        /// <summary>
        /// 关闭底部菜单栏
        /// </summary>
        EVENT_CLOSE_CONTROLSTRIPTWINDOW,
        /// <summary>
        /// 锁定相机旋转视角
        /// </summary>
        EVENT_LOCK_CAMERA,
        /// <summary>
        /// 解锁相机旋转视角
        /// </summary>
        EVENT_UNLOCK_CAMERA,
        /// <summary>
        /// 解锁相机旋转视角
        /// </summary>
        EVENT_CONTROLSTRIP_NEEDUNLOCK,

        /// <summary>
        /// 器械选择的物体加载完成
        /// </summary>
        EVENT_APPARATUS_LOAD_FINISH,
        /// <summary>
        /// 人文关怀选择正确
        /// </summary>
        EVENT_RWGH_SELECTION_YES,

        /// <summary>
        /// 查看病历本
        /// </summary>
        EVENT_CHECK_CASEINFO,

        /// <summary>
        ///  任务重置显示病历本
        /// </summary>
        EVENT_OPEN_CASEINFO,
        /// <summary>
        /// 分数上传
        /// </summary>
        EVENT_SCORE_UPDATE,
        /// <summary>
        /// 每次退出都把总分清空一下
        /// </summary>
        EVENT_REC_FINALSCORE,
        #region 日志事件
        /// <summary>
        /// 日志窗口
        /// </summary>
        EVENT_OPEN_OPERATELOGWIN,
        /// <summary>
        /// 日志窗口更新
        /// </summary>
        EVENT_UPTDATE_OPERATEWIN,
        /// <summary>
        /// 日志窗口关闭
        /// </summary>
        EVENT_CLOSE_OPERATELOGWIN,
        #endregion
        /// <summary>
        /// 拔针结束
        /// </summary>
        EVENT_PUNCTURE_EXIT_END,
        /// <summary>
        /// 拔针开始
        /// </summary>
        EVENT_PUNCTURE_EXIT_START,
        /// <summary>
        /// 通过ID 播NPC对话
        /// </summary>
        EVENT_NPCDIALOGUG_ID,
        /// <summary>
        /// 点击了注射器
        /// </summary>
        EVENT_CHUANCI_ZHUSHEQI,
        /// <summary>
        /// 点击了纱布
        /// </summary>
        EVENT_CHUANCI_SHABU,
        /// <summary>
        /// 拔针
        /// </summary>
        EVENT_CHUANCI_BAZHEN,
        /// <summary>
        /// 按压纱布手
        /// </summary>
        EVENT_CHUANCI_ANYASHOU,
        /// <summary>
        /// 物体可以被点击
        /// </summary>
        EVENT_ITEMS_OPEN,
        /// <summary>
        /// 进入穿刺步骤
        /// </summary>
        EVENT_CHUANCI_CHUANCI,
        /// <summary>
        /// 物体被点击
        /// </summary>
        EVENT_ITEMS_ON,
        /// <summary>
        /// 消毒开始
        /// </summary>
        EVENT_CHANGE_XIAODUSTART,

        /// <summary>
        /// 体温计数据
        /// </summary>
        EVENT_TIWENJI_DATE,
        /// <summary>
        /// 血压仪数据
        /// </summary>
        EVENT_XUEYAYI_DATE,
        /// <summary>
        /// 道具显示左右手 true右手，false左手
        /// </summary>
        EVENT_ITEMS_HAND,

        EVENT_SHOWORHIDE_LEFTCONTROLSTRIP,
        EVENT_SHOWORHIDE_LEFTMARK,
        /// <summary>
        ///  任务开启
        /// </summary>
        EVENT_TASK_START_PATIRNT,
        /// <summary>
        /// 开始物品回收
        /// </summary>
        EVENT_HUISHOU_START,
        /// <summary>
        /// 消毒保持处理（特殊处理）
        /// </summary>
        DisinfectHover,

        /// <summary>
        /// 把modelclick点击的物体发出来
        /// </summary>
        EVENT_CLICK_MODELCLICKOBJ,
        /// <summary>
        /// 帮助按钮遮罩
        /// </summary>
        EVENT_OPEN_HELPMARK,

        /// <summary>
        /// 进入场景开启音效，解决音效关闭后二次打开读取缓存没声音的问题
        /// </summary>
        EVENT_OPEN_AUDIO,

        /// <summary>
        /// ROOT场景增加一个相机，启动渲染花屏问题，进入场景后就关闭
        /// </summary>
        EVENT_CLOSE_CAM,

        /// <summary>
        /// 当前任务的model
        /// </summary>
        EVENT_TASK_TASKMODEL,
        /// <summary>
        /// 清空当前任务的model
        /// </summary>
        EVENT_TASKCLOSE_TASKMODEL,
        /// <summary>
        /// 无菌指示器 点击使用
        /// </summary>
        EVENT_WJZSQ_USE,

        /// <summary>
        /// 无菌指示器 点击更换
        /// </summary>
        EVENT_WJZSQ_CHANG,
        EVENT_WJZSQ_HYCX_RESET,
        /// <summary>
        /// 穿刺包更换完成
        /// </summary>
        EVENT_CCB_CHANG,
        /// <summary>
        /// 蘸取碘伏
        /// </summary>
        EVENT_XIAODU_CLICKIODINE,
        /// <summary>
        /// 点击棉签
        /// </summary>
        EVENT_XIAODU_CLICKMIANQIAN,
        /// <summary>
        /// 消毒棉签状态
        /// </summary>
        EVENT_XIAODU_MIANQIANSTATUS,
        /// <summary>
        /// 消毒棉签移动
        /// </summary>
        EVENT_XIAODU_MIANQIANMOVE,
        /// <summary>
        /// 消毒次数
        /// </summary>
        EVENT_XIAODU_COUNT,
        /// <summary>
        /// 洗刷次数
        /// </summary>
        EVENT_XISHUA_COUNT,
        /// <summary>
        /// 激活隐藏背部
        /// </summary>
        EVENT_ACTIVE_PATIENTTORSO,
        /// <summary>
        /// 增加绘画组件
        /// </summary>
        EVENT_ADDPAINTENABLE,

        EVENT_JUDGE_DIANWEI,
        EVENT_END_KOUZHEN,

        #region 手术衣
        /// <summary>
        /// 手术衣windo
        /// </summary>
        EVENT_OPEN_SHOUSHUYIMAINWINDOW,
        EVENT_CLOSE_SHOUSHUYIMAINWINDOW,
        /// <summary>
        /// 答题开始，
        /// </summary>
        EVENT_ANSWER_TASKSTART,
        /// <summary>
        ///  答题结束，
        /// </summary>
        EVENT_ANSWER_TASKOVER,
        #endregion
        /// <summary>
        /// 穿刺点高亮
        /// </summary>
        EVENT_HIGHLIGHT_CHUANCIDIAN,
        /// <summary>
        /// 点位标记结束
        /// </summary>
        EVENT_END_BIAOJI,
        /// <summary>
        /// 高亮棉球
        /// </summary>
        EVENT_HIGHT_MIANQIU,
        /// <summary>
        /// 关闭高亮
        /// </summary>
        EVENT_HIde_MIANQIU,
        /// <summary>
        /// 点击棉球
        /// </summary>
        EVENT_CLICK_MIANQIU,


        EVENT_CREAT_WEBWINDOW,

        EVENT_CLOSE_WEBWINDOW,
        /// <summary>
        /// 骨骼meshcollider更新
        /// </summary>
        EVENT_SKINNEDMESHCOLLIDERUPDATE,
        /// <summary>
        /// 骨骼meshcollider更新,Update增加时间控制
        /// </summary>
        EVENT_SKINNEDMESHCOLLIDER_TIMEUPDATE,
        /// <summary>
        /// boxcollider更新
        /// </summary>
        EVENT_BOXCOLLIDER_UPDATE,
        /// <summary>
        /// 改变材质类型，针对MaterialChangebehaviour脚本
        /// </summary>
        EVENT_MATERIAL_CHANGE,
        /// <summary>
        /// 粒子特效操作
        /// </summary>
        EVENT_PARTICLESYSTEMOPERATE,
        /// <summary>
        /// 四肢骨折手指白点
        /// </summary>
        EVENT_SIZHIGUZE_HANDPOINTLIGHT,
        /// <summary>
        /// 打开消毒窗口
        /// </summary>
        EVENT_OPERATEXIAODUWINDOWS,
        /// <summary>
        /// 打开洗刷窗口
        /// </summary>
        EVENT_OPENXISHUAWINDOW,
        /// <summary>
        /// 消毒按钮点击完成
        /// </summary>
        EVENT_CLICKXIAODUCOMPLETE,
        /// <summary>
        /// 洗刷完成
        /// </summary>
        EVENT_CLICKXISHUABUTTON,
        /// <summary>
        /// heroMoveId更新
        /// </summary>
        EVENT_UPDATEHEROMOVEID,
        /// <summary>
        /// 器械选择步骤是否完成
        /// </summary>
        EVENT_STATUS_APPARATUSSELECTOPERATE,
        /// <summary>
        /// 设置消毒自动移动状态
        /// </summary>
        EVENT_SET_XIAODU_AUTO_MOVE,
        /// <summary>
        /// 单独设置消毒自动移动速度
        /// </summary>
        EVENT_REJUST_XIAODU_AUTO_MOVE_SPEED,
        /// <summary>
        /// 重置物体位置
        /// </summary>
        EVENT_RESET_XIAODU_AUTO_POS,

        EVENT_TASKMUTEX,

        /// <summary>
        /// 眨眼动画
        /// </summary>
        EVENT_BLINK_START,
        EVENT_BLINK_END,
        /// <summary>
        /// 转场UI
        /// </summary>
        EVENT_CREAT_TRANSITIONWINDOW,
        EVENT_CLOSE_TRANSITIONWINDOW,
        EVENT_CREAT_TRANSITIONWINDOW1,
        EVENT_CLOSE_TRANSITIONWINDOW1,

        /// <summary>
        ///内置网页url 
        /// </summary>
        EVENT_WEBVIEWUI_URL,
        /// <summary>
        /// 无菌指示器动画时跳过
        /// </summary>
        EVENT_WJZSQ_END,

        /// <summary>
        /// 发送个人中心折线图数据
        /// </summary>
        EVENT_PERSONALCENTER_LINECHARTDATA,
        /// <summary>
        /// 更新个人数据
        /// </summary>
        EVETN_UPDATE_PERSONAL_DATA,
        /// <summary>
        /// 更新个人头像
        /// </summary>
        EVETN_UPDATE_PERSONAL_HEAD_ICON,
        /// <summary>
        /// 获取个人中心token
        /// </summary>
        EVENT_GET_PERSONAL_TOKEN,

        /// <summary>
        /// 清空提示
        /// </summary>
        EVENT_CLEAR_TIPS,

        /// <summary>
        /// 开始计时
        /// </summary>
        EVENT_START_RECORDTIME,

        /// <summary>
        /// 确认选择考试提示
        /// </summary>
        EVENT_SELECT_OPERATIP,

        /// <summary>
        /// 考试提示界面确认按钮
        /// </summary>
        EVENT_CONFIRM_OPERATIP,

        /// <summary>
        /// 选择tog之后通知其他ison=false
        /// </summary>
        EVENT_SEND_OPERATIPCONFIRM,

        /// <summary>
        /// 选择了一个考试的视角
        /// </summary>
        EVENT_SELECT_OPERATRANS,

        /// <summary>
        /// 提示的任务完成了
        /// </summary>
        EVENT_END_TASKTIP,

        /// <summary>
        /// 移动物体
        /// </summary>
        EVENT_WEIGUAN_MOVE,
        /// <summary>
        /// 设置物体
        /// </summary>
        EVENT_SET_POS,
        /// <summary>
        /// 设置物体
        /// </summary>
        EVENT_SET_TWOPOS,
        /// <summary>
        /// 重置坐标
        /// </summary>
        EVENT_RESET_POS,
        /// <summary>
        /// 打开特写窗口
        /// </summary>
        EVENT_CREATE_FEATUREWINDOW,
        /// <summary>
        /// 关闭特写窗口
        /// </summary>
        EVENT_CLOSE_FEATUREWINDOW,
        /// <summary>
        /// 完成当前步骤
        /// </summary>
        EVENT_OVER_OPERATEHANDLECOMMAND,
        /// <summary>
        /// 当前任务是否完成成功
        /// </summary>
        EVENT_OVER_OPERATEENDSTATUS,
        /// <summary>
        /// 是否显示胃管
        /// </summary>
        EVENT_ISSHOW_WEIGUANM,
        /// <summary>
        /// 移动三腔二囊管
        /// </summary>
        EVENT_SQENG_MOVE,
        /// <summary>
        /// 设置置管、拔管操作的三腔模型状态
        /// </summary>
        EVENT_SET_SQENG_STATE,
        /// <summary>
        /// 是否能移动三腔二囊管
        /// </summary>
        EVENT_SQENG_CANMOVE,
        /// <summary>
        /// 三腔二囊管播放动画
        /// </summary>
        EVENT_SQENG_PLAYANI,
        /// <summary>
        /// 三腔二囊管50ml针管液体材质
        /// </summary>
        EVENT_SQENG_YETIMAT,
        /// <summary>
        /// 三腔护理记录单
        /// </summary>
        EVENT_SQENG_HLJLD,

        /// <summary>
        /// NPC播放前先检查是否开启了考试提示
        /// </summary>
        EVENT_OPEN_TESTTIP,

        /// <summary>
        /// NPC播放前先检查是否开启了考试提示
        /// </summary>
        EVENT_SEND_TESTTIP,

        /// <summary>
        /// 是否能移动胃管
        /// </summary>
        EVENT_WEIGUAN_CANMOVE,

        /// <summary>
        /// 提示窗口打开的时候先确认是否已经有选择项，控制确认按钮状态
        /// </summary>
        EVENT_CONFIRM_HASSELECT,
        /// <summary>
        /// 提示窗口打开的时候先确认是否已经有选择项，接收状态
        /// </summary>
        EVENT_RECEIVE_HASSELECT,
        /// <summary>
        /// 胃管处理了呛咳
        /// </summary>
        EVENT_WEIGUAN_QIANGKE,
        /// <summary>
        /// 跳步完成胃管置入前半步骤
        /// </summary>
        EVENT_WEIGUAN_QIANBU,
        /// <summary>
        /// 跳步完成胃管置入前后步骤
        /// </summary>
        EVENT_WEIGUAN_HOUBU,
        /// <summary>
        /// 更换胃管落下抬起胃管的path  从而改变位置
        /// </summary>
        EVENT_WEIGUAN_CHANGEPATH,
        /// <summary>
        /// 重置置入胃管的全部状态
        /// </summary>
        EVENT_WEIGUAN_RESETALL,
        #region 跳多步
        EVENT_JUMPSTEP_ENTER = 40000,
        /// <summary>
        /// 跳步的索引
        /// </summary>
        EVENT_JUMPSTEP_INDEX,
        /// <summary>
        /// 跳步的索引+1
        /// </summary>
        EVENT_JUMPSTEP_ADD_INDEX,
        /// <summary>
        /// 激活NPC预制体
        /// </summary>
        EVENT_SHOW_NPC,
        /// <summary>
        /// Npc操作通过任务id激活
        /// </summary>
        EVENT_ANPC_ID,
        /// <summary>
        /// 任务设置完成
        /// </summary>
        EVENT_SET_TASK_FINISH,
        /// <summary>
        /// 任务初始化完成
        /// </summary>
        EVENT_TASK_INIT_FINISH,
        /// <summary>
        /// 跳多步
        /// </summary>
        EVENT_TAKS_JUMP_NUM,
        EVENT_JUMPSTEP_MAX = 41000,
        #endregion


        EVENT_HILIGHT_BASE,

        EVENT_OPEN_TIPS,
        /// <summary>
        /// 长按进度条
        /// </summary>
        EVENT_LONGPRESS_PROGRESS,
        /// <summary>
        /// 特写镜头界面 箭头显示 默认向左 true向左 false 向右
        /// </summary>
        EVENT_FEATURE_ARROWS,
        /// <summary>
        /// 特写镜头界面 24hour
        /// </summary>
        EVENT_FEATURE_24HOUR,
        /// <summary>
        /// 特写镜头界面 24hour结束
        /// </summary>
        EVENT_FEATURE_24HOUR_OVER,

        /// <summary>
        /// 打开紫绀提示界面
        /// </summary>
        EVENT_CREATE_TIPSZIGANWINDOW,
        /// <summary>
        /// 关闭紫绀提示界面
        /// </summary>
        EVENT_CLOSE_TIPSZIGANWINDOW,

        /// <summary>
        /// 请求获取场景灯光
        /// </summary>
        EVENT_REQUIRE_ROOMLIGHT,
        /// <summary>
        /// 发送场景灯光物体
        /// </summary>
        EVENT_SEND_ROOMLIGHT,
        /// <summary>
        /// 开空调
        /// </summary>
        EVENT_KAIKONGTIAO,
        /// <summary>
        /// 空调高亮
        /// </summary>
        EVENT_HIGHLIGHT_KONGTIAO,

        EVENT_SEND_XDLIGHT,

        #region 消毒与铺巾
        /// <summary>
        /// 展开铺巾滑动条
        /// </summary>
        EVENT_PUJINZHANKAI_SLIDER,
        /// <summary>
        /// 展开中单滑动条
        /// </summary>
        EVENT_ZHONGDAN_SLIDER,
        /// <summary>
        /// 大单滑动条
        /// </summary>
        EVENT_DADAN_SLIDER,
        /// <summary>
        /// 展开无菌巾结束
        /// </summary>
        EVENT_PUJINZHANKAI_END,
        /// <summary>
        /// 展开中单结束
        /// </summary>
        EVENT_ZHANKAIZHONGDAN_END,
        /// <summary>
        /// 展开大单结束
        /// </summary>
        EVENT_DADAN_END,
        /// <summary>
        /// 铺巾位置：上
        /// </summary>
        EVENT_PUJINSHUNXU_SHANG,
        /// <summary>
        /// 铺巾位置：下
        /// </summary>
        EVENT_PUJINSHUNXU_XIA,
        /// <summary>
        /// 铺巾位置：左
        /// </summary>
        EVENT_PUJINSHUNXU_ZUO,
        /// <summary>
        /// 铺巾位置：右
        /// </summary>
        EVENT_PUJINSHUNXU_YOU,
        #endregion

        /// <summary>
        /// 手术衣透明切换
        /// </summary>
        EVENT_SHOUSHUYI_CHANGE,
        /// <summary>
        /// 手术衣透明瞬间切换
        /// </summary>
        EVENT_SHOUSHUYI_NOTIMECHANGE,
        /// <summary>
        /// 打开关闭特写镜头 特写显示
        /// </summary>
        EVENT_FEATURE_IMAGE,

        EVENT_GET_HAPTICTOOLOBJ,

        EVENT_CHANGE_P3dHITNEARBY,

        EVENT_DONGJING_HIDEPOINT,

        EVENT_SHOW_XDPJRIGHTLEFT,

        EVENT_SEND_XDPERSENT,

        EVENT_CLICK_RENWENGUANHUAI,
        /// <summary>
        /// 滑动条UI数值变动
        /// </summary>
        EVENT_SLIDERWINDOW_VALUECHANGED,
        EVENT_SLIDERWINDOW_OVERBTNCLICK,
        EVENT_OPEN_OBIFLUIDRENDERER,
        /// <summary>
        /// 操作完成按钮UI点击
        /// </summary>
        EVENT_OPERATEBTNWINDOW_BTNCLICK,
        /// <summary>
        /// 打开横截面窗口
        /// </summary>
        EVENT_CREATE_HENGJIEMIANWINDOW,
        /// <summary>
        /// 关闭横截面窗口
        /// </summary>
        EVENT_CLOSE_HENGJIEMIANWINDOW,

        EVENT_CAMERALOOKMASK,

        /// <summary>
        /// 打开通用选择照片界面
        /// </summary>
        EVENT_CREATE_COMMONSELECTPHOTOWINDOW,
        EVENT_CLOSE_COMMONSELECTPHOTOWINDOW,

        #region 心肺复苏事件
        /// <summary>
        /// 打开/关闭倒计时页面
        /// </summary>
        EVENT_CREATE_TIMECOUNTDOWNWINDOW,
        EVENT_CLOSE_TIMECOUNTDOWNWINDOW,
        /// <summary>
        /// 更改心肺复苏倒计时状态
        /// </summary>
        EVENT_CHANGE_XFFS_COUNTDOWN,

        /// <summary>
        /// 胸外按压事件
        /// </summary>
        EVENT_START_ANYA,
        EVENT_END_ANYA,
        EVENT_COUNT_ANYA,
        #endregion

        #region 吸痰术事件
        /// <summary>
        /// 显示吸痰管
        /// </summary>
        EVENT_SHOW_XTG,
        /// <summary>
        /// 隐藏吸痰管
        /// </summary>
        EVENT_HIDE_XTG,
        /// <summary>
        /// 设置吸痰机动画enable
        /// </summary>
        EVENT_SET_XTG_ANI_STATE,
        #endregion

        /// <summary>
        /// 打开关闭切开页面
        /// </summary>
        EVENT_CREATE_QIEKAIWINDOW,
        EVENT_CLOSE_QIEKAIWINDOW,
        EVENT_QIEKAI_CAN_MOVE,

        /// <summary>
        /// P3D Clear操作  解决bug
        /// </summary>
        EVENT_CLEAR_TEXTURE,
        #region OperateUI
        /// <summary>
        /// 点击
        /// </summary>
        EVENT_ADDCLICK_OPERATEUI,
        /// <summary>
        /// 点击结束
        /// </summary>
        EVENT_OVERCLICK_OPERATEUI,
        /// <summary>
        /// 设置文本
        /// </summary>
        EVENT_SETTEXT_OPERATEUI,
        #endregion

        /// <summary>
        /// 清创术冲洗
        /// </summary>
        EVENT_QCS_CHONGXI,
        EVENT_QCS_CHONGXITIAOBU,
        /// <summary>
        /// 清创术泡沫开关
        /// </summary>
        EVENT_QCS_PAOMO_OPEN,
        EVENT_QCS_PAOMO_CLOSE,
        /// <summary>
        /// 清创术洗刷
        /// </summary>
        EVENT_QCS_XISHUA,

        /// <summary>
        /// 刷新重置点击两个物体触发任务状态
        /// </summary>
        EVENT_TWOONCLICK_REFRESH,

        /// <summary>
        /// 重置切开划线状态
        /// </summary>
        EVENT_QIEKAISTATE_REFRESH,
        /// <summary>
        /// 切开的完成状态
        /// </summary>
        EVENT_QIEKAISTATE_DATA,
        /// <summary>
        /// 清除切开划线痕迹
        /// </summary>
        EVENT_QIEKAISTATE_CLEAR,
        /// <summary>
        ///激活 确定进出针 UI
        /// </summary>
        EVENT_UIBTU_QUEDINGFENGHE,

        /// <summary>
        /// 确定进出针 UI(FengHeQueDingWindow界面点击按钮事件FengHeSetButton.ButType)
        /// </summary>
        EVENT_UIBTU_QUEDINGJINCHUZHEN,
        /// <summary>
        /// 缝合设置固定动画状态
        /// </summary>
        EVENT_FENGHE_SET_GUDINGANIM,

        /// <summary>
        /// 开关缝合UI界面
        /// </summary>
        EVENT_CREATE_FENGHEWINDOW,
        EVENT_CLOSE_FENGHEIWINDOW,

        /// <summary>
        /// 创建绳子
        /// </summary>
        EVENT_CTEATE_OBISHENGZI,

        /// <summary>
        /// 绳子移动距离
        /// </summary>
        EVENT_SHENGZI_MOVERDIS,

        #region 腹腔穿刺特写
        /// <summary>
        /// 移动物体
        /// </summary>
        EVENT_FQCC_MOVE,
        /// <summary>
        /// 打开腹穿特写窗口
        /// </summary>
        EVENT_CREATE_FQCCFEATUREWINDOW,
        /// <summary>
        /// 关闭腹穿特写窗口
        /// </summary>
        EVENT_CLOSE_FQCCFEATUREWINDOW,
        /// <summary>
        /// 腹穿特写镜头界面 箭头显示 默认向下 true向下 false 向上
        /// </summary>
        EVENT_FQCC_FEATURE_ARROWS,
        /// <summary>
        /// 腹穿特写重置
        /// </summary>
        EVENT_FQCC_FEATURE_RESET,
        /// <summary>
        /// 用JSON重置利多卡因针管
        /// </summary>
        EVENT_RESET_ZHENGUAN_JSON,
        #endregion
        /// <summary>
        /// 打开任务选择阶段窗口
        /// </summary>
        EVENT_CREATE_TASKSELECTWINDOW,
        /// <summary>
        /// 关闭任务选择阶段窗口
        /// </summary>
        EVENT_CLOSE_TASKSELECTWINDOW,


        /// <summary>
        /// 打开导尿
        /// </summary>
        EVENT_CREATE_DAONIAOWINDOW,
        /// <summary>
        /// 关闭导尿窗口
        /// </summary>
        EVENT_CLOSE_DAONIAOWINDOW,
        /// <summary>
        /// 导尿管插入物体移动事件
        /// </summary>
        EVENT_DAONIAOGUANOBJ_POSZ,

        /// <summary>
        /// 确认缝合打结
        /// </summary>
        EVENT_FENGHE_QUERENDAJIE,
        EVENT_FENGHE_QUERENDAJIE_HIDE_SZ,

        /// <summary>
        /// 止血病人贴图刷新
        /// </summary>
        EVENT_REFRESH_BINGRENTIETU,

        /// <summary>
        /// 选择病例方法，
        /// </summary>
        EVENT_TYPE_XUANZHEBINGLI,

        /// <summary>
        /// 缝合结束
        /// </summary>
        EVENT_FENGHE_OVER,
        /// <summary>
        /// 皮肤缝合状态
        /// </summary>
        EVENT_FENGHE_ANIMATION,
        /// <summary>
        /// 止血缝合状态
        /// </summary>
        EVENT_ZHIXUEFENGHE_TYPE,
        /// <summary>
        /// 缝合得分更新
        /// </summary>
        EVENT_FENGHE_UPDATE_SCORE,

        /// <summary>
        /// 自身旋转
        /// </summary>
        EVENT_ROTATESELF,
        /// <summary>
        /// 自身旋转(纯动画)
        /// </summary>
        EVENT_ROTATESELF_ANIM,

        /// <summary>
        /// 旋转消毒路径加载
        /// </summary>
        EVENT_LOADROTATEPAINT,

        /// <summary>
        /// 旋转消毒路径存储
        /// </summary>
        EVENT_SAVEROTATEPAINT,

        /// <summary>
        /// 放置棉球
        /// </summary>
        EVENT_FANGZHIMIANQIU,

        /// <summary>
        /// json放置 param格式 => n_0 第一个参数是控制第几个棉球，第二个参数是是否控制m_Index更新
        /// </summary>
        EVENT_JSONFANGZHIMIANQIU,

        /// <summary>
        /// json拿起 param格式 => n_0 第一个参数是控制第几个棉球，第二个参数是是否控制m_Index更新
        /// </summary>
        EVENT_JSONNAQUMIANQIU,
        /// <summary>
        /// 清理棉球
        /// </summary>
        EVENT_CLERAMIANQIU,

        /// <summary>
        /// 清理棉球
        /// </summary>
        EVENT_JSONCLERAMIANQIU,
        /// <summary>
        /// 棉球控制
        /// </summary>
        EVENT_MIANQIU_CTRL,
        /// <summary>
        /// 利用Tween控制shader
        /// </summary>
        EVENT_TWEEN_SHADER,
        /// <summary>
        // 关闭跳步
        /// </summary>
        EVENT__LOCK_JUMP,

        /// <summary>
        /// 针头初始化
        /// </summary>
        EVENT_ZHENTOU_INIT,
        /// <summary>
        /// 针头碰撞绳子
        /// </summary>
        EVENT_ZHENTOU_SET_COLLIDER,
        /// <summary>
        /// 针头显示隐藏
        /// </summary>
        EVENT_ZHENTOU_SET_SHOW,

        /// <summary>
        /// 让TaskManager和OperatorManager清空数据
        /// </summary>
        EVENT_CLEARDATA,

        /// <summary>
        /// 需要锁定TaskStageUI
        /// </summary>
        EVENT_CLOSECREAT,
        /// <summary>
        /// 打开UI重新生成
        /// </summary>
        EVENT_OPENCREAT,
        /// <summary>
        /// 当前ChangeTaskUIStageInfo
        /// </summary>
        EVENT_SENDCURCHANGEUI,
        /// <summary>
        /// 刷新taskUIstepMenu
        /// </summary>
        EVENT_TASKUISTEPMENU_REFRESH,
        /// <summary>
        /// 是否切换分支任务
        /// </summary>
        EVENT_CHANGESTEPUI,
        /// <summary>
        /// 打开切换分支弹窗
        /// </summary>
        EVENT_OPEN_CHANGESTEPUI,
        /// <summary>
        /// 禁用步骤切换
        /// </summary>
        EVENT_CLOSE_CHANGESTEP,
        /// <summary>
        /// 更新taskstageUI
        /// </summary>
        EVENT_FRESH_TASKSTAGE,
        /// <summary>
        /// 切换分支确认
        /// </summary>
        EVENT_STEPINFO_WINDOW_CONFIM,
        /// <summary>
        /// 收起TASKUISTAGEINFO
        /// </summary>
        EVENT_TASKUISTAGEINFO_SHOUQI,
        /// <summary>
        /// 全部收起TASKUISTAGEINFO
        /// </summary>
        EVENT_TASKUISTAGEINFO_ALL_SHOUQI,

        /// <summary>
        /// 更新顶部轮播图
        /// </summary>
        EVENT_UPDATE_TOPIMG,
        /// <summary>
        /// 按下考核提示按钮
        /// </summary>
        EVENT_KAOHE_TIPS,

        /// <summary>
        /// 点击所有课程按钮
        /// </summary>
        EVENT_CLICK_ALLCOURSE,

        /// <summary>
        /// 可以获取顶部动态图时机
        /// </summary>
        EVENT_CAN_REQUESTTOPIMGDATA,

        /// <summary>
        /// 系统设置界面
        /// </summary>
        EVENT_CREATE_SYSTEMSETTINGWINDOW,
        /// <summary>
        /// 关闭系统设置界面
        /// </summary>
        EVENT_CLOSE_SYSTEMSETTINGWINDOW,

        EVENT_RET_SCOREDETAILITEMSIZE,

        EVENT_UPDATE_OPERATEDETAILITEMSIZE,

        EVENT_SET_SCOREDETAILITEMSIZE,

        /// <summary>
        /// 动脉消毒手指或戴手套二选一
        /// </summary>
        EVENT_DM_SHOWORHIDESHOUTAO,
        /// <summary>
        /// 物体高亮开关
        /// </summary>
        EVENT_DM_HIGHLIGHT,

        /// <summary>
        /// 吸氧术鼻导管开始插管
        /// </summary>
        EVENT_XYS_BIDAOGUAN_STAR,
        /// <summary>
        /// 吸氧术鼻导管插管重置
        /// </summary>
        EVENT_XYS_BIDAOGUAN_RELEASE,
        /// <summary>
        /// 吸氧术鼻导管结束插管
        /// </summary>
        EVENT_XYS_BIDAOGUAN_END,
        /// <summary>
        /// 吸氧术鼻导管移动
        /// </summary>
        EVENT_XYS_BIDAOGUAN_MOVE,


        EVENT_CLICK_MESSAGECENTER,


        /// <summary>
        /// 
        /// </summary>
        EVENT_CREATE_MESSAGESEARCHWINDOW,
        /// <summary>
        /// 
        /// </summary>
        EVENT_CLOSE_MESSAGESEARCHWINDOW,

        /// <summary>
        /// 
        /// </summary>
        EVENT_CREATE_MESSAGEDETAILWINDOW,
        /// <summary>
        /// 
        /// </summary>
        EVENT_CLOSE_MESSAGEDETAILWINDOW,


        EVENT_UPDATE_MESSAGEDATA,

        EVENT_DELETE_MESSAGEDATA,

        EVENT_STATUS_CHOSEMESSAGE,


        EVENT_CREATE_ADDQUESWINDOW,
        /// <summary>
        /// 
        /// </summary>
        EVENT_CLOSE_ADDQUESWINDOW,

        /// <summary>
        /// 氧气瓶指针旋转
        /// </summary>
        EVENT_ROTATE_XIYANGZHIZHEN,
        /// <summary>
        /// 氧气瓶指针旋转结束
        /// </summary>
        EVENT_OVER_ROTATEZHIZHEN,
        
        /// <summary>
        /// 消毒逻辑，把消毒范围值传给消毒逻辑
        /// </summary>
        EVENT_SEND_FANWEI,
        EVENT_CREATE_YIXUESUYANGWINDOW,
        
        EVENT_CLOSE_YIXUESUYANGWINDOW,

        EVENT_CONFIRM_YIXUESUYANG,
        EVENT_CREATE_SHUHOUZONGJIEWINDOW,
        
        EVENT_CLOSE_SHUHOUZONGJIEWINDOW,
        
        EVENT_CONFIRM_SHUHUOZONGJIE,

        EVENT_RECEIVE_OPERATETIME,
        
        /// <summary>
        /// 所有答案创建完毕再开启自适应排位
        /// </summary>
        EVENT_CREATEANSWER,

        #region 电除颤相关
        EVENT_DCC_INIT,
        /// <summary>
        /// 改变电除颤状态
        /// </summary>
        EVENT_DCC_CHANGE_STATE,
        /// <summary>
        /// 改变电除颤心电图状态
        /// </summary>
        EVENT_DCC_CHANGE_XDT_STATE,
        /// <summary>
        /// 改变电除颤放电状态
        /// </summary>
        EVENT_DCC_FANGDIAN,
        
        EVENT_CREATE_DCCBXWINDOW,
        
        EVENT_CLOSE_DCCBXWINDOW,
        
        EVENT_CREATE_REGISTERWINDOW,
        
        EVENT_CLOSE_REGISTERWINDOW,
        #endregion
        
        
        EVENT_UNBINDDEVICE,

        #region  状态栏控制

        
        EVENT_STATUSBARCOLOR,

        #endregion
        
        EVENT_HELPMASK_SHOW,          //帮助遮罩显示
        EVENT_CHANGE_HELPMASK,          // 帮助遮罩改变
    }

    /// <summary>
    /// 事件ID定义（应用BroadEventDispatch事件机制）
    /// </summary>
    public enum EventID
    {
        //实体已死亡（实体播放完动画之后才销毁）
        EVENT_ENTITY_DEAD = 1,
        //实体复活倒计时
        EVENT_ENTITY_REVIVE,
        //实体销毁完成
        EVENT_ENTITY_DESTROYED,
        //实体创建完成
        EVENT_ENTITY_CREATED,
        //其他创建完成
        EVENT_OTHER_HERO_CREATED,
        //主角创建完成
        EVENT_MAIN_HERO_CREATED,
        //主角销毁
        EVENT_MAIN_HERO_DESTROYED,
        //实体生物属性变化
        EVENT_ENTITY_CREATURE_PROPETY_CHANGED,
        //实体进入视野
        EVENT_ENTITY_ENTER_VIEW,
        //实体离开视野
        EVENT_ENTITY_LEAVE_VIEW,
        //实体切换皮肤
        EVENT_ENTITY_SKIN_CHANGED,

        //切换到战场状态（场景刚开始加载）
        EVENT_BATTLE_ENTERED,
        //离开战场状态
        EVENT_BATTLE_LEAVED,
        //战场准备完毕（场景、角色等皆已创建OK，可以开始游戏）
        EVENT_BATTLE_READY,

        //画质改变
        EVENT_QUALITY_SETTING_CHANGED,

        //主相机渲染模式切换（是否渲染到RT）
        EVENT_MAIN_CAMERA_RENDER_MODE_CHANGED,

    }

    ///////////////////////////// 摇杆移动命令选项 //////////////////////////
    public enum EMJoystickCmd
    {
        Joystick_Cmd_NONE = 0,                  // 无
        Joystick_Cmd_START_MOVE,                // 开始移动
        Joystick_Cmd_MOVE,                      // 移动中
        Joystick_Cmd_STOP_MOVE,                 // 停止移动	
    };

    ///////////////////////// 所有消息的消息头 ////////////////////////
    /// <summary>
    /// 消息头结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct SGameMsgHead
    {
        public int SerialNumber;        //序列number
        public int SrcEndPoint;        // 源端点码
        public int DestEndPoint;       // 目的端点码
        public int wKeyModule;       // 目标模块消息码
        public int wKeyAction;       // 行为消息码     
    }

    /***************************************************************/
    ///////////////////// 目标模块层的全局定义 //////////////////////
    /***************************************************************/

    // 场景加载
    // GVIEWCMD_MATCH_LOAD_SCENE
    public class gamelogic_LoadSceneInfo : cmd_Base
    {
        public int nCmdID; // 执行加载场景的命令

        public string strSceneName; // 场景名字
        public UInt32 nMapID; // 地图ID
        public bool reloadIfAlreadyLoad = false; // 如果场景已经加载，是否强制重新加载
        public Vector3 mPosition; // 出生坐标
        public string nLayoutID; // 布局ID
        public string nPanGuID; // 盘古ID

        public override void ReSet()
        {
            nCmdID = 0;
            strSceneName = null;
            nMapID = 0;
            reloadIfAlreadyLoad = false;
            mPosition = Vector3.zero;
        }
    };



    public class gamelogic_LoadPrefabInfo : cmd_Base
    {
        public uint nMapID;
        public string strSceneName;
        public string sDMoudle;
        public string sModulePos;
        public string sIsSceneLoadFinish;
    }

    // 通用效果
    public class LightingEffectContext : cmd_Base
    {
        public uint id; //效果Id;
        public int feedbackID = 0; //光效的反馈ID (用于碰撞反馈、光效删除等)
        public uint src; //技能效果发起者
        public uint target; //技能效果承受者

        public Vector3 ptCenter; //技能效果中心点(施放位置)

        public Vector3 ptTarget;		            //目标点       
        public CoordinateSpace coordSpace; //坐标是否是UI坐标系
        public Transform parent; //指定光效挂载点（目前，仅对UI光效有效）

        public int otherKind = 0;//其它类型，比如以场景中的金币和钻石框为移动者  1:金币 2：钻石 3:背包
        public override void ReSet()
        {
            id = 0;
            feedbackID = 0;
            src = 0;
            target = 0;
            otherKind = 0;
            ptCenter = Vector3.zero;
            ptTarget = Vector3.zero;
            coordSpace = CoordinateSpace.WorldSpace;
            parent = null;
        }
    };

    // 停用效果
    public class LightingEffectStopContext : cmd_Base
    {
        public uint id; //效果Id;
        public int feedbackID = 0; //光效的反馈ID (用于碰撞反馈、光效删除等)

        public override void ReSet()
        {
            id = 0;
            feedbackID = 0;
        }
    };



    // 资源预加载加载（场景切换时）
    public class gamelogic_PreloadResourceInfo : cmd_Base
    {
        public string strSceneName;                 // 场景名字
        public UInt32 nMapID;                       // 地图ID       
        public Vector3 mPosition;                   // 出生坐标

        public override void ReSet()
        {
            strSceneName = null;
            nMapID = 0;
            mPosition = Vector3.zero;
        }
    };
    public class SetCameraQuatandLocat
    {
        /// <summary>
        /// 摄像机位置
        /// </summary>
        public Vector3 m_Cam_Position = new Vector3(10000, 10000, 10000);
        /// <summary>
        /// 摄像机面向
        /// </summary>
        public Vector3 m_Cam_Rotation = new Vector3(10000, 10000, 10000);
    }

    public class cmd_HighLight : cmd_Base
    {
        public Transform trans;
        public bool isCludeChild;
        public bool enable;
        public int taskId;
    }

    public class cmd_OpModelClick : cmd_Base
    {
        public GameObject target;
        public bool isHighLight;
        public int taskId;
        public cmd_OpModelClick(GameObject _target, bool _isHighLight, int _taskId)
        {
            target = _target;
            isHighLight = _isHighLight;
            taskId = _taskId;
        }
    }

    public class cmd_Guide_Grab : cmd_Base
    {
        public bool IsLeftHand;
        public GameObject sMode;
    }

    public class cmd_Guide_Transfer : cmd_Base
    {
        public GameObject sMode;
    }

    public class cmd_Guide_Handle : cmd_Base
    {
        public GameObject sMode;
        public int flag;
        public bool IsLeftHand;
    }


    public class cmd_Hero_OnTrigger : cmd_Base
    {
        public TriggerState mState;
        public GameObject heroObj;
        public GameObject otherObj;

        public enum TriggerState
        {
            Enter,
            Exit,
            Stay
        }
    }

    public class cmd_Hero_JoystickChange : cmd_Base
    {
        /// <summary>
        /// 是否顺时针旋转
        /// </summary>
        public bool isClockwise;
        /// <summary>
        /// 旋转角度
        /// </summary>
        public float angle;
    }

    public class cmd_LongTimeRay : cmd_Base
    {
        /// <summary>
        /// 长时间指向并点击的物体
        /// </summary>
        public GameObject go;
    }

    //场景变换参数
    public class gamelogic_SceneChange : cmd_Base
    {
        //1新建场景 2：加载场景 3：退出场景 4:重置场景
        public int nState;
        //盘古文件夹
        public string strPanGUInfo;
        //类型 0单人；1多人；
        public int nSceneType;
        //场景id
        public uint nSceneID;
        //布局id
        public string nLayoutID;
    }

    //场景变换参数
    public class gamelogic_SceneChange_CreateFinish : cmd_Base
    {
        public Scene_CreateScene_CS_Reply reply;
    }



    public class assembly_move_camera : cmd_Base
    {
        public bool isAdd;
        public Transform modelTf;
        /// <summary>
        /// 当前旋转的模型是否和之前选中的模型是同一个
        /// </summary>
        public bool isBeforeSame;
    }

    #region Window显示逻辑
    public class gamelogic_ShowIp : cmd_Base
    {
        public string[] ipArray;
    }

    public class cmd_Connect : cmd_Base
    {
        public string ip;
        public int port;
    }
    #endregion


    // 通知显示层显示SystemTips
    // GVIEWCMD_ADD_SYSTEMTIPS
    public class gamelogic_SAddSystemTips : cmd_AsyncBase
    {
        public UInt32 pos;
        public string text;
        public Color color = Color.white;
        public EMFInfoIcon icon = EMFInfoIcon.None;
        public override void ReSet()
        {
            pos = 0;
            text = null;
            color = Color.white;
        }
    }

    public class gamelogic_Waiting : cmd_AsyncBase
    {
        public string szTips;
    }

    public class gamelogic_OpenBoxProgress : cmd_AsyncBase
    {
        public string sInfoName;               //显示名
        public int iTime;                       //时间  单位：毫秒
        public bool bPositive;                  //是否正向
    }

    public class mainscene : cmd_AsyncBase
    {
    }

    public class gameloading_Create : cmd_AsyncBase
    {
    }

    public class portraitGetPhonePhoto_Create : cmd_AsyncBase
    {

    }

    public class portraitCreateRole : cmd_AsyncBase
    {
    }

    public class portraitLogin : cmd_AsyncBase
    {
    }

    public class mainCreate : cmd_AsyncBase
    {

    }

    public class baseCog : cmd_AsyncBase
    {

    }

    public class gamelogic_MessageBox : cmd_AsyncBase
    {
        public string message;
        public int id;
        public int time;
        public bool bTop;
        public bool isHideClose;
        public object obj;
        public bool isForceShowCloseBtn;
    }

    // 登录界面创建
    public class gamelogic_Create : cmd_AsyncBase
    {

    }

    public class Talk_Info : cmd_AsyncBase
    {
        public string TalkContent;
        public string TalkId;
    }


    /// <summary>
    /// 创建视频window
    /// </summary>
    public class cmd_CreateVideoWindow : cmd_AsyncBase
    {
        public string path;
        public FileLocation fileLocation;
        public UnityAction finishAction;
    }
    public class cmd_CreateTaskWindow : cmd_AsyncBase
    {

    }

    /// <summary>
    /// 创建能调整UI显示和是否全屏的视频window
    /// </summary>
    public class cmd_CreateCodeVideoWindow : cmd_CreateVideoWindow
    {
        public bool isUIShow;
        public bool isFullWindow;
    }

    public class cmd_CreateMiniGameVideoWindow : cmd_AsyncBase
    {
        public string path;
        public FileLocation fileLocation;
        public int videoId;
    }

    /// <summary>
    /// 拍照信息
    /// </summary>
    public class cmd_CreateTakePhotoWindow : cmd_AsyncBase
    {
        public string url;           // 资源路径
        public bool canSave;         // 是否允许保存
        public bool isFullWindow;    // 是否是全屏窗口
        public int showTime;         // 展示时间
        public string targetName;    // 目标名称，若指定了则需要在指定名称中才可执行拍照
    }

    /// <summary>
    /// 创建智能提示window
    /// </summary>
    public class cmd_CreateSmartTipWindow : cmd_AsyncBase
    {
        public string tip;
        public UnityAction tipAction;
    }

    /// <summary>
    /// 创建打开摄像机并保存相册window
    /// </summary>
    public class cmd_CreateCameraAndSaveTextureWindow : cmd_AsyncBase
    {
        public Action AuthorizationBack;
        /// <summary>
        /// 默认false为打开该参数
        /// </summary>
        public bool IsRaycaseTarget;
        /// <summary>
        /// 打开照相机之前是否竖屏
        /// </summary>
        public bool IsVertical;
    }

    public class CountTimeMessageInfo : cmd_AsyncBase
    {
        /// <summary>
        /// 文本
        /// </summary>
        public string text;
        /// <summary>
        /// 倒计时时间
        /// </summary>
        public float time;
        /// <summary>
        /// 是否展示关闭按钮
        /// </summary>
        public bool showBtn;
        /// <summary>
        /// 是否是无限循环的窗口
        /// </summary>
        public bool isLoop = false;
        /// <summary>
        /// 回调
        /// </summary>
        public Action callBack;
    }

    public class cmd_MessageCenterWindow : cmd_AsyncBase
    {
        public List<MessageDataBody> m_messageQues = null;

        public List<MessageDataBody> m_messageAnswer = null;
    }

    public class cmd_MessageDetailWindow : cmd_AsyncBase
    {
        public MessageDataBody m_messageQues;
        public MessageDataBody m_messageAnswer;
    }

    public class cmd_AddQuesWindow : cmd_AsyncBase
    {
        public MessageDataBody m_messageQues;
        //public MessageDataBody m_messageAnswer;
    }
    public class AssemblyFinishVideoInfo : cmd_AsyncBase
    {
        public PartFinishConfig.Types.Item item;
    }

    public class CourseEvnetFinsiInfo : cmd_AsyncBase
    {
        public int eventId;
    }

    public class EducationInfo : cmd_AsyncBase
    {
    }
    /// <summary>
    /// COURCE_CREAT_SERISEWINDOW
    /// </summary>
    public class SeriesInfo : cmd_AsyncBase
    {
    }

    public class CourseInfo : cmd_AsyncBase
    {
    }

    public class RobotInfo : cmd_AsyncBase
    {
    }

    public class ChooseRobotInfo : cmd_AsyncBase
    {
    }
    public class buxianCog : cmd_AsyncBase
    {

    }
    public class buxianOpInfo : cmd_AsyncBase
    {

    }
    
    public class cmd_YiXueSuYangWindow : cmd_AsyncBase
    {

    }
    
    public class cmd_ShuHouZongJieWindow : cmd_AsyncBase
    {

    }
    public class cmd_DCCBoXingTuWindow : cmd_AsyncBase
    {

    }
    
    public class cmd_RegisterWindow : cmd_AsyncBase
    {

    }
    
    /// <summary>
    /// 提示窗口信息
    /// </summary>
    public class TipsFingerInfo : cmd_AsyncBase
    {
        public Vector3 setPos; // 设置位置，世界坐标
        public float showTime; // 持续时间，单位秒
        public string text;    // 显示文本，就是手指下面再显示一段话
        public int direction;//方向 0从下往上 1从左往右 2从右往左
        public bool isUIPos;//是否是UI坐标
        public int voiceId;//音频Id
        public int tipsImageId;//提示图片ID
    }

    public class UIGuideInfo : cmd_AsyncBase
    {
        /// <summary>
        ///  提示点的位置
        /// </summary>
        public UIGuidePlaceType PlaceType;
        /// <summary>
        /// 图片加载地址
        /// </summary>
        public string ImgPath;
        /// <summary>
        /// 持续时间
        /// </summary>
        public int DelayTime;
    }


    public class AnswerAwardInfo : cmd_AsyncBase
    {
        /// <summary>
        /// 星级
        /// </summary>
        public int starNum;
        /// <summary>
        /// 连击数
        /// </summary>
        public int comboNum;
    }


    public class cmd_MCSetSkillIcon : cmd_AsyncBase
    {
        public uint SkillID;
        public string IconName;
    }

    /// <summary>
    /// 创建任务
    /// EVENT_CREATE_TASK_WINDOW
    /// </summary>
    public class cmd_TaskGameData : cmd_AsyncBase
    {
        /// <summary>
        /// 坐标，(0,0)表示不改变坐标位置
        /// </summary>
        public Vector2 vectPos;
        /// <summary>
        /// 是否是左模式
        /// </summary>
        public bool m_IsLeftModel;
        /// <summary>
        /// 是否显示任务内容
        /// </summary>
        public bool isShowTaskContent;
        /// <summary>
        /// 任务ID,如果任务Id>0，那么便会直接进行任务触发和注册任务UI，否则不会直接触发任务，需要自己去触发任务。
        /// </summary>
        public int taskId;
        /// <summary>
        /// 隐藏是否销毁
        /// </summary>
        public bool isHideDestroy;
        /// <summary>
        /// 是否重置任务
        /// </summary>
        public bool isRestTask;
        /// <summary>
        /// 是否显示任务结算后提示按钮
        /// </summary>
        public bool isShowTaskDetailedBut;
        /// <summary>
        /// 是否启用结算
        /// </summary>
        public bool isEnableDetailed;
        /// <summary>
        /// 任务内容背景颜色
        /// </summary>
        public Color? TaskConentBackColor;
        /// <summary>
        /// 任务内容字体颜色
        /// </summary>
        public Color? TaskContentTextColor;
        public cmd_TaskGameData()
        {
            wModel = WindowModel.GeneralTaskWindow;
            aCommandState = AsyncCommandState.CreateCommmand;
            vectPos = Vector2.zero;
            m_IsLeftModel = false;
            isShowTaskContent = true;
            isHideDestroy = false;
            isShowTaskDetailedBut = false;
            isEnableDetailed = true;
            TaskConentBackColor = null;
            TaskContentTextColor = null;
        }
    }
    /// <summary>
    /// 视频播放数据
    /// </summary>
    public class AvProVideoData : cmd_Base
    {
        /// <summary>
        /// 视频来源
        /// </summary>
        public FileLocation fileLocation = FileLocation.RelativeToStreamingAssetsFolder;
        public string videoPath;
        public bool isPlay;
        /// <summary>
        /// 视频id
        /// </summary>
        public int videoId;
    }
    public class AvProVideoDef
    {
        /// <summary>
        /// 播放视频数据
        /// </summary>
        public AvProVideoData avProVideoData;
        /// <summary>
        /// 关闭时间回调
        /// </summary>
        public Action m_CloseVideo;
        /// <summary>
        /// 播放错误回调
        /// </summary>
        public Action m_ErrorVideo;
        /// <summary>
        /// 播放完成回调
        /// </summary>
        public Action m_OverVideo;
        /// <summary>
        /// 播放完成回调(基类)
        /// </summary>
        public Action m_OverVideo1;
        /// <summary>
        /// 开始播放回调
        /// </summary>
        public Action<float> m_StartedVideo;
        /// <summary>
        /// 播放进度回调
        /// </summary>
        public UnityAction<float> m_SliderListener;
        /// <summary>
        /// 点击拖动进度条回调
        /// </summary>
        public UnityAction<float> m_UpdateGetCurtimeListener;
        /// <summary>
        /// 事件索引 用来区分该视频是哪个事件的 目前保存视频进度用到了
        /// </summary>
        public int eventIndex;
        /// <summary>
        /// 视频进度
        /// </summary>
        public float videoProcess;
        /// <summary>
        /// 视频时长
        /// </summary>
        public float videoDuration;
    }

    /// <summary>
    /// 结束任务
    /// INFORM_MODEL_TASK_OVER
    /// </summary>
    public class TaskOverDef
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskInstanceId;
        public bool IsLastTask;
    }
    /// <summary>
    /// 开始任务
    /// INFORM_MODEL_TASK_START
    /// </summary>
    public class TaskStartDef
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId;
        /// <summary>
        /// 是否重置资源
        /// </summary>
        public bool IsRestResource;
        /// <summary>
        /// 是否显示Pin
        /// </summary>
        public bool IsShowPin;
        /// <summary>
        /// 积木块布局ID
        /// </summary>
        public string blockLayout;
        /// <summary>
        /// 运行时是否播放视频
        /// </summary>
        public bool IsRunPlayVideo;
        /// <summary>
        /// 引导使用次数
        /// </summary>
        public int GuidanceCount;
    }
    /// <summary>
    /// 重新加载布局
    /// INFORM_ANEW_LOAD_BLOCK_INFO
    /// </summary>
    public class AnewLoadBlockLayout
    {
        /// <summary>
        /// 布局ID
        /// </summary>
        public string LayoutId;
    }
    /// <summary>
    /// 闭编程UI
    /// INFORM_BLOCK_PROGRAMME_CLOSE
    /// </summary>
    public class BlockProgrameClose
    {
        /// <summary>
        /// 回调
        /// </summary>
        public Action action;
    }
    /// <summary>
    /// 其他区域通知保存积木块
    /// INFORM_OTHER_SAVE_XML
    /// </summary>
    public class OtherSaveProgrameXml
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id;
        /// <summary>
        /// Xml
        /// </summary>
        public string workspaceXml;
    }
    /// <summary>
    /// 运行积木块返回
    /// INFORM_BLOCK_RUN_BACK
    /// </summary>
    public class BlockRunBack
    {
        /// <summary>
        /// 回调
        /// </summary>
        public Action action;
    }
    /// <summary>
    /// 运行积木块关闭
    /// INFORM_BLOCK_RUN_CLOSE
    /// </summary>
    public class BlockRunClose
    {
        /// <summary>
        /// 回调
        /// </summary>
        public Action action;
    }
    /// <summary>
    /// 任务类型
    /// </summary>
    public enum TaskWindowType
    {
        /// <summary>
        /// 任务窗口
        /// </summary>
        TaskWindow,
        /// <summary>
        /// 任务内容
        /// </summary>
        TaskContent,
        /// <summary>
        /// 任务结算
        /// </summary>
        TaskDetailed,
    }
    /// <summary>
    /// 任务窗口配置
    /// INFORM_TASK_WINDOW_CONFIG
    /// </summary>
    public class TaskWindowConfig
    {
        /// <summary>
        /// 任务类型
        /// </summary>
        public TaskWindowType m_TaskWindowType;
        /// <summary>
        /// 显示状态
        /// </summary>
        public bool m_ShowState;
        /// <summary>
        /// 任务UI坐标
        /// </summary>
        public Vector2? m_VectPos;
        /// <summary>
        /// UI大小
        /// </summary>
        public Vector2? m_SideDate;
    }
    /// <summary>
    /// 设置运行UI数据类
    /// SET_BLOCK_RUN_UI_STATE
    /// </summary>
    public class BlockRunUIState
    {
        /// <summary>
        /// 显示状态
        /// </summary>
        public bool m_ShowState;
    }
    /// <summary>
    /// 替换积木块
    /// INFORM_REPLACE_BLCOK
    /// </summary>
    public class ReplaceBlock
    {
        /// <summary>
        /// 消耗次数
        /// </summary>
        public int ConsumeNum;
        /// <summary>
        /// 替换积木块ID
        /// </summary>
        public int ReplaceTaskId;
        /// <summary>
        /// 替换完成引导图ID
        /// </summary>
        public int ReplaceOverImageId;
        /// <summary>
        /// 替换完成播放语音ID
        /// </summary>
        public int ReplaceOverVoiceId;
    }

    #region 竖屏菜单相关事件数据类
    /// <summary>
    /// 创建菜单UI窗口
    /// EVENT_CREATE_MEUN_WINDOW
    /// </summary>
    public class cmd_CreateMeunGameData : cmd_AsyncBase
    {
        public PortraitScreenStateDef PSStateDef;
    }
    /// <summary>
    /// 竖屏状态设置数据类
    /// INFORM_PORTRAIT_SCREEN_STATE
    /// </summary>
    public class PortraitScreenStateDef
    {
        /// <summary>
        /// 是否显示竖屏菜单
        /// </summary>
        public bool IsShow;
        /// <summary>
        /// 竖屏Y轴距离
        /// </summary>
        public float PortrationPosY;
        /// <summary>
        /// 竖屏模式
        /// </summary>
        public PortrationScreenType ProtrantionType = PortrationScreenType.BottomModel;

    }
    /// <summary>
    /// 竖屏模式
    /// </summary>
    public enum PortrationScreenType
    {
        /// <summary>
        /// 顶部
        /// </summary>
        TopModel,
        /// <summary>
        /// 中间
        /// </summary>
        CenterModel,
        /// <summary>
        /// 底部
        /// </summary>
        BottomModel,
    }

    #endregion
    #region 内置浏览器模块
    public class cmd_CreateWebViewData : cmd_AsyncBase
    {
        /// <summary>
        /// 加载Url
        /// </summary>
        public string m_LoadUrl;
        public UniWebViewConfig uniWebViewConfig;
        /// <summary>
        /// 返回类型
        /// </summary>
        public WebViewBackType webViewBackType;
    }
    /// <summary>
    /// 创建内置浏览器
    /// EVENT_CREATE_WEBVIEW_WINDOW
    /// </summary>
    public class cmd_UniWebViewWindow : cmd_AsyncBase
    {

    }
    /// <summary>
    /// 内置浏览器配置
    /// INFORM_WEBVIEW_CONFIG
    /// </summary>
    public class UniWebViewConfig
    {
        /// <summary>
        /// 布局配置
        /// </summary>
        public LayoutType m_LayoutType;
        public Rect m_WebViewRect;
        /// <summary>
        /// 是否显示
        /// </summary>
        public bool m_IsShow;
        public string Title;
        /// <summary>
        /// 是否显示dialog
        /// </summary>
        public bool m_IsSendShowDiaLog;
    }
    #endregion

    /// <summary>
    /// 创建分享窗口
    /// EVENT_CRETAE_SHARE_WINDOW
    /// </summary>
    public class cmd_CreateShareWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 分享类容
        /// </summary>
        public ShareContentDef m_shareContent;
    }
    public class cmd_LoginWindow : cmd_AsyncBase
    {

    }

    public class cmd_MainWindow : cmd_AsyncBase
    {

    }
    public class cmd_LoadingWindow : cmd_AsyncBase
    {

    }

    public class cmd_MainSearchWindow : cmd_AsyncBase
    {

    }

    public class cmd_MainBottomWindow : cmd_AsyncBase
    {

    }
    public class cmd_XiaoDuWindow : cmd_AsyncBase
    {

    }

    public class cmd_NewXiaoDuWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 是否改变消毒完成按钮的index
        /// </summary>
        public bool isChangeBtnIndex;
    }
    public class cmd_LongPressWindow : cmd_AsyncBase
    {

    }

    public class cmd_SliderWindow : cmd_AsyncBase
    {
        public float originValue;
        public float targetValue;
        public string text;
        public GameObject target;
        public Vector3 vectorOffset;
        public Vector3 uiPos;
    }

    public class cmd_OperateBtnWindow : cmd_AsyncBase
    {

    }

    public class cmd_TimeGoWindow : cmd_AsyncBase
    {

    }

    public class cmd_MainPageWindow : cmd_AsyncBase
    {

    }
    public class cmd_CourseWindow : cmd_AsyncBase
    {

    }
    public class cmd_SubscribeWindow : cmd_AsyncBase
    {

    }
    public class cmd_PersonalCenterWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 是否不刷新chart图表
        /// </summary>
        public bool isNotRefreshChart;
    }
    public class cmd_AccountAndSecurityWindow : cmd_AsyncBase
    {

    }

    public class cmd_OpinionFeedbackWindow : cmd_AsyncBase
    {

    }

    public class cmd_SubmitScoreWindow : cmd_AsyncBase
    {

    }

    public class cmd_AboutUsWindow : cmd_AsyncBase
    {

    }

    public class cmd_OpenPersonalDataWindow : cmd_AsyncBase
    { }

    public class cmd_OpenModifyDataWindow : cmd_AsyncBase { }

    public class cmd_CommonWebUIWindow : cmd_AsyncBase
    {
        public bool isShowTopTitle;
        public PersonalDataMenuDef menuDef;
    }

    public class cmd_MockRankWindow : cmd_AsyncBase
    {
    }

    public class cmd_NoticeWindow : cmd_AsyncBase
    {
    }


    public class cmd_HelpCenterWindow : cmd_AsyncBase
    {
    }

    public class cmd_MyClassWindow : cmd_AsyncBase
    {

    }

    public class cmd_MyCourseWindow : cmd_AsyncBase
    {

    }

    public class cmd_PrivacyPolicyWindow : cmd_AsyncBase
    {

    }

    public class cmd_UserProtocolWindow : cmd_AsyncBase
    {
    }

    public class cmd_PersonalDataWindow : cmd_AsyncBase
    {

    }

    public class cmd_ModifyDataWindow : cmd_AsyncBase
    {

    }

    public class cmd_SingleCourseWindow : cmd_AsyncBase
    {
    }
    public class cmd_CourseSearchWindow : cmd_AsyncBase
    {
        public string searchStr;
    }
    public class cmd_DianWeiWindow : cmd_AsyncBase
    {

    }

    public class cmd_TransitionWindow : cmd_AsyncBase
    {

    }

    public class cmd_SimulatHelpInfoWindow : cmd_AsyncBase
    {

    }

    public class cmd_OperaTransWindow : cmd_AsyncBase
    {

    }

    public class cmd_PuJinSilder : cmd_AsyncBase
    {
        public int infoID;
    }

    public class cmd_XiYangSlider : cmd_AsyncBase
    {
        public int infoID;
    }

    public class cmd_DCCEnergyWindow: cmd_AsyncBase
    {
        /// <summary>
        /// 当前任务ID
        /// </summary>
        public int OverTaskId;
        /// <summary>
        /// 能量数组  1;2;3;4
        /// </summary>
        public string energyStr;
        /// <summary>
        /// 正确能量值
        /// </summary>
        public int correctEnergy;
    }

    public class cmd_TimeCountDownWindow : cmd_AsyncBase
    {

    }

    public class cmd_Window : cmd_AsyncBase
    {

    }

    public class cmd_XiShua : cmd_AsyncBase
    {

    }

    public class cmd_QieKai : cmd_AsyncBase
    {
        public int TaskId;
    }
    public class cmd_FengHe : cmd_AsyncBase
    {

    }
    public class cmd_TaskSelect : cmd_AsyncBase
    {

    }

    public class cmd_SystemSettingWindow : cmd_AsyncBase
    {

    }

    public class cmd_MessageSearchWindow : cmd_AsyncBase
    {
        public string searchStr;

        public List<MessageDataBody> m_messageQues = null;

        public List<MessageDataBody> m_messageAnswer = null;
    }
    public class AssemblyTargetTask
    {
        public int targetId;
        public int time;
    }

    public class HotUpdateCommon
    {
        public int courseId;
        public bool isCommonUpdate;
    }
    /// <summary>
    /// 绑定任务发布数据
    /// INFORM_LOAD_ISSUE
    /// </summary>
    public class TaskIssueData
    {
        /// <summary>
        /// 任务Id
        /// </summary>
        public int VoiceId;
        /// <summary>
        /// 标题
        /// </summary>
        public string IssueTitle;
        /// <summary>
        /// 任务目标
        /// </summary>
        public string taskTarget;
        /// <summary>
        /// 提示内容
        /// </summary>
        public string tipsContent;
        /// <summary>
        /// 按钮文字
        /// </summary>
        public string ConfirmText;
        /// <summary>
        /// 是否发送消息，0表示不发送，1表示发送
        /// </summary>
        public int IsSendInfo;
        /// <summary>
        /// 启动发布模块、1表示试运行、2表示编程教学、3表示擂台赛、4表示任务
        /// </summary>
        public int StartIssueModule;
        public void Rest()
        {
            VoiceId = 0;
            IssueTitle = "";
            taskTarget = "";
            tipsContent = "";
            ConfirmText = "";
            IsSendInfo = 0;
        }

    }
    /// <summary>
    /// 创建任务发布数据
    /// TASK_CREATE_ISSUE
    /// </summary>
    public class CreateTaskIssueData
    {
        /// <summary>
        /// 窗口模式
        /// </summary>
        public EMEventType eMEventType;
        /// <summary>
        /// 回调
        /// </summary>
        public Action<Action<bool>> createBack;
    }
    /// <summary>
    /// 其他地方通知停止运行
    /// INFORM_RUN_BLOCK_STOP
    /// </summary>
    public class OtherInformStopRun
    {
        /// <summary>
        /// 停止运行以后的回调
        /// </summary>
        public Action m_StopRunAction;
    }

    /// <summary>
    /// 热更新信息
    /// </summary>
    public class HotUpdateInfo
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string m_tip;
        /// <summary>
        /// 下载安装信息
        /// </summary>
        public string m_info;
        /// <summary>
        /// 进度
        /// </summary>
        public int m_process;
    }
    /// <summary>
    /// 执行完成以后的回到数据
    /// INFORM_WEB_EXECUTE_OVER
    /// </summary>
    public class ExecuteOverDef
    {
        /// <summary>
        /// 返回数据
        /// </summary>
        public string BackData;
        /// <summary>
        /// 返回的路径
        /// </summary>
        public string BackPath;
    }


    public class TriggerTypeData
    {
        public EMTriggerType eMTriggerType;
        /// <summary>
        /// 返回的路径
        /// </summary>
        public GameObject obj;
        public Collider2D collider2D;
        public Collision2D collision2D;
        public Collider collider;
        public Collision collision;
    }
    /// <summary>
    /// INFORM_MEUN_NAVIGATION_MODULE
    /// </summary>
    public class cmd_InformChangeModel
    {
        /// <summary>
        /// 需要切换的菜单，1表示首页，2表示计划，3表示我的
        /// </summary>
        public int meunNavigationId;
    }

    /// <summary>
    /// 机房上架步骤数据
    /// </summary>
    public class ComputerPutOnStepData
    {
        public int sendStepIndex;
        public string tipsStr;

        public ComputerPutOnStepData(int _sendStepIndex, string _tipsStr)
        {
            sendStepIndex = _sendStepIndex;
            tipsStr = _tipsStr;
        }
    }

    /// <summary>
    /// 力反馈设备硬件返回数据
    /// </summary>
    public class HDeviceDataTest
    {
        public HDeviceData hDeviceData;
        public int ID;
    }

    public class BlinkStart
    {
        public uint mapID;
        public Vector3 pos;
        public bool reloadIfAlreadyLoad;
    }

    public class Removee_HapticTool
    {
        public bool isLeft;
        public int toolId;
    }

    public class Haptic_Ray
    {
        public bool IsRayEnter;
        public GameObject hitObj;
        public RaycastHit rayHit;
    }

    public class HapticEffect_HandData
    {
        public int EFFECT_TYPE;
        public bool isLeft;
        public bool isKai;
        public float Gain;
        public float Magnitude;
    }

    public class UpdateOperateLogDate
    {
        /// <summary>
        /// 条件描述1
        /// </summary>
        public string ConditionDescription;
        /// <summary>
        /// 操作物体名称
        /// </summary>
        public string OneOperateName;
        /// <summary>
        /// 被操作物体名称
        /// </summary>
        public string TwoOperatedName;
        /// <summary>
        /// 操作数据类型
        /// </summary>
        public string LogType; 
        /// <summary>
        /// 操作数据
        /// </summary>
        public string OperateDate;

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskID;
    }
    public class UpdateScoreDate
    {
        /// <summary>
        /// 是否跳步骤
        /// </summary>
        public bool IsJumpStep;
        /// <summary>
        /// 别名
        /// </summary>
        public string AliasInt;
        /// <summary>
        /// 条件数据 
        /// </summary>
        public string ConiditionDate;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSucced;
    }
    public class cmd_ToggleCloseWindow : cmd_AsyncBase
    {
        public int BeginPosID;
        public int EndPosID;
    }

    public class HapticDevice_Transform
    {
        public EMHandType handType;
        public Vector3 pos;
        public Vector3 rotation;
        public Vector3 scale;

    }

    public class cmd_CaseSelection : cmd_AsyncBase
    {
    }

    public class cmd_RecyQualityWindow : cmd_AsyncBase
    {
        public string ItemName;
        public string NowTime;
        public int BaoZhiQi;
        public int IconId;
        public int Overdue;
        public bool isZhiShiQiChang = false;
        public bool isDirtyModel;
        public int DirtyIconId;
    }

    public class cmd_QualityStyleWindow : cmd_AsyncBase
    {
        public string ItemName;
        public int IconId;
        public int Overdue;
        public string[] StyleArray;
        public int TypeTargetIdx;
    }

    public class UseExpiredOrNot
    {
        /// <summary>
        /// 时间是否成功
        /// </summary>
        public bool IsExpiredOrNot;
        /// <summary>
        /// 是否是脏模型
        /// </summary>
        public bool IsDirtyModel;
        public string ModelName;
    }
    /// <summary>
    /// 病人播放动画
    /// </summary>
    public class BingRenPlayAni
    {
        /// <summary>
        /// 动作名字
        /// </summary>
        public string AniName;
        /// <summary>
        /// 动画时间
        /// </summary>
        public string AniTime;
        /// <summary>
        /// 动画完成回调
        /// </summary>
        public UnityAction unityAction;
        /// <summary>
        /// 需要绑到骨骼上节点的物体(如纱布)
        /// </summary>
        public Transform m_Node;
        /// <summary>
        /// 节点类型
        /// </summary>
        public BingRenAniNode EMbingRenAniNode;
        /// <summary>
        /// 设置完父物体之后m_Node的局部坐标
        /// </summary>
        public Vector3 pos;
        /// <summary>
        /// 设置完父物体之后m_Node的局部角度
        /// </summary>
        public Vector3 rorate;
    }

    public class cmd_CreateChangeTiWeiWindow : cmd_AsyncBase
    {
        public UnityAction<bool> unityAction;
    }
    public class cmd_CreateDiaLogueWin : cmd_AsyncBase
    {
        public int dialogeStatue;
        public int StepID;
        public int PosID;
    }

    public class cmd_RecycleWindow : cmd_AsyncBase
    {
        public Transform racyTrans;
        public int recycleId;
        public string recycleName;
    }

    public class cmd_ApparatusSelectWindow : cmd_AsyncBase
    {
        public string AliasName;   // 任务别名（是否有分）
    }

    public class DialogueMes
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public int StepID;
    }

    public class OperateLogInfo
    {
        /// <summary>
        /// 更新或者移除的信息
        /// </summary>
        public OperationLogInfo Info;
    }

    /// <summary>
    /// 丢垃圾信息
    /// </summary>
    public class ThrowGarbageInfo
    {
        public ThrowGarbageInfo throwGarbageInfo;
        public TrashCanType trashType;
        public string ModelAsset;
    }

    public class HandPoseInfo : cmd_Base
    {
        //对应的id
        public int poseID = -1;
        /// <summary>
        /// 需要匹配的模型名字
        /// </summary>
        public string matchingModelName;
    }

    public class ChouYeChange
    {
        public float chouYeValue;
        public float yetiValue;
        public int time;
        public Action callBack;
    }

    public class MoveZhenGuan
    {
        public Vector3 startPos;
        public Vector3 endPos;
        public int time;
        public Action callBack;
    }

    public class OverOperateHandle
    {
        public int taskId;
        public bool isSuccess = true;
    }

    public class cmd_MainTopWindow : cmd_AsyncBase { }


    public class cmd_CourseHomepageWindow : cmd_AsyncBase
    {
        public int index;
    }
    public class cmd_ScoreWindow : cmd_AsyncBase
    {
    }
    public class cmd_QuestionWindow : cmd_AsyncBase
    {
        public int QuestionID;
    }

    public class cmd_PatientInfoWindow : cmd_AsyncBase
    {
        public bool OpenMedicalRecords;
        public OperateInfo operate;
    }
    public class cmd_ControlStripWindow : cmd_AsyncBase { }

    public class cmd_DoctorDialogueWindow : cmd_AsyncBase
    {
        public int StepID;
        /// <summary>
        /// 结束对话时是否发送答题结束事件
        /// </summary>
        public bool IsSendAnswerOver;
    }
    public class cmd_AnswerWindow : cmd_AsyncBase
    {
        public int ID;
    }
    public class cmd_NPCDialogueWindow : cmd_AsyncBase
    {
        public int npcid;
    }
    public class cmd_PunctureWindow : cmd_AsyncBase { }

    public class cmd_RenWenGuanHuaiWindow : cmd_AsyncBase
    {
        public int StepID;
    }
    public class cmd_ShouShuYiMainWindow : cmd_AsyncBase
    {
    }
    public class cmd_TestTipsWindow : cmd_AsyncBase
    { }

    public class cmd_OperationFeatureWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 箭头方向 true 为左 false 为右
        /// </summary>
        public bool ArrowsDir = true;
        /// <summary>
        /// 是否可以移动管子
        /// </summary>
        public bool IsCanMove = true;

        /// <summary>
        /// 控制箭头显示隐藏及方向
        /// 0为左 1为右2 为不显示
        /// </summary>
        public int ArrowState = 0;
    }

    public class cmd_FQCCFeatureWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 箭头状态  0为不显示箭头 1为下  2为上
        /// </summary>
        public bool ArrowsState = true;

        /// <summary>
        /// 是否可以移动针头
        /// </summary>
        public bool IsCanMove = true;
    }

    public class cmd_OpenHengJieMianWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 箭头状态  0为不显示箭头 1为下  2为上
        /// </summary>
        public int ArrowsDir = 1;

        /// <summary>
        /// 是否可以移动
        /// </summary>
        public bool IsCanMove = true;
    }
    public class cmd_OpenDaoNiaoWindow : cmd_AsyncBase
    {
        /// <summary>
        /// 是否可以移动
        /// </summary>
        public bool IsCanMove = true;

        /// <summary>
        /// 插入至多少值时关闭界面
        /// </summary>
        public double ZhenMax;
        public double ZhenMin;
        /// <summary>
        /// 正确插入范围
        /// </summary>
        public double CorrectMax;
        public double CorrectMin;

        public int OverTaskId;
    }

    public class cmd_CommonSelectPhotoShow : cmd_AsyncBase
    {
        /// <summary>
        /// 是否从底部tween弹出
        /// </summary>
        public bool isFormBottom;
        /// <summary>
        /// 最大选择照片数量
        /// </summary>
        public int SelectPhotoMax;
        /// <summary>
        /// 是否显示拍照选择
        /// </summary>
        public bool isShowTakePhoto;
    }

    public class cmd_SetSanQaingCanMove : cmd_AsyncBase
    {
        /// <summary>
        /// 能否移动
        /// </summary>
        public bool CanMove;
        /// <summary>
        /// 箭头状态 0为左 1为右 2为都不显示
        /// </summary>
        public int ArrowState;
    }

    public class ShouShuYIAnswerData
    {
        public List<int> ID;

    }

    public class XiaoDuPuJinSlider
    {
        /// <summary>
        /// 判断左右
        /// </summary>
        public bool RightOrLeft;
        /// <summary>
        /// 滑动条的值
        /// </summary>
        public float SliderValue;

        public int infoId;

        public bool isRight;
    }

    public class cmd_ParticleOperate
    {
        public GameObject gameObject;
        public float duration;
    }

    public class cmd_OperateUISetText
    {
        public GameObject gameObject;
        public string Text;
        public cmd_OperateUISetText(GameObject go, string text)
        {
            gameObject = go;
            Text = text;
        }
    }

    public class MaterialChange
    {
        public string gameObjectName;
        public string materialName;
        public int materialIdx;
    }

    public class FengHeSetButton
    {
        /// <summary>
        /// 1进针 2出针 3打结（现在和剪线一起） 4结束缝合 5取消进针点
        /// </summary>
        public int ButType;
        public bool IsJiHuo;
        public Transform transZhenDian;

    }
    public class CreateObiShengZi
    {
        public Transform TouPos;
        public Transform WeiPos;
        public Transform CeateShengziPos;
        public bool IsShow;

    }

    public class DaoNiaoPaint
    {
        /// <summary>
        /// 物体名
        /// </summary>
        public string Name;
        public string SaveName;
        /// <summary>
        /// 是否开启Paintable
        /// </summary>
        public bool bOpenPaintable;
    }

    public class MianQiuCtrlParam
    {
        /// <summary>
        /// 是否是显示棉球
        /// </summary>
        public bool bActive;
        /// <summary>
        /// 控制棉球个数
        /// </summary>
        public int ctrlCnt;
    }

    public class SelectTaskInfo
    {
        public string Name;
        public int taskStep;
    }
}