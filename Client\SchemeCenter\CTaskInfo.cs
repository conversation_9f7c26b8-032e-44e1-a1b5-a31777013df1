﻿/// <summary>
/// CTaskInfo
/// </summary>
/// <remarks>
/// 2022/10/21 15:17:57: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CTaskInfo : ISchemeNode, ITaskInfo
    {
        public const string Task_Info = "TaskInfo";
        private Dictionary<int, Dictionary<int, OperateInfo>> m_TaskInfoById;
        /// <summary>
        /// 获取该课程有序的表数据(根据后续任务来,因为现在表里有些人id顺序是乱的)
        /// </summary>
        private List<OperateInfo> m_TaskInfosByTaskFollowUp;
        /// <summary>
        /// 跳步骤 有序,不能用字典(所有主线任务)
        /// </summary>
        private List<OperateInfo> m_TaskInfos;
        /// <summary>
        /// 所有任务 包括支线
        /// </summary>
        private List<OperateInfo> m_AllTaskInfos;
        /// <summary>
        /// 各个分支的无序数据
        /// </summary>
        private Dictionary<int, List<OperateInfo>> m_StepTaskInfoDic;
        /// <summary>
        /// 各个分支的有序数据
        /// </summary>
        private Dictionary<int, List<OperateInfo>> m_StepTaskByTaskFollowUpInfoDic;
        /// <summary>
        /// 所有支线任务id
        /// </summary>
        private List<int> m_faids;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Action<Dictionary<int, OperateInfo>> m_action;
        private int m_courseId;
        private int m_taskStep;
        /// <summary>
        /// 暂存所有数据
        /// </summary>
        private List<OperateInfo> m_taskStepList;
        /// <summary>
        /// 暂存所有数据(包括支线)
        /// </summary>
        private List<OperateInfo> m_taskAllStepList;
        /// <summary>
        /// 分支任务中 公共任务中最后一行任务
        /// </summary>
        private OperateInfo m_CommonFinalyTaskInfo;
        public CTaskInfo()
        {
            m_TaskInfoById = new Dictionary<int, Dictionary<int, OperateInfo>>();
            m_TaskInfosByTaskFollowUp = new List<OperateInfo>();
            m_TaskInfos = new List<OperateInfo>();
            m_faids = new List<int>();
            m_AllTaskInfos = new List<OperateInfo>();
            m_StepTaskInfoDic = new Dictionary<int, List<OperateInfo>>();
            m_StepTaskByTaskFollowUpInfoDic = new Dictionary<int, List<OperateInfo>>();
            m_taskStepList = new List<OperateInfo>();
            m_taskAllStepList = new List<OperateInfo>();
        }
        ~CTaskInfo()
        {
        }
        public bool Create()
        {
            //if (!LoadScheme())
            //{
            //    return false;
            //}
            return true;
        }
        public bool LoadScheme(int CourseId, Action<Dictionary<int, OperateInfo>> action = null, int TaskStepId = 0)
        {
            m_action = action;
            m_courseId = CourseId;
            if (GlobalGame.Instance.SchemeCenter.GetCourseDate().IsHasFenZhi(GlobalGame.Instance.CourseMgr.GetCourseDate().Id, CourseId))
            {
                GlobalGame.Instance.CourseMgr.m_IsFenZhi = true;
            } else
            {
                GlobalGame.Instance.CourseMgr.m_IsFenZhi = false;
            }
            m_TaskInfosByTaskFollowUp.Clear();
            m_taskStepList.Clear();
            m_taskAllStepList.Clear();
            m_TaskInfos.Clear();
            m_faids.Clear();
            m_CommonFinalyTaskInfo = null;
            if (!m_TaskInfoById.ContainsKey(CourseId))
            {
                string strPath = "CourseCsv/" + CourseId + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<int, OperateInfo> datas = new Dictionary<int, OperateInfo>();
                m_TaskInfoById.TryGetValue(CourseId, out datas);
                m_AllTaskInfos.Clear();
                foreach (var item in datas)
                {
                    m_TaskInfos.Add(item.Value);
                    m_AllTaskInfos.Add(item.Value);
                    m_taskAllStepList.Add(item.Value);
                    //如果是支线任务id,在移除
                    if (!string.IsNullOrEmpty(item.Value.TaskFaID))
                    {
                        string[] TaskFaIDs = item.Value.TaskFaID.Split(';');
                        for (int i = 0; i < TaskFaIDs.Length; i++)
                        {
                            m_faids.Add(int.Parse(TaskFaIDs[i]));
                        }
                    }
                }
                for (int i = 0; i < m_faids.Count; i++)
                {
                    m_TaskInfos.Remove(m_TaskInfos.Find(item => item.Id == m_faids[i]));
                }
                GetTaskInfosByTaskFollowUp();
                if (GlobalGame.Instance.CourseMgr.m_IsFenZhi)
                {
                    LoadStepInfo();
                }
                m_taskStepList.AddRange(m_TaskInfos);
                m_action?.Invoke(datas);
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, OperateInfo> datas = null;
            m_TaskInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, OperateInfo>();
                m_TaskInfoById[(int)pData] = datas;
            }
            m_AllTaskInfos.Clear();
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                OperateInfo taskInfo = new OperateInfo();


                taskInfo.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.TaskStep = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.TaskUpdate = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.Branchline = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.TaskFaID = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskDecribe = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskModel = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskModelId = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskUseTool = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskOperate = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskOperateDate = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskAnim = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskAnimTime = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskFinishOpera = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskFinishOperaDate = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskFollowUp = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.Dialogue = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.AliasName = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.IsOrder = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.FinishType = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.AssistantNPCID = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.RightAndWrongVoice = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.HeroMove = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.ExamPredecessorsID = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskEndHeroMove = pCSVReader.GetInt(nRow, tmp_col++, 0);
                taskInfo.TaskTipDecribe = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskFinshDec = pCSVReader.GetString(nRow, tmp_col++, "");
                taskInfo.TaskTipNPCID = pCSVReader.GetString(nRow, tmp_col++, "");
                if (!datas.ContainsKey(taskInfo.Id))
                    datas.Add(taskInfo.Id, taskInfo);
                m_TaskInfos.Add(taskInfo);
                m_AllTaskInfos.Add(taskInfo);
                m_taskAllStepList.Add(taskInfo);
                if (!string.IsNullOrEmpty(taskInfo.TaskFaID))
                {
                    //如果是支线任务id,在移除
                    string[] TaskFaIDs = taskInfo.TaskFaID.Split(';');
                    for (int i = 0; i < TaskFaIDs.Length; i++)
                    {
                        m_faids.Add(int.Parse(TaskFaIDs[i]));
                    }
                }
            }
            for (int i = 0; i < m_faids.Count; i++)
            {
                m_TaskInfos.Remove(m_TaskInfos.Find(item => item.Id == m_faids[i]));
            }
            GetTaskInfosByTaskFollowUp();
            if (GlobalGame.Instance.CourseMgr.m_IsFenZhi)
            {
                LoadStepInfo();
            }
            m_taskStepList.AddRange(m_TaskInfos);
            m_action?.Invoke(datas);
            return true;
        }

        public OperateInfo GetTaskInfoByID(int tID)
        {
            OperateInfo info = null;
            Dictionary<int, OperateInfo> datas = new Dictionary<int, OperateInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            datas.TryGetValue(tID, out info);
            return info;
        }
        public Dictionary<int, OperateInfo> GetAllTask(int courseId)
        {
            Dictionary<int, OperateInfo> datas = new Dictionary<int, OperateInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            return datas;
        }

        public OperateInfo GetFirstTask()
        {
            Dictionary<int, OperateInfo> datas = new Dictionary<int, OperateInfo>();
            List<OperateInfo> operateInfos = new List<OperateInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            foreach (var item in datas.Values)
            {
                operateInfos.Add(item);
            }
            return operateInfos[0];
        }
        public List<OperateInfo> GetTaskInfos(int step = 0)
        {
            if (step == 0)
            {
                return m_TaskInfosByTaskFollowUp;
            }
            else
            {
                return m_StepTaskByTaskFollowUpInfoDic[step];
            }
        }
        public void Release()
        {
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
            m_StepTaskInfoDic.Clear();
            m_StepTaskByTaskFollowUpInfoDic.Clear();
            m_StepTaskInfoDic = null;
            m_StepTaskByTaskFollowUpInfoDic = null;
        }

        public List<OperateInfo> GetAllTaskInfos()
        {
            return m_AllTaskInfos;
        }

        public OperateInfo GetZhuXianTaskByAliasName(string AliasName)
        {
            OperateInfo operateInfo = null;
            //先找到别名的支线任务
            OperateInfo operateInfoZhiXian = null;
            for (int i = 0; i < m_AllTaskInfos.Count; i++)
            {
                operateInfoZhiXian = null;
                if (!string.IsNullOrEmpty(m_AllTaskInfos[i].AliasName))
                {
                    string[] AliasNames = m_AllTaskInfos[i].AliasName.Split(';');
                    for (int j = 0; j < AliasNames.Length; j++)
                    {
                        if (AliasNames[j].Equals(AliasName))
                        {
                            operateInfoZhiXian = m_AllTaskInfos[i];
                            break;
                        }
                    }
                }
                if (operateInfoZhiXian != null)
                {
                    break;
                }
            }
            if (operateInfoZhiXian == null)
            {
                //TRACE.TraceLn(string.Format("未找到任务别名:{0},请在后台或TaskInfo字段AliasName配置", AliasName));
                return null;
            }
            //支线任务找主线任务
            for (int i = 0; i < m_TaskInfosByTaskFollowUp.Count; i++)
            {
                if (!string.IsNullOrEmpty(m_TaskInfosByTaskFollowUp[i].TaskFaID))
                {
                    string[] TaskFaIDs = m_TaskInfosByTaskFollowUp[i].TaskFaID.Split(';');
                    for (int j = 0; j < TaskFaIDs.Length; j++)
                    {
                        if (int.Parse(TaskFaIDs[j]) == operateInfoZhiXian.Id)
                        {
                            return m_TaskInfosByTaskFollowUp[i];
                        }
                    }
                }
                else
                {
                    if (operateInfoZhiXian == m_TaskInfosByTaskFollowUp[i])
                    {
                        return m_TaskInfosByTaskFollowUp[i];
                    }
                }
            }
            if (operateInfo == null)
            {
                TRACE.ErrorLn(string.Format("未找到包含子任务Id:{0}的主线任务", operateInfoZhiXian.Id));
                return null;
            }
            return operateInfo;
        }

        public List<OperateInfo> GetZhiXianTasksByZhuXianId(int operateInfoId)
        {
            List<OperateInfo> operateInfos = new List<OperateInfo>();
            OperateInfo operateInfoZhuXian = m_TaskInfosByTaskFollowUp.Find(item => item.Id == operateInfoId);
            if (!string.IsNullOrEmpty(operateInfoZhuXian.TaskFaID))
            {
                string[] TaskFaIDs = operateInfoZhuXian.TaskFaID.Split(';');
                for (int i = 0; i < TaskFaIDs.Length; i++)
                {
                    OperateInfo operateInfoZhiXian = m_taskAllStepList.Find(item => item.Id == int.Parse(TaskFaIDs[i]));
                    operateInfos.Add(operateInfoZhiXian);
                }
            }
            else
            {
                operateInfos.Add(operateInfoZhuXian);
            }
            return operateInfos;
        }

        public bool IsZhuXianTaskId(int taskId)
        {
            for (int i = 0; i < GetTaskInfos().Count; i++)
            {
                if (GetTaskInfos()[i].Id == taskId)
                {
                    return true;
                }
            }
            return false;
        }

        public OperateInfo GetZhuXianTaskById(int operateInfoId)
        {
            for (int i = 0; i < m_TaskInfosByTaskFollowUp.Count; i++)
            {
                if (m_TaskInfosByTaskFollowUp[i].Id == operateInfoId)
                {
                    return m_TaskInfosByTaskFollowUp[i];
                }
                else
                {
                    if (!string.IsNullOrEmpty(m_TaskInfosByTaskFollowUp[i].TaskFaID))
                    {
                        string[] TaskFaIDs = m_TaskInfosByTaskFollowUp[i].TaskFaID.Split(';');
                        for (int j = 0; j < TaskFaIDs.Length; j++)
                        {
                            if (int.Parse(TaskFaIDs[j]) == operateInfoId)
                            {
                                return m_TaskInfosByTaskFollowUp[i];
                            }
                        }
                    }
                }
            }
            TRACE.ErrorLn(string.Format("未找到OperateInfoId为{0}的主线任务Id", operateInfoId));
            return null;
        }

        public List<OperateInfo> GetTaskInfosByTaskFollowUp(List<OperateInfo> wuXuTaskInfos = null, List<OperateInfo> youXuTaskInfos = null)
        {
            if (wuXuTaskInfos == null) wuXuTaskInfos = m_TaskInfos;
            if (youXuTaskInfos == null) youXuTaskInfos = m_TaskInfosByTaskFollowUp;
            youXuTaskInfos.Add(wuXuTaskInfos[0]);
            GetTaskFollowUp(wuXuTaskInfos[0].Id, wuXuTaskInfos, youXuTaskInfos);
            return youXuTaskInfos;
        }

        private void GetTaskFollowUp(int id, List<OperateInfo> wuXuTaskInfos, List<OperateInfo> youXuTaskInfos)
        {
            OperateInfo operateInfo = wuXuTaskInfos.Find((item) => (item.Id == id));
            if (operateInfo != null)
            {
                if (!string.IsNullOrEmpty(operateInfo.TaskFollowUp))
                {
                    operateInfo = wuXuTaskInfos.Find((item) => (item.Id == int.Parse(operateInfo.TaskFollowUp)));
                    if (operateInfo != null)
                    {
                        youXuTaskInfos.Add(operateInfo);
                        GetTaskFollowUp(operateInfo.Id, wuXuTaskInfos, youXuTaskInfos);
                    }
                }
            }
        }

        public bool GetIsZhuXianHaveJson(int taskId)
        {
            for (int i = 0; i < m_TaskInfosByTaskFollowUp.Count; i++)
            {
                if (m_TaskInfosByTaskFollowUp[i].Id == taskId)
                {
                    return int.Parse(m_TaskInfosByTaskFollowUp[i].TaskOperate) >= (int)TaskOperateEnum.Currency_Combination;
                }
                else
                {
                    if (!string.IsNullOrEmpty(m_TaskInfosByTaskFollowUp[i].TaskFaID))
                    {
                        string[] TaskFaIDs = m_TaskInfosByTaskFollowUp[i].TaskFaID.Split(';');
                        for (int j = 0; j < TaskFaIDs.Length; j++)
                        {
                            if (int.Parse(TaskFaIDs[j]) == taskId)
                            {
                                if (int.Parse(GetTaskInfoByID(taskId).TaskOperate) >= (int)TaskOperateEnum.Currency_Combination)
                                {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }

        public Dictionary<int, List<int>> GetTaskStepLists()
        {
            Dictionary<int, List<int>> dic = new Dictionary<int, List<int>>();
            //获取该课程的主线任务
            List<OperateInfo> datas = new List<OperateInfo>();
            if (GlobalGame.Instance.CourseMgr.m_IsFenZhi)
            {
                //(止血)获取该课程的主线任务
                datas = GetZhiXueTaskInfos();
            }
            else
            {
                //获取该课程的主线任务
                datas = GetTaskInfos();
            }
            for (int i = 0; i < datas.Count; i++)
            {
                OperateInfo info = datas[i];
                if (info.TaskStep != 0)
                {
                    if (!dic.ContainsKey(info.TaskStep))
                    {
                        List<int> _list = new List<int>();
                        _list.Add(info.Id);
                        dic[info.TaskStep] = _list;
                    }
                    else
                    {
                        if (!dic[info.TaskStep].Contains(info.Id))
                        {
                            dic[info.TaskStep].Add(info.Id);
                        }
                    }
                }
            }

            return dic;
        }
      
        public List<OperateInfo> GetZhiXueTaskInfos()
        {
            return m_taskStepList;
        }

        public OperateInfo GetZhuXianTaskStepId(int TaskStep)
        {
            for (int i = 0; i < m_TaskInfosByTaskFollowUp.Count; i++)
            {
                if (m_TaskInfosByTaskFollowUp[i].TaskStep == TaskStep)
                {
                    return m_TaskInfosByTaskFollowUp[i];
                }
                //else
                //{
                //    //if (!string.IsNullOrEmpty(m_TaskInfosByTaskFollowUp[i].TaskFaID))
                //    //{
                //    //    string[] TaskFaIDs = m_TaskInfosByTaskFollowUp[i].TaskFaID.Split(';');
                //    //    for (int j = 0; j < TaskFaIDs.Length; j++)
                //    //    {
                //    //        if (int.Parse(TaskFaIDs[j]) == TaskStep)
                //    //        {
                //    //            return m_TaskInfosByTaskFollowUp[i];
                //    //        }
                //    //    }
                //    //}
                //}
            }
            TRACE.ErrorLn(string.Format("未找到OperateInfoId为{0}的主线任务Id", TaskStep));
            return null;
        }

        /// <summary>
        /// 跳步后根据任务id来获取当前taskStepid
        /// </summary>
        /// <returns></returns>
        public int GetJumpTaskStepById(int taskId)
        {
            if (m_taskStepList != null)
            {
                foreach (var item in m_taskStepList)
                {
                    if (item.Id == taskId)
                    {
                        return item.TaskStep;
                    }
                }
            }
            return 0;
        }

        private void LoadStepInfo()
        {
            //获取有多少个分支
            List<int> stepName = new List<int>();
            for (int i = 0; i < m_TaskInfos.Count; i++)
            {
                if (!stepName.Contains(m_TaskInfos[i].TaskStep))
                {
                    stepName.Add(m_TaskInfos[i].TaskStep);
                }
            }
            for (int i = 0; i < stepName.Count; i++)
            {
                m_StepTaskInfoDic[stepName[i]] = new List<OperateInfo>();
                m_StepTaskByTaskFollowUpInfoDic[stepName[i]] = new List<OperateInfo>();
            }
            for (int i = 0; i < m_TaskInfos.Count; i++)
            {
                OperateInfo operateInfo = m_TaskInfos[i];
                for (int j = 0; j < stepName.Count; j++)
                {
                    if (stepName[j] != 0)
                    {
                        if (operateInfo.TaskStep == stepName[j])
                        {
                            m_StepTaskInfoDic[stepName[j]].Add(m_TaskInfos[i]);
                        }
                    }
                    else
                    {
                        m_StepTaskInfoDic[0].Add(m_TaskInfos[i]);
                    }
                }
            }
            foreach (var item in m_StepTaskInfoDic)
            {
                GetTaskInfosByTaskFollowUp(item.Value, m_StepTaskByTaskFollowUpInfoDic[item.Key]);
            }
        }

        public void LoadSchemeStepInfo(int step)
        {
            m_TaskInfos.Clear();
            m_AllTaskInfos.Clear();
            m_TaskInfos.AddRange(m_StepTaskInfoDic[step]);
            m_AllTaskInfos.AddRange(m_StepTaskInfoDic[step]);
        }

        public int GetLastTaskId(int curTaskId)
        {
            if (curTaskId == 0)
            {
                curTaskId = GlobalGame.Instance.TaskMgr.GetCurrentOperate().Id;
            }
            //当前任务的索引
            int index = 0;
            for (int i = 0; i < m_TaskInfos.Count; i++)
            {
                if (m_TaskInfos[i].Id == curTaskId)
                {
                    index = i;
                    break;
                }
            }
            if (index == 0)
            {
                //找到公共任务的最后一步
                OperateInfo operateInfo = GetCommonFinalyTask();
                if (string.IsNullOrEmpty(operateInfo.TaskFaID))
                {
                    return operateInfo.Id;
                }
                else
                {
                    string[] faids = operateInfo.TaskFaID.Split(';');
                    return int.Parse(faids[0]);
                }
            }
            else
            {
                OperateInfo operateInfo = m_TaskInfos[index - 1];
                if (string.IsNullOrEmpty(operateInfo.TaskFaID))
                {
                    return operateInfo.Id;
                }
                else
                {
                    string[] faids = operateInfo.TaskFaID.Split(';');
                    return int.Parse(faids[0]);
                }
            }
        }

        public int GetNextTaskId()
        {
            int curTaskId = GlobalGame.Instance.TaskMgr.GetCurrentOperate().Id;
            if (GlobalGame.Instance.TaskMgr.GetCurrentOperate().TaskOperateDate.Equals("TaskSelect") && GlobalGame.Instance.CourseMgr.ClickTaskStep == 0)
            {
                curTaskId = GetCommonFinalyTask().Id;
            }
            //当前任务索引
            int index = 0;
            if (GlobalGame.Instance.TaskMgr.GetCurrentOperate().TaskStep == 0)
            {
                if (GlobalGame.Instance.CourseMgr.ClickTaskStep == 0 && GlobalGame.Instance.CourseMgr.NowTaskStep == 0)
                {
                    return 0;
                }
                else
                {
                    if (curTaskId == GetCommonFinalyTask().Id)
                    {
                        OperateInfo operateInfo = m_StepTaskInfoDic[GlobalGame.Instance.CourseMgr.NowTaskStep][0];
                        if (string.IsNullOrEmpty(operateInfo.TaskFaID))
                        {
                            return operateInfo.Id;
                        }
                        else
                        {
                            string[] faids = operateInfo.TaskFaID.Split(';');
                            return int.Parse(faids[0]);
                        }
                    }
                    else
                    {
                        return 0;
                    }
                }
            }
            else
            {
                for (int i = 0; i < m_TaskInfos.Count; i++)
                {
                    if (m_TaskInfos[i].Id == curTaskId)
                    {
                        index = i;
                        break;
                    }
                }
                if (index == m_TaskInfos.Count - 1)
                {
                    //证明要弹窗
                    return 0;
                }
                else
                {
                    OperateInfo operateInfo = m_TaskInfos[index];
                    if (string.IsNullOrEmpty(operateInfo.TaskFaID))
                    {
                        return operateInfo.Id;
                    }
                    else
                    {
                        string[] faids = operateInfo.TaskFaID.Split(';');
                        return int.Parse(faids[0]);
                    }
                }
            }
        }

        /// <summary>
        /// 公共任务的最后一步
        /// 公共任务一定在所有分支任务前面
        /// </summary>
        private OperateInfo GetCommonFinalyTask()
        {
            if (m_CommonFinalyTaskInfo == null)
            {
                OperateInfo operateInfo = null;
                for (int i = 0; i < m_taskStepList.Count; i++)
                {
                    if (m_taskStepList[i].TaskStep == 0 && !m_taskStepList[i].TaskOperateDate.Equals("TaskSelect"))
                    {
                        operateInfo = m_taskStepList[i];
                    }
                    else
                    {
                        break;
                    }
                }
                m_CommonFinalyTaskInfo = operateInfo;
            }
            return m_CommonFinalyTaskInfo;
        }

        /// <summary>
        /// 获取需要跳几步
        /// </summary>
        /// <returns></returns>
        public int GetJumpNum(int taskId)
        {
            int num = 0;
            for (int i = 0; i < m_taskStepList.Count; i++)
            {
                if (string.IsNullOrEmpty(m_taskStepList[i].TaskFaID))
                {
                    if (m_taskStepList[i].Id != taskId && !m_taskStepList[i].TaskOperateDate.Equals("TaskSelect") && m_taskStepList[i].TaskStep == 0)
                    {
                        num++;
                    }
                    else if (m_taskStepList[i].Id == taskId)
                    {
                        break;
                    }
                }
                else
                {
                    string[] faids = m_taskStepList[i].TaskFaID.Split(';');
                    bool ishave = false;
                    for (int j = 0; j < faids.Length; j++)
                    {
                        if (int.Parse(faids[j])== taskId)
                        {
                            ishave = true;
                        }
                    }
                    if (ishave)
                    {
                        break;
                    }
                    else
                    {
                        num++;
                    }
                }
            }
            return num;
        }

        public OperateInfo GetOperateInfoAlltaskStep(int taskId)
        {
            return m_taskAllStepList.Find(item => item.Id == taskId);
        }
    }
}