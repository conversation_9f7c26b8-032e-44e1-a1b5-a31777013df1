﻿/// <summary>
/// COpHandleCommandHightLight
/// </summary>
/// <remarks>
/// 2023/5/10 16:59:26: 创建. 熊洋 <br/>
/// <br/>
/// </remarks>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandHightLight : IHandleCommand
    {
        // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        private GameObject m_target;
        private int m_isShow;
        private int m_isCludeChild;
        private int taskId;
        private SOpHandle_RunInstance runInstance;
        private bool m_isPlay;
        private List<IHandleCommand> m_others;
        public COpHandleCommandHightLight(SOpHandleCommand_HightLight data)
        {
            m_target = data.target;
            m_isShow = data.isShow;
            m_isCludeChild = data.isCludeChild;
            runInstance = data.runInstance;
            m_others = data.otherCommand;
            taskId = data.taskId;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpHightLight;
        }

        public void OnPause()
        {

        }

        public void release()
        {
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
                m_isPlay = true;
            }
            if (m_target == null)
            {
                TRACE.ErrorLn("error target = null");
                m_isEnd = true;
                return false;
            }
            cmd_HighLight cmd_High = new cmd_HighLight();
            cmd_High.trans = m_target.transform;
            cmd_High.enable = m_isShow == 1 ? true : false;
            cmd_High.isCludeChild = m_isCludeChild == 1 ? true : false;
            cmd_High.taskId = taskId;
            GlobalGame.Instance.RenderViewProxy.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_HIGHLIGHT_Enable, 0, "", cmd_High);
            return true;
        }

        public void update()
        {
        }
    }
}