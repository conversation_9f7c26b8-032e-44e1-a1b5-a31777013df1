﻿/// <summary>
/// OperateCondition
/// </summary>
/// <remarks>
/// 2023/1/13 16:56:36: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class COperateCondition : ISchemeNode, IOperateCondition
    {
        public const string Task_Info = "OperateCondition";
        Dictionary<int, OperateConditionInfo> m_DialogueInfoById;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }

        private Action<Dictionary<int, OperateConditionInfo>> m_curLoadFinishCallback;
        private int m_CourseId;
        public COperateCondition()
        {
            m_DialogueInfoById = new Dictionary<int, OperateConditionInfo>();
        }
        ~COperateCondition()
        {
        }
        public bool Create()
        {
            return true;
        }
        public bool LoadScheme(int courseID, Action<Dictionary<int, OperateConditionInfo>> action)
        {
            m_curLoadFinishCallback = action;

            //此表现在使用太少，为减少一些不必要表，暂时去除直接将数据写死
            if (m_DialogueInfoById.Count <= 0)
            {
                //因为当前配置表中主要使用的是这三个信息，所以暂时增加
                OperateConditionInfo ConditionInfo = GenNewData(1007, "1", "1?(1:0)", "ture");
                m_DialogueInfoById.Add(ConditionInfo.ID, ConditionInfo);
                ConditionInfo = GenNewData(2001, "1", "x=y?(1:0)", "TRUE");
                m_DialogueInfoById.Add(ConditionInfo.ID, ConditionInfo);
                ConditionInfo = GenNewData(2002, "1", "x=y?(1:0)", "FALSE");
                m_DialogueInfoById.Add(ConditionInfo.ID, ConditionInfo);

                m_curLoadFinishCallback?.Invoke(m_DialogueInfoById);
            }
            else
            {
                m_curLoadFinishCallback?.Invoke(m_DialogueInfoById);
            }
            /*
            m_CourseId = courseID;
            if (!m_DialogueInfoById.ContainsKey(courseID))
            {
                m_DialogueInfoById.Add(courseID, null);
                string strPath = "CourseCsv/" + courseID + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, courseID);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<int, OperateConditionInfo> datas = new Dictionary<int, OperateConditionInfo>();
                m_DialogueInfoById.TryGetValue(courseID, out datas);
                m_curLoadFinishCallback?.Invoke(datas);
            }*/
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

           /* Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, OperateConditionInfo> datas = null;
            m_DialogueInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, OperateConditionInfo>();
                m_DialogueInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                OperateConditionInfo ConditionInfo = new OperateConditionInfo();

                ConditionInfo.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                ConditionInfo.ConditionType = pCSVReader.GetString(nRow, tmp_col++, "");
                ConditionInfo.Condiotion = pCSVReader.GetString(nRow, tmp_col++, "");
                ConditionInfo.ConditionDate = pCSVReader.GetString(nRow, tmp_col++, "");

                datas.Add(ConditionInfo.ID, ConditionInfo);
            }
            m_curLoadFinishCallback?.Invoke(datas);*/
            return true;
        }
        public OperateConditionInfo GetAllDialogue(int StepID)
        {
            OperateConditionInfo info = null;
         //   Dictionary<int, OperateConditionInfo> datas = new Dictionary<int, OperateConditionInfo>();
      //      m_DialogueInfoById.TryGetValue(m_CourseId, out datas);
      //      datas.TryGetValue(StepID, out info);
            return info;
        }

        private OperateConditionInfo GenNewData(int id, string type, string condition, string data)
        {
            OperateConditionInfo ConditionInfo = new OperateConditionInfo();
            ConditionInfo.ID = id;
            ConditionInfo.ConditionType = type;
            ConditionInfo.Condiotion = condition;
            ConditionInfo.ConditionDate = data;
            return ConditionInfo;
        }

        public void Release()
        {
            m_DialogueInfoById.Clear();
            m_DialogueInfoById = null;
        }
    }
}
