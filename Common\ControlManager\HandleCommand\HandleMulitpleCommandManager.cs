﻿/// <summary>
/// CAssemblingClient
/// </summary>
/// <remarks>
/// 2021.5.7: 创建. 秦勉 <br/>
/// 执行模块Client<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
	public class CHandleMulitpleCommandManager : ITimerHandler, IHandleCommandManagerMulitple, IEventExecuteSink
	{
		/** 多队列消息字典
		*/
		CHandleCommandDictionary m_CommandDictionary;

		// 命令工厂
		CHandleCommandFactory m_HandleCommandFactory;

		// 操作命令逻辑定时器
		const int HANDLE_COMMAND_LOGIC_TIMER = 0;
		// 操作命令逻辑定时器时间
		const int HANDLE_COMMAND_LOGIC_TIME = 16;

		/* 在地图加载过程中寻路失败，影响到命令队列的执行，所以为了解决该问题，引入了队列暂停机制
		 * 监听地图加载开始事件和结束事件，开始加载时暂停命令队列，加载结束后开启命令队列
		 * 记得暂停时间，定义最大暂停时间，超过最大时间启动命令队列，以免出现其他问题时影响到命令队列
		 */
		// 队列暂停最长时长
		const UInt32 m_uMaxTick = 30000;
		// 队列是否暂停
		bool m_bSuspend = false;
		// 暂停队列开始时间
		UInt32 m_uSuspendTick = 0;
		// 备份列表
		List<IHandleCommand> m_backUpCmdList = null;
		// 是否备份了命令
		private bool m_bBackupCmd = false;
		// 备份命令的TICK
		private int m_nBackupCmdTick = 0;
		// 备份命令的最长TICK
		const UInt32 m_uMaxBackupTick = 10000;

		public bool Create()
		{
			IEventEngine pEventEngine = GHelp.GetEventEngine();
			if (pEventEngine != null)
			{
				pEventEngine.Subscibe((IEventExecuteSink)this, (ushort)ViewLogicDef.GVIEWCMD_MATCH_LOAD_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "场景加载");
				pEventEngine.Subscibe((IEventExecuteSink)this, (ushort)ViewLogicDef.STOP_SCRIPT_RESPONSE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "停止编程");
				pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "NetManaer::Create");
				pEventEngine.Subscibe((IEventExecuteSink)this, (ushort)ViewLogicDef.INFORM_FORCED_LOGIN_OUT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "强制登出");
			}
			return create();
		}

		public void Release()
		{
			m_CommandDictionary.clearAll();
			m_HandleCommandFactory = null;
			IEventEngine pEventEngine = GHelp.GetEventEngine();
			if (pEventEngine != null)
			{
				pEventEngine.UnSubscibe((IEventExecuteSink)this, (ushort)ViewLogicDef.GVIEWCMD_MATCH_LOAD_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
				pEventEngine.UnSubscibe((IEventExecuteSink)this, (ushort)ViewLogicDef.STOP_SCRIPT_RESPONSE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
				pEventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
				pEventEngine.UnSubscibe((IEventExecuteSink)this, (ushort)ViewLogicDef.INFORM_FORCED_LOGIN_OUT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
			}
		}

		/////////////////////////////////////////IHandleCommandManager/////////////////////////////////////////
		/** 获取命令工厂接口
		*/
		public IHandleCommandFactory getHandleCommandFactory()
		{
			return m_HandleCommandFactory;
		}

		public bool create()
		{
			GlobalGame.Instance.TimerManager.AddTimer(this, HANDLE_COMMAND_LOGIC_TIMER, HANDLE_COMMAND_LOGIC_TIME, "CHandleCommandManager");

			return true;
		}

		public void close()
		{
			GlobalGame.Instance.TimerManager.RemoveTimer(this, HANDLE_COMMAND_LOGIC_TIMER);

			clear();

		}

		/// 执行事件sink 
		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			switch (wEventID)
			{
				case (ushort)ViewLogicDef.GVIEWCMD_MATCH_LOAD_SCENE:
					{
						clear();
					}
					break;
				case (ushort)ViewLogicDef.START_SCRIPT_RESPONSE:
					{
						if (m_CommandDictionary != null)
						{
							m_CommandDictionary.clearAll();
						}
					}
					break;
				case (ushort)ViewLogicDef.STOP_SCRIPT_RESPONSE:
					{
						if (m_CommandDictionary != null)
						{
							m_CommandDictionary.clearAll();
						}
						// 停掉积木块的音频指令
						//GHelp.StopMusic(0, AudioTagClass.OPCodeAudio);
						// 停止了积木块就要删除timeline
						GlobalGame.Instance.TimelineClient.DestroyMainTimeline(true);

						if (GHelp.GetHero() != null)
						{
							// 无论如何，结束了编程都要显示回UI摄像机
							cmd_SetUICameraShow data = new cmd_SetUICameraShow();
							data.IsShow = true;
							// 设置UI摄像机
							GHelp.sendEntityCommand(GHelp.GetHero().GetEntityViewID(), (int)EntityLogicDef.ENTITY_SET_UI_CAMERA_SHOW, 0, "", data);
						}
					}
					break;
				case (ushort)DGlobalEvent.EVENT_NET_CONNERCT:
					{
						bool isConnerct = (bool)pContext;
						if (!isConnerct)
						{
							if (GHelp.GetHero() != null)
							{
								// 重连成功设置UI摄像机
								cmd_SetUICameraShow data = new cmd_SetUICameraShow();
								data.IsShow = true;
								// 设置UI摄像机
								GHelp.sendEntityCommand(GHelp.GetHero().GetEntityViewID(), (int)EntityLogicDef.ENTITY_SET_UI_CAMERA_SHOW, 0, "", data);
							}
						}
					}
					break;
				case (ushort)ViewLogicDef.INFORM_FORCED_LOGIN_OUT:
					{
						if (GHelp.GetHero() != null)
						{
							// 被踢出时也要重设UI摄像机
							cmd_SetUICameraShow data = new cmd_SetUICameraShow();
							data.IsShow = true;
							// 设置UI摄像机
							GHelp.sendEntityCommand(GHelp.GetHero().GetEntityViewID(), (int)EntityLogicDef.ENTITY_SET_UI_CAMERA_SHOW, 0, "", data);
						}
					}
					break;
				default: break;
			}
		}

		public void update()
		{
			// GetGameServerTime 每个频率产生20B开销，所以换用GetTickCount by dujiawu
			// UInt32 uCurTick = GlobalGame.Instance.CountryClient.GetGameServerTime(); 
			UInt32 uCurTick = (UInt32)Api.GetTickCount();
			if (m_uSuspendTick > 0 && uCurTick - m_uSuspendTick > m_uMaxTick)
			{
				m_bSuspend = false;
				m_uSuspendTick = 0;
			}
			if (m_bSuspend)
			{
				return;
			}
			m_CommandDictionary.update();
		}

		/////////////////////////////////////////字典相关处理/////////////////////////////////////////
		public void ExecuteCommandFail(string hatId)
		{
			m_CommandDictionary.RemoveCommand(hatId);
		}

		public void appendCommandTail(string hatId, IHandleCommand pCmd)
		{
			m_CommandDictionary.pushBack(hatId, pCmd);
		}

		public void appendCommandTailAllExecute(string hatId, IHandleCommand pCmd)
		{
			m_CommandDictionary.pushBack(hatId, pCmd);
			m_CommandDictionary.AddAllExecute(hatId);
		}

		public void insertCommandFront(string hatId, IHandleCommand pCmd)
		{
			m_CommandDictionary.pushFront(hatId, pCmd);
		}

		/** 清除队列
		*/
		public void clear()
		{
			m_CommandDictionary.clearAll();
		}
		public void clear(string hatId)
		{
			m_CommandDictionary.clearAll(hatId);
		}

		public void clearBefore(string hatId, IHandleCommand pCmd)
		{
			m_CommandDictionary.clearBefore(hatId, pCmd);
		}

		public void clearAfter(string hatId, IHandleCommand pCmd)
		{
			m_CommandDictionary.clearAfter(hatId, pCmd);
		}

		/** 移除某项指令
		*/
		public void RemoveCommand(string hatId, IHandleCommand pCommand)
		{
			m_CommandDictionary.RemoveCommand(hatId, pCommand);
		}

		public IHandleCommand GetHead(string hatId)
		{
			return m_CommandDictionary.GetHead(hatId);
		}

		//返回最后一条指令
		public IHandleCommand GetTail(string hatId)
		{
			return m_CommandDictionary.GetTail(hatId);
		}

		/////////////////////////////////////////TimerHandler/////////////////////////////////////////
		// 定时器到了
		public void OnTimer(TimerInfo ti)
		{
			switch (ti.timerId)
			{
				case HANDLE_COMMAND_LOGIC_TIMER:
					{
						update();

						break;
					}
				default:
					break;
			}
		}

		public bool isIdle()
		{
			return true;
		}

		/////////////////////////////////////////HandleCommandManager/////////////////////////////////////////
		public CHandleMulitpleCommandManager()
		{
			m_CommandDictionary = new CHandleCommandDictionary();
			m_HandleCommandFactory = new CHandleCommandFactory();
		}

		// 备份当前指令
		public void BackUpCmd(string hatId)
		{
			if (m_bBackupCmd && Api.GetTickCount() - m_nBackupCmdTick < m_uMaxBackupTick)
				return;
			m_bBackupCmd = true;
			m_nBackupCmdTick = Api.GetTickCount();
			m_backUpCmdList = m_CommandDictionary.BackupToList(hatId);
		}

		// 恢复当前指令
		public void ResumeBackUpCmd(string hatId)
		{
			if (m_backUpCmdList != null)
			{
				if (Api.GetTickCount() - m_nBackupCmdTick < m_uMaxBackupTick)
				{
					for (int i = 0; i < m_backUpCmdList.Count; i++)
					{
						m_CommandDictionary.pushBack(hatId, m_backUpCmdList[i]);
					}
				}
				m_backUpCmdList.Clear();
			}
			m_bBackupCmd = false;
			m_nBackupCmdTick = 0;

		}

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}
	}
}