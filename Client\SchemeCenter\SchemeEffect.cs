﻿/// <summary>
/// IBuffClient
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// 效果配置<br/>
/// </remarks>
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;




namespace GLib.Client
{
	public class CSchemeEffect : ISchemeNode,ISchemeEffect
	{
		// Effect配置脚本名字
		public const string EFFECT_SCHEME_FILENAME = "BuffEffect";

		// 所有效果配置属性
		private Dictionary<uint, BuffEffect.Types.Item> m_buffInfoByID;

		// 效果加载器
		FastReader m_EffectReader;

		/// <summary>
		/// 脚本名称(模块实现者不用设置)
		/// </summary>
		public string SchemeName { get; set; }

		/// <summary>
		/// 异步脚本的加载状态(注释:异步模块专用)
		/// </summary>
		public EMSchemeState SchemeLoadState { get; set; }

		/// <summary>
		/// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
		/// </summary>
		public float Progress { get; set; }

		public CSchemeEffect()
		{
			m_buffInfoByID = new Dictionary<uint, BuffEffect.Types.Item>();
		}

		
		/// <summary>
		/// 初始化
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
			// 加载脚本
			string stringPath = EFFECT_SCHEME_FILENAME;
			bool bResult = FastReader.FastRead(stringPath, out m_EffectReader, OnEffectReadFinish);
			if (!bResult)
			{
				TRACE.ErrorLn("加载配置文件失败。文件名 = " + stringPath);
				return false;
			}
			return true;
		}


		/// <summary>
		/// 释放资源
		/// </summary>
		public void Release()
		{
			m_buffInfoByID.Clear();

		}


		/** 取得效果配置信息
		@param   
		@param   
		@return  
		*/
		public BuffEffect.Types.Item GetEffectSchemeInfo(UInt32 dwEffectID)
		{
			BuffEffect.Types.Item sInfo = null;
			m_buffInfoByID.TryGetValue(dwEffectID, out sInfo);
			if (sInfo != null)
			{
				return sInfo;
			}

			sInfo = ReadEffect((Int32)dwEffectID);
			if (sInfo == null)
			{
				return null;
			}
			m_buffInfoByID.Add(dwEffectID, sInfo);
			return sInfo;
		}


		/// <summary>
		/// 加载Effect表回调
		/// </summary>
		/// <param name="szFileName"></param>
		/// <returns></returns>
		bool OnEffectReadFinish(string szFileName)
		{
#if UNITY_EDITOR
			Dictionary<Int32, Int32> allData = m_EffectReader.AllData();
			var iter = allData.GetEnumerator();
			Int32 nError = 0;
			while (iter.MoveNext())
			{
				BuffEffect.Types.Item sInfo = ReadEffect(iter.Current.Key);
				if (sInfo == null)
				{
					nError++;
					continue;
				}
				m_buffInfoByID.Add((UInt32)iter.Current.Key, sInfo);
			}
			iter.Dispose();
			return nError == 0;
#else
			return true;
#endif
		}


		/// <summary>
		/// 读取一行数据
		/// </summary>
		/// <param name="nFormatID"></param>
		/// <returns></returns>
		BuffEffect.Types.Item ReadEffect(Int32 nFormatID)
		{
			FastLine pCSVReader = m_EffectReader.StartLine(nFormatID);
			if (pCSVReader == null)
			{
				return null;
			}
			int nRow = 0;

			Int32 nCol = 0;
			BuffEffect.Types.Item effectschemeinfo = new BuffEffect.Types.Item();
			// 效果id
			effectschemeinfo.Id = nFormatID;

			if (effectschemeinfo.Id <= 0)
			{
				return null;
			}

			// 效果id不能大于0xffff，否则存数据库会出问题
			if (effectschemeinfo.Id >= 0xFFFF)
			{
				TRACE.ErrorLn(string.Format("effectid不能大于等于0xFFFF! effectid={0}", effectschemeinfo.Id));
				return null;
			}

			// 类型
			effectschemeinfo.BuffEffectType = pCSVReader.GetInt(nRow, nCol++, 0);

			// 描述
			effectschemeinfo.Description = pCSVReader.GetString(nRow, nCol++, "");
			//增加百分比
			effectschemeinfo.Percentage = pCSVReader.GetInt(nRow, nCol++, 0);
			//增加值
			effectschemeinfo.Added = pCSVReader.GetInt(nRow, nCol++, 0);
			// 动作
			effectschemeinfo.Animation = pCSVReader.GetString(nRow, nCol++, "", false);
			//光效
			effectschemeinfo.SpecialEffect = pCSVReader.GetInt(nRow, nCol++, 0);
			//模型颜色
			effectschemeinfo.ModelColor = pCSVReader.GetString(nRow, nCol++, "", false);
			//模型大小
			effectschemeinfo.ModelScale = pCSVReader.GetFloat(nRow, nCol++, 0);
			//消失后效果
			effectschemeinfo.Restore = pCSVReader.GetInt(nRow, nCol++, 0);
			return effectschemeinfo;
		}


	}

}
