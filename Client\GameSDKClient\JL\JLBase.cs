﻿

using GLib.Common;
using UnityEngine;

namespace GLib.Client
{
    public class JLBase : IGameSDKInterface
    {
        // 执行结果
        protected EMGameSDKResult m_result = EMGameSDKResult.None;
        // SDK用户信息
        protected CSDKUserInfo m_userInfo = null;
        public virtual bool Exit()
        {
            Application.Quit();
            return true;
        }

        public virtual void FixedUpdate()
        {
        }

        public virtual EMGameSDKResult GetResult()
        {
            return m_result;
        }

        public virtual EMGameSDKType GetSDKType()
        {
            return EMGameSDKType.NONE;
        }

        public virtual EMGameLoginType GetLoginType()
        {
            return EMGameLoginType.NONE;
        }

        public CSDKUserInfo GetUserInfo()
        {
            return m_userInfo;
        }

        public virtual void Init()
        {
            m_userInfo = new CSDKUserInfo();
            m_userInfo.sdkUserID = "";
            m_userInfo.sdkUsername = "";
            m_userInfo.userID = "";
            m_userInfo.username = "";
            m_userInfo.timestamp = "";
            m_userInfo.token = "";
            m_userInfo.timestamp = "0";
            m_userInfo.robotTargetID = "";
            m_userInfo.phoneNumber = "";
        }

        public bool IsLogined()
        {
            throw new System.NotImplementedException();
        }

        public virtual bool LoginByPassWord()
        {
            m_result = EMGameSDKResult.Succee;
            return true;
        }

        public virtual bool LoginBySMS()
        {
            m_result = EMGameSDKResult.Succee;
            return true;
        }

        public virtual bool LoginByAuto(string accessToken)
        {
            m_result = EMGameSDKResult.Succee;
            return true;
        }

        public virtual bool LoginByOneClick(string token)
        {
            m_result = EMGameSDKResult.Succee;
            return true;
        }

        public virtual bool Logout()
        {
            return true;
        }

        public virtual bool ReqVerify()
        {
            return false;
        }

        public void SetResult(EMGameSDKResult result)
        {
            m_result = result;
        }


        /// <summary>
		/// 显示个人中心
		/// </summary>
		/// <returns></returns>
        public virtual bool ShowAccountCenter()
        {
            return false;
        }

        public virtual void SwitchLogin()
        {
        }


        public void SetUserInfo(string token, string accesstoken)
        {
            if (string.IsNullOrEmpty(token))
            {
                TRACE.TraceLn("===JLBase::SetUserInfo:token是空的");
                return;
            }
            m_userInfo.token = token;
            m_userInfo.access_token = accesstoken;
        }

        public void ResetUserInfo()
        {
            m_userInfo.token = "";
            m_userInfo.access_token = "";
        }

        public void SetUserID(string userID)
        {
            m_userInfo.userID = userID;
            PluginPlatform.Instance.Plugin().SetBuglyUserID(userID);
        }

        public void SetRobotTargetID(string targetID)
        {
            m_userInfo.robotTargetID = targetID;
        }

        public void SetNickName(string name)
        {
            m_userInfo.username = name;
        }

        public void SetPhoneNumber(string num)
        {
            m_userInfo.phoneNumber = num;
            TRACE.TraceLn("===设置当前手机号为：" + num);
        }
    }
}
