﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandClassChange :IHandleCommand
    {
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private string m_className;
        private int m_enableClassKind;
        private GameObject m_target;
        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;
        public COpHandleCommandClassChange(SOpHandleCommand_ClassChange data)
        {
            m_target = data.target;
            runInstance = data.runInstance;
            m_enableClassKind = data.enableClassKind;
            m_className = data.className;
            m_isPlay = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpMove;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
                m_isPlay = true;
            }
            if (m_target == null)
            {
                m_isEnd = true;
                return false;
            }

            Component component =  m_target.GetComponent(m_className);
            if (component != null)
            {
                bool isShow = m_enableClassKind == 0 ? false : true;
                component.GetType().GetProperty("enabled").SetValue(component, isShow);
            }
            return true;
        }

        public void update()
        {
        }
    }
}
