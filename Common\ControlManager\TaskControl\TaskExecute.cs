﻿/// <summary>
/// TaskExecute
/// </summary>
/// <remarks>
/// 2021/7/23 18:28:36: 创建. 王正勇 <br/>
/// 
/// </remarks>
using game.scene;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// TaskExecute
    /// </summary>
    public class TaskExecute
    {
        private string UBBToHtml(string uBBContent)
        {

            return "";
        }
        public void SendChangeTaskState(string taskGuid, ITaskEvent taskEvent, TaskType taskType, object taskContent)
        {

            switch (taskType)
            {
                //加载任务内容
                case TaskType.TaskStrike:
                case TaskType.ReceiveTask:
                    ChangeTaskMessage(taskGuid, taskEvent, taskType, taskContent);
                    break;
                //任务进行
                case TaskType.TaskProceed:
                    ChangeTaskProceedMessage(taskGuid, taskEvent, taskType, taskContent);
                    break;
                //任务结算
                case TaskType.TaskSettle:
                    ChangeTaskOverMessage(taskGuid, taskEvent, taskType, taskContent);
                    break;
                //任务结束
                case TaskType.TaskOver:
                    ChangeTaskOverMessage(taskGuid, taskEvent, taskType, taskContent);
                    break;
                case TaskType.TaskLose:
                    ChangeTaskOverMessage(taskGuid, taskEvent, taskType, taskContent);
                    break;
            }
        }
        private void ChangeTaskMessage(string taskGuid, ITaskEvent taskEvent, TaskType taskType, object taskContent)
        {
            TaskInfo task = (TaskInfo)taskContent;
            TaskInfo taskInfo = new TaskInfo(task);
            taskInfo.TrackingMessage = UBB.UBBStr(taskInfo.TrackingMessage);
            taskEvent.OnChangeTaskMessage(taskGuid, taskType, taskInfo);
        }
        private void ChangeTaskProceedMessage(string taskGuid, ITaskEvent taskEvent, TaskType taskType, object taskContent)
        {
            taskEvent.OnChangeTaskMessage(taskGuid, taskType, taskContent);
        }
        private void ChangeTaskOverMessage(string taskGuid, ITaskEvent taskEvent, TaskType taskType, object taskContent)
        {
            taskEvent.OnChangeTaskMessage(taskGuid, taskType, taskContent);
        }
    }
}
