﻿/// <summary>
/// CSchemeCenter
/// </summary>
/// <remarks>
/// 2019.6.24: 创建. 谌安 <br/>
/// </remarks>
//#define OpenDebugInfo_SchemeCenter

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GLib;
using GLib.Common;

namespace GLib.Client.Scheme
{
    public class CSchemeCenter : ITimerHandler, ISchemeCenter
    {

        #region // 加载脚本流程控制相关定义
        // 配置中心流程状态
        public enum EMRunState
        {
            None = 0,       // 初始化前
            Init,           // 初始化
            Create,         // 正在创建模块
            Finish,         // 模块创建完成
            Release,        // 正在释放模块
            Error,          // 错误状态
            Close,          // 关闭

            MAX,            // 最大值
        }

        /// <summary>
        /// 脚本的加载状态
        /// </summary>
        public enum EMSchemeLoadState
        {
            None = 0,
            Create,             // 创建中
            Loading,            // 加载中
            Wait,               // 等待中
            Success,            // 加载成功
            Fail,               // 加载失败
            TimeOut,            // 加载超时
            Error,              // 错误状态
            Close,              // 已释放

            MAX,                // 最大值
        }
        /// <summary>
        /// 脚本加载配置信息  
        /// </summary>
        public class SSchemeLoadInfo
        {
            // 配置参数
            public ISchemeNode schemeNode;          // 脚本对象
            public float fTimeOut;                  // 脚本加载超时时间 单位: 秒
            public GameState nPreLoadState;         // 在某个游戏状态之前需加载完成

            public bool bIsAsyn = false;            // 是否需要异步处理
            public EMSchemeLoadState m_nState;      // 脚本加载的状态
                                                    // 运行时的变量
            public int nModuleNum;                  // 脚本的注册时序号 新注册时为m_LoadInfo.Count
            public float[] fStateTime = new float[(int)EMSchemeLoadState.MAX]; // 每个状态变化时间
            public string id;
            //////////////////////////////////////////////

            /// <summary>
            /// 改变游戏流程
            /// </summary>
            /// <param name="nState">当前状态</param>
            /// <returns>是否成功</returns>
            public bool SetState(EMSchemeLoadState nState)
            {
                if (nState == m_nState)
                {
                    return false;
                }
                // 旧的流程
                EMSchemeLoadState nOldState = m_nState;

                fStateTime[(int)nOldState] = Time.realtimeSinceStartup;

                // 改变流程
                m_nState = nState;

                fStateTime[(int)nState] = Time.realtimeSinceStartup;

#if OpenDebugInfo_SchemeCenter
                //TRACE.TraceLn("SSchemeLoadInfo.SetState():" + nOldState.ToString() + "->" + nState.ToString());
#endif

                return true;
            }
        }

#if OpenDebugInfo_Profiler
		public class SSchemeProfilerInfo
		{
			public string strName;
			public float fTimes;
		}

		private List<SSchemeProfilerInfo> m_SchemeProfilerInfo = new List<SSchemeProfilerInfo>();
#endif
        //////  模块常量 //////////////////////////////////////////////////////
        // 检查是否加载完成脚本的间隔时间，节省性能
        private const float CHECK_END_INTERVAL = 0.5f;
        // 检查同时可以加载的数量
        private const int SAME_LOAD_COUNT = 10;
        // 检查需要加载脚本的间隔时间，节省性能
        private const float CHECK_INTERVAL = 0.1f;
        // 脚本加载超时时间
        private const float LOAD_TIMEOUT = 1.0f;
        /////  模块变量 ///////////////////////////////////////////////////////

        // 配置中心流程状态
        private EMRunState m_nState;
        // 流程类型触发fix时间
        private float[] m_fStateTime = new float[(int)EMRunState.MAX];

        //需要加载的脚本
        private List<SSchemeLoadInfo> m_LoadInfo = new List<SSchemeLoadInfo>();
        //已加载的脚本，按加载顺序加入，专门用来释放的时候调用
        private List<SSchemeLoadInfo> m_SchemeList = new List<SSchemeLoadInfo>();
        //正在加载的脚本
        private List<SSchemeLoadInfo> m_Loading = new List<SSchemeLoadInfo>();
        // 最后检查需要加载的时间
        private float m_fLastCheckTime = 0;

        #endregion // 流程控制相关定义


        #region // 所有配置脚本对像定义
        //实体配置
        SchemeEntity m_schemeEntity;
        MapInfoCenter m_mapInfoCenter;
        ScenePointCenter m_scenePointCenter;
        MonsterCenter m_MonsterInfoCenter;
        IconCenter m_IconCenter;
        DepartmentCenter m_DepartmentCenter;
        CourseDataCenter m_CourseDataCenter;
        PersonalDataMenuCenter m_PersonalDataMenuCenter;
        HelpCenterMenuCenter m_HelpCenterMenuCenter;

        //效果数据
        CSchemeEffect m_SchemeEffect;

        InstallExerciseCenter m_InstallExerciseCenter;

        VideoCenter m_videoCenter;
        AudioConfigCenter m_AudioConfigCenter;
        DefaultConfigCenter m_DefaultConfigCenter;


        ApparatusSelectCenter m_apparatusSelectCenter;

        COperationLog m_CoperationLog;
        COperateCondition m_OperateCondition;

        AssistantNpcCenter m_assistantNpcCenter;
        HapticToolCenter m_hapticToolCenter;
        CameraTransformCenter m_cameraTransformCenter;
        OperaAreaTransformCenter m_operaAreaTransformCenter;

        //病患数据
        CHandPoseCenter m_handPoseCenter;

        //任务数据
        CTaskInfo m_ctaskInfo;
        CToolTransform m_toolTransform;
        //医生对话
        CDoctorDialogue m_DoctorDialogue;
        CDoctorPreperativecs m_DoctorPreperativecs;
        //答题
        CAnswerUI m_AnswerUI;

        CScoreInfo m_cScoreInfo;

        CScoreCondition m_cScoreCond;

        //菜单帮助信息
        CMenuHelp m_cMenuHelp;
        /////// 请大家规范写代码，加上注释，不要偷懒不加，方便大家看代码 ////////////////////////////////////////////////////////////////////////////////////////////

        #endregion // 所有配置脚本对像定义


        #region // 加载脚本流程控制相关函数
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        /** 
        @param   
        @param   
        @return  
        */
        public CSchemeCenter()
        {
            ModuleLoadState = EMModuleLoadState.None;
        }

        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        /// <summary>
        /// 模块创建
        /// 如果是同步模块，Create成功就表示加载成功。
        /// 如果是异步模块, Create成功不表示加载成功，必须通过模块的ModuleLoadState属性来判断。
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
            // 注册逻辑调用 FixedUpdate，是在固定的时间间隔执行，不受游戏帧率（fps）的影响
            GlobalGame.Instance.RegisterModuleEvent(this, (uint)EMModuleEvent.FixedUpdate);

            // 异步加载
            GlobalGame.Instance.StartCoroutineEx(Init());

            return true;
        }

        /// <summary>
        /// 模块释放
        /// </summary>
        public void Release()
        {
            //释放所有模块
            SetState(EMRunState.Release);
            // 注册逻辑调用
            GlobalGame.Instance.UnRegisterModuleEvent(this);
        }


        /// <summary>
        /// 反序释放所有脚本
        /// </summary>
        private void OnReleaseAllScheme()
        {
            for (int i = m_SchemeList.Count - 1; i >= 0; i--)
            {
                m_SchemeList[i].schemeNode.Release();
            }
            m_SchemeList.Clear();
        }


        /// <summary>
        /// 注册加载脚本配置信息  为了提高加载时的性能，请在加载脚本时 先预先人工排好序
        /// </summary>
        /// <param name="strSchemeName">脚本中文名称</param>
        /// <param name="scheme">需要加载的脚本</param>
        /// <param name="nLoadState">加载脚本时的初始状态</param>
        /// <param name="fTimeOut">脚本加载的大致时间，当超出这个时间系统会提示加载超时</param>
        /// <param name="bAsyn">设置脚本是否需要异步加载，默认false为同步加载</param>
        /// <returns></returns>
        public bool Reg(string strSchemeName, ISchemeNode scheme, GameState nLoadState = GameState.Init, float fTimeOut = 1.0f, bool bAsyn = false)
        {
            // 无效脚本
            if (scheme == null)
            {
                TRACE.ErrorLn("GameLoader.Reg() 无效脚本:" + strSchemeName);
                return false;
            }
            // 脚本名称设置 脚本初始化可以不设置
            scheme.SchemeName = strSchemeName;

            // 创建脚本加载配置信息，并保存到list对象
            SSchemeLoadInfo node = new SSchemeLoadInfo();
            node.m_nState = EMSchemeLoadState.Wait;
            node.schemeNode = scheme;
            node.nPreLoadState = nLoadState;
            node.fTimeOut = (fTimeOut < 0.5f) ? 0.5f : fTimeOut;

            node.SetState(EMSchemeLoadState.Create);
            node.nModuleNum = m_LoadInfo.Count;
            node.bIsAsyn = bAsyn;
            m_LoadInfo.Add(node);
            return true;

        }


        /// <summary>
        /// 取得需要加载脚本某个状态的数量
        /// </summary>
        /// <param name="nState">脚本需要检查的加载状态</param>
        /// <returns></returns>
        private int GetPreLoadCount(EMSchemeLoadState nState)
        {
            int nCount = 0;
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SSchemeLoadInfo info = m_LoadInfo[i];
                if (info.m_nState == nState)
                {
                    nCount++;
                }
            }
            return nCount;
        }
        /// <summary>
        /// 取得正在加载脚本某个状态的数量
        /// </summary>
        /// <param name="nState">脚本需要检查的加载状态</param>
        /// <returns></returns>
        private int GetLoadingCount(EMSchemeLoadState nState)
        {
            int nCount = 0;
            for (int i = 0; i < m_Loading.Count; i++)
            {
                SSchemeLoadInfo info = m_Loading[i];
                if (info.m_nState == nState)
                {
                    nCount++;
                }
            }
            return nCount;
        }


        /// <summary>
        /// 判断某个游戏状态需要使用的脚本是否加载完成
        /// 由于脚本比较多，外部轮询不要太频繁
        /// </summary>
        /// <param name="nState">游戏状态</param>
        /// <param name="fLastCheckTime">检查冷却时间 暂未用</param>
        /// <returns></returns>
        public bool IsLoadFinish(GameState nState, float fLastCheckTime = 1.0f)
        {
            // 如果脚本加载状态已经设置为完成，则直接返回
            if (this.ModuleLoadState == EMModuleLoadState.Success)
            {
                return true;
            }
            else if (this.ModuleLoadState == EMModuleLoadState.None)
            {
                return false;
            }
            // 判断某个游戏状态下的脚本是否全部加载完成
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SSchemeLoadInfo info = m_LoadInfo[i];
                if (info.nPreLoadState == nState)
                {
                    //TRACE.ErrorLn("SchemeName: " + info.schemeNode.SchemeName + " -> PreLoadState" + info.nPreLoadState + " -> m_nState" + info.m_nState);
                    if (info.m_nState != EMSchemeLoadState.Success)
                    {
                        return false;   // 退出脚本加载状态检测
                    }
                    //TRACE.ErrorLn("SchemeName: " + info.schemeNode.SchemeName + " -> Success");
                }
            }

            return true;
        }


        /// <summary>
        /// 取得指定状态的脚本加载完成度
        /// </summary>
        /// <param name="nState">游戏状态</param>
        /// <returns>加载的进度,范围(0.0f,1.0f)</returns>
        public float GetProgress(GameState nState)
        {
            float fProgress = 0.0f;
            // 如果脚本加载状态已经设置为完成，则直接返回
            if (this.ModuleLoadState == EMModuleLoadState.Success)
            {
                return 1.0f;
            }
            int nAll = 1;   // 所有个数
            int nOk = 1;    // 加载成功个数
                            // 判断某个游戏状态下的脚本是否全部加载完成
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SSchemeLoadInfo info = m_LoadInfo[i];
                if (info.nPreLoadState == nState)
                {
                    nAll++;
                    if (info.m_nState == EMSchemeLoadState.Success)
                    {
                        nOk++;
                    }
                }
            }

            fProgress = (float)nOk / (float)nAll;

            return fProgress;
        }

        /// <summary>
        /// 处理加载脚本，所以的脚本使用协程加载来实现后台加载
        /// </summary>
        /// <param name="info">脚本</param>
        /// <returns></returns>
        private bool LoadScheme(SSchemeLoadInfo info)
        {
            if (info.schemeNode == null)
            {
                return false;
            }

#if OpenDebugInfo_GameLoader
            TRACE.TraceLn("CSchemeCenter: 加载脚本[ " + info.schemeNode.SchemeName + "] ...");
#endif

            m_Loading.Add(info);

            m_SchemeList.Add(info);

            info.SetState(EMSchemeLoadState.Loading);
            info.schemeNode.SchemeLoadState = EMSchemeState.Loading;

            GlobalGame.Instance.StartCoroutineEx(LoadHandler(info));

            return true;
        }

        /// <summary>
        /// 资源异步处理器
        /// </summary>
        /// <param name="info">脚本</param>
        /// <returns></returns>
        private IEnumerator LoadHandler(SSchemeLoadInfo info)
        {
            // 等待下载
            yield return 0;

#if OpenDebugInfo_Profiler
			//Api.PP_BY_NAME_START("CGameLoader.LoadScheme. "+info.schemeNode.SchemeName);
			float fTime = Time.realtimeSinceStartup;
#endif
            // 开始加载资源
            bool bCreate = info.schemeNode.Create();

            // 同步处理的可不管设置加载状态，异步的要自己设置
            if (bCreate)
            {
                if (info.bIsAsyn)   // 需要异步处理的脚本
                {
                    if (info.schemeNode.SchemeLoadState == EMSchemeState.Success)
                    {
                        info.SetState(EMSchemeLoadState.Success);
                    }
                }
                else
                {
                    info.SetState(EMSchemeLoadState.Success);
                    info.schemeNode.SchemeLoadState = EMSchemeState.Success;
                }

            }
            else
            {

                TRACE.ErrorLn("CSchemeCenter: 加载脚本[ " + info.schemeNode.SchemeName + "] 失败 ");

                info.SetState(EMSchemeLoadState.Fail);
                info.schemeNode.SchemeLoadState = EMSchemeState.Fail;
            }

#if OpenDebugInfo_Profiler
			float fTime1 = Time.realtimeSinceStartup;


			SSchemeProfilerInfo pNode = new SSchemeProfilerInfo();
			pNode.strName = info.schemeNode.SchemeName;
			pNode.fTimes = (fTime1 - fTime) * 1000;
			m_SchemeProfilerInfo.Add(pNode);

			if(m_SchemeProfilerInfo.Count==m_LoadInfo.Count)
			{
				TRACE.TraceLn("CSchemeCenter:SchemeProfilerInfo List");
				for (int i = 0; i < m_SchemeProfilerInfo.Count; i++)
				{
					SSchemeProfilerInfo pInfo = m_SchemeProfilerInfo[i];
					TRACE.TraceLn("CSchemeCenter:"+pInfo.strName+","+pInfo.fTimes.ToString());
				}
			}

			//if (fTime1 > fTime + 0.02f)
			//{
			//	//TRACE.TraceLn("CSchemeCenter: 加载脚本[ " + info.schemeNode.SchemeName + "] 时间：" + (fTime1 - fTime).ToString());
			//}
			//TRACE.TraceLn("CSchemeCenter:"+info.schemeNode.SchemeName+","+(fTime1-fTime).ToString());
			//Api.PP_BY_NAME_STOP();
#endif


        }



        /// <summary>
        /// 遍历需要加载的列表,当前状态前的优先加入
        /// </summary>
        /// <param name="FindCount">需要加载的数量, 返回剩余模块数量</param>
        private void AddPrevStateScheme(GameState nState, ref int FindCount)
        {
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {

                SSchemeLoadInfo info = m_LoadInfo[i];
                // 当前GameClient的状态之前的状态都加载
                if ((int)info.m_nState < (int)EMSchemeLoadState.Loading && (int)nState >= (int)info.nPreLoadState)
                {
                    FindCount--;
                    // 处理加载脚本，所以的脚本使用协程加载来实现后台加载
                    LoadScheme(info);
                    if (FindCount < 1)
                    {
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 需要加载的列表,所有状态都可加入
        /// </summary>
        /// <param name="nState">需要加载的游戏状态</param>
        /// <param name="FindCount">需要加载的数量, 返回剩余模块数量</param>
        private void AddCanAllScheme(GameState nState, ref int FindCount)
        {
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SSchemeLoadInfo info = m_LoadInfo[i];
                // 当前GameClient的状态之前的状态都加载
                if ((int)info.m_nState < (int)EMSchemeLoadState.Loading)
                {
                    FindCount--;
                    // 处理加载脚本，所以的脚本使用协程加载来实现后台加载
                    LoadScheme(info);
                    if (FindCount < 1)
                    {
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 检查一下正在加载的资源个数
        /// </summary>
        /// <returns>查到一个就返回</returns>
        private int HaveNotSuccess()
        {
            int nNotOk = 0; // 不OK的数量
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SSchemeLoadInfo info = m_LoadInfo[i];
                // 当前GameClient的状态之前的状态都加载
                if (info.m_nState != EMSchemeLoadState.Success)
                {
                    nNotOk++;
                    break;
                }
            }
            return nNotOk;
        }
        ////////////////模块驱动基础接口//////////////////////

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {

        }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {
            DoTask();
        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {

        }

        public void OnTimer(TimerInfo ti)
        {
            // 当模块加载完成后，反注册逻辑调用,节约CPU资源
            GlobalGame.Instance.UnRegisterModuleEvent(this);
            // 不以在fixupdate 反注册自己的fixupdate调用，要延迟调用
            GlobalGame.Instance.TimerManager.RemoveTimer((ITimerHandler)this, 1);
        }
        /////// 流程控制 //////////////////////////////////////////////////////////////////////////////////////
        /// <summary>
        /// 改变游戏流程,不允许外部调用
        /// </summary>
        /// <param name="nState">当前状态</param>
        /// <returns>是否成功</returns>
        private bool SetState(EMRunState nState)
        {
            if (nState == m_nState)
            {
                return false;
            }

            // 旧的流程
            EMRunState nOldState = m_nState;

            m_fStateTime[(int)nOldState] = Time.realtimeSinceStartup;

            // 当游戏流程退出
            OnExit(nOldState, nState);

            // 改变流程
            m_nState = nState;

            m_fStateTime[(int)nState] = Time.realtimeSinceStartup;

            // 当游戏流程进入
            OnEnter(nState, nOldState);


#if OpenDebugInfo_SchemeCenter
            TRACE.TraceLn("SchemeCenter.SetState():" + nOldState.ToString() + "->" + nState.ToString());
#endif
            return true;
        }

        /// <summary>
        /// 当游戏流程进入
        /// </summary>
        /// <param name="nState">新状态</param>
        /// <param name="nOldState">旧状态</param>
        private void OnEnter(EMRunState nState, EMRunState nOldState)
        {
            //////////////////////////////////////////////////////////////////////////
            // 流程
            switch (nState)
            {
                case EMRunState.None:       // 无用的
                    {
                    }
                    break;
                case EMRunState.Init:       // 初始化
                    {
                        ModuleLoadState = EMModuleLoadState.Create;
                    }
                    break;
                case EMRunState.Create:     // 正在创建模块
                    {
                        ModuleLoadState = EMModuleLoadState.Loading;
                    }
                    break;
                case EMRunState.Finish:     // 模块创建完成
                    {
                        ModuleLoadState = EMModuleLoadState.Success;
                        // 不以在fixupdate 反注册自己的fixupdate调用，要延迟调用
                        GlobalGame.Instance.TimerManager.AddTimer((ITimerHandler)this, 1, 1000, 1, "SchemeCenter");
                    }
                    break;
                case EMRunState.Release:        // 正在释放模块
                    {
                        // 释放所有的资源
                        ModuleLoadState = EMModuleLoadState.Close;
                        OnReleaseAllScheme();
                    }
                    break;
                case EMRunState.Error:      // 错误
                    {
                        ModuleLoadState = EMModuleLoadState.Fail;
                    }
                    break;
                case EMRunState.Close:      // 关闭
                    {
                        ModuleLoadState = EMModuleLoadState.Close;
                    }
                    break;
            }
        }

        /// <summary>
        /// 驱动游戏流程
        /// </summary>
        private void DoTask()
        {
            //////////////////////////////////////////////////////////////////////////

            // 流程
            switch (m_nState)
            {
                case EMRunState.None:       // 无用的
                    {

                    }
                    break;
                case EMRunState.Init:       // 初始化
                    {
                        // 启动进入创建模块
                        SetState(EMRunState.Create);
                    }
                    break;
                case EMRunState.Create:     // 正在创建模块
                    {

                        // 检查需要加载的当前时间
                        float fNow = Time.realtimeSinceStartup;
                        // 取得正在加载的数量
                        int nLoadingCount = 0;

                        #region// 处理正在加载的列表
                        for (int i = m_Loading.Count - 1; i >= 0; i--)
                        {
                            SSchemeLoadInfo loading = m_Loading[i];

                            // 是否要删除
                            bool bNeedRemove = false;

                            switch (loading.m_nState)
                            {
                                case EMSchemeLoadState.None:
                                    {
                                    }
                                    break;
                                case EMSchemeLoadState.Create:             // 创建中
                                    {
                                    }
                                    break;
                                case EMSchemeLoadState.Loading:            // 加载中
                                    {
                                        if (loading.schemeNode.SchemeLoadState == EMSchemeState.Success)
                                        {
#if OpenDebugInfo_SchemeCenter
                                            //TRACE.TraceLn("SchemeCenter: 加载脚本[ " + loading.schemeNode.SchemeName + "] 成功！   耗时：" + (fNow - loading.fStateTime[(int)EMSchemeLoadState.Loading]));
#endif
                                            loading.SetState(EMSchemeLoadState.Success);
                                            bNeedRemove = true;
                                        }
                                        else if (loading.schemeNode.SchemeLoadState == EMSchemeState.Fail)
                                        {
                                            TRACE.ErrorLn("SchemeCenter: 加载脚本[ " + loading.schemeNode.SchemeName + "] 失败！   耗时：" + (fNow - loading.fStateTime[(int)EMSchemeLoadState.Loading]));
                                            loading.SetState(EMSchemeLoadState.Fail);
                                            // 加载模块失败 启动失败，进入错误状态
                                            SetState(EMRunState.Error);
                                            bNeedRemove = true;
                                        }
                                        else
                                        {
                                            if (fNow > loading.fStateTime[(int)EMSchemeLoadState.Loading] + loading.fTimeOut)
                                            {
                                                TRACE.WarningLn("SchemeCenter: 加载脚本[ " + loading.schemeNode.SchemeName + "] 超时！   耗时：" + loading.fTimeOut);
                                                loading.SetState(EMSchemeLoadState.TimeOut);
                                            }
                                            else
                                            {
                                                nLoadingCount++;
                                            }
                                        }
                                    }
                                    break;
                                case EMSchemeLoadState.Wait:               // 等待中
                                    {
                                    }
                                    break;
                                case EMSchemeLoadState.Success:            // 加载成功
                                    {
                                        bNeedRemove = true;
                                    }
                                    break;
                                case EMSchemeLoadState.Fail:               // 加载失败
                                    {
                                        // 加载模块失败 启动失败，进入错误状态
                                        SetState(EMRunState.Error);
                                        bNeedRemove = true;
                                    }
                                    break;
                                case EMSchemeLoadState.TimeOut:         // 超时
                                    {
                                        if (fNow > loading.fStateTime[(int)EMSchemeLoadState.TimeOut] + 3.0f)
                                        {
                                            loading.SetState(EMSchemeLoadState.Loading);
                                        }
                                    }
                                    break;
                                case EMSchemeLoadState.Error:               // 错误状态
                                    {
                                    }
                                    break;
                                case EMSchemeLoadState.Close:              // 已释放
                                    {
                                    }
                                    break;
                                default:                // 最大值
                                    break;

                            }

                            // 要删除的
                            if (bNeedRemove)
                            {
                                m_Loading.RemoveAt(i);
                            }

                        }
                        #endregion

                        #region// 间隔时间到了就检查一次
                        if (fNow > m_fLastCheckTime + CHECK_INTERVAL)
                        {
                            m_fLastCheckTime = fNow;

                            // 查一下是不全部加载成功，如全部加载完，就进入完成状态
                            int nNotOk = HaveNotSuccess(); // 不OK的数量
                                                           // 全部加载成功，就进入完成状态
                            if (nNotOk < 1)
                            {
                                SetState(EMRunState.Finish);
                                break;
                            }
                            else
                            {
                                // 取得GameClient的当前状态
                                GameState nState = GlobalGame.Instance.GameClient.GetState();
                                nState = ((int)nState < (int)GameState.MAX) ? (nState + 1) : nState;

                                // 当数量小于指定的数量时 从需要加载的模块中加入
                                if (nLoadingCount < SAME_LOAD_COUNT)
                                {
                                    int nFindCount = SAME_LOAD_COUNT - nLoadingCount;

                                    // 第一遍历需要加载的列表,当前状态前的优先加入
                                    AddPrevStateScheme(nState, ref nFindCount);

                                    // 还有空位，到所有状态找
                                    if (nFindCount > 0)
                                    {
                                        // 第一遍历需要加载的列表,所有状态都可加入
                                        AddCanAllScheme(nState, ref nFindCount);
                                    }

                                }
                            }

                        }
                        #endregion

                    }
                    break;
                case EMRunState.Finish:     // 完成
                    {

                    }
                    break;
                case EMRunState.Release:        // 正在释放模块
                    {

                    }
                    break;
                case EMRunState.Error:      // 错误
                    {
                    }
                    break;
                case EMRunState.Close:      // 关闭
                    {
                    }
                    break;
            }

            //////////////////////////////////////////////////////////////////////////


        }

        /// <summary>
        /// 当游戏流程退出
        /// </summary>
        /// <param name="nState">旧状态</param>
        /// <param name="nNewState">老状态</param>
        private void OnExit(EMRunState nState, EMRunState nNewState)
        {
            //////////////////////////////////////////////////////////////////////////
            // 流程
            switch (nState)
            {
                case EMRunState.None:       // 无用的
                    {
                    }
                    break;
                case EMRunState.Init:       // 初始化
                    {
                    }
                    break;
                case EMRunState.Create:     // 正在创建模块
                    {
                    }
                    break;
                case EMRunState.Finish:     // 完成
                    {

                    }
                    break;
                case EMRunState.Release:        // 正在释放模块
                    {
                    }
                    break;
                case EMRunState.Error:      // 错误
                    {
                    }
                    break;
                case EMRunState.Close:      // 关闭
                    {
                    }
                    break;
            }
        }


        //////////////////////////////////////////////////////////////////////////////////////////////////////////////

        #endregion // 流程控制相关函数


        #region // 所有配置脚本 new 对像

        private IEnumerator Init()
        {
            // 等待加载,异步处理
            yield return 0;


            /////// 请大家规范写代码，加上注释，不要偷懒不加，方便大家看代码 ////////////////////////////////////////////////////////////////////////////////////////////

            //实体配置
            m_schemeEntity = new SchemeEntity();

            m_mapInfoCenter = new MapInfoCenter();
            m_scenePointCenter = new ScenePointCenter();
            m_MonsterInfoCenter = new MonsterCenter();
          
            m_IconCenter = new IconCenter();
           
            m_SchemeEffect = new CSchemeEffect();
           
            m_videoCenter = new VideoCenter();
            m_AudioConfigCenter = new AudioConfigCenter();
            m_DefaultConfigCenter = new DefaultConfigCenter();
            m_InstallExerciseCenter = new InstallExerciseCenter();
            m_apparatusSelectCenter = new ApparatusSelectCenter();
            m_CoperationLog = new COperationLog();
            m_OperateCondition = new COperateCondition();
            m_assistantNpcCenter = new AssistantNpcCenter();
            m_hapticToolCenter = new HapticToolCenter();
            m_cameraTransformCenter = new CameraTransformCenter();
            m_operaAreaTransformCenter = new OperaAreaTransformCenter();
            m_handPoseCenter = new CHandPoseCenter();
            m_ctaskInfo = new CTaskInfo();
            m_DepartmentCenter = new DepartmentCenter();
            m_CourseDataCenter = new CourseDataCenter();
            m_PersonalDataMenuCenter = new PersonalDataMenuCenter();
            m_HelpCenterMenuCenter = new HelpCenterMenuCenter();
            m_DoctorDialogue = new CDoctorDialogue();
            m_DoctorPreperativecs = new CDoctorPreperativecs();
            m_AnswerUI = new CAnswerUI();
            m_toolTransform = new CToolTransform();
            m_cScoreInfo = new CScoreInfo();

            m_cScoreCond = new CScoreCondition();

            m_cMenuHelp = new CMenuHelp();
            // 等待加载
            yield return 0;

            // 注册所有配置脚本并加载
            RegisterScheme();

            // 启动进入加载资源状态，资源之间不能有相互依赖
            SetState(EMRunState.Init);

        }

        #endregion // 所有配置脚本 new 对像



        #region // 注册所有配置脚本并加载

        /// <summary>
        /// 注册脚本，系统将自动初始化加载脚本
        /// 调用格式：Reg("脚本名", (ISchemeNode)m_SchemeBaseConfig,GameState.Game, 1.0f);
        /// </summary>
        public void RegisterScheme()
        {

            // 所有的脚本初始化创建RegisterModlue的在下面用Reg注册到脚本加载系统来统一管理加载，
            // 脚本加载系统自动用协程加载每个系统，
            // 并可注明分开在游戏状态 SelectActor,Game 中不同阶段需要此脚本
            // 先注册先加载

            ///// SchemeCenter在Init游戏状态才初始化


            Reg("地图配置:MapInfo",				(ISchemeNode)m_mapInfoCenter,			GameState.Init, 1.0f);                 //  地图配置
            Reg("地图配置:ScenePointCenter", (ISchemeNode)m_scenePointCenter, GameState.Init, 1.0f);
            Reg("实体加载", (ISchemeNode)m_schemeEntity, GameState.Init, 2.0f);
            Reg("实体加载", (ISchemeNode)m_hapticToolCenter, GameState.Init, 2.0f);
            Reg("器械选择加载", (ISchemeNode)m_apparatusSelectCenter, GameState.Init, 2.0f);
            Reg("手的姿势信息", (ISchemeNode)m_handPoseCenter, GameState.Init, 2.0f);
            Reg("所有科室信息", (ISchemeNode)m_DepartmentCenter, GameState.Init, 2.0f);
            Reg("所有科室信息", (ISchemeNode)m_CourseDataCenter, GameState.Init, 2.0f);
            Reg("所有个人中心入口信息", (ISchemeNode)m_PersonalDataMenuCenter, GameState.Init, 2.0f);
            Reg("所有帮助中心入口信息", (ISchemeNode)m_HelpCenterMenuCenter, GameState.Init, 2.0f);
            Reg("Icon加载", (ISchemeNode)m_IconCenter, GameState.Init, 2.0f);
            Reg("一些默认配置", (ISchemeNode)m_DefaultConfigCenter, GameState.Init, 2.0f);
            //////////////////////////////////////////////////////////////////////////////////////////////////////

            ///// 在【阶段： Game 主城游戏】 时要用的系统脚本在下面注册 /////////////////////////////////////


            ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

            Reg("效果数据:Effect", (ISchemeNode)m_SchemeEffect, GameState.Game, 1.0f);                    //  效果配置
            Reg("m_InstallExerciseCenter", (ISchemeNode)m_InstallExerciseCenter, GameState.Game, 1.0f);
            Reg("摄像机改变Transform", m_cameraTransformCenter, GameState.Game, 2.0f);
            Reg("摄像机改变Transform", m_operaAreaTransformCenter, GameState.Game, 2.0f);


            ////脚本默认在上面加载 /////////////////////////////////////////////////////////////////////////////////////////////////


        }

        #endregion // 注册所有配置脚本并加载



        #region // 取得配置脚本相关接口全在这


        public ISchemeEntity GetSchemeEntity()
        {
            return m_schemeEntity;
        }

        public IMapInfoCenter GetMapInfo()
        {
            return m_mapInfoCenter;
        }

        public IScenePointCenter GetScenePoint()
        {
            return m_scenePointCenter;
        }
        public ISchemeEntity GetEntityInfo()
        {
            return m_schemeEntity;
        }
        public IMonsterCenter GetMonsterInfo()
        {
            return m_MonsterInfoCenter;
        }

        public IIconCenter GetIconInfo()
        {
            return m_IconCenter;
        }
        public ISchemeEffect GetSchemeEffect()
        {
            return m_SchemeEffect;
        }

        public IInstallExerciseCenter GetInstallExercise()
        {
            return m_InstallExerciseCenter;
        }

        public IApparatusSelectCenter GetApparatusSelectCenter()
        {
            return m_apparatusSelectCenter;
        }

        public IOperationLog GetOperationLog()
        {
            return m_CoperationLog;
        }
        public IOperateCondition GetOperateCondition()
        {
            return m_OperateCondition;
        }
        public IAssistantNpcCenter GetAssistantNpcCenter()
        {
            return m_assistantNpcCenter;
        }

        public ICameraTransformCenter GetCameraTransformCenter()
        {
            return m_cameraTransformCenter;
        }

        public IOperaAreaTransformCenter GetOperaAreaTransformCenter()
        {
            return m_operaAreaTransformCenter;
        }

        public IHapticToolCenter GetHapticToolCenter()
        {
            return m_hapticToolCenter;
        }

        public IPersonalDataMenuCenter GetPersonalDataMenuCenter()
        {
            return m_PersonalDataMenuCenter;
        }

        public IHelpCenterMenuCenter GetHelpCenterMenuCenter()
        {
            return m_HelpCenterMenuCenter;
        }

        public IHandPoseCenter GetHandPoseCenter()
        {
            return m_handPoseCenter;
        }

        public IMenuHelp GetMenuHelp()
        {
            return m_cMenuHelp;
        }

        public ITaskInfo GetTaskInfo()
        {
            return m_ctaskInfo;
        }

        public IDepartment GetDepartment()
        {
            return m_DepartmentCenter ;
        }

        public ICourseDate GetCourseDate()
        {
            return m_CourseDataCenter;
        }

        public IDoctorDialogue GetDoctorDialogue()
        {
            return m_DoctorDialogue;
        }
        public IDoctorPreperative GetDoctorPreperativ()
        {
            return m_DoctorPreperativecs;
        }
        public IAnswerUI GetAnswerUI()
        {
            return m_AnswerUI;
        }

        public IToolTransform GetToolTransform()
        {
            return m_toolTransform;
        }

        public IScoreInfo GetScoreInfo()
        {
            return m_cScoreInfo;
        }

        public IScoreCondition GetScoreCondition()
        {
            return m_cScoreCond;
        }
        public IDefaultConfigCenter GetDefaultConfigCenter()
        {
            return m_DefaultConfigCenter;
        }

        /////////////////////////////////////////////////////////////////////////////////////////////////////////////
        #endregion // 取得配置脚本相关接口全在这

    }
}

