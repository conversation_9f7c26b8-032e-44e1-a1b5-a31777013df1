﻿/// <summary>
/// CourseHomepage
/// </summary>
/// <remarks>
/// 2023/02/07 15：00：00: 创建. 郭才志 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CAnswerUI : ISchemeNode, IAnswerUI
    {
        public const string Task_Info = "AnswerUIInfo";
        private Dictionary<int, Dictionary<int, AnswerUIInfo>> m_TaskInfoById;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Action<Dictionary<int, AnswerUIInfo>> m_action;
        private int m_courseId;
        private List<AnswerUIInfo> m_answerUIAllInfos;
        //查找AnswerUIInfo.csv属于TaskInfo.csv中的ID,不需要每一条数据都去找一次是否有主线任务,减少循环次数
        private List<AnswerUIInfo> m_answerUIInfosInTaskInfo;
        public CAnswerUI()
        {
            m_TaskInfoById = new Dictionary<int, Dictionary<int, AnswerUIInfo>>();
            m_answerUIAllInfos = new List<AnswerUIInfo>();
            m_answerUIInfosInTaskInfo = new List<AnswerUIInfo>();
        }
        ~CAnswerUI()
        {
        }
        public bool Create()
        {
            //if (!LoadScheme())
            //{
            //    return false;
            //}
            return true;
        }
        public bool LoadScheme(int CourseId, Action<Dictionary<int, AnswerUIInfo>> action = null)
        {
            m_action = action;
            m_courseId = CourseId;
            if (!m_TaskInfoById.ContainsKey(CourseId))
            {
                string strPath = "CourseCsv/" + CourseId + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<int, AnswerUIInfo> datas = new Dictionary<int, AnswerUIInfo>();
                m_TaskInfoById.TryGetValue(CourseId, out datas);
                foreach (var item in datas)
                {
                    if (!m_answerUIAllInfos.Contains(item.Value))
                    {
                        m_answerUIAllInfos.Add(item.Value);
                    }
                }
                List<OperateInfo> operateInfos = GlobalGame.Instance.SchemeCenter.GetTaskInfo().GetAllTaskInfos();
                for (int i = 0; i < m_answerUIAllInfos.Count; i++)
                {
                    OperateInfo operateInfo = operateInfos.Find(item => m_answerUIAllInfos[i].StepID == item.Id);
                    if (operateInfo != null)
                    {
                        if (!m_answerUIInfosInTaskInfo.Contains(m_answerUIAllInfos[i]))
                        {
                            m_answerUIInfosInTaskInfo.Add(m_answerUIAllInfos[i]);
                        }
                    }
                }

                m_action?.Invoke(datas);
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, AnswerUIInfo> datas = null;
            m_TaskInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, AnswerUIInfo>();
                m_TaskInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                AnswerUIInfo preperativeInfo = new AnswerUIInfo();

                preperativeInfo.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.StepID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.Issue = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.Text = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.ChooseSure = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.ChooseScore = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.NOIndicate = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.IsDialoguel= pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.IsSingleSelection = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.IsNextAnswer = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.NextStepID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.AnswerType = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.ScoreAliasInt = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.Branch =pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.IsOpenBranch = pCSVReader.GetString(nRow, tmp_col++, "");
                preperativeInfo.NextTaskID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                preperativeInfo.IconId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                datas.Add(preperativeInfo.ID, preperativeInfo);
            }
            m_action?.Invoke(datas);
            foreach (var item in datas)
            {
                if (!m_answerUIAllInfos.Contains(item.Value))
                {
                    m_answerUIAllInfos.Add(item.Value);
                }
            }
            List<OperateInfo> operateInfos = GlobalGame.Instance.SchemeCenter.GetTaskInfo().GetAllTaskInfos();
            for (int i = 0; i < m_answerUIAllInfos.Count; i++)
            {
                OperateInfo operateInfo = operateInfos.Find(item => m_answerUIAllInfos[i].StepID == item.Id);
                if (operateInfo != null)
                {
                    if (!m_answerUIInfosInTaskInfo.Contains(m_answerUIAllInfos[i]))
                    {
                        m_answerUIInfosInTaskInfo.Add(m_answerUIAllInfos[i]);
                    }
                }
            }
            return true;
        }


        public void Release()
        {
            m_answerUIAllInfos.Clear();
            m_answerUIInfosInTaskInfo.Clear();
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
        }

        public AnswerUIInfo GetAnswerInfoByID(int tID)
        {
            AnswerUIInfo info = null;
            Dictionary<int, AnswerUIInfo> datas = new Dictionary<int, AnswerUIInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            datas.TryGetValue(tID, out info);
            return info;
        }
        public List<AnswerUIInfo> GetAllAnswere(int StepID)
        {
            List<AnswerUIInfo> tempAll = new List<AnswerUIInfo>();
            Dictionary<int, AnswerUIInfo> datas = new Dictionary<int, AnswerUIInfo>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            foreach (var item in datas.Values)
            {
                if (item.StepID == StepID)
                {
                    tempAll.Add(item);
                }
            }
            return tempAll;
        }

        public OperateInfo GetZhuXianTaskByAliasName(string AliasName)
        {
            OperateInfo operateInfo = null;
            //先找到别名的支线任务
            OperateInfo operateInfoZhiXian = null;
            for (int i = 0; i < m_answerUIInfosInTaskInfo.Count; i++)
            {
                operateInfoZhiXian = null;
                if (!string.IsNullOrEmpty(m_answerUIInfosInTaskInfo[i].ScoreAliasInt))
                {
                    string[] AliasNames = m_answerUIInfosInTaskInfo[i].ScoreAliasInt.Split(';');
                    for (int j = 0; j < AliasNames.Length; j++)
                    {
                        if (AliasNames[j].Equals(AliasName))
                        {
                            operateInfoZhiXian = GlobalGame.Instance.SchemeCenter.GetTaskInfo().GetTaskInfoByID(m_answerUIInfosInTaskInfo[i].StepID);
                            break;
                        }
                    }
                }
                if (operateInfoZhiXian != null)
                {
                    break;
                }
            }
            if (operateInfoZhiXian == null)
            {
                //TRACE.TraceLn(string.Format("未找到任务别名:{0},请在后台或TaskInfo字段AliasName配置", AliasName));
                return null;
            }
            //支线任务找主线任务
            List<OperateInfo> m_TaskInfos = GlobalGame.Instance.SchemeCenter.GetTaskInfo().GetTaskInfos();
            for (int i = 0; i < m_TaskInfos.Count; i++)
            {
                if (!string.IsNullOrEmpty(m_TaskInfos[i].TaskFaID))
                {
                    string[] TaskFaIDs = m_TaskInfos[i].TaskFaID.Split(';');
                    for (int j = 0; j < TaskFaIDs.Length; j++)
                    {
                        if (int.Parse(TaskFaIDs[j]) == operateInfoZhiXian.Id)
                        {
                            return m_TaskInfos[i];
                        }
                    }
                }
                else
                {
                    if (operateInfoZhiXian.Id == m_TaskInfos[i].Id)
                    {
                        return m_TaskInfos[i];
                    }
                }
            }
            if (operateInfo == null)
            {
                TRACE.ErrorLn(string.Format("未找到包含子任务Id:{0}的主线任务", operateInfoZhiXian.Id));
                return null;
            }
            return operateInfo;
        }
    }
}
