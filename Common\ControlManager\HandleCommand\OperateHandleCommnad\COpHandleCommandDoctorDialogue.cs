﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandDoctorDialogue : IHandleCommand, IEventExecuteSink
    {
        // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private int m_dialogueId;
        private int m_needClick;
        private int stepId;

        private bool m_isOver;
        private bool skipDialogue;
        private List<IHandleCommand> m_others;
        public COpHandleCommandDoctorDialogue(SOpHandleCommand_DoctorDialogue data)
        {
            m_dialogueId = data.dialogueId;
            m_needClick = data.needClick;
            skipDialogue = data.skipDialogue;
            stepId = data.stepId;
            m_isPlay = false;
            m_isOver = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpDoctorDialogue;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)ViewLogicDef.EVENT_RENWENGUANHAI_OVER:
                    {
                        GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)ViewLogicDef.EVENT_RENWENGUANHAI_OVER, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                        m_isOver = true;
                    }
                    break;
                case (ushort)ViewLogicDef.EVENT_CLICK_RENWENGUANHUAI:
                    {
                        GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)ViewLogicDef.EVENT_CLICK_RENWENGUANHUAI, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                        int id = (int)pContext;
                        if (id == stepId && skipDialogue)
                        {
                            m_isOver = true;
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isOver = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)ViewLogicDef.EVENT_RENWENGUANHAI_OVER, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
                GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)ViewLogicDef.EVENT_CLICK_RENWENGUANHUAI, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
                if (m_needClick == 0)
                {
                    cmd_DoctorDialogueWindow cmd_Help = new cmd_DoctorDialogueWindow();
                    cmd_Help.wModel = WindowModel.DoctorDialogueWindow;
                    cmd_Help.aCommandState = AsyncCommandState.CreateCommmand;
                    cmd_Help.StepID = m_dialogueId;
                    GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CREATE_DOCTORDIALOGUEWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Help);
                }
                else
                {
                    GHelp.FireExecute((ushort)ViewLogicDef.EVENT_RWGH_SELECTION_YES, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 1, m_dialogueId);
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }
          

            if (m_isOver)
            {
                return true;
            }
            return false;
        }

        public void update()
        {
        }
    }
}
