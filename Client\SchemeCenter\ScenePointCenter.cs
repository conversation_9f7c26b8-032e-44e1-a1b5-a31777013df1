﻿/// <summary>
/// MapInfoCenter
/// </summary>
/// <remarks>
/// 2019.7.19: 创建. 谌安 <br/>
/// 地图信息中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class ScenePointCenter : ISchemeNode, IScenePointCenter
    {
        private const string MAP_INFO = "ScenePoint";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, List<ScenePointDef>> m_scenePointByID;

        private Dictionary<int, ScenePointDef> m_scenePointAll;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public ScenePointCenter()
        {
            m_scenePointByID = new Dictionary<int, List<ScenePointDef>>();
            m_scenePointAll = new Dictionary<int, ScenePointDef>();
        }

        ~ScenePointCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = MAP_INFO;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            List<ScenePointDef> scenePointDefs = null;
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                ScenePointDef map = new ScenePointDef();

                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.SceneName = pCSVReader.GetString(nRow, tmp_col++, "");
              
                map.SceneOtherPrefab = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SceneOtherPrefabPos = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SceneOtherPrefabRot = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SceneOtherPrefabScale = pCSVReader.GetString(nRow, tmp_col++, "");
                map.IsSceneLoadFinish = pCSVReader.GetString(nRow, tmp_col++, "");
                map.IsDefaultShow = pCSVReader.GetInt(nRow, tmp_col++, 1);
                map.IsPatient = pCSVReader.GetInt(nRow, tmp_col++, 0);

                m_scenePointByID.TryGetValue(int.Parse(map.SceneName), out scenePointDefs);
                if (scenePointDefs == null)
                {
                    scenePointDefs = new List<ScenePointDef>();
                    m_scenePointByID.Add(int.Parse(map.SceneName), scenePointDefs);
                }
                scenePointDefs.Add(map);

                m_scenePointAll.Add(map.Id, map);
            }
            return true;
        }

        public void Release()
        {
            m_scenePointByID.Clear();
            m_scenePointByID = null;
        }

        public List<ScenePointDef> GetScenePointByID(int tID)
        {
            List<ScenePointDef> info = null;
            m_scenePointByID.TryGetValue(tID, out info);

            return info;
        }

        public ScenePointDef GetScenePointSingleByID(int tID)
        {
            ScenePointDef scenePointDef;
            m_scenePointAll.TryGetValue(tID, out scenePointDef);
            return scenePointDef;
        }
    }
}
