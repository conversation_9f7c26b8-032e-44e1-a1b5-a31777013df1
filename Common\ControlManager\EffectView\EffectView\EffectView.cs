﻿/// <summary>
/// CEffectView
/// </summary>
/// <remarks>
/// 2021.4.26: 创建. 谌安 <br/>
/// 技能包装<br/>
/// </remarks>
using System;
using UnityEngine;
using System.Collections.Generic;
using game.schemes;

namespace GLib.Common
{
    public struct EffectViewExtraInfo
    {
        public UInt32 srcViewID;
        public UInt32 targetViewID;
        public UInt32 feedbackID;
    }

    public interface IEffectViewItem
    {
        void PlayEffect(UInt32 viewID, EffectView.Types.Item effectData, EffectViewExtraInfo extraInfo);

        void StopEffect(UInt32 viewID, EffectView.Types.Item effectData, EffectViewExtraInfo extraInfo);
    }
    public class CEffectView : IEffectView
    {
#if UNITY_EDITOR
        Dictionary<UInt32, List<UInt32>> m_mapViewID2EffectViewIDs = new Dictionary<UInt32, List<UInt32>>();

        private void AddEffect(UInt32 viewID, UInt32 effectID)
        {
            List<UInt32> effectViewLst = GetEffectViewLst(viewID, true);
            if (effectViewLst.Contains(effectID))
            {
                //TRACE.ErrorLn("重复添加 EffectView,viewID=" + viewID.ToString() + "effectID=" + effectID.ToString());
            }
            else
            {
                effectViewLst.Add(effectID);
            }
    
        }

        private void RemoveEffect(UInt32 viewID, UInt32 effectID)
        {
            List<UInt32> effectViewLst = GetEffectViewLst(viewID);
            if (effectViewLst == null || !effectViewLst.Remove(effectID))
            {
                //TRACE.ErrorLn("无效移除 EffectView,viewID=" + viewID.ToString() + "effectID=" + effectID.ToString());
            }
        }

        public List<UInt32> GetEffectViewLst(UInt32 viewID, bool createIfNotExist = false)
        {
            List<UInt32> rt = null;
            if (!m_mapViewID2EffectViewIDs.TryGetValue(viewID, out rt))
            {
                if (createIfNotExist)
                {
                    rt = new List<UInt32>();
                    m_mapViewID2EffectViewIDs.Add(viewID, rt);
                }
            }
            return rt;
        }
#endif
        List<IEffectViewItem> m_EffectViewItems;
        int effectItemNum = 0;

        public CEffectView()
        {
            m_EffectViewItems = new List<IEffectViewItem>
            {
                new EffectViewItem_LightingEffect(),
            };
            effectItemNum = m_EffectViewItems.Count;
        }

        public void PlayEffect(UInt32 viewID, UInt32 effectID, UInt32 srcViewID, UInt32 targetViewID, UInt32 feedbackID = 0)
        {
#if UNITY_EDITOR
            AddEffect(viewID, effectID);
#endif

            EffectView.Types.Item effectData = null;// GlobalGame.Instance.SchemeCenter.GetEffectViewCenter().GetEffectViewInfoByID((int)effectID);
            if (effectData == null)
            {
                TRACE.WarningLn("EffectView.csv=> EffectData=null: effectId = " + effectID.ToString());
                return;
            }
            EffectViewExtraInfo info = new EffectViewExtraInfo();
            info.srcViewID = srcViewID;
            info.targetViewID = targetViewID;
            info.feedbackID = feedbackID;

            for (int i = 0; i < effectItemNum; i++)
            {
                m_EffectViewItems[i].PlayEffect(viewID, effectData, info);
            }
        }

        /// <summary>
        /// 无实体特效
        /// </summary>
        /// <param name="effectID"></param>
        /// <param name="ptCenter"></param>
        /// <param name="parent"></param>
        public void PlayEffectNoEntity(uint effectID, Vector3 ptCenter, Transform parent = null)
        {
            EffectView.Types.Item effectData = null;// GlobalGame.Instance.SchemeCenter.GetEffectViewCenter().GetEffectViewInfoByID((int)effectID);
            if (effectData == null)
            {
                TRACE.WarningLn("EffectView.csv=> EffectData=null: effectId = " + effectID.ToString());
                return;
            }
            if (effectData.DoEffectList != null)
            {
                for (int i = 0; i < effectData.DoEffectList.Count; i++)
                {
                    GHelp.CreateLightingEffect((uint)effectData.DoEffectList[i], ptCenter, parent);
                }
            }
        }


        //技能效果(含角色动作)
        public void StopEffect(UInt32 viewID, int effectID, UInt32 feedbackID = 0)
        {
#if UNITY_EDITOR
            RemoveEffect(viewID, (uint)effectID);
#endif

            EffectView.Types.Item effectData = null;// GlobalGame.Instance.SchemeCenter.GetEffectViewCenter().GetEffectViewInfoByID((int)effectID);
            if (effectData == null)
            {
                TRACE.WarningLn("EffectView.csv=> EffectData=null: effectId = " + effectID.ToString());
                return;
            }

            EffectViewExtraInfo info = new EffectViewExtraInfo();
            info.feedbackID = feedbackID;

            for (int i = 0; i < effectItemNum; i++)
            {
                m_EffectViewItems[i].StopEffect(viewID, effectData, info);
            }
        }
    }
}
