﻿/// <summary>
/// CScoreInfo
/// </summary>
/// <remarks>
/// 2023/2/20 10:53:16: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CScoreInfo : ISchemeNode, IScoreInfo
    {
        public const string Task_Info = "ScoreInfo";
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Dictionary<int, Dictionary<string, ScoreInfo>> m_ScoreInfoByID;
        private Action<Dictionary<string, ScoreInfo>> m_curLoadFinishCallback;
        private int m_CourseId;
        public CScoreInfo()
        {
            m_ScoreInfoByID = new Dictionary<int, Dictionary<string, ScoreInfo>>();

        }
        ~CScoreInfo()
        {
        }

        public bool Create()
        {
            return true;
        }
        public bool LoadScheme(int courseID, Action<Dictionary<string, ScoreInfo>> action)
        {
            m_curLoadFinishCallback = action;
            m_CourseId = courseID;
            if (!m_ScoreInfoByID.ContainsKey(courseID))
            {
                m_ScoreInfoByID.Add(courseID, null);
                string strPath = "CourseCsv/" + courseID + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, courseID);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<string, ScoreInfo> datas = new Dictionary<string, ScoreInfo>();
                m_ScoreInfoByID.TryGetValue(courseID, out datas);
                m_curLoadFinishCallback?.Invoke(datas);
            }
            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<string, ScoreInfo> datas = null;
            m_ScoreInfoByID.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<string, ScoreInfo>();
                m_ScoreInfoByID[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                ScoreInfo scoreInfo = new ScoreInfo();

                scoreInfo.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                scoreInfo.TaskDec = pCSVReader.GetString(nRow, tmp_col++, "");

                scoreInfo.ConditionID = pCSVReader.GetString(nRow, tmp_col++, "");
                scoreInfo.ConditionType = pCSVReader.GetString(nRow, tmp_col++, "");
                scoreInfo.Alias = pCSVReader.GetString(nRow, tmp_col++, "");
                scoreInfo.Deduction = pCSVReader.GetInt(nRow, tmp_col++, 0);
                scoreInfo.Method = pCSVReader.GetString(nRow, tmp_col++, "");
                scoreInfo.TrueDescription = pCSVReader.GetString(nRow, tmp_col++, "");
                scoreInfo.AddScore = 0;

                if (datas.ContainsKey(scoreInfo.Alias))
                {
                    Debug.LogError("---策划注意--- ScoreInfo 重复得分别名alias = " + scoreInfo.Alias + " 步骤名称 = " + scoreInfo.name);
                }
                datas.Add(scoreInfo.Alias, scoreInfo);
            }

            m_curLoadFinishCallback?.Invoke(datas);
            return true;
        }
        public void Release()
        {

        }
    }
}