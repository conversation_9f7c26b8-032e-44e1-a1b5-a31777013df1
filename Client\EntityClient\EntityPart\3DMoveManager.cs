﻿/// <summary>
/// Person
/// </summary>
/// <remarks>
/// 2021.3.15: 创建. 谌安 <br/>
/// 客户端玩家3D移动控制 <br/>
/// </remarks>
using System;
using System.Collections.Generic;
using game.proto;
using Game.Entity;
using GLib.Common;
using UnityEngine;
namespace GLib.Client.Entity
{
    public class C3DMoveManager : ITimerHandler,I3DMoveManager
	{//移动位置上传时间间隔
		public const int MOVE_CHECK_INTERVAL = 300;
		//最小步数
		public const float MIN_STEP_DISTANCE = 0.01f;
		//一次同步路径节点的最大数
		public const float MAX_SYN_PATH_NODE_SIZE = 200;

		private bool m_bStartTimer = false;

		//定时器相关定义
		enum TimerDef
		{
			TimerID_Check = 0,                      //检测遥感状态定时器ID
			TimerID_Check_Interval = 50,            //检测遥感状态定时器时间间隔
		};

		// 人物实体
		private IEntity m_pMaster = null;

		//最近一次同步发包时间。
		private Int32 m_nLastSendTick = 0;

		//最近一次同步位置
		private Vector3 m_vLastSynPosition = Vector3.zero;

		//最近一次检测是否是在遥感控制状态
		private bool m_bLastJoyStickControl = false;

		//当前遥感控制标志
		private bool m_bOnJoyStickCtl = false;


		//首次移动标志
		private bool m_bFirstMoveFlag = false;
		private int m_lastJoystickCmd = (int)EMJoystickCmd.Joystick_Cmd_NONE;

		private bool m_bCancelNextJoystickStopCmd = false;

		/// <summary>
		/// 是否遥感控制标志
		/// </summary>
		public bool JoystickControl
		{
			get
			{
				return m_bOnJoyStickCtl;
			}
			set
			{
				m_bOnJoyStickCtl = value;
				if (!m_bLastJoyStickControl && m_bOnJoyStickCtl)
				{
					OnStartMove();
				}
				else if (m_bLastJoyStickControl && !m_bOnJoyStickCtl)
				{
					OnStopMove();
				}
				m_bLastJoyStickControl = m_bOnJoyStickCtl;
			}
		}

		public bool Create(IEntity entity)
		{
			if (entity == null)
				return false;
			m_pMaster = entity;

			return true;
		}

		public void Release()
		{
			IPerson person = m_pMaster as IPerson;
			if (person != null && person.IsHero())
			{
				GlobalGame.Instance.TimerManager.RemoveTimer((ITimerHandler)this, (int)TimerDef.TimerID_Check);
			}
			m_pMaster = null;
			m_nLastSendTick = 0;

			//最近一次同步位置
			m_vLastSynPosition = Vector3.zero;

			//最近一次检测是否是在遥感控制状态
			m_bLastJoyStickControl = false;

			//当前遥感控制标志
			m_bOnJoyStickCtl = false;

			//首次移动标志
			m_bFirstMoveFlag = false;
		}

		public void Restore()
		{
			Release();
		}

		public void OnTimer(TimerInfo ti)
		{
			switch (ti.timerId)
			{
				case (int)TimerDef.TimerID_Check:
					{
						OnMoveStateCheck();
						break;
					}
				default:
					{
						break;
					}

			}
		}

		/// <summary>
		/// 3D移动控制消息
		/// </summary>
		/// <param name="pGameMsgHead"></param>
		/// <param name="pszMsg"></param>
		public void OnMessage(SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			switch (pGameMsgHead.wKeyAction)
			{
				case DGlobalMessage.MSG_ACTION_3D_MOVE:
					{
						On3DMove(pGameMsgHead, pszMsg);
					}
					break;
				default:
					{
						TRACE.ErrorLn("3DMoveManager::OnMessage 未处理的消息 ModuleID=" + pGameMsgHead.wKeyModule + "actionID=" + pGameMsgHead.wKeyAction);
					}
					break;
			}
		}

		
		/// <summary>
		/// MSG_ACTION_3D_MOVE消息处理函数
		/// </summary>
		/// <param name="pGameMsgHead"></param>
		/// <param name="pszMsg"></param>
		private void On3DMove(SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			if (pszMsg == null)
			{
				TRACE.ErrorLn("3DMoveManager::On3DMove error pszMsg == null");
				return;
			}

			byte bFlag = 0;
			pszMsg.Pop(out bFlag);
			if ((bFlag & (int)EM3DMoveFlag.EM3DMoveFlag_FollowPath) == 0)
			{
				UInt16 wPathLen = 0;
				pszMsg.Pop(out wPathLen);
				UInt32 dwMovedTime = 0;
				pszMsg.Pop(out dwMovedTime);
				if (wPathLen == 0)
				{
					TRACE.WarningLn("3DMoveManager::On3DMove wPathLen == 0");
					return;
				}

				List<Vector3> pathList = new List<Vector3>();
				for (UInt16 i = 0; i < wPathLen; i++)
				{
					Vector3 pos = default(Vector3);
					pszMsg.Pop(out pos.x);
					pszMsg.Pop(out pos.y);
					pszMsg.Pop(out pos.z);
					pathList.Add(pos);

				}
#if OpenDebugInfo_3DMoveMGR
				TRACE.TraceLn("3DMoveCmd::On3DMove 获取移动列表 Flag=" + bFlag + ",PathLen = " + wPathLen);
				for (UInt16 i = 0; i < pathList.Count; i++)
				{
					TRACE.TraceLn("第" + i + "个位置点=" + pathList[i]);
				}
#endif

				//通知表现层，按路径行走
				cmd_MovePos data = new cmd_MovePos();
				//data.nCount = (int)wPathLen;
				data.listPath = pathList;
				data.bFlag = bFlag;
				// 发送命令的时间
				data.fSendCmdTime = Time.realtimeSinceStartup;

				//延时最大值
				int nMaxLatency = 300;
				// 两点短距离，延时最大值
				if (pathList.Count == 2)
				{
					nMaxLatency = 90;
				}

				// 服务器已经移动的时间
				data.fMovedTime = dwMovedTime / 1000.0f + Math.Min(nMaxLatency, GlobalGame.Instance.NetManager.GetLatency()) / 1000.0f;
				// 帧同步方式稍微走慢一点，补偿少一点,就不会出现一走一停的问题
				if ((bFlag & (int)EM3DMoveFlag.EM3DMoveFlag_ServerFrameSyn) > 0)
				{
					//data.fMovedTime -= 0.05f;
					if (data.fMovedTime < 0)
						data.fMovedTime = 0.0f;
				}

				GHelp.sendEntityCommand(m_pMaster.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_MOVE_POS, 0, "", data);


				if (m_pMaster.GetEntityClass().IsPerson())
				{
					IPerson person = m_pMaster as IPerson;
					if (person != null && person.IsHero())
					{
						//cmd_AsyncBase info = GHelp.GetObjectItem<cmd_AsyncBase>();
						//GHelp.sendUIAysncCommand(WindowModel.MapWindow_Map, (ushort)GameLogicDef.GVIEWCMD_TOVIEW_MOVE_POS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, info);
					}
				}
			}
			else
			{
				//todo
			}
		}

		private void OnStartMove()
		{
			m_nLastSendTick = 0;
			m_bFirstMoveFlag = true;
			OnMoveStateCheck();
			//发送开始移动事件
			GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_CREATURE_STARTMOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_PERSON, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_pMaster.GetUID()), null);

			IPerson person = m_pMaster as IPerson;
			if (person != null && person.IsHero() && !m_bStartTimer)
			{
				//添加定时器
				GlobalGame.Instance.TimerManager.AddTimer((ITimerHandler)this, (int)TimerDef.TimerID_Check, (int)TimerDef.TimerID_Check_Interval, "C3DMoveManager::Create");
				m_bStartTimer = true;
			}
		}

		private void OnStopMove()
		{
			SendPosSynMessage(EMJoystickCmd.Joystick_Cmd_STOP_MOVE);
			IPerson person = m_pMaster as IPerson;
			if (person != null && person.IsHero() && m_bStartTimer)
			{
				//删除定时器
				GlobalGame.Instance.TimerManager.RemoveTimer((ITimerHandler)this, (int)TimerDef.TimerID_Check);
				m_bStartTimer = false;
			}
		}

		/// <summary>
		/// 检测移动状态变化
		/// </summary>
		private void OnMoveStateCheck()
		{
			if (!m_bOnJoyStickCtl)
				return;
			int nCurTick = Api.GetTickCount();

			//判断位置同步时间
			if (nCurTick - m_nLastSendTick < MOVE_CHECK_INTERVAL)
			{
				return;
			}
			SendPosSynMessage(EMJoystickCmd.Joystick_Cmd_MOVE);
		}

		private void SendPosSynMessage(EMJoystickCmd nCmd)
		{

			if (m_bCancelNextJoystickStopCmd)
			{
				m_bCancelNextJoystickStopCmd = false;
				if (nCmd == EMJoystickCmd.Joystick_Cmd_STOP_MOVE)
				{
					//TRACE.ErrorLn("当前结束遥感移动命令被取消");
					return;
				}
			}


			bool bOnDriver = false;
			IPerson hero = m_pMaster as IPerson;
			if (hero == null)
				return;
			Vector3 pos = Vector3.zero;

			IPersonTankPart pTankPart = (IPersonTankPart)hero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
			if (pTankPart == null)
			{
				return;
			}

			// 判断玩家是否在载具上面
			if (pTankPart.IsOnTank())
			{
				//不是驾驶员,不能移动
				if (pTankPart.IsOnDriver())
				{
					ITank tank = pTankPart.GetTank();
					if (tank != null)
					{
						pos = tank.GetPosition();
						if (Mathf.Abs(pos.x) < 0.00001f && Mathf.Abs(pos.z) < 0.00001f)
						{
							TRACE.ErrorLn("严重错误!!3DMoveManager::SendPosSynMessage 遥感移动载具，获取载具位置非法坐标 发送载具坐标数据失败 pos=" + pos);
							return;
						}
						bOnDriver = true;
					}


				}
			}

			// 取玩家的位置
			if (!bOnDriver)
			{
				pos = hero.GetPosition();
				if (Mathf.Abs(pos.x) < 0.00001f && Mathf.Abs(pos.z) < 0.00001f)
				{
					TRACE.ErrorLn("严重错误!! 3DMoveManager::SendPosSynMessage 遥感移动主角，获取主角位置非法坐标 发送主角坐标数据失败 pos=" + pos);
					return;
				}
			}

			if (m_bFirstMoveFlag && nCmd == EMJoystickCmd.Joystick_Cmd_MOVE)
			{
				nCmd = EMJoystickCmd.Joystick_Cmd_START_MOVE;
				//开始移动
				m_bFirstMoveFlag = false;
			}
			//避免重复发送结束指令，如果发送无效的结束指令服务器会强制拉回来
			if (m_lastJoystickCmd == (int)nCmd && nCmd == EMJoystickCmd.Joystick_Cmd_STOP_MOVE)
			{
				m_nLastSendTick = Api.GetTickCount();
				return;
			}

			SMsgActionJoystick3DMove_CS msgJoystickCmd = default(SMsgActionJoystick3DMove_CS);
			//移动命令类型
			msgJoystickCmd.bMoveCmd = (byte)nCmd;
			msgJoystickCmd.fPosx = pos.x;
			msgJoystickCmd.fPosy = pos.y;
			msgJoystickCmd.fPosz = pos.z;
			Vector3 vCurForward = m_pMaster.GetForward();
			//实体角度
			msgJoystickCmd.fAngle = Function.Vector2AngleWithXAxis(vCurForward.x, vCurForward.z);

			//如果是载具驾驶员，移动消息转到人物PersonTankPart处理.
			if (bOnDriver)
			{
				pTankPart.DriverTankMoveJoystick(msgJoystickCmd);
			}
			else
			{
				if (nCmd == EMJoystickCmd.Joystick_Cmd_MOVE || nCmd == EMJoystickCmd.Joystick_Cmd_STOP_MOVE)
				{
					List<Vector3> pathList = new List<Vector3>();
					pathList.Add(m_vLastSynPosition);
					pathList.Add(pos);
					byte bFlag = (byte)EM3DMoveFlag.EM3DMoveFlag_ServerFrameSyn;
					MoveTo(pathList, bFlag, false);
				}
			}
			m_vLastSynPosition = pos;
			m_lastJoystickCmd = (int)nCmd;
			m_nLastSendTick = Api.GetTickCount();
		}

		/// <summary>
		/// 按路径移动
		/// </summary>
		/// <param name="pathList"></param>
		public bool MoveTo(List<Vector3> pathList, byte bFlag, bool bFireEvent = true)
		{
			if (!SendNavMeshPathMessage(pathList, bFlag))
				return false;

			if (bFireEvent)
			{
				//发送开始移动事件
				GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_CREATURE_STARTMOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_PERSON, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_pMaster.GetUID()), null);
			}
			return true;
		}
		/// <summary>
		/// 发送导航数据消息
		/// </summary>
		/// <param name="pathList"></param>
		private bool SendNavMeshPathMessage(List<Vector3> pathList, byte bFlag)
		{
			//首先判断pathList的大小,如果超过阈值，则分段发送
			if (pathList.Count > MAX_SYN_PATH_NODE_SIZE)
			{
				//todo
				TRACE.ErrorLn("发送移路径节点给服务器 nPathNode >" + MAX_SYN_PATH_NODE_SIZE + ",发送失败");
				return false;
			}
			else
			{
				MCSyncPos_CS cs = new MCSyncPos_CS();

				string syncPos = string.Format("{0}:{1}:{2}", m_pMaster.GetPosition().x, m_pMaster.GetPosition().y, m_pMaster.GetPosition().z);
				cs.Properties.Add("SyncPosition", syncPos);
				cs.TargetId = m_pMaster.GetStrGUID();
				//回调的消息头
				SGameMsgHead head = new SGameMsgHead();
				head.SerialNumber = GHelp.GenSerialNumber();
				head.SrcEndPoint = (int)ENDPOINT.Appclient;
				head.DestEndPoint = (int)ENDPOINT.Scene;
				head.wKeyModule = (int)MSG_MODULEID.Entity;
				head.wKeyAction = (int)MessageCodesOfEntity.CodeMcsyncPosCs;

				//把消息头压进发送栈
				CPacketSend packet = new CPacketSend();
				packet.Push(head);
				//回调的协议

				//把协议压进发送栈
				packet.PushPB<MCSyncPos_CS>(cs);
				//发送
				GlobalGame.Instance.NetManager.SendMessage(packet);
				return true;
			}
		}
	}
}
