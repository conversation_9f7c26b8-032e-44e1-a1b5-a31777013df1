﻿/// <summary>
/// TaskDef
/// </summary>
/// <remarks>
/// 2021/7/23 16:03:39: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// TaskDef
    /// </summary>
    public class TriggerTaskDef
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int taskId;
        /// <summary>
        /// 是否重置任务
        /// </summary>
        public bool IsRestTask;
    }
    public enum TaskType
    {
        /// <summary>
        /// 无
        /// </summary>
        None,
        /// <summary>
        /// 触发任务
        /// </summary>
        TaskStrike,
        /// <summary>
        /// 接受任务
        /// </summary>
        ReceiveTask,
        /// <summary>
        /// 开始任务
        /// </summary>
        TaskProceed,
        /// <summary>
        /// 任务结束
        /// </summary>
        TaskOver,
        /// <summary>
        /// 任务结算
        /// </summary>
        TaskSettle,
        /// <summary>
        /// 任务失败
        /// </summary>
        TaskLose,

    }
    public class TaskRunState
    {
        /// <summary>
        /// 是否进行中
        /// </summary>
        public bool m_IsTaskRun;
        /// <summary>
        /// 进行任务ID
        /// </summary>
        public int m_RunTaskId;
        public void Rest()
        {
            m_IsTaskRun = false;
            m_RunTaskId = 0;
        }
    }


    public class TaskGuideTipsInfo
    {
        /// <summary>
        /// 提示语音
        /// </summary>
        public int TipsVoiceId;
        /// <summary>
        /// 提示图片Id
        /// </summary>
        public int TipsImageId;
    }
    public class TaskGuideDef
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId;
        public int Priority;
        public List<TipsImageGuide> listTipsImageGuide;
        public TipsBlockGuide tipsBlockGuide;
        public ReplaceBlockGuide replaceBlockGuide;
        /// <summary>
        /// 引导点击任务UI的语言
        /// </summary>
        public int GuideClickTaskVoiceId;
    }
    public class TipsImageGuide
    {
        /// <summary>
        /// 提示图片ID
        /// </summary>
        public int TipsImgId;
        /// <summary>
        /// 提示错误语音
        /// </summary>
        public int TipsImgVoiceId;
        public int Priority;
    }
    public class TipsBlockGuide
    {
        public int Priority;
        /// <summary>
        /// 提示积木块组ID
        /// </summary>
        public int TaskId;
        /// <summary>
        /// 提示积木块组语音ID
        /// </summary>
        public int TipsBlockVoiceId;

    }
    public class ReplaceBlockGuide
    {
        /// <summary>
        /// 任务引导ID
        /// </summary>
        public int TaskGuidId;
        /// <summary>
        /// 直接替换积木块ID
        /// </summary>
        public int tipsReplaceTaskId;
        /// <summary>
        /// 提醒语音
        /// </summary>
        public int tipsFreeReplaceVoiceId;
        /// <summary>
        /// 消耗次数
        /// </summary>
        public int ConsumeNum;
        
        /// <summary>
        /// 消耗金币
        /// </summary>
        public int ConsumeGold;
        /// <summary>
        /// 替换内容
        /// </summary>
        public string ReplaceContent;
        /// <summary>
        /// 替换完成引导图ID
        /// </summary>
        public int ReplaceOverImageId;
        /// <summary>
        /// 替换完成播放语音ID
        /// </summary>
        public int ReplaceOverVoiceId;
        public int Priority;
    }

    public class TaskIssueDef
    {
        /// <summary>
        /// 发布音频
        /// </summary>
        public int IssueVoiceId;
        /// <summary>
        /// 发布标题
        /// </summary>
        public string IssueTitle;
        /// <summary>
        /// 任务目的
        /// </summary>
        public string TaskTarget;
        /// <summary>
        /// 任务内容
        /// </summary>
        public string TipsContent;
    }

    public enum TaskOperateEnum
    {
        None = 0,
        /// <summary>
        /// 拉帘
        /// </summary>
        DrawCurtain,
        /// <summary>
        /// UI触发
        /// </summary>
        UIClick_Opera,
        /// <summary>
        /// 手触碰并且按键
        /// </summary>
        TouchAndClick,
        /// <summary>
        /// 工具触碰扣扳机
        /// </summary>
        ToolsTouchAndTrigger,
        /// <summary>
        /// 切换体位
        /// </summary>
        ChangeTiWei,
        /// <summary>
        /// 点位选择
        /// </summary>
        PointChoose,
        /// <summary>
        /// 消毒
        /// </summary>
        Disinfect,
        /// <summary>
        /// 铺洞巾
        /// </summary>
        PuDongJin,
        /// <summary>
        /// 穿刺
        /// </summary>
        Puncture,
        /// <summary>
        /// 物品回收
        /// </summary>
        ItemRecycling,
        /// <summary>
        /// 人文关怀
        /// </summary>
        RenWenGuanHuai,

        /// <summary>
        /// 分数
        /// </summary>
        Score,
        /// <summary>
        /// 患者选择
        /// </summary>
        CaseSelect,
        /// <summary>
        /// 对话
        /// </summary>
        Dialogue,
        /// <summary>
        /// 护士对话
        /// </summary>
        NPCDialogue,
        /// 穿刺固定皮肤
        /// </summary>
        GuDingPiFu,

        /// <summary>
        /// 穿刺多少层(0层代表进针点)
        /// </summary>
        ChuanCiCeng,
        /// <summary>
        /// 左手抓取无菌棉球按压穿刺部位
        /// </summary>
        WuJunMianQiu,
        /// <summary>
        /// 手指消毒
        /// </summary>
        FingerDisnfect,
        /// <summary>
        /// 穿刺橡皮塞
        /// </summary>
        PunctureXiangPiSai,
        /// <summary>
        /// 穿刺检查样本
        /// </summary>
        PunctureJianChaYangBen,
        /// <summary>
        /// 物品选择
        /// </summary>
        ApparatusSelect,
        /// <summary>
        /// 回收物品
        /// </summary>
        RecycleGoods,
        /// <summary>
        /// 分数
        /// </summary>
        ScoreUI = 25,
        /// <summary>
        /// 灯
        /// </summary>
        Deng,
        /// <summary>
        /// 空调
        /// </summary>
        KongTiao,
        /// <summary>
        /// 病历本
        /// </summary>
        BingliBen,
        /// <summary>
        /// 体温计
        /// </summary>
        TiWenJi,
        /// <summary>
        /// 术前传送点
        /// </summary>
        ShuQianZhunBeiChuanSongDian,
        /// <summary>
        /// 血压仪
        /// </summary>
        XueYaYi,
        /// <summary>
        /// 帽子和口罩
        /// </summary>
        HatAndKouZhao,

        /// <summary>
        /// 手环
        /// </summary>
        Bracelet,
        /// <summary>
        /// 冰箱
        /// </summary>
        BingXiang,
        /// <summary>
        /// 手套
        /// </summary>
        Glove,
        /// <summary>
        /// 洗手
        /// </summary>
        WashHand,
        /// <summary>
        /// 体格检查
        /// </summary>
        PhysicalExamination,
        /// <summary>
        /// 答题
        /// </summary>
        OperateAnswerUI,

        /// <summary>
        /// 标本送检
        /// </summary>
        BiaoBenSongJian,
        /// <summary>
        /// 器械检查
        /// </summary>
        ApparatusCheck,
        /// <summary>
        /// 清洗针管
        /// </summary>
        ZhenGuanCheck,
        /// 消毒送检
        /// </summary>
        DisinfectManager,
        /// 点击5ml注射器
        /// </summary>
        OperateZhenGuan,
        /// 查看病历本
        /// </summary>
        CheckCase,
        /// 点击目标事件病历本
        /// </summary>
        TouchAndGo,
        /// 体位选择
        /// </summary>
        TiWeiXuanZe,
        /// 穿刺答题
        /// </summary>
        ChuanCiAnswer,
        /// <summary>
        /// 纱布
        /// </summary>
        ShaBuCheck,
        /// <summary>
        /// 选择器械
        /// </summary>
        TouchTool,
        /// <summary>
        /// 消毒术者
        /// </summary>
        DisinfectPerformer,
        /// <summary>
        /// 抽血
        /// </summary>
        ChouXue,
        /// <summary>
        /// 纱布按压
        /// </summary>
        ShaBuAnYa,
        /// <summary>
        /// 器械选择
        /// </summary>
        SelectTool,
        /// <summary>
        /// 查看腕带
        /// </summary>
        CheckWanDai,
        /// <summary>
        /// 靠近床边
        /// </summary>
        KaoJinChuangBian,
        /// <summary>
        /// 点击移动
        /// </summary>
        TouchAndClickMove,
        /// <summary>
        /// 关门
        /// </summary>
        GuanMen,
        /// <summary>
        /// 开灯
        /// </summary>
        KaiDeng,
        /// <summary>
        /// 开空调
        /// </summary>
        KaiKongTiao,
        /// <summary>
        /// 戴口罩
        /// </summary>
        DaiKouZhao,
        /// <summary>
        /// 戴帽子
        /// </summary>
        DaiMaoZhi,
        /// <summary>
        /// 洗手
        /// </summary>
        XiShou,
        /// <summary>
        /// 戴无菌手套
        /// </summary>
        DaiWuJunShouTao,
        /// <summary>
        /// 消毒选择器械
        /// </summary>
        SelectXiaoDuTool,
        /// <summary>
        /// 检查肝素
        /// </summary>
        GanSuCheck,
        AngleSelect,
        /// <summary>
        /// 人文关怀
        /// </summary>
        RenWenGuanHuaiCS,

        /// <summary>
        /// 仪容仪表
        /// </summary>
        YiRongYiBiao,
        ZhiQingTongYiShu,
        /// <summary>
        /// 展开穿刺包
        /// </summary>
        OpenPuncturePackage,
        /// <summary>
        /// 打开无菌显示器
        /// </summary>
        OpenWuJunZhiShiQi,
        /// <summary>
        /// 蘸取碘伏
        /// </summary>
        MianqianAddIodinem,
        /// <summary>
        /// 胸腔消毒
        /// </summary>
        XiongQiangXiaoDu,
        /// <summary>
        /// 胸腔点位叩诊
        /// </summary>
        XiongQiangDianWeiKouZhen,
        /// <summary>
        /// 器械检查
        /// </summary>
        EquiomentInspection,
        /// <summary>
        /// 消毒范围
        /// </summary>
        XiaoDuFanWei,
        /// <summary>
        /// 抽取利多卡因
        /// </summary>
        SuckLiDuoKaYin,
        /// 胸腔点位标记
        /// </summary>
        XiongQiangDianWeiBiaoJi,
        /// <summary>
        /// 夹闭器
        /// </summary>
        JiaBiQi,
        /// <summary>
        /// 左手固定
        /// </summary>
        LeftHandFixation,
        /// <summary>
        /// 打皮丘
        /// </summary>
        DaPiQiu,
        /// <summary>
        /// 消毒与铺巾-铺无菌巾
        /// </summary>
        XiaoPu_PuJin,
        /// <summary>
        /// 消毒与铺巾-展开无菌巾
        /// </summary>
        XiaoPu_ZhanKaiWuJunJin,
        /// <summary>
        /// 麻醉
        /// </summary>
        ZhenGuan_MaZui,
        /// <summary>
        /// 消毒与铺巾-选择操作位置
        /// </summary>
        XiaoPu_CaoZuoWeiZhi,
        /// <summary>
        /// 麻醉拔针
        /// </summary>
        MaZuiBaZhen,
        /// 手术衣问答
        /// </summary>
        OperateAnswerUIShouShuYi,
        /// <summary>
        /// 选择棉签包
        /// </summary>
        MianQianBao,
        /// <summary>
        /// 静脉穿刺点按压
        /// </summary>
        ChuanCiDianAnYa,
        /// <summary>
        /// 标本送检
        /// </summary>
        BiaoBenSongJian_XQCC,
        XueYaYi_SZGZ,
        /// <summary>
        /// 长按操作
        /// </summary>
        LongPressOperate,
        XiaoTuiXiaoDu,
        /// <summary>
        /// 消毒与铺巾-铺中单
        /// </summary>
        XiaoPu_PuZhongDan,

        XDPJ_XiaoDu,
        /// <summary>
        /// 消毒与铺巾-展开大单
        /// </summary>
        XiaoPu_ZhanKaiDaDan,

        /// <summary>
        /// 清创术-洗刷
        /// </summary>
        QCS_XiShua,
        /// <summary>
        /// 清创术-血压仪
        /// </summary>
        QCS_XueYaYi,

        /// <summary>
        /// 补留白
        /// </summary>
        BuLiuBai,

        /// <summary>
        /// 胸腔消毒
        /// </summary>
        GuSuiXiaoDu,
        /// <summary>
        /// 心肺复苏-胸外按压
        /// </summary>
        XiongWaiAnYa,
        /// <summary>
        /// 手术基本操作缝合
        /// </summary>
        FenHe,
        /// <summary>
        /// 导尿术消毒
        /// </summary>
        DaoNiaoXiaoDu,
        /// <summary>
        /// 环境准备(开灯开空调)
        /// </summary>
        HuanJingZhunBei,
        /// <summary>
        /// 戴手套或消毒（动脉穿刺消毒术者和戴手套二选一）
        /// </summary>
        ShouTaoHuoXiaoDu,
        /// <summary>
        /// 吸氧术查看腕带需要新增后台没有的字段，先写死特殊处理
        /// </summary>
        ChaKanWanDai_XiYangShu,

        /// <summary>
        /// 吸氧术调节氧流量
        /// </summary>
        TiaoJieYangLiuLiang,
        /// <summary>
        /// 重写吸氧术插鼻导管任务
        /// </summary>
        XiYangShu_BiDaoGuan,
        YiXueSuYang,
        ShuHouZongJie,
        NieQiNang,
        //-------------------------------------通用系统，包含移动，动画，回调等（持续增加功能）------------
        /// <summary>
        /// 通用组合
        /// </summary>
        Currency_Combination =400,
        //-----------------------------------------      不往后面增加
        //-----------------------------------------      不往后面增加
        //-----------------------------------------      不往后面增加
        //-----------------------------------------      不往后面增加
    }

    public enum OperateFinishEnum
    {
        None=0,
        /// <summary>
        /// 切换场景
        /// </summary>
        ChangeScene,
        /// <summary>
        /// 移动位置
        /// </summary>
        ChangeHero,
        /// <summary>
        /// 切换上一个场景
        /// </summary>
        ChangeLastScene,
        /// <summary>
        /// 播放动画
        /// </summary>
        PlayerAnim,
    }
    public enum TaskStatues
    {
        None = 0,
        /// <summary>
        /// 任务开始
        /// </summary>
        TaskBegin,
        /// <summary>
        /// 任务完成
        /// </summary>
        TaskFinish,
    }
    [Serializable]

    public class ScoreStepInfo
    {
        /// <summary>
        /// 阶段名称
        /// </summary>
        public int ScoreStepID;
        /// <summary>
        /// 序列号
        /// </summary>
        public int Order;
        /// <summary>
        /// 完成数
        /// </summary>
        public int Num;
        /// <summary>
        /// 总数
        /// </summary>
        public int TotalNum;
    }
    //任务数据
    [Serializable]

    public class OperateInfo
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int Id;
        /// <summary>
        /// 任务阶段
        /// </summary>
        public int TaskStep;
        /// <summary>
        /// 是否上传
        /// </summary>
        public int TaskUpdate;
        /// <summary>
        /// 任务支线
        /// </summary>
        public int Branchline;
        /// <summary>
        /// 任务前置ID
        /// </summary>
        public string TaskFaID;
        /// <summary>
        /// 任务描述
        /// </summary>
        public string TaskDecribe;
        /// <summary>
        /// 任务模型
        /// </summary>
        public string TaskModel;
        /// <summary>
        /// 任务模型Id
        /// </summary>
        public string TaskModelId;
        /// <summary>
        /// 使用工具
        /// </summary>
        public string TaskUseTool;
        /// <summary>
        ///  任务操作
        /// </summary>
        public string TaskOperate;
        /// <summary>
        /// 任务操作数据
        /// </summary>
        public string TaskOperateDate;
        /// <summary>
        /// 任务动画
        /// </summary>
        public string TaskAnim;
        /// <summary>
        /// 任务动画时间
        /// </summary>
        public string TaskAnimTime;
        /// <summary>
        ///  任务完成操作
        /// </summary>
        public string TaskFinishOpera;
        /// <summary>
        /// 任务完成操作数据
        /// </summary>
        public string TaskFinishOperaDate;
        /// <summary>
        /// 后置任务
        /// </summary>
        public string TaskFollowUp;
        /// <summary>
        /// 对话
        /// </summary>
        public int Dialogue;
        /// <summary>
        /// 任务别名
        /// </summary>
        public string AliasName;
        /// <summary>
        /// 多个操作物体是否有顺序的执行0否1有
        /// </summary>
        public int IsOrder;
        /// <summary>
        /// 该步骤操作完成类型
        /// 0:所有操作物体都需要Finish 1:多个操作物体只有一个正确的Finish就算完成
        /// 配置1_2 代表第3个操作物体完成就算步骤完成
        /// </summary>
        public string FinishType;
        /// <summary>
        /// 辅助NPC需要激活的ID
        /// </summary>
        public string AssistantNPCID;
        /// <summary>
        /// 正确错误语音
        /// </summary>
        public string RightAndWrongVoice;
        /// <summary>
        /// 主角移动的坐标和角度Id
        /// </summary>
        public int HeroMove;
        /// <summary>
        /// 无序考核模式下的前置任务ID
        /// </summary>
        public string ExamPredecessorsID;
        /// <summary>
        /// 任务结束时需要自动执行的主角移动的坐标和角度Id
        /// </summary>
        public int TaskEndHeroMove;
        /// <summary>
        /// 考核模式提示时显示的条目
        /// </summary>
        public string TaskTipDecribe;
        /// <summary>
        /// 考核模式任务完成的提示内容，不填就不提示
        /// </summary>
        public string TaskFinshDec;
        /// <summary>
        /// 考核模式进行提示的时候需要激活的辅助NPC ID
        /// </summary>
        public string TaskTipNPCID;
        
       
    }
    #region 对话
    public class DialogueInfo
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 步骤ID
        /// </summary>
        public int StepID;
        /// <summary>
        /// 步骤状态
        /// </summary>
        public int StepStaus;
        /// <summary>
        /// 人员身份
        /// </summary>
        public int PerosnIdentity;
        /// <summary>
        /// 人员名称
        /// </summary>
        public string PerosnName;
        /// <summary>
        /// 对话内容
        /// </summary>
        public string Meassage;
        /// <summary>
        /// 选择题ID
        /// </summary>
        public string QuestionID;

        /// <summary>
        /// 音频ID
        /// </summary>
        public int AudioID;

        /// <summary>
        /// 单个延迟时间
        /// </summary>
        public int LaterTime;
    }

    public enum PerosnIdentity
    {
        None = 0,
        /// <summary>
        /// 医生
        /// </summary>
        Doctor,
        /// <summary>
        /// 患者--李先生
        /// </summary>
        Patient,
    }
    #endregion
    
    [Serializable]

    public class DepartData
    {
        /// <summary>
        /// 科室id
        /// </summary>
        public int id;
        /// <summary>
        /// 手术名字
        /// </summary>
        public string name;
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool avaliable;
        /// <summary>
        /// 科室名字
        /// </summary>
        public string departmentName;
        /// <summary>
        /// PC、VR、Android
        /// </summary>
        public string platform;
        /// <summary>
        /// 项目种类
        /// </summary>
        public int projectKind;
        /// <summary>
        /// 是否是精品课程
        /// </summary>
        public bool isElite;


        /// <summary>
        /// 课程表
        /// </summary>
        public CourseData Cfg = null;

        public CourseData GetCourseDataCfg()
        {
            if (Cfg == null)
                Cfg = GlobalGame.Instance.SchemeCenter.GetCourseDate().GetCourseDateByID(id);
            return Cfg;
        }
    }

    [Serializable]
    public class CourseData 
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id;
        /// <summary>
        /// 课程名称
        /// </summary>
        public string CourseName;
        /// <summary>
        /// 课程介绍
        /// </summary>
        public string CourseIntroduction;
        /// <summary>
        /// 课程难度
        /// </summary>
        public int CourseDifficulty;
        /// <summary>
        /// 课程图片路径
        /// </summary>
        public string CourseImage;
        /// <summary>
        /// 病例选择场景ID
        /// </summary>
        public int CaseSelectScene;
        /// <summary>
        /// 操作场景ID
        /// </summary>
        public int CaseOperateScene;
        /// <summary>
        /// 病例模块(1代表有0代表没有)
        /// </summary>
        public int CaseModule;
        /// <summary>
        /// 术前模块(同上) 
        /// </summary>
        public int PreoperativeModule;
        /// <summary>
        /// 物品选择模块(同上) 
        /// </summary>
        public int ModelSelectModule;
        /// <summary>
        /// 操作模块(同上)
        /// </summary>
        public int OperateModule;
        /// <summary>
        /// 物品回收模块(同上)
        /// </summary>
        public int ModelRecycleModule;
        /// <summary>
        /// 术后模块(同上)
        /// </summary>
        public int PostoperativeModule;
        /// <summary>
        /// 是否开启
        /// </summary>
        public int IsOpen;
        /// <summary>
        /// 病例个数
        /// </summary>
        public int CaseNum;
        /// <summary>
        /// 公司logo
        /// </summary>
        public string[] LogoName;
        /// <summary>
        /// 医院名字
        /// </summary>
        public string[] HospitalName;
        /// <summary>
        /// 小手术名字
        /// </summary>
        public string[] SmallOperationName;
        /// <summary>
        /// 概述
        /// </summary>
        public string Summary;
        /// <summary>
        /// 考点
        /// </summary>
        public string ExaminationSite;
        /// <summary>
        /// 小手术图片名
        /// </summary>
        public string[] SmallOperationImage;
        /// <summary>
        /// 训练场景ID
        /// </summary>
        public string[] TrainScnenID;
        /// <summary>
        /// 模考场景ID
        /// </summary>
        public string[] ExaminationScnenID;
        /// <summary>
        /// 首页图片
        /// </summary>
        public string HomepageImage;
        /// <summary>
        /// 考核任务配置
        /// </summary>
        public string AssessmentTask;
        /// <summary>
        /// 是否有分支
        /// </summary>
        public string HasFenZhi;
        /// <summary>
        /// 术后总结标题
        /// </summary>
        public string ZongJieTitle;
        /// <summary>
        /// 术后总结
        /// </summary>
        public string[] ShuHouZongJie;

        public float OperateTime;
        
        public string[] YiXueSuYang;

    }
    [Serializable]

    public class OperationLogInfo
    {
        /// <summary>
        /// 操作日志ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 操作日志名称
        /// </summary>
        public string OperateName;
        /// <summary>
        /// 条件描述1
        /// </summary>
        public string ConditionDescription;
        /// <summary>
        /// 操作物体名称
        /// </summary>
        public string OperateModelName;
        /// <summary>
        /// 被操作物体名称
        /// </summary>
        public string OperatedModelName;
        /// <summary>
        /// 操作种类
        /// </summary>
        public int NecessaryOperation;
        /// <summary>
        /// 操作条件ID
        /// </summary>
        public string ConditionID;
        /// <summary>
        /// 操作数据类型
        /// </summary>
        public string ConditionDateType;
        /// <summary>
        ///操作逻辑类型
        /// </summary>
        public string LogicType;
        /// <summary>
        ///任务ID
        /// </summary>
        public int TaskID;
    }
    [Serializable]
    public class ScoreInfo
    {
        //ID 
        public int ID;
        //步骤名称
        public string name;
        //任务描述
        public string TaskDec;
        //任务条件
        public string ConditionID;
        //加分条件
        public string ConditionType;
        //别名
        public string Alias;
        //减分
        public float Deduction;

        /// <summary>
        /// 正确答案描述内容
        /// </summary>
        public string TrueDescription;
        //不同方法(用于一个任务表中多种方法显示)
        public string Method;

        /// <summary>
        /// 管理后台配置分数（非表格配置）
        /// </summary>
        public float AddScore;
        //条件名称(网上下载)
        public string ConditionName;
        /// <summary>
        /// 用于UI显示(不做读取)
        /// </summary>
        public float FinalScore;
        //顺序
        public int StepID;
    }

    [Serializable]
    public class ScoreDataList
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string PrejectName;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName;

        /// <summary>
        /// 训练时间
        /// </summary>
        public string TrainTime;
        
        public List<ScoreInfoList> ScoreInfo;
    }

    [Serializable]
    public class ScoreInfoList
    {
        /// <summary>
        /// 任务阶段
        /// </summary>
        public string TaskStep;
        
        /// <summary>
        /// 阶段描述
        /// </summary>
        public string  TaskStepDescription;
        
        public List<ScoreInfoDetail> ScoreDetails;
        
    }

    [Serializable]
    public class ScoreInfoDetail
    {
        /// <summary>
        /// 任务阶段
        /// </summary>
        public string TaskStep;

        /// <summary>
        /// 任务步骤
        /// </summary>
        public string Step;

        /// <summary>
        /// -9999推荐项  >0必要项
        /// </summary>
        public float AddScore;
        

        /// <summary>
        /// 任务类型：必要项，推荐项
        /// </summary>
        public string TaskType;

        /// <summary>
        /// 分数  推荐项正确得-9999  错误0    必要项 正确得分  错误0
        /// </summary>
        public float Score;

        /// <summary>
        /// 原因
        /// </summary>
        public string Reason;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string TaskDescription;

        /// <summary>
        /// 步骤评分标准
        /// </summary>
        public string StepScoringCriteria;
    }

    public enum ScoreConditionType
    {
        None,
        /// <summary>
        /// 未完成此任务的后续任务
        /// </summary>
        WithOutNext,
        /// <summary>
        /// 完成此任务的前续任务
        /// </summary>
        WithOutLast,
        /// <summary>
        /// 完成特殊步骤
        /// </summary>
        SpecialSteps,
        /// <summary>
        /// 未完成此区间任务
        /// </summary>
        IncompleteInterval,
        /// <summary>
        /// 完成此区间的任务
        /// </summary>
        FinishInterval,
        /// <summary>
        /// 特殊任务ID
        /// </summary>
        SpecialTaskSteps,
    }
    [Serializable]

    public class OperateConditionInfo 
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 条件类型
        /// </summary>
        public string ConditionType;
        /// <summary>
        /// 条件
        /// </summary>
        public string Condiotion;
        /// <summary>
        /// 条件数据
        /// </summary>
        public string ConditionDate;
    }

    [Serializable]
    public class CaseInfoDataMsg
    {
        /// <summary>
        /// 返回信息
        /// </summary>
        public string msg;

        /// <summary>
        /// 返回病例信息 
        /// </summary>
        public List<CaseInfo> data;

        /// <summary>
        /// 状态
        /// </summary>
        public string status;
    }

    [Serializable]
    public class XueYaYiDate {
        /// <summary>
        /// 高压
        /// </summary>
        public int gaoYa;
        /// <summary>
        /// 低压
        /// </summary>
        public int diYa;
        /// <summary>
        /// 脉搏
        /// </summary>
        public int maiBo;
    }

    [Serializable]
    public class CaseInfo
    {
        /// <summary>
        /// 病例ID
        /// </summary>
        public string id;
        /// <summary>
        /// 病人姓名
        /// </summary>
        public string name;
        /// <summary>
        /// 
        /// </summary>
        public string createTime;
        /// <summary>
        /// 
        /// </summary>
        public string updateTime;
        /// <summary>
        /// 
        /// </summary>
        public int createBy;
        /// <summary>
        /// 
        /// </summary>
        public int updateBy;
        /// <summary>
        /// 
        /// </summary>
        public bool delFlag;
        /// <summary>
        /// 病例信息主诉
        /// </summary>
        public string illDescribe;
        /// <summary>
        /// 病例课程ID
        /// </summary>
        public int projectId;
        /// <summary>
        /// 病人ID
        /// </summary>
        public int patientId;

        /// <summary>
        /// 性别 1男 2女
        /// </summary>
        public string gender;
        /// <summary>
        /// 年龄
        /// </summary>
        public int age;
        /// <summary>
        /// 民族
        /// </summary>
        public string ethnic;
        /// <summary>
        /// 婚姻状态1已婚，2未婚，3离异，4丧偶
        /// </summary>
        public bool marriage;
        /// <summary>
        /// 职业
        /// </summary>
        public string career;
        /// <summary>
        /// 工作单位
        /// </summary>
        public string depart;
        /// <summary>
        /// 住址
        /// </summary>
        public string address;
        /// <summary>
        /// 入院时间
        /// </summary>
        public string timeOfIn;
        /// <summary>
        /// 记录时间
        /// </summary>
        public string timeOfRec;
        /// <summary>
        /// 详细信息（除主诉）
        /// </summary>
        public string detail;
        /// <summary>
        /// 检查体温
        /// </summary>
        public string temperature;
        /// <summary>
        /// 检查血压
        /// </summary>
        public string blood;
        /// <summary>
        /// 检查心率
        /// </summary>
        public string heartRate;
        /// <summary>
        /// 检查脉搏
        /// </summary>
        public string pulse;
        /// <summary>
        /// 检查细节
        /// </summary>
        public string detailOfCheck;

        public string filePath;

        public string ctDescribe;

        public string parts;
    

        public string GetGender()
        {
            if (gender == 1.ToString())
                return "男";
            else
                return "女";
        }


        public string GetMarriage()
        {
            if (marriage)
                return "已婚";
            else /*if (marriage == 2)*/
                return "未婚";
            /*else if (marriage == 3)
                return "离异";
            else
                return "丧偶";*/
        }


        public string GetDetail()
        {
            string _str = "";
            if (string.IsNullOrEmpty(detail))
            {
                TRACE.WarningLn("管理平台病例详细信息为空");
                //return string.Empty;
            }
            else
            {
                string[] tmp = detail.Split('&');
                for (int i = 0; i < tmp.Length; i++)
                {
                    _str += tmp[i] + "\n";
                }
            }
           
            return string.Format("{0}\n{1}", illDescribe, _str);
        }

    }
    /// <summary>
    /// 操作数据类型
    /// </summary>
    public enum ConditionDateType 
    {
        None,
        /// <summary>
        /// Bool
        /// </summary>
        Bool=1,
        /// <summary>
        /// Int
        /// </summary>
        Int,
        /// <summary>
        /// String
        /// </summary>
        String,
    }
    //计时类型
    public enum TimeStatue
    {
        /// <summary>
        /// 计时
        /// </summary>
        AddTime,
        /// <summary>
        /// 倒计时
        /// </summary>
        CountDown,
    }
    //课程首页数据
    [Serializable]

    public class CourseHomepageInfo
    {
        /// <summary>
        /// ID
        /// </summary>
        public int Id;
        /// <summary>
        /// 公司logo
        /// </summary>
        public string LogoName;
        /// <summary>
        /// 医院名字
        /// </summary>
        public string HospitalName;
        /// <summary>
        /// 首页图片
        /// </summary>
        public string HomepageImage;
        /// <summary>
        /// 手术名字
        /// </summary>
        public string OperationName;
        /// <summary>
        /// 难度指数
        /// </summary>
        public int DifficultyCount;
        /// <summary>
        /// 概述
        /// </summary>
        public string Summary;
        /// <summary>
        /// 考点
        /// </summary>
        public string ExaminationSite;
        /// <summary>
        /// 病例个数
        /// </summary>
        public int CaseCount;
        /// <summary>
        /// 小手术名字
        /// </summary>
        public string[] SmallOperationName;
        /// <summary>
        /// 小手术图片名
        /// </summary>
        public string[] SmallOperationImage;
        /// <summary>
        /// 训练场景ID
        /// </summary>
        public string[] TrainScnenID;
        /// <summary>
        /// 模考场景ID
        /// </summary>
        public string[] ExaminationScnenID;
    }
    //医生对话数据
    [Serializable]

    public class DoctorDialogueInfo
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 步骤ID
        /// </summary>
        public int StepID;
        /// <summary>
        /// 人员身份
        /// </summary>
        public int PerosnIdentity;
        /// <summary>
        /// 人员名称
        /// </summary>
        public string PerosnName;
        /// <summary>
        /// 对话内容
        /// </summary>
        public string Meassage;

        /// <summary>
        /// 音频ID
        /// </summary>
        public int AudioID;

        /// <summary>
        /// 单个延迟时间
        /// </summary>
        public int LaterTime;
    }
    [Serializable]

    public class TaskStepInfo
    {
        /// <summary>
        /// 任务阶段
        /// </summary>
        public int TaskStep;
        /// <summary>
        /// 完成数
        /// </summary>
        public int Num;
        /// <summary>
        /// 总数
        /// </summary>
        public int TotalNum;
    }
    //医生对话按钮
    [Serializable]

    public class DoctorPreperativeInfo
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 步骤ID
        /// </summary>
        public int StepID;
        /// <summary>
        /// 选择名字
        /// </summary>
        public string ChooseName;
        /// <summary>
        /// 是否有答题
        /// </summary>
        public int IsAnswer;
        /// <summary>
        /// 别名
        /// </summary>
        public string AliasName;

    }
    //答题
    [Serializable]
    public class AnswerUIInfo
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 步骤ID
        /// </summary>
        public int StepID;
        /// <summary>
        /// 问题
        /// </summary>
        public string Issue;
        /// <summary>
        /// 答案文本
        /// </summary>
        public string Text;
        /// <summary>
        /// 正确选项
        /// </summary>
        public int ChooseSure;
        /// <summary>
        /// 选择分数
        /// </summary>
        public int ChooseScore;
        /// <summary>
        /// 错误提示
        /// </summary>
        public string NOIndicate;
        /// <summary>
        /// 是否有对话(0没有，1有)
        /// </summary>
        public int IsDialoguel;
        /// <summary>
        ///是否单选(0单选，1多选)
        /// </summary>
        public int IsSingleSelection;
        /// <summary>
        ///是否有下一题答题(0没有，1有)
        /// </summary>
        public int IsNextAnswer;
        /// <summary>
        ///是否有下一题答题(0没有，1有)
        /// </summary>
        public int NextStepID;
        /// <summary>
        ///答题类型(1.人文关怀，2.穿刺答题，3体位答题)
        /// </summary>
        public int AnswerType;
        //分数别名
        public string ScoreAliasInt;
        /// <summary>
        /// 分支所属（0默认分支 1分支1 ；2分支2）
        /// </summary>
        public int Branch;
        /// <summary>
        /// 开启分支0默认分支 1分支1 ；2分支2）
        /// </summary>
        public string IsOpenBranch;
        /// <summary>
        /// 答题类型6 搭配使用 答题后的下一步任务ID 
        /// </summary>
        public int NextTaskID;
        /// <summary>
        /// 选择题图片（Icon表ID）
        /// </summary>
        public int IconId;
    }

    [Serializable]
    public class ToolTransform
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 工序ID
        /// </summary>
        public int ChooseID;
        /// <summary>
        /// 器械ID
        /// </summary>
        public int ToolID;
        /// <summary>
        /// 器械名
        /// </summary>
        public string ToolName;
        /// <summary>
        /// 位置
        /// </summary>
        public string Pos;
        /// <summary>
        /// 角度
        /// </summary>
        public string Rotation;
        /// <summary>
        /// 大小
        /// </summary>
        public string Scale;
    }
    [Serializable]
    public class CreateTrainRoot
    {
        //ID
        public int id;
        //步骤
        public Step step;

        public string name;
        //分数
        public double point;

        public string alias;

        public string condition;

        public string standard;
        /// <summary>
        /// 病例id
        /// </summary>
        public int medId;
    }
    [Serializable]
    public class Step
    {
        public int id;
        //项目名称
        public string projectName;
        //顺序
        public int stepOrder;
        //描述
        public string name;

        public checkPoints[] checkPoints; 

    }


    public class checkPoints
    {
        public int id;
        public string name;
        public double point;
        public string alias;
        public int medId;
    }

    [Serializable]
    public class ScoreConditionInfo
    {
        //ID
        public int ID;
        //加分条件
        public int ConditionType;
        //条件数据
        public string ConditionDate;
    }


    [Serializable]
    public class TrainDate
    {
        public string message;

        public TrainID data;

        public string status;
    }
    [Serializable]
    public class TrainID
    {
        public int id;

        public Project project;

        public bool finished;

        public bool test;

        public int timeUsed;

        public double score;

        public string detail;

        public int? medRecordId;

    }
    [Serializable]
    public class Project
    {
        public int id;

        public string name;

        public bool avaliable;

        public string departmentName;

        public string platform;

        public int projectKind;

        public bool isElite;
    }
    [Serializable]
    public class Student
    {
        public int id;

        public string name;

        public string stdClass;

        public string stdNumber;

        public string stdPhone;

        public string schoolID;

        public string email;

        public string studentID;
    }
    public class MenuHelp
    {
        /// <summary>
        /// ID
        /// </summary>
        public int ID;
        /// <summary>
        /// 菜单类型
        /// </summary>
        public string MenuName;
        /// <summary>
        /// 内容
        /// </summary>
        public string Content;
    }

    [Serializable]
    public class CourseDataReturn
    {
        /// <summary>
		/// 代码
		/// </summary>
		public string msg;

        /// <summary>
		/// 错误信息
		/// </summary>
		public string status;

        /// <summary>
        /// 返回的数据
        /// </summary>
        public Data data;
    }

    [System.Serializable]
    public class Data
    {
        /// <summary>
        /// 训练数据
        /// </summary>
        public SubData[] trainData;

        /// <summary>
        /// 考核分数
        /// </summary>
        public double fration;

        /// <summary>
        /// 考核次数
        /// </summary>
        public int count;

        /// <summary>
        /// 考核时间
        /// </summary>
        //public string trainTime;

    }

    [System.Serializable]
    public class SubData
    {
        /// <summary>
        /// 训练数据
        /// </summary>
        public string trainInfo;

        /// <summary>
        /// 病例
        /// </summary>
        public int medRecord;
    }

    [Serializable]
    public class TrainInfo
    {
        /// <summary>
        /// 训练ID
        /// </summary>
        public int TrainId;

        /// <summary>
        /// 训练实际数量
        /// </summary>
        public int TrainCount;

        /// <summary>
        /// 训练总数
        /// </summary>
        public int TrainTotalNum;
    }
    public class MistakeReturn
    {
        /// <summary>
		/// 代码
		/// </summary>
		public string msg;

        /// <summary>
		/// 错误信息
		/// </summary>
		public string status;

        /// <summary>
        /// 返回的数据
        /// </summary>
        public List<MistakeInfo> data;
    }
    public class MistakeInfo 
    {
        /// <summary>
        /// 步骤名称
        /// </summary>
        public string stepName;
        /// <summary>
        /// 操作名称
        /// </summary>
        public string checkpointName;
        /// <summary>
        /// 分数
        /// </summary>
        public double point;
        /// <summary>
        /// 是否做了
        /// </summary>
        public int done;
        /// <summary>
        /// 病例ID
        /// </summary>
        public int medId;
        /// <summary>
        /// 别名
        /// </summary>
        public int alias;
    }
}  

