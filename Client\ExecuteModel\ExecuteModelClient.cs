﻿/// <summary>
/// CAssemblingClient
/// </summary>
/// <remarks>
/// 2021.4.26: 创建. 秦勉 <br/>
/// 执行模块Client<br/>
/// </remarks>
using game.proto;
using GLib.Common;

namespace GLib.Client
{
    public class ExecuteModelClient : IExecuteModelClient, IEventExecuteSink
    {
        ExecuteModelPart executeModelPart;
        public ExecuteModelClient()
        {
            
        }

        ~ExecuteModelClient()
        {
            
        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName
        {
            get;
            set;
        }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState
        {
            get;
            set;
        }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress
        {
            get;
            set;
        }

        public bool Create()
        {
            executeModelPart = new ExecuteModelPart();
            executeModelPart.Create();
            return true;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
           // switch (wEventID)
            //{
                // executeModelPart.StartExecute(szValue);
           // }
        }

        public void Release()
        {
            executeModelPart.Release();
        }

        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }

        public void Update()
        {
            executeModelPart.update();
        }
    }
}
