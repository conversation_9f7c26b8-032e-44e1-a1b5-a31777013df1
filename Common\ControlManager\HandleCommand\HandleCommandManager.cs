﻿/// <summary>
/// HandleCommandManager
/// </summary>
/// <remarks>
/// 2021.4.21: 创建. 谌安 <br/>
/// 命令管理<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
	public class CHandleCommandManager : ITimerHandler, IHandleCommandManager, IEventExecuteSink
    {
        /** 消息队列
	    */
	    CHandleCommandQueue			m_commandQueue;

	    // 命令工厂
	    CHandleCommandFactory		m_HandleCommandFactory;

        // 操作命令逻辑定时器
	    const int HANDLE_COMMAND_LOGIC_TIMER=0;
	    // 操作命令逻辑定时器时间
        const int HANDLE_COMMAND_LOGIC_TIME = 16;

		/* 在地图加载过程中寻路失败，影响到命令队列的执行，所以为了解决该问题，引入了队列暂停机制
		 * 监听地图加载开始事件和结束事件，开始加载时暂停命令队列，加载结束后开启命令队列
		 * 记得暂停时间，定义最大暂停时间，超过最大时间启动命令队列，以免出现其他问题时影响到命令队列
		 */
		// 队列暂停最长时长
		const UInt32 m_uMaxTick = 30000;
		// 队列是否暂停
		bool m_bSuspend = false;
		// 暂停队列开始时间
		UInt32 m_uSuspendTick = 0;
		// 备份列表
		List<IHandleCommand> m_backUpCmdList = null;
		// 是否备份了命令
		private bool m_bBackupCmd = false;
		// 备份命令的TICK
		private int m_nBackupCmdTick = 0;
		// 备份命令的最长TICK
		const UInt32 m_uMaxBackupTick = 10000;

        public bool Create()
        {
            return create();
            
        }

        public void Release()
        {
            m_commandQueue.clearAll();
            m_HandleCommandFactory = null;
        }

        /////////////////////////////////////////IHandleCommandManager/////////////////////////////////////////
        /** 获取命令工厂接口
        */
        public IHandleCommandFactory getHandleCommandFactory()
        {
	        return m_HandleCommandFactory;
        }

        public bool create()
        {
            GlobalGame.Instance.TimerManager.AddTimer(this,HANDLE_COMMAND_LOGIC_TIMER, HANDLE_COMMAND_LOGIC_TIME, "CHandleCommandManager");
			//GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SYSTEM_BUILDZONE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "CHandleCommandManager::create");
			//GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH_EX, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "CHandleCommandManager::create");
			return true;
        }

        public void close()
        {
			GlobalGame.Instance.TimerManager.RemoveTimer(this,HANDLE_COMMAND_LOGIC_TIMER);
			//GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_SYSTEM_BUILDZONE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
			//GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH_EX, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
	        // 清除数据
	        clear();

        }

		/// 执行事件sink 
		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			/*if (wEventID == (ushort)DGlobalEvent.EVENT_SYSTEM_BUILDZONE && bSrcType == (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM)
			{
				m_bSuspend = true;
				m_uSuspendTick = (UInt32)Api.GetTickCount();// GlobalGame.Instance.CountryClient.GetGameServerTime();
			}
			else if (wEventID == (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH_EX && bSrcType == (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM)
			{
				m_bSuspend = false;
				m_uSuspendTick = 0;
			}*/
		}

        public void appendCommandTail(IHandleCommand pCmd)
        {
	        m_commandQueue.pushBack(pCmd);
        }

        public void insertCommandFront(IHandleCommand pCmd)
        {
	        m_commandQueue.pushFront(pCmd);
        }

        /** 清除队列
        */
        public void clear()
        {
            m_commandQueue.clearAll();
        }

        public void clearBefore(IHandleCommand pCmd)
        {
	        m_commandQueue.clearBefore(pCmd);
        }

        public void clearAfter(IHandleCommand pCmd)
        {
	        m_commandQueue.clearAfter(pCmd);
        }

        public void update()
        {
			// GetGameServerTime 每个频率产生20B开销，所以换用GetTickCount by dujiawu
			// UInt32 uCurTick = GlobalGame.Instance.CountryClient.GetGameServerTime(); 
			UInt32 uCurTick = (UInt32)Api.GetTickCount();
			if (m_uSuspendTick > 0 && uCurTick - m_uSuspendTick > m_uMaxTick)
			{
				m_bSuspend = false;
				m_uSuspendTick = 0;
			}
			if (m_bSuspend)
			{
				return;
			}
	        m_commandQueue.update();
        }

        /** 移除某项指令
        */
        public void RemoveCommand(IHandleCommand pCommand)
        {
	        m_commandQueue.RemoveCommand(pCommand);
        }

		public IHandleCommand GetHead()
		{
			return m_commandQueue.GetHead();
		}

		//返回最后一条指令
		public IHandleCommand GetTail()
		{
			return m_commandQueue.GetTail();
		}
        /////////////////////////////////////////TimerHandler/////////////////////////////////////////
        // 定时器到了
        public void OnTimer(TimerInfo ti)
        {
            switch (ti.timerId)
	        {
	        case HANDLE_COMMAND_LOGIC_TIMER:
		        {
			        update();

			        break;
		        }
	        default:
		        break;
	        }
        }

        public bool isIdle()
        {
            return true;
        }

        /////////////////////////////////////////HandleCommandManager/////////////////////////////////////////
        public CHandleCommandManager()
        {
            m_commandQueue = new CHandleCommandQueue();
            m_HandleCommandFactory = new CHandleCommandFactory();
        }

		// 备份当前指令
		public void BackUpCmd()
		{
			if (m_bBackupCmd && Api.GetTickCount() - m_nBackupCmdTick < m_uMaxBackupTick)
				return;
			m_bBackupCmd = true;
			m_nBackupCmdTick = Api.GetTickCount();
			m_backUpCmdList = m_commandQueue.BackupToList();
		}

		// 恢复当前指令
		public void ResumeBackUpCmd()
		{
			if(m_backUpCmdList != null)
			{
				if (Api.GetTickCount() - m_nBackupCmdTick < m_uMaxBackupTick)
				{
					for (int i = 0; i < m_backUpCmdList.Count; i++)
					{
						m_commandQueue.pushBack(m_backUpCmdList[i]);
					}
				}
				m_backUpCmdList.Clear();
			}
			m_bBackupCmd = false;
			m_nBackupCmdTick = 0;
			
		}

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}
    }
}