﻿/// <summary>
/// TaskIssueServerInof
/// </summary>
/// <remarks>
/// 2021/12/25 15:33:30: 创建. 王正勇 <br/>
/// 任务发布管理
/// </remarks>
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// TaskIssueServerInof
    /// </summary>
    public class TaskIssueServerInfo
    {
        private int m_StepIndex;
        private int m_LastTaskId;
        /// <summary>
        /// 当前任务发布数据
        /// </summary>
        private List<TaskIssue.Types.Item> m_NowTaskIssue;
        private TaskIssueData taskIssueData;
        /// <summary>
        /// 是否可以刷新任务发布
        /// </summary>
        private bool m_isCanShowIssue;

        public TaskIssueServerInfo()
        {
            taskIssueData = new TaskIssueData();
        }
        /// <summary>
        /// 设置当前任务的发布数据
        /// </summary>
        public void SetNowTaskIssue()
        {
            m_StepIndex = 0;
        }
        /// <summary>
        /// 释放任务发布
        /// </summary>
        public void Release()
        {
            m_isCanShowIssue = false;
            m_StepIndex = 0;
            m_LastTaskId = 0;
            m_NowTaskIssue = null;
        }
        /// <summary>
        /// 设置是否运行自动弹出发布
        /// </summary>
        /// <param name="bState"></param>
        public void SetOpenAutoIssue()
        {
            m_isCanShowIssue = true;
            if (m_StepIndex > 0)
            {
                GHelp.FireExecute((ushort)ViewLogicDef.TASK_CREATE_ISSUE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
            }
        }
        public int GetNowStepIndex()
        {
            return m_StepIndex;
        }
        /// <summary>
        /// 获取当前步骤任务发布信息
        /// </summary>
        public TaskIssue.Types.Item GetNowStepTaskIssue(int iStepIndex)
        {
            if (m_NowTaskIssue == null)
            {
                return null;
            }
            return m_NowTaskIssue.Where(w => w.StepIndex == iStepIndex).FirstOrDefault();
        }
        public bool JudgeAutoIssueStepIndex(int nowStepIndex)
        {
            if (m_LastTaskId == 0)
            {
                m_LastTaskId = GlobalGame.Instance.TaskControler.GetNowTaskID();
            }
            if (nowStepIndex != m_StepIndex|| m_LastTaskId!= GlobalGame.Instance.TaskControler.GetNowTaskID())
            {
                m_LastTaskId = GlobalGame.Instance.TaskControler.GetNowTaskID();
                m_StepIndex = nowStepIndex;
                if (m_isCanShowIssue)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        public void CreateTaskIsuueWindow(EMEventType eMEventType, int iStepIndex, int isSendInfo)
        {
            if (m_NowTaskIssue == null || m_NowTaskIssue.Count <= 0)
            {
                SetNowTaskIssue();
            }
            TaskIssue.Types.Item nowTaskIssue = m_NowTaskIssue.Where(w => w.StepIndex == iStepIndex).FirstOrDefault();
            if (nowTaskIssue != null)
            {
                taskIssueData.Rest();
                taskIssueData.IssueTitle = Api.TR("提示");
                if (!string.IsNullOrEmpty(nowTaskIssue.IssueTitle))
                {
                    taskIssueData.IssueTitle = Api.TR(UBB.UBBStr(nowTaskIssue.IssueTitle));
                }
                taskIssueData.taskTarget = Api.TR(UBB.UBBStr(nowTaskIssue.TaskTarget));
                taskIssueData.tipsContent = Api.TR(UBB.UBBStr(nowTaskIssue.TipsContent));
                taskIssueData.VoiceId = nowTaskIssue.IssueVoiceId;
                taskIssueData.IsSendInfo = isSendInfo;
                switch (eMEventType)
                {
                    //编程教学
                    case EMEventType.ProgramTeach:
                    case EMEventType.Program:
                        taskIssueData.ConfirmText = Api.TR("点击挑战");
                        taskIssueData.StartIssueModule = 2;
                        break;
                    //试运行
                    case EMEventType.ProgramTestOperation:
                        taskIssueData.ConfirmText = Api.TR("点击挑战");
                        taskIssueData.StartIssueModule = 1;
                        break;
                    case EMEventType.Challenge:
                        taskIssueData.ConfirmText = Api.TR("点击挑战");
                        taskIssueData.StartIssueModule = 3;
                        break;
                    default:
                        taskIssueData.ConfirmText = Api.TR("知道了");
                        taskIssueData.StartIssueModule = 4;
                        break;
                }
                GHelp.FireExecute((ushort)ViewLogicDef.INFORM_LOAD_ISSUE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, taskIssueData);
            }
        }
    }
}
