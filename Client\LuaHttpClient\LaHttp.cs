﻿/// <summary>
/// LuaHttp
/// </summary>
/// <remarks>
/// 2021.10.22: 创建. 王康阳 <br/>
/// lua层的HTTP请求<br/>
/// </remarks>
using System;
using GLib;
using System.Collections.Generic;
using UnityEngine;
using GLib.Common;

namespace GLib.Client
{
    public class LaHttp : HTTP_Response_Handler
    {
        private string m_http_keyName;
        private EMHTTP_METHOD m_httpMethod;
        public LaHttp()
        {
        }

        ~LaHttp()
        {

        }

        /// <summary>
        /// 向web发送消息
        /// </summary>
        public void SendWebMessage(string keyName, EMHTTP_METHOD requestType, string szUrl, WWWForm data = null, string Authorization = "", byte[] bodyRaw = null)
        {
            m_http_keyName = keyName;
            m_httpMethod = requestType;
            CHttp.Instance.Request(requestType, szUrl, this, data, Authorization, bodyRaw);
        }


        #region web请求
        public EMHTTP_METHOD GetCurrentHttpMethod()
        {
            return m_httpMethod;
        }

        public void OnResponse(byte[] pContent, uint dwLen, string url)
        {
            //GlobalToLa.Instance.LaDataByFunction("LaHttpManager.OnResponse", m_http_keyName,pContent,dwLen,url);
        }

        public void OnError(uint dwError, string url)
        {
            //GlobalToLa.Instance.LaDataByFunction("LaHttpManager.OnError", m_http_keyName, dwError, url);
        }

        public void OnLocation(string new_url, string url)
        {
            //GlobalToLa.Instance.LaDataByFunction("LaHttpManager.OnLocation", m_http_keyName, new_url, url);
        }

        public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
        {
            //GlobalToLa.Instance.LaDataByFunction("LaHttpManager.OnDataStream", m_http_keyName, pData, dwLen, fProgress,url);
            return true;
        }
        #endregion
    }
}
