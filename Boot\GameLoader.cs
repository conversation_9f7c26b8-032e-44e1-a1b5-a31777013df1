﻿//#define OpenDebugInfo_GameLoader

/// <summary>
/// CGameLoader
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 模块加载<br/>
/// </remarks>
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using GLib.Common;

namespace GLib.Boot
{
    /// <summary>
    /// 游戏模块加载系统
    /// </summary>
    public class CGameLoader : ITimerHandler,IModule
    {

        // 模块加载器流程状态
        public enum EMGameLoaderState
        {
            None = 0,		// 初始化前
            Init,			// 初始化
            Create,		    // 正在创建模块
            Finish,		    // 模块创建完成
            Release,		// 正在释放模块
            Error,			// 错误状态
            Close,			// 关闭

            MAX,            // 最大值
        }

        /// <summary>
        /// 模块加载的状态
        /// </summary>
        public enum EMModuleCreateState
        {
            None = 0,
            Create,             // 创建中
            Loading,            // 加载中
            Wait,               // 等待中
            Success,            // 加载成功
            Fail,               // 加载失败
            TimeOut,            // 加载超时
            Error,			    // 错误状态
            Close,              // 已释放

            MAX,                // 最大值
        }

        /// <summary>
        /// 模块加载配置信息  
        /// </summary>
        public class SModuleLoadInfo
        {
            // 配置参数
            public IModule Module;                  // 模块对象
            public GameState nPreLoadState;         // 在某个游戏状态之前需加载完成
            public float fTimeOut;                  // 模块加载超时时间 单位: 秒

            // 运行时的变量
            public int nModuleNum;                  // 模块的注册时序号 新注册时为m_LoadInfo.Count
            public EMModuleCreateState m_nState;    // 模块加载的状态
            public float[] fStateTime = new float[(int)EMModuleCreateState.MAX]; // 每个状态变化时间
            public bool bIsAsyn = false;            // 是否需要异步处理

            //////////////////////////////////////////////


            /// <summary>
            /// 改变游戏流程
            /// </summary>
            /// <param name="nState">当前状态</param>
            /// <returns>是否成功</returns>
            public bool SetState(EMModuleCreateState nState)
            {
                if (nState == m_nState)
                {
                    return false;
                }
                // 旧的流程
                EMModuleCreateState nOldState = m_nState;

                fStateTime[(int)nOldState] = Time.realtimeSinceStartup;

                // 改变流程
                m_nState = nState;

                fStateTime[(int)nState] = Time.realtimeSinceStartup;

#if OpenDebugInfo_GameLoader
                TRACE.TraceLn("GameLoader.SetState():" + nOldState.ToString() + "->" + nState.ToString());
#endif

                return true;
            }
        }

        //////  模块常量 //////////////////////////////////////////////////////
        // 检查是否加载完成模块的间隔时间，节省性能
        private const float CHECK_END_INTERVAL = 1.0f;
        // 检查需要加载模块的间隔时间，节省性能
        private const float CHECK_INTERVAL = 0.2f;
        // 检查同时可以加载的数量
        private const int SAME_LOAD_COUNT = 5;
        // 模块加载超时时间
        private const float LOAD_TIMEOUT = 3.0f;
        /////  模块变量 ///////////////////////////////////////////////////////

        // 模块加载器流程状态
        private EMGameLoaderState m_nState;
        // 流程类型触发fix时间
        private float[] m_fStateTime = new float[(int)EMGameLoaderState.MAX];
        
        //需要加载的模块
        private List<SModuleLoadInfo> m_LoadInfo = new List<SModuleLoadInfo>();
        //已加载的模块，按加载顺序加入，专门用来释放的时候调用
        private List<SModuleLoadInfo> m_ModuleList = new List<SModuleLoadInfo>();
        //正在加载的模块
        private List<SModuleLoadInfo> m_Loading = new List<SModuleLoadInfo>();
        // 最后检查需要加载的时间
        private float m_fLastCheckTime = 0;
        // 最后检查是否加载完成
        //private float m_fLastCheckEndTime = 0;
		// 正在加载的状态
		private GameState m_nLoadingState;


        /// <summary>
        /// 模块中文名称
        /// </summary>
        public string ModuleName { get; set; }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress { get; set; }

        ////// 模块方法 //////////////////////////////////////////
        /// <summary>
        /// 构造函数
        /// </summary>
        public CGameLoader()
        {
            ModuleLoadState = EMModuleLoadState.None;
			m_nLoadingState = GameState.None;

        }
        /// <summary>
        /// 模块同步创建.
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
            // 模块名赋值 add by zhanggx
            this.ModuleName = "GameLoader";

            // 注册逻辑调用 FixedUpdate，是在固定的时间间隔执行，不受游戏帧率（fps）的影响
            CGame.Instance.RegisterModuleEvent(this, (uint)EMModuleEvent.FixedUpdate);

            // 启动进入加载基础资源状态，资源之间不能有相互依赖
            SetState(EMGameLoaderState.Init);

            return true;
        }

        /// <summary>
        /// 模块释放
        /// </summary>
        public void Release()
        {
            //释放所有模块
            SetState(EMGameLoaderState.Release);

            // 注册逻辑调用
            CGame.Instance.UnRegisterModuleEvent(this);
        }

        /// <summary>
        /// 反序释放所有模块
        /// </summary>
        private void OnReleaseAllModule()
        {
            for (int i = m_ModuleList.Count - 1; i >= 0; i--)
            {
                m_ModuleList[i].Module.Release();
            }
            m_ModuleList.Clear();
        }

        ////////////////模块驱动基础接口//////////////////////

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {
            DoTask();
        }

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {
        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {
        }

		public void OnTimer(TimerInfo ti)
		{
			// 当模块加载完成后，反注册逻辑调用,节约CPU资源
			CGame.Instance.UnRegisterModuleEvent(this);
			// 不以在fixupdate 反注册自己的fixupdate调用，要延迟调用
			GlobalGame.Instance.TimerManager.RemoveTimer((ITimerHandler)this, 1);
		}

        /// <summary>
        /// 注册加载模块配置信息  为了提高加载时的性能，请在加载配置时 先预先人工排好序
        /// </summary>
        /// <param name="strModuleName">模块中文名称</param>
        /// <param name="module">需要加载的模块</param>
        /// <param name="nLoadState">加载模块时的初始状态</param>
        /// <param name="fTimeOut">模块加载的大致时间，当超出这个时间系统会提示加载超时</param>
        /// <param name="bAsyn">设置模块是否需要异步加载，默认false为同步加载</param>
        /// <returns></returns>
        public bool Reg(string strModuleName, IModule module, GameState nLoadState = GameState.Init, float fTimeOut = 3.0f, bool bAsyn = false)
        {
            // 无效模块
            if (module == null)
            {
                TRACE.ErrorLn("GameLoader.Reg() 无效模块:" + strModuleName);
                return false;
            }
			if (m_nLoadingState > nLoadState)
			{
				TRACE.ErrorLn("GameLoader.Reg() 严重错误,模块加载顺序不对,模块:" + strModuleName + ", nLoadingState=" + m_nLoadingState + ", nLoadState=" + nLoadState);
				return false;
			}
			
			m_nLoadingState = nLoadState;
			
            // 模块名称设置 模块初始化可以不设置
            module.ModuleName = strModuleName;

            // 创建模块加载配置信息，并保存到list对象
            SModuleLoadInfo node = new SModuleLoadInfo();
            node.m_nState = EMModuleCreateState.Wait;
            node.Module = module;
            node.nPreLoadState = nLoadState;
            node.fTimeOut = (fTimeOut < 1.0f) ? 1.0f : fTimeOut;
            //
            node.SetState(EMModuleCreateState.Create);
            node.nModuleNum = m_LoadInfo.Count;
            node.bIsAsyn = bAsyn;
            m_LoadInfo.Add(node);

            return true;

        }

        /// <summary>
        /// 取得正在加载模块某个状态的数量
        /// </summary>
        /// <param name="nState">模块需要检查的加载状态</param>
        /// <returns></returns>
        private int GetLoadingCount(EMModuleCreateState nState)
        {
            int nCount = 0;
            for (int i = 0; i < m_Loading.Count; i++)
            {
                SModuleLoadInfo info = m_Loading[i];
                if (info.m_nState == nState)
                {
                    nCount++;
                }
            }
            return nCount;
        }

        /// <summary>
        /// 取得需要加载模块某个状态的数量
        /// </summary>
        /// <param name="nState">模块需要检查的加载状态</param>
        /// <returns></returns>
        private int GetPreLoadCount(EMModuleCreateState nState)
        {
            int nCount = 0;
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SModuleLoadInfo info = m_LoadInfo[i];
                if (info.m_nState == nState)
                {
                    nCount++;
                }
            }
            return nCount;
        }

        /// <summary>
        /// 判断某个状态需要使用的模块是否加载完成
        /// 由于模块比较多，外部轮询不要太频繁
        /// </summary>
        /// <param name="nState">游戏状态</param>
        /// <param name="fLastCheckTime">检查冷却时间 暂未用</param>
        /// <returns></returns>
        public bool IsLoadFinish(GameState nState, float fLastCheckTime = 1.0f)
        {
            // 如果模块加载状态已经设置为完成，则直接返回
            if (this.ModuleLoadState == EMModuleLoadState.Success)
            {
                return true;
            }

            // 判断某个游戏状态下的模块是否全部加载完成
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SModuleLoadInfo info = m_LoadInfo[i];
                if (info.nPreLoadState == nState)
                {
                    //TRACE.ErrorLn("ModuleName: " + info.Module.ModuleName + " -> PreLoadState" + info.nPreLoadState + " -> m_nState" + info.m_nState);
                    if (info.m_nState != EMModuleCreateState.Success)
                    {
                        return false;   // 退出模块加载状态检测
                    }
                    //TRACE.ErrorLn("ModuleName: " + info.Module.ModuleName + " -> Success");
                }
            }
            //m_fLastCheckEndTime = fNow;
            return true;
        }


        /// <summary>
        /// 取得指定状态的模块加载完成度
        /// </summary>
        /// <param name="nState">游戏状态</param>
        /// <returns>加载的进度,范围(0.0f,1.0f)</returns>
        public float GetProgress(GameState nState)
        {
            float fProgress = 0.0f;
            // 如果模块加载状态已经设置为完成，则直接返回
            if (this.ModuleLoadState == EMModuleLoadState.Success)
            {
                return 1.0f;
            }
            int nAll = 1;	// 所有个数
            int nOk = 1;	// 加载成功个数
            // 判断某个游戏状态下的模块是否全部加载完成
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SModuleLoadInfo info = m_LoadInfo[i];
                if (info.nPreLoadState == nState)
                {
                    nAll++;
                    if (info.m_nState == EMModuleCreateState.Success)
                    {
                        nOk++;
                    }
                }
            }

            fProgress = (float)nOk / (float)nAll;

            return fProgress;
        }

        /// <summary>
        /// 处理加载模块，所以的模块使用协程加载来实现后台加载
        /// </summary>
        /// <param name="info">模块</param>
        /// <returns></returns>
        private bool LoadModule(SModuleLoadInfo info)
        {
            if (info.Module == null)
            {
                return false;
            }

#if OpenDebugInfo_GameLoader
            TRACE.TraceLn("GameLoader: 加载模块[ " + info.Module.ModuleName + "] ...");
#endif

            m_Loading.Add(info);

            m_ModuleList.Add(info);

            info.SetState(EMModuleCreateState.Loading);
			info.Module.ModuleLoadState = EMModuleLoadState.Loading;

            CGame.Instance.StartCoroutineEx(LoadHandler(info));

            return true;
        }

        /// <summary>
        /// 资源异步处理器
        /// </summary>
        /// <param name="info">模块</param>
        /// <returns></returns>
        private IEnumerator LoadHandler(SModuleLoadInfo info)
        {
            // 等待下载
            yield return 0;

#if OpenDebugInfo_Profiler
			//Api.PP_BY_NAME_START("CGameLoader.LoadModule. "+info.Module.ModuleName);
			float fTime = Time.realtimeSinceStartup;
#endif
            // 开始加载资源
            bool bCreate = info.Module.Create();

			// 同步处理的可不管设置模块加载状态，异步的要自己设置
            if (bCreate)
            {
                if (info.bIsAsyn)   // 需要异步处理的模块
                {
                    if (info.Module.ModuleLoadState == EMModuleLoadState.Success)
                    {
                        info.SetState(EMModuleCreateState.Success);
                    }
                }
				else
				{
					info.SetState(EMModuleCreateState.Success);
					info.Module.ModuleLoadState = EMModuleLoadState.Success;
                }
                
            }
            else
            {
                TRACE.ErrorLn("GameLoader:LoadHandler 加载模块[ " + info.Module.ModuleName + "] 失败 ");

				info.SetState(EMModuleCreateState.Fail);
				info.Module.ModuleLoadState = EMModuleLoadState.Fail;
			}

#if OpenDebugInfo_Profiler
			float fTime1 = Time.realtimeSinceStartup;
			info.fStateTime[0] = (fTime1 - fTime)*1000;
			//if (fTime1 > fTime + 0.02f)
			//{
			//	//TRACE.TraceLn("GameLoader: 加载模块[ " + info.Module.ModuleName + "] 时间：" + (fTime1 - fTime).ToString());
			//}
			//TRACE.ErrorLn("LoadModule:"+info.Module.ModuleName+","+(fTime1-fTime).ToString());
			//Api.PP_BY_NAME_STOP();
#endif


        }


        /// <summary>
        /// 开始加载所有的模块，由外面调用
        /// </summary>
        public void StartLoadAllModule()
        {
            SetState(EMGameLoaderState.Create);
        }


        /// <summary>
        /// 检查一下正在加载的资源个数
        /// </summary>
        /// <returns>查到一个就返回</returns>
        private int HaveNotSuccess()
        {
            int nNotOk = 0; // 不OK的数量
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SModuleLoadInfo info = m_LoadInfo[i];
                // 当前GameClient的状态之前的状态都加载
                if (info.m_nState != EMModuleCreateState.Success)
                {
                    nNotOk++;
                    break;
                }
            }
            return nNotOk;
        }

        /// <summary>
        /// 遍历需要加载的列表,当前状态前的优先加入
        /// </summary>
		/// <param name="FindCount">需要加载的数量, 返回剩余模块数量</param>
        private void AddPrevStateModule(GameState nState, ref int FindCount)
        {
			//需要加载的模块
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {

                SModuleLoadInfo info = m_LoadInfo[i];
                // 当前GameClient的状态之前的状态都加载
                if ((int)info.m_nState < (int)EMModuleCreateState.Loading && (int)nState >= (int)info.nPreLoadState)
                {
                    FindCount--;
                    // 处理加载模块，所以的模块使用协程加载来实现后台加载
                    LoadModule(info);
					if (FindCount<1)
					{
						break;
					}
                   
                }
            }
        }

        /// <summary>
        /// 需要加载的列表,所有状态都可加入
        /// </summary>
        /// <param name="nState">需要加载的游戏状态</param>
        /// <param name="FindCount">需要加载的数量, 返回剩余模块数量</param>
        private void AddCanAllModule(GameState nState, ref int FindCount)
        {
			//需要加载的模块
            for (int i = 0; i < m_LoadInfo.Count; i++)
            {
                SModuleLoadInfo info = m_LoadInfo[i];
                // 当前GameClient的状态之前的状态都加载
                if ((int)info.m_nState < (int)EMModuleCreateState.Loading)
                {
                    FindCount--;
                    // 处理加载模块，所以的模块使用协程加载来实现后台加载
                    LoadModule(info);
					if (FindCount < 1)
					{
						break;
					}
                }
            }
        }

        /// <summary>
        /// 改变游戏流程,不允许外部调用
        /// </summary>
        /// <param name="nState">当前状态</param>
        /// <returns>是否成功</returns>
        private bool SetState(EMGameLoaderState nState)
        {
            if (nState == m_nState)
            {
                return false;
            }

            // 旧的流程
            EMGameLoaderState nOldState = m_nState;

            // 当游戏流程退出
            OnExit(nOldState, nState);

			// 改变流程
			m_nState = nState;
            m_fStateTime[(int)nOldState] = Time.realtimeSinceStartup;

            // 当游戏流程进入
            OnEnter(nState, nOldState);

            m_fStateTime[(int)nState] = Time.realtimeSinceStartup;

#if OpenDebugInfo_GameLoader
            TRACE.TraceLn("GameLoader.SetState():" + nOldState.ToString() + "->" + nState.ToString());
#endif
            return true;
        }

        /// <summary>
        /// 当游戏流程进入
        /// </summary>
        /// <param name="nState">新状态</param>
        /// <param name="nOldState">旧状态</param>
        private void OnEnter(EMGameLoaderState nState, EMGameLoaderState nOldState)
        {
            //////////////////////////////////////////////////////////////////////////
            // 流程
            switch (nState)
            {
                case EMGameLoaderState.None:		// 无用的
                    {
                    }
                    break;
                case EMGameLoaderState.Init:		// 初始化
					{
						ModuleLoadState = EMModuleLoadState.Create;
                    }
                    break;
                case EMGameLoaderState.Create:		// 正在创建模块
                    {
                        ModuleLoadState = EMModuleLoadState.Loading;
                    }
                    break;
                case EMGameLoaderState.Finish:		// 模块创建完成
                    {

#if OpenDebugInfo_Profiler

						for (int i = 0; i < m_LoadInfo.Count; i++)
						{
							SModuleLoadInfo info = m_LoadInfo[i];
							TRACE.TraceLn("LoadModule:"+info.Module.ModuleName+","+info.fStateTime[0].ToString());
						}
#endif
                        ModuleLoadState = EMModuleLoadState.Success;

						// 不以在fixupdate 反注册自己的fixupdate调用，要延迟调用
						GlobalGame.Instance.TimerManager.AddTimer((ITimerHandler)this, 1, 1000,1, "Gameloader");
                    }
                    break;
                case EMGameLoaderState.Release:		// 正在释放模块
                    {
                        // 释放所有的资源
                        ModuleLoadState = EMModuleLoadState.Close;
                        OnReleaseAllModule();
                    }
                    break;
                case EMGameLoaderState.Error:		// 错误
                    {
						// 发送加载错误事件 
						GHelp.FireExecute((UInt16)DGlobalEvent.EVENT_LOADER_FAIL, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                        ModuleLoadState = EMModuleLoadState.Fail;
                    }
                    break;
                case EMGameLoaderState.Close:		// 关闭
                    {
                        ModuleLoadState = EMModuleLoadState.Close;
                    }
                    break;
            }
        }

        /// <summary>
        /// 驱动游戏流程
        /// </summary>
        private void DoTask()
        {
            //////////////////////////////////////////////////////////////////////////

            // 流程
            switch (m_nState)
            {
                case EMGameLoaderState.None:		// 无用的
                    {

                    }
                    break;
                case EMGameLoaderState.Init:		// 初始化
                    {
                        //todo 等待GameClient初始化之后 再进入Create状态

                        // 启动进入创建模块
                        SetState(EMGameLoaderState.Create);
                    }
                    break;
                case EMGameLoaderState.Create:		// 正在创建模块
                    {

                        // 检查需要加载的当前时间
						float fNow = Time.realtimeSinceStartup;
						// 取得正在加载的数量
						int nLoadingCount = 0;

                        #region// 处理正在加载的列表
                        for (int i = m_Loading.Count - 1; i >= 0; i--)
                        {
                            SModuleLoadInfo loading = m_Loading[i];

                            // 是否要删除
                            bool bNeedRemove = false;

                            switch (loading.m_nState)
                            {
                                case EMModuleCreateState.None:
                                    {
                                    }
                                    break;
                                case EMModuleCreateState.Create:             // 创建中
                                    {
                                    }
                                    break;
                                case EMModuleCreateState.Loading:            // 加载中
                                    {
                                        if (loading.Module.ModuleLoadState == EMModuleLoadState.Success)
                                        {
#if OpenDebugInfo_GameLoader
                                            TRACE.TraceLn("GameLoader: 加载模块[ " + loading.Module.ModuleName + "] 成功！   耗时：" + (fNow - loading.fStateTime[(int)EMModuleCreateState.Loading]));
#endif
											loading.SetState(EMModuleCreateState.Success);
											bNeedRemove = true;
                                        }
                                        else if (loading.Module.ModuleLoadState == EMModuleLoadState.Fail)
                                        {
                                            TRACE.ErrorLn("GameLoader: 加载模块[ " + loading.Module.ModuleName + "] 失败！   耗时：" + (fNow - loading.fStateTime[(int)EMModuleCreateState.Loading]));
											loading.SetState(EMModuleCreateState.Fail);
											bNeedRemove = true;
											// 加载模块失败 启动失败，进入错误状态
											SetState(EMGameLoaderState.Error);
                                        }
                                        else
                                        {
                                            if (fNow > loading.fStateTime[(int)EMModuleCreateState.Loading] + loading.fTimeOut)
                                            {
                                                TRACE.WarningLn("GameLoader: 加载模块[ " + loading.Module.ModuleName + "] 超时！   耗时：" + loading.fTimeOut);
                                                loading.SetState(EMModuleCreateState.TimeOut);
											}
											else
											{
												nLoadingCount++;
											}
                                        }
                                    }
                                    break;
                                case EMModuleCreateState.Wait:               // 等待中
                                    {
                                    }
                                    break;
                                case EMModuleCreateState.Success:            // 加载成功
									{
										bNeedRemove = true;
                                    }
                                    break;
                                case EMModuleCreateState.Fail:               // 加载失败
                                    {
										// 加载模块失败 启动失败，进入错误状态
										SetState(EMGameLoaderState.Error);
										bNeedRemove = true;
                                    }
                                    break;
                                case EMModuleCreateState.TimeOut:			// 超时
                                    {
                                        if (fNow > loading.fStateTime[(int)EMModuleCreateState.TimeOut] + 3.0f)
                                        {
                                            loading.SetState(EMModuleCreateState.Loading);
                                        }
                                    }
                                    break;
                                case EMModuleCreateState.Error:			    // 错误状态
                                    {
                                    }
                                    break;
                                case EMModuleCreateState.Close:              // 已释放
                                    {
                                    }
                                    break;
                                default:                // 最大值
                                    break;

                            }

                            // 要删除的
                            if (bNeedRemove)
                            {
                                m_Loading.RemoveAt(i);
                            }

                        }
                        #endregion

                        #region// 间隔时间到了就检查一次
                        if (fNow > m_fLastCheckTime + CHECK_INTERVAL)
                        {
							m_fLastCheckTime = fNow;

                            // 查一下是不全部加载成功，如全部加载完，就进入完成状态
                            int nNotOk = HaveNotSuccess(); // 不OK的数量
                            // 全部加载成功，就进入完成状态
                            if (nNotOk < 1)
                            {
                                SetState(EMGameLoaderState.Finish);
                            }
							else
							{
								// 取得GameClient的当前状态
								GameState nGameState = CGame.Instance.GameClient.GetState();
								// 要加载的状态
								GameState nState = ((int)nGameState < (int)GameState.MAX) ? (nGameState + 1) : nGameState;

								// 当数量小于指定的数量时 从需要加载的模块中加入
								if (nLoadingCount < SAME_LOAD_COUNT)
								{
									int nFindCount = SAME_LOAD_COUNT - nLoadingCount;
									
									// 第一遍历需要加载的列表,当前状态前的优先加入
									AddPrevStateModule(nState, ref nFindCount);

									// 如果是login及后面的状态，没找到，可加载后面所有的，在login前就空出cpu来让给其他初始
									if (nGameState>=GameState.Game && nFindCount > 0)
									{
										// 第一遍历需要加载的列表,所有状态都可加入
										AddCanAllModule(nState, ref nFindCount);
									}

								}
							}

                        }
                        #endregion

                    }
                    break;
                case EMGameLoaderState.Finish:		// 完成
                    {

                    }
                    break;
                case EMGameLoaderState.Release:		// 正在释放模块
                    {

                    }
                    break;
                case EMGameLoaderState.Error:		// 错误
                    {
                    }
                    break;
                case EMGameLoaderState.Close:		// 关闭
                    {
                    }
                    break;
            }

            //////////////////////////////////////////////////////////////////////////


        }

        /// <summary>
        /// 当游戏流程退出
        /// </summary>
        /// <param name="nState">旧状态</param>
        /// <param name="nNewState">新状态</param>
        private void OnExit(EMGameLoaderState nState, EMGameLoaderState nNewState)
        {
            //////////////////////////////////////////////////////////////////////////
            // 流程
            switch (nState)
            {
                case EMGameLoaderState.None:		// 无用的
                    {
                    }
                    break;
                case EMGameLoaderState.Init:		// 初始化
                    {
                    }
                    break;
                case EMGameLoaderState.Create:		// 正在创建模块
                    {
                    }
                    break;
                case EMGameLoaderState.Finish:		// 完成
                    {

                    }
                    break;
                case EMGameLoaderState.Release:		// 正在释放模块
                    {
                    }
                    break;
                case EMGameLoaderState.Error:		// 错误
                    {
                    }
                    break;
                case EMGameLoaderState.Close:		// 关闭
                    {
                    }
                    break;
            }
        }



    }
}


