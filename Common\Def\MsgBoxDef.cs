﻿/// <summary>
/// MsgBoxDef
/// </summary>
/// <remarks>
/// 2021.5.28: 创建. 王康阳 <br/>
/// </remarks>
using System;
using System.Collections.Generic;

namespace GLib.Common
{
    // 消息模块
    public enum MsgBoxModule
    {
        none = 0,
        // 登录模块消息
        MSGBOX_LOGIN,
        //组装模块消息
        MSGBOX_ASSEMBLY,
        /// <summary>
        /// 清除机器人编程
        /// </summary>
        MSGBOX_BLOCK_MODEL,
        /// <summary>
        /// 打擂台模块消息
        /// </summary>
        MSGBOX_CHALLENGE,
        /// <summary>
        /// 课程模块消息
        /// </summary>
        MSGBOX_COURSE,
        /// <summary>
        /// 编程驱动消息
        /// </summary>
        MSGBOX_BLOCK_DIVE,
        /// <summary>
        /// 作业
        /// </summary>
        MSGBOX_HOME_WROK,
        /// <summary>
        /// 通用
        /// </summary>
        GENERAL,
        /// <summary>
        /// 退出登陆
        /// </summary>
        LOGIN_OUT,
        /// <summary>
        /// 小游戏模块消息
        /// </summary>
        MSGBOX_MINIGAME,
        /// <summary>
        /// 替换积木块
        /// </summary>
        MSGBOX_REPLACE_BLOCK,
        /// <summary>
        /// 热更新
        /// </summary>
        MSGBOX_HOT_UPDATE,
    }

    // 消息action
    public enum MsgBoxAction
    {
        none = 0,
        // 登录退出
        MSGBOX_LOGIN_EXIT,
        //组装重装
        MSGBOX_ASSEMBLY_RESET,
        /// <summary>
        /// 清除积木块
        /// </summary>
        MSGBOX_CLOSE_BLOCK,
        /// <summary>
        /// 打擂台--重置场景
        /// </summary>
        MSGBOX_CHALLENGE_RESET,
        /// <summary>
        /// 打擂台--提交代码
        /// </summary>
        MSGBOX_CHALLENGE_SUBMIT,
        /// <summary>
        /// 课程-课程未解锁提示
        /// </summary>
        MSGBOX_COURSE_LOCKTIP,
        /// <summary>
        /// 编程驱动-完成回调
        /// </summary>
        MSGBOX_BLOCK_DIVE_BACK,
        /// <summary>
        /// 作业-提示信息
        /// </summary>
        MSGBOX_HOME_WORKE_BACK,
        /// <summary>
        /// 消息确认
        /// </summary>
        MESSAGE_CONFIRM,
        /// <summary>
        /// 异常退出
        /// </summary>
        ANOMALYLOGINOUT,
        /// <summary>
        /// 舒尔特小游戏重新开始
        /// </summary>
        MSGBOX_MINIGAME_SCHULT_AGAIN,
        /// <summary>
        /// 退出并回到主窗口
        /// </summary>
        EXIT_GOTOMAINWINDOW,
        /// <summary>
        /// 替换积木块
        /// </summary>
        REPLACE_BLOCK,
        /// <summary>
        /// 课程重置环节
        /// </summary>
        COURSE_LINK_RESET,
        /// <summary>
        /// 网络状态提示
        /// </summary>
        HOT_UPDATE_ISWIFI,
        /// <summary>
        /// 联系老师免费领取试听课
        /// </summary>
        COURSE_CALL_TEACHER,
        /// <summary>
        /// 购买体验课
        /// </summary>
        COURSE_BUY_TESTCOURSE,
        /// <summary>
        /// 购买系统课
        /// </summary>
        COURSE_BUY_SYSTEMCOURSE,
    }

    // 消息ID
    public enum MessageBoxID 
    {
        COMMON = 0,     // 公用
        MSG_WKY = 100,   // 王康阳
        MSG_BLOCK=200,   //积木块
    }
    /// <summary>
    /// 提示框传输数据
    /// </summary>
    public class ShowMessageBoxData
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public MessageBoxID messageBoxID;
        /// <summary>
        /// 消息模块
        /// </summary>
        public MsgBoxModule msgBoxModule;
        /// <summary>
        /// 执行
        /// </summary>
        public MsgBoxAction msgBoxAction;
        /// <summary>
        /// 标题
        /// </summary>
        public string title;
        /// <summary>
        /// 内容
        /// </summary>
        public string content;
        /// <summary>
        /// 按钮数量及显示内容
        /// </summary>
        public Dictionary<int, string> dicTemp;

    }

}
