﻿/// <summary>
/// HandleCommandPlayAnimated
/// </summary>
/// <remarks>
/// 2021.6.8: 创建. 王康阳 <br/>
/// 实体播放指定动画动作命令<br/>
/// </remarks>
using System;
using GLib;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
	public class HandleCommandPlayAnimated : IHandleCommand
	{
		uint m_uidEntity;
		string m_animatedName;
		bool m_isEnd = false; // 是否不正确的执行完指令
		float m_fOverTime = 2f; // 超时
		float m_fOverWaittingTime; // 超时等待时间

		public HandleCommandPlayAnimated(SHandleCommand_PlayAnimated data)
		{
			m_uidEntity = data.EntityUID;
			m_animatedName = data.AnimatedName;
		}

		public virtual EHandleCommandType GetTypeEX()
		{ 
			return EHandleCommandType.PlayAnimated;
		}

		public virtual CommandsType GetCommandType()
		{
			return CommandsType.Default;
		}
		public void release()
		{
			m_uidEntity = 0;
			m_isEnd = false;
			m_fOverWaittingTime = 0;
		}

		public bool run()
		{
			// 超时的话就直接强制结束指令
			if (m_fOverWaittingTime >= m_fOverTime)
			{
				m_isEnd = true;
				return false;
			}
			IEntity entity = GlobalGame.Instance.EntityClient.Get(m_uidEntity);
			if(entity != null)
            {
				// 判断实体是否创建完毕
				bool isCreateFinish = GHelp.GetEntityFactory().EntityCreateIsFinish(entity.GetEntityViewID());
                if (isCreateFinish)
                {
					//animator.GetCurrentAnimatorStateInfo(0).IsName(action);
					// 播放实体动画
					PlayAnimationContext context = GHelp.GetObjectItem<PlayAnimationContext>();
                    context.name = m_animatedName;
					//if (GHelp.GetEntityFactory().EnitiyCreatureIsPlayFinish(entity.GetEntityViewID()))
					//{
					//	//不设置这个布尔值 会导致下次点击动画直接return ture
					//	if (m_StartPlayAniamtion)
					//	{
					//		m_StartPlayAniamtion = false;
					//		return true;
					//	}
					//}
					//m_StartPlayAniamtion = true;
					GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_PLAY_ANIMATION, 0, "", context);

					return true;
				}
                else
				{
					m_fOverWaittingTime += Time.deltaTime;
				}
            }
            else
			{
				m_fOverWaittingTime += Time.deltaTime;
			}
			return false;
		}

		public void update()
		{

		}
		public bool finish()
		{
			return m_isEnd;
		}

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{

		}
	}
}
