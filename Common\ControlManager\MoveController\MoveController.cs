﻿/*******************************************************************
** 文件名:	MoveController.cs
** 版  权:	(C) 深圳冰川网络股份有限公司
** 创建人:	代越强
** 日  期:	2016-03-3
** 版  本:	1.0
** 描  述:	移动控制器模块
** 应  用:  
	
**************************** 修改记录 ******************************
** 修改人: 
** 日  期: 
** 描  述: 
********************************************************************/
using game.common;
using game.proto;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
	public class CMoveController : IMoveController, IEventExecuteSink
	{
		//最近一次遥感状态
		private bool m_bLastJoyControl = false;


		private bool m_bLastSampingState = false;
		
		/// <summary>
		/// 模块名称(模块实现者不用设置)
		/// </summary>
		public string ModuleName { get; set; }

		/// <summary>
		/// 异步模块的加载状态(注释:异步模块专用)
		/// </summary>
		public EMModuleLoadState ModuleLoadState { get; set; }

		/// <summary>
		/// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
		/// </summary>
		public float Progress { get; set; }

		public Vector3 m_vLastPosition = Vector3.zero;
		public float m_fLastAngle = 0.0f;

		public List<Vector3> m_tempPaths = new List<Vector3>();
		/// <summary>
		/// 模块创建
		/// 如果是同步模块，Create成功就表示加载成功。
		/// 如果是异步模块, Create成功不表示加载成功，必须通过模块的ModuleLoadState属性来判断。
		/// </summary>
		/// <returns></returns>
		public bool Create() 
		{
			GlobalGame.Instance.RegisterModuleEvent(this, (int)EMModuleEvent.FixedUpdate);
			return true;
		}

		/// <summary>
		/// 模块释放
		/// </summary>
		public void Release() 
		{
			GlobalGame.Instance.UnRegisterModuleEvent(this);
		}

		////////////////模块驱动基础接口//////////////////////

		/// <summary>
		/// 每渲染帧
		/// </summary>
		public void Update() 
		{
			//CheckInput();
		}

		/// <summary>
		/// 每逻辑帧
		/// </summary>
		public void FixedUpdate()
		{
			HeroCheckInput();
		}

		/// <summary>
		/// LateUpdate更新
		/// </summary>
		public void LateUpdate() { }

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
		}

       /// <summary>
		/// 延路径点移动
		/// </summary>
		/// <param name="pathList"></param>
		/// <returns></returns>
        public bool MoveTarget(ICreature creature, List<Vector3> pathList,byte bFlag)
        {
			//TRACE.WarningLn("call MoveTarget...........");
			if (pathList == null || pathList.Count < 2)
				return false;
			//拷贝一份
			List<Vector3> newPathList = new List<Vector3>();
			for(int i = 0; i < pathList.Count ;i ++)
			{
				newPathList.Add(pathList[i]);
			}
			ICreature hero = creature;
			if(hero == null)
				hero = (ICreature)GlobalGame.Instance.EntityClient.GetHero();
            if (hero == null)
            {
                TRACE.ErrorLn("MoveController::OnEventSelMoveTarget hero == null");
                return false;
            }

			/*if (!hero.CanMove())
			{
				return false;
			}*/

			int dwPathLen = newPathList.Count;

			IPersonTankPart pTankPart = (IPersonTankPart)hero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);


			// 判断玩家是否在载具上面
			if (pTankPart != null && pTankPart.IsOnTank())
			{
				//不是驾驶员,不能移动
				if (!pTankPart.IsOnDriver())
				{
					return false;
				}
				//载具移动方式
				return pTankPart.DriverTankMove(newPathList, bFlag);
			}
			else
			{
				cmd_MovePos data = new cmd_MovePos();
				//data.nCount = (int)dwPathLen;
				data.bFlag = bFlag;
				data.fSendCmdTime = Time.realtimeSinceStartup;
				data.listPath = newPathList;

				SGameMsgHead head = default(SGameMsgHead);
				head.SrcEndPoint = (byte)ENDPOINT.Appclient;
				head.DestEndPoint = (byte)ENDPOINT.Scene;
				head.wKeyModule = (ushort)MSG_MODULEID.Entity;
				head.wKeyAction = (ushort)DGlobalMessage.MSG_ACTION_PREP3DMOVE;

				CPacketSend packet = new CPacketSend();
				for (int i = 0; i < newPathList.Count; i++)
				{
					packet.Push<Vector3>(newPathList[i]);
				}
				byte[] dataByte = packet.GetByte();
				CPacketRecv recvPackage = new CPacketRecv();
				recvPackage.Init(dataByte);
				(hero as ICreature).OnMessage((uint)DGlobalMessage.MSG_ACTION_PREP3DMOVE, head, recvPackage);

				GHelp.sendEntityCommand(hero.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_MOVE_POS, 0, "", data);

				return true;
			}
        }

		private void HeroCheckInput()
		{
			IEntityFactory entityViewFactory = GlobalGame.Instance.EntityFactory;
			if (entityViewFactory == null)
				return;
			bool bJoystickControl = false;

			bJoystickControl = entityViewFactory.IsJoyStickCtrol();

			IPerson pHero = GlobalGame.Instance.EntityClient.GetHero();
			if (pHero == null)
				return;
			I3DMoveManager moveManger = pHero.Get3DMoveManager();
			if (moveManger == null)
				return;

			//如果采样结束，需要强制停一下遥感
			if (m_bLastSampingState)
			{
				moveManger.JoystickControl = false;
				m_bLastSampingState = false;
				return;
			}

			// 正常状态采样
			//如果遥感状态没有变化,没有按下。
			if (!bJoystickControl && !m_bLastJoyControl)
			{
				return;
			}

			// 是否能移动
			bool CanMove = pHero.CanMove();

			bool bflag = bJoystickControl && CanMove;

			moveManger.JoystickControl = bflag;
			//更新遥感状态
			m_bLastJoyControl = bflag;
		}

		private Vector3 GenNewTarget()
		{
			return Vector3.zero;
		}

		public bool MoveEnd(long uid)
        {
			ICreature creature = GlobalGame.Instance.EntityClient.Get(uid) as ICreature;
			if (creature == null)
			{
				TRACE.ErrorLn("MoveController::OnEventSelMoveTarget hero == null");
				return false;
			}
			
			if (creature.GetEntityType() != EMEntityType.typeRobot)
            {
				IEntityPart pTankPart = creature.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
				if (pTankPart == null)
				{
					return false;
				}
			}

			GHelp.sendEntityCommand(creature.GetEntityViewID(), (int)EntityLogicDef.ENTITY_MOVE_END, 0, "", null);

			return true;
			

		}
	}
}
