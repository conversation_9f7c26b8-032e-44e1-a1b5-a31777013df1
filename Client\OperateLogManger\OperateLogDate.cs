﻿/// <summary>
/// OperateLogDate
/// </summary>
/// <remarks>
/// 2023/1/4 9:59:10: 创建. 但传红 <br/>
///  <br/>
/// </remarks>
using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
    public class OperateLogDate : Singleton<OperateLogDate>
    {
        private string m_NowTime = "00:00:00";
        public void SetNowTime(string time)
        {
            m_NowTime = time;
        }

        public string GetNowTime()
        {
            return m_NowTime;
        }
        private int m_NowTotleTime;

        public void SetNowTotleTime(int time)
        {
            m_NowTotleTime = time;
        }
        public int GetNowTotleTime()
        {
            return m_NowTotleTime;
        }
        private int Num = 0;
        private Dictionary<int, OperateLogDataDisPose> m_OperateLog = new Dictionary<int, OperateLogDataDisPose>();
        /// <summary>
        /// 无重复的
        /// </summary>
        /// <param name="Operate"></param>
        public void SetDate(OperateLogDataDisPose Operate)
        {
            if (m_OperateLog.ContainsKey(Num))
            {
                m_OperateLog.Remove(Num);
                // return;
            }
            if (m_OperateLog.ContainsValue(Operate))
            {
               // return;
            }
            Operate.Time = m_NowTime;
           
            foreach (var item in m_OperateLog)
            {
                if (item.Value.ID == Operate.ID && item.Value.OperateName == Operate.OperateName && item.Value.Time == Operate.Time)
                    return;
            }
            m_OperateLog.Add(Num, Operate);
            Num++;
        }
        public Dictionary<int, OperateLogDataDisPose> GetDate()
        {
            return m_OperateLog;
        }

        public List<OperateLogDataDisPose> m_Alldatas=new List<OperateLogDataDisPose>();

        public void SetOperateLogDate(OperateLogDataDisPose operateLog)
        {
            foreach (var item in m_Alldatas)
            {
                if (item.ID == operateLog.ID)
                    return;
            }
            m_Alldatas.Add(operateLog);
        }

        public List<OperateLogDataDisPose> GetOperateLog()
        {
            return m_Alldatas;
        }
        public string CaculteTime()
        {
            string time = "";
            int min = (int)m_NowTotleTime / 60;
            int sec = (int)(m_NowTotleTime - min * 60);
            time = String.Format("{0:D2}:{1:D2}", min, sec);
            return time;
        }
        public void Release()
        {
            m_OperateLog.Clear();
            m_Alldatas.Clear();
            Num = 0;
            m_NowTime = "00:00:00";
            m_NowTotleTime = 0;
        }
  
    }
}