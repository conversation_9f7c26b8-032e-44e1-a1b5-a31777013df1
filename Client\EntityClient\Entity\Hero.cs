﻿/// <summary>
/// Hero
/// </summary>
/// <remarks>
/// 2021.3.15: 创建. 谌安 <br/>
/// 主角 <br/>
/// </remarks>
/// 
using game.common;
using game.proto;
using game.scene;
using Game.Entity;
using GLib.Common;
using System;
using System.Collections.Generic;


namespace GLib.Client.Entity
{
    public class CHero : CPerson,IEventExecuteSink,IEventVoteSink
    {
		// 数值属性
		int[] m_nNumProp;

		// 当前状态
		//CCreatureState m_pCreatureCurState;

		// 死亡状态
		//CCreatureDieState m_CreatureDieState;

		// 服务器时间差异值
		int m_nServerTimeDiff;

		// 是否在场景上
		bool m_bInSceneFlag;
		/** 
        @param   
        @param   
        @return  
        */
		public CHero()
		{
			// 当前状态
			//m_pCreatureCurState = null;


			// 服务器时间
			m_nServerTimeDiff = 0;

			// 是否在场景上
			m_bInSceneFlag = false;

			m_bIsHero = true;
		}

		public override void Init()
		{
			base.Init();

			m_nNumProp = new int[(int)eEntityProp.EEntityMax];
			// 数值属性	
			for (int i = 0; i < (int)eEntityProp.EEntityMax; i++)
			{
				m_nNumProp[i] = 0;
			}

			// 订阅准备移动投票事件
			GlobalGame.Instance.EventEngine.Subscibe((IEventVoteSink)this, (ushort)DGlobalEvent.EVENT_CREATURE_PRE3DMOVE, (byte)GetEventSourceType(), (uint)UID_DATA.ANALYZEUID_SERIALNO(GetUID()), "");
		}

		/** 释放,会释放内存
        @param   
        @param   
        @return  
        */
		public override void Release()
		{
			// 订阅准备移动投票事件
			GlobalGame.Instance.EventEngine.UnSubscibe((IEventVoteSink)this, (ushort)DGlobalEvent.EVENT_CREATURE_PRE3DMOVE, (byte)GetEventSourceType(), (uint)UID_DATA.ANALYZEUID_SERIALNO(GetUID()));


			///////////////////////////////////////////////////////////////////
			// 发送事件
			/*SEventEntityDestroryEntity_C eventdestroryentity;
			eventdestroryentity.uidEntity = m_uid;
			byte bSrcType = GetEventSourceType();
			GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
									eventdestroryentity);*/

			///////////////////////////////////////////////////////////////////
			// 发送事件
			/*SEventPersonDestroyHero_C eventdestroyhero;
			eventdestroyhero.uidHero = m_uid;
			GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_PERSON_DESTROYHERO, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(Api.GuidCInt(m_uid)),
										eventdestroyhero);*/

			///////////////////////////////////////////////////////////////////
			// 释放部件
			for (int i = 0; i < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; i++)
			{
				if (m_pEntityPart[i] != null)
				{
					m_pEntityPart[i].Release();
					m_pEntityPart[i] = null;
				}
			}

            ///////////////////////////////////////////////////////////////////
            // 主角数据层不移除
            //((CEntityClient)GHelp.GetEntityClient()).Remove(this);
            //((CEntityClient)GHelp.GetEntityClient()).SetHero(null);

            ///////////////////////////////////////////////////////////////////

            // 从场景中移除掉
            if (m_nEntityViewID != 0 && m_bInSceneFlag)
			{
				EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
				Destroy.ENTITY_ID = GetEntityViewID();
				Destroy.ENTITY_UID = m_uid;

				// 移除实体视图
				GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);

				m_nEntityViewID = 0;

				m_bInSceneFlag = false;
			}

			// 消息槽
			m_MessageSlot.Close();
		}

		/** 创建
        @param   
        @param   
        @return  
        */
		public override bool Create(Int64 uid)
		{
			/*if (uid == Guid.Empty)
			{
				return false;
			}*/

			m_uid = uid;

			// 实体类型
			m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Person);

			TRACE.TraceLn("Hero::Create pos=" + m_Position);
			// 创建实体视图
			if (!CreateView())
				return false;

			// 是否在场景上
			m_bInSceneFlag = true;

			// 添加到实体世界中
			if (!((CEntityClient)GHelp.GetEntityClient()).Add(this))
			{
				return false;
			}

			((CEntityClient)GHelp.GetEntityClient()).SetHero(this);
			return true;
		}

		/** 设置数值属性
        @param   nValue ：属性值
        @param   
        @return  
        */
		public override bool SetNumProp(uint dwPropID, int nValue)
		{
			
			if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
			{
				return false;
			}

			m_nNumProp[dwPropID] = nValue;

			return true;
		}

		/** 取得数值属性
        @param   
        @param   
        @return  
        */
		public override int GetNumProp(uint dwPropID)
		{
			if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
			{
				return 0;
			}
			return m_nNumProp[dwPropID];
		}

		/** 设置字符属性
        @param   pszValue ：属性值
        @param   
        @return  
        */
		public override bool SetStrProp(uint dwPropID, string pszValue)
		{
			int _value = 0;
			GHelp.ConvertEntityPropInt((eEntityProp)dwPropID, pszValue, ref _value);
			m_nNumProp[dwPropID] = _value;
			return true;
		}

		/** 批量更新属性
	  @param   
	  @param   
	  @return  
	  */
		public override bool BatchUpdateProp(CPacketRecv pszProp, int nLen)
		{
			if (pszProp != null)
			{
				pszProp.Pop(out nLen);
				byte[] szValue = null;
				pszProp.ReadByteBuffer(out szValue, nLen);
				Entity_CreateHero response = Entity_CreateHero.Parser.ParseFrom(szValue);
				m_uid = Api.GuidCInt(response.Heroid);
				m_guid = response.Heroid;
				m_Position = new UnityEngine.Vector3(response.Position.X, response.Position.Y, response.Position.Z);
				m_entitiyType = EMEntityType.typeHero;

				foreach (ProItem item in response.Props)
				{
					SetStrProp((uint)item.PropType, item.PropValue);
				}
			}
			return true;
		}


		/** 消息
        @param   
        @param   
        @return  true：正常执行；false：被否决  
        */
		public override bool OnMessage(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			// 先状态过滤
			//if (m_pCreatureCurState != null && !m_pCreatureCurState.OnMessage(dwMsgID, pGameMsgHead, pszMsg))
			{
			//	return false;
			}

			// 转到消息槽中
			return m_MessageSlot.Fire(dwMsgID, pGameMsgHead, pszMsg); 
		}

		public bool OnVote(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			//状态处理投票事件
			//if (m_pCreatureCurState != null)
			{
			//	return m_pCreatureCurState.OnEventVote(wEventID, bSrcType, dwSrcID, pContext);
			}
			return true;
		}

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			//状态处理执行事件
			//if (m_pCreatureCurState != null)
			{
			//	m_pCreatureCurState.OnEventExecute(wEventID, bSrcType, dwSrcID, pContext);
			}
		}

		

		/** 
        @param   
        @param   
        @return  
        */
		public void RemoveFromScene()
		{
			// 从场景中移除掉
			if (m_nEntityViewID != 0 && m_bInSceneFlag)
			{

				EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
				Destroy.ENTITY_ID = GetEntityViewID();
				Destroy.ENTITY_UID = m_uid;

				// 移除实体视图
				GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);

				m_nEntityViewID = 0;

				m_bInSceneFlag = false;
			}
		}

		public void OnEnterFlyEvent()
		{

		}

		public void OnExitFlyEvent()
		{

		}

		public override bool GetBasicViewInfo(ref EntityViewItem item)
		{
			base.GetBasicViewInfo(ref item);
			item.EntityType = (byte)EMEntityType.typeHero;
			item.byIsHero = 1;
			//item.prefabPath = "Prefab/Character/Player/PCW001/p_c_w_001";
			item.szName = GlobalGame.Instance.GameSDK.GetUserInfo().username;// "testHeroName";
			int modelId = 1;
			m_nSkinID = item.nSkinID = modelId;
			item.bCreateImmediate = true;
			item.fMoveSpeed = GetMoveSpeed();
			return true;
		}

		
	}
}

