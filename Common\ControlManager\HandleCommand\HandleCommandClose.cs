﻿/// <summary>
/// HandleCommandClose
/// </summary>
/// <remarks>
/// 2021.4.8: 创建. 谌安 <br/>
/// 靠近指定坐标<br/>
/// </remarks>
using System;
using GLib;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
    public class CHandleCommandClose : IHandleCommand, IEventExecuteSink
	{
        // 每次最长移动的距离,移动太长了穿越九宫格时会出现同步问题
        const float MAX_MOVE_STEP = 64;
		// 检测移动状态时间间隔
		const int MOVE_STATE_CHECK_INTERVAL = 2000;
		// 最小距离
		const float MOVE_STATE_MIN_DISTANCE = 0.01f;
        // 路径超过多少米需要上马
        const float CHECK_HORSE_MIN_DISTANCE = 24.0f;

		//自动上马的距离
		const float RIDE_HORSE_DISTANCE = 0f;

        Int64           m_uidHero;
        protected       Vector3 m_ptTarget;      // 调整过后的目标点
        Vector3         m_ptSourceTarget;        // 最初的目标点
        //Vector3         m_ptSegmentEnd;          // 本次路段的终点,因为要分段发给服务器
        protected float m_fAvailableRadius;      // 有效距离
        int             m_nGiveupDistance;       // 放弃距离
        float           m_fKeepSpace;
	    bool            m_bIsMoving;
		protected float m_fMoveMaxDistance;		// 当前移动的最大距离(如果为0，表示不做限制)
		protected bool	m_bAdjustMoveDis = true;
	    protected   UInt32       m_dwRetryTimes;
		//移动失败的次数
		private UInt32 m_dwMoveFailTimes = 0;
		//载具移动标志
		//bool m_bTankMove = false;
        //private bool m_bMountMove = false;
        //private bool m_bMountDownMove = false;
		protected bool m_bAntoFindPath = true;
		protected bool m_bAdjustEndPos = true;
		// 检测移动状态开始时间
		private int m_nCheckMoveStateStartTick = 0;
		private bool m_bFirstCheck = true;//第一次检查是否达到终点
        // 请求进入骑马状态
        //bool m_bRequestEnterHorseState = true;
		//需要召唤坐骑
		private bool m_bNeedCallHorse = false;
		//已经移动的距离
		private float m_fHasMoveDistance = 0.0f;
		//起点
		private Vector3 m_vStartPos = Vector3.zero;

        private bool m_bIsDIE = false;

		bool m_isEnd = false; // 是否不正确的执行完指令

		uint m_uidEntity;

		float m_fOverTime = 2f; // 超时
		float m_fOverWaittingTime; // 超时等待时间

		private float m_fRotation; // 转动角度

		private MoveMode m_eMoveMode;

		private List<Vector3> m_pathList = new List<Vector3>();

		//////////////////////////////////////////////////////////////////////////
		public CHandleCommandClose(SHandleCommand_Close data)
		{
			m_uidEntity = data.EntityUID;
			m_ptTarget =data.ptTargetTile;
            m_ptSourceTarget = data.ptTargetTile;
            m_fAvailableRadius = data.fDistance;
            m_nGiveupDistance = data.nGiveupDistance == 0 ? 4096 : data.nGiveupDistance;
			m_fMoveMaxDistance = data.fMoveMaxDistance;
			m_fKeepSpace =0;
			m_uidHero=DGlobalGame.INVALID_UID;
			m_dwRetryTimes=0;
			m_bIsMoving=false;
			m_bAntoFindPath = data.bAutoFindPath;
			m_eMoveMode = data.moveMode;
			m_pathList = new List<Vector3>();
			if(data.pathList != null)
            {
				m_pathList = data.pathList;
            }
			//IPerson pEntityHero =GlobalGame.Instance.EntityClient.GetHero();
			IEntity entity = GlobalGame.Instance.EntityClient.Get(m_uidEntity);
            if (entity == null)
            {
                return ;
            }
		        
	        m_uidHero = entity.GetUID();
			m_vStartPos = entity.GetPosition();
			//m_ptSegmentEnd.x = 0;
			//m_ptSegmentEnd.y = 0;
			m_fRotation = data.fRotation;

			GlobalGame.Instance.EventEngine.Subscibe(this, (int)EntityLogicDef.ENTITY_STOP_MOVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "同步实体位置");
		}

		public virtual EHandleCommandType GetTypeEX()
		{
			return EHandleCommandType.Close;
		}

		public virtual CommandsType GetCommandType()
		{
			return CommandsType.EntityProperty;
		}

		public void restart(Vector3 ptTarget, float fAvailableRadius, int nKeepSpace)
        {
	        m_ptTarget = ptTarget;
	        m_ptSourceTarget = ptTarget;
	        m_fAvailableRadius = fAvailableRadius;
	        m_fKeepSpace = nKeepSpace;
	        m_dwRetryTimes = 0;
			m_dwMoveFailTimes = 0;
	        m_bIsMoving = false;
			//m_bMountMove = false;
			//m_ptSegmentEnd.x = 0;
			//m_ptSegmentEnd.y = 0;

			//IPerson pEntityHero = GlobalGame.Instance.EntityClient.GetHero();
			IEntity entity = GlobalGame.Instance.EntityClient.Get(m_uidEntity);
			if (entity == null)
			{
				return;
			}

			m_uidHero = entity.GetUID();
			m_vStartPos = entity.GetPosition();

			
			GlobalGame.Instance.EventEngine.UnSubscibe(this, (int)EntityLogicDef.ENTITY_STOP_MOVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
		}

        //////////////////////////////////////////////////////////////////////////
		public virtual void release()
        {
			//IPerson pEntityHero = GlobalGame.Instance.EntityClient.GetHero();
			IEntity entity = GlobalGame.Instance.EntityClient.Get(m_uidEntity);
			if (entity != null)
			{
				//删除光标(地面点击光效)
				GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, 2, "", null);
			}
			GlobalGame.Instance.MoveController.MoveEnd(m_uidEntity);
			m_isEnd = false;
			m_uidEntity = 0;
			m_fOverWaittingTime = 0f;
			m_pathList.Clear();
			//移动完了后调整角度
			uint entityViewId = entity.GetEntityViewID();
            if (m_fRotation > -9997)
            {
				cmd_RotateAngle c_data = new cmd_RotateAngle()
				{
					Angle = m_fRotation,
					targetAngle = m_fRotation,
					timeCount = 1
				};
				//获取实体 然后旋转
				GHelp.sendEntityCommand(entityViewId, (int)EntityLogicDef.ENTITY_ROTATION_BY_ONE, 0, "", c_data);
			}

			GlobalGame.Instance.EventEngine.UnSubscibe(this, (int)EntityLogicDef.ENTITY_STOP_MOVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
		}

        //////////////////////////////////////////////////////////////////////////

        public virtual bool run()
        {
			// 超时的话就直接强制结束指令
			if(m_fOverWaittingTime >= m_fOverTime)
            {
				m_isEnd = true;
				return false;
            }

            //IPerson pEntityHero = GlobalGame.Instance.EntityClient.GetHero();
			IEntity entity = GlobalGame.Instance.EntityClient.Get(m_uidEntity);
			if (entity == null)
            {
				m_fOverWaittingTime += Time.deltaTime;
                TRACE.ErrorLn("机器人还没有创建，自动寻路失败！");
                return false;
            }else if(m_uidHero == 0)
            {
				m_uidHero = entity.GetUID();
				m_vStartPos = entity.GetPosition();
			}

			Vector3 ptSource = entity.GetPosition();//GetSourcePos();

            if(m_bIsDIE)
            {
				// 已经死亡打断寻路
				//GHelp.sendEntityCommand(pEntityHero.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ClEARPATH, 0, "", null);
				m_isEnd = true;
				return false;
			}

            INavigationManager navMgr = GlobalGame.Instance.NavigationManager;
            if (navMgr == null)
			{
				m_isEnd = true;
				return false;
			}


			//载具有可能还没有创建出来，坐标为(0,0,0),暂时用人物的坐标，不然会出现寻路失败，退出命令的情况
			/*if (ptSource.x < 0.00001&&ptSource.x>-0.00001f && ptSource.y < 0.00001)
			{
				ptSource = pEntityHero.GetPosition();
			}*/
       
            // 因为在切换地图时，客户端猛点地图，使得不停地向服务器发路径，
	        // 有可能发的路径还是上一张地图的，这样就出现将上张地图的路径
	        // 发到服务器来，就会出现人物移动第一步失败"的问题，因为上张地
	        // 图的节点在本地图可能是占位的，如果返回无效的tile，表示是正在
	        // 切换地图
			
			// 上马或者上坐骑可能取到坐标为0，0点,所有这里重试一下
			if (Math.Abs(ptSource.x) < 0.001f && Math.Abs(ptSource.z) < 0.001f)
	        {
				m_dwRetryTimes++;
				if(m_dwRetryTimes < 3)
					TRACE.ErrorLn("严重错误!!! HandleCommandClose坐标非法了，pos=" + ptSource);
	        }
         
	        // 不要拼命重试，防止卡死
	        if ( m_dwRetryTimes>256)
			{
				m_isEnd = true;
				return false;
			}

			//如果移动失败次数达到256，就结束该命令
			if (m_dwMoveFailTimes > 256)
            {
				m_isEnd = true;
				return false;
            }

			Vector2 TempSource = new Vector2(ptSource.x, ptSource.z);
            Vector2 TempTarget = new Vector2(m_ptTarget.x, m_ptTarget.z);

			if (m_bFirstCheck)
			{
				float dis = Vector2.SqrMagnitude(TempTarget - TempSource) - (m_fAvailableRadius * m_fAvailableRadius);
				// 不用寻路就已经达到目标点
				if (dis <= MOVE_STATE_MIN_DISTANCE)
				{
					GHelp.FireExecute((ushort)ViewLogicDef.MANUAL_MOVETO_END, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
					return true;
				}
				m_bFirstCheck = false;
			}
           

	        // 不能直接通过IsMoving判断，否则第一次点击时，如果正在移动则不会响应,操作感很不好
	        if (m_bIsMoving==false)
	        {
		        // 到达目的地
				float nDis = Vector2.SqrMagnitude(TempTarget - TempSource);
				if (nDis <= MOVE_STATE_MIN_DISTANCE)
                {
					GHelp.FireExecute((ushort)ViewLogicDef.MANUAL_MOVETO_END, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
					return true;
		        }

				List<Vector3> FindPath = null;
				if(m_pathList != null && m_pathList.Count > 0)
                {
					FindPath = m_pathList;
                }
                else
                {
					FindPath = navMgr.FindPath(ptSource, m_ptTarget);
				}

                int nCount = FindPath.Count;
				//在路径上面调整一下终点
				/*if (m_bAdjustEndPos && nCount > 1 && m_fAvailableRadius > 0.1f)
				{
					GHelp.AdjustPahtListAtEnd(ref FindPath, m_fAvailableRadius);
					//重新取一次长度
					nCount = FindPath.Count;
					m_bAdjustEndPos = false;
					//重置目标点
					m_ptTarget = FindPath[nCount-1];
				}*/

                if (nCount > 1)
                {
                    //目的地可能不在导航网格上面，findPath会找一个最近的点。所以需要重新设置目标点
                    m_ptTarget = FindPath[nCount - 1];
                    TempTarget.x = m_ptTarget.x;
                    TempTarget.y = m_ptTarget.z;
                    // 太远则放弃
                    if (nCount > m_nGiveupDistance)
					{
						m_isEnd = true;
						return false;
					}

					//移动距离限制，修正FindPath列表
					/*if (m_bAdjustMoveDis && m_fMoveMaxDistance > 0.1f)
					{
						m_bAdjustMoveDis = false;
						if(GameHelp.AdjustPahtListAtBegin(ref FindPath, m_fMoveMaxDistance))
						{
							//路径被截断，半径设置为0
							//m_fAvailableRadius = 0.0f;
							nCount = FindPath.Count;
							m_ptTarget = FindPath[nCount - 1];
							TempTarget.x = m_ptTarget.x;
							TempTarget.y = m_ptTarget.z;
						}					
					}*/
                    
					byte bFlag = 0;
					
                    //向目标移动
					if (!GlobalGame.Instance.MoveController.MoveTarget((ICreature)entity, FindPath, bFlag))
                    {
						m_dwMoveFailTimes++;
                        return false;
                    }

					//MoveTarget成功就认为移动成功了，然后每隔2s检测一次移动状态
					m_nCheckMoveStateStartTick = Api.GetTickCount() + MOVE_STATE_CHECK_INTERVAL;
					m_bIsMoving = true;

					//先删除光标(寻路中...)
					//GameHelp.closeBuffEffect(25);
					//是否自动寻路
					if (m_bAntoFindPath)
					{
						//再创建光标
						//GameHelp.createBuffEffect(25);
					}

					// 获取光效目标对象
					//IEntity pTargetEntity = pEntityHero;
					//IPersonTankPart pTankPart = (IPersonTankPart)pEntityHero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
					//if (pTankPart != null)
					//{
					//	ITank tank = pTankPart.GetTank();
					//	if(tank != null)
					//	{
					//		pTargetEntity = tank;
					//	}
					//}
					
                   
                    //先删除光标(地面点击光效)
					GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, 2, "", null);

					/*if(!bIsFlying)
					{
						//再创建光标
						SkillEffectContext effectContext = GameHelp.GetObjectItem<SkillEffectContext>();
						effectContext.id = 1001;
						effectContext.ptTarget = FindPath[nCount - 1];
						effectContext.ptCenter = FindPath[nCount - 1];
						effectContext.target = 0;
						GameHelp.sendEntityCommand(pTargetEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ADD_LIGHTING, 2, "", effectContext);
					}*/
                   
                    /*if (pEntityHero.IsHero())
                    {
                        UInt32 nCurState = pEntityHero.GetCurState();
                        if (nCurState != (UInt32)EMCREATURE_CONTROLSTATE.CREATURE_CONTROLSTATE_RIDE)
                        {
                            m_bMountMove = true;
							GHelp.sendEntityCommand(pTargetEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, 510, "", null);
                            SkillEffectContext context = GameHelp.GetObjectItem<SkillEffectContext>();
                            context.id = 510;  // 效果Id;
							context.target = context.src = pTargetEntity.GetEntityViewID();      // 技能效果发起者
							GHelp.sendEntityCommand(pTargetEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ADD_LIGHTING, 510, "", context);
                            m_bMountDownMove = false;
                        }
                    }*/
                   
                    m_dwRetryTimes++;
                }
                else
                {
					//寻路失败，退出当前命令
					m_isEnd = true;
					return false;
				}
	        }
			else
			{

				// 获取光效目标对象
				//IEntity pTargetEntity = pEntityHero;
				//IPersonTankPart pTankPart = (IPersonTankPart)pEntityHero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
				//if (pTankPart != null)
				//{
				//	ITank tank = pTankPart.GetTank();
				//	if (tank != null)
				//	{
				//		pTargetEntity = tank;
				//	}
				//}
                /*if(pEntityHero.GetCurState() == (uint)(EMCREATURE_CONTROLSTATE.CREATURE_CONTROLSTATE_RIDE))
                {
                    if(m_bMountMove)
                    {
						GameHelp.sendEntityCommand(pTargetEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, 510, "", null);
                        m_bMountMove = false;
                        m_bMountDownMove = true;
                    }
                }
                else
                {
                    if(m_bMountDownMove)
                    {
						GameHelp.sendEntityCommand(pTargetEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, 510, "", null);
                        SkillEffectContext context = GameHelp.GetObjectItem<SkillEffectContext>();
                        context.id = 510;  // 效果Id;
						context.target = context.src = pTargetEntity.GetEntityViewID();      // 技能效果发起者
						GameHelp.sendEntityCommand(pTargetEntity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ADD_LIGHTING, 510, "", context);
                        m_bMountDownMove = false;
                        m_bMountMove = true;
                    }
                }*/

				//由于现在检测isMoving状态的间隔很长，所以即使当isMoving = true的时候，有可能已经停下来了
				// 到达目的地
				float nDis = Vector2.SqrMagnitude(TempTarget - TempSource);
				if (nDis <= MOVE_STATE_MIN_DISTANCE)
				{
					GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, 510, "", null);
					GHelp.FireExecute((ushort)ViewLogicDef.MANUAL_MOVETO_END, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
					return true;
				}

				if(m_bNeedCallHorse)
				{
					m_vStartPos.y = 0;
					ptSource.y = 0;
					m_fHasMoveDistance = Vector3.Distance(m_vStartPos, ptSource);
				}

			}

			if (Api.GetTickCount() > m_nCheckMoveStateStartTick)
			{
				m_bIsMoving = IsSourceMoving((ICreature)entity);
				//后面检测快一点
				m_nCheckMoveStateStartTick += MOVE_STATE_CHECK_INTERVAL/20;
			}
	        
			
	        // 返回false表示继续执行
	        return false;
        }

		/// <summary>
		/// 获取源位置
		/// </summary>
		/// <returns></returns>
		private Vector3 GetSourcePos()
		{
			IPerson pEntityHero = GHelp.GetHero();
			if (pEntityHero == null)
				return Vector3.zero;
			Vector3 ptSource;
			ITank tank = null;
			//判断是否在载具上面
			//if (pEntityHero.GetCurState() == (uint)(EMCREATURE_CONTROLSTATE.CREATURE_CONTROLSTATE_DRIVE))
			{
				IPersonTankPart pTankPart = (IPersonTankPart)pEntityHero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
				if (pTankPart == null)
					return Vector3.zero;
				tank = pTankPart.GetTank();
			}

			//取当前位置
			if (tank != null)
			{
				ptSource = tank.GetPosition();
			}
			else
			{
				ptSource = pEntityHero.GetPosition();
			}

			return ptSource;
		}

		/// <summary>
		/// 是否在移动
		/// </summary>
		/// <returns></returns>
		public bool IsSourceMoving(ICreature creature)
		{
			ICreature pEntityHero = creature;
			if (pEntityHero == null)
			{
				pEntityHero = GHelp.GetHero();
			}
			if (pEntityHero == null)
				return false;
			ITank tank = null;
			//判断是否在载具上面
			//if (pEntityHero.GetCurState() == (uint)(EMCREATURE_CONTROLSTATE.CREATURE_CONTROLSTATE_DRIVE))
			{
				IPersonTankPart pTankPart = (IPersonTankPart)pEntityHero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
				if (pTankPart != null)
					tank = pTankPart.GetTank();
			}

			//取当前位置
			if (tank != null)
			{
				return tank.IsMoving();
			}
			else
			{
				return pEntityHero.IsMoving();
			}
		}

        //////////////////////////////////////////////////////////////////////////
        public virtual void update()                                                    
        {

        }
		public bool finish()
		{
			return m_isEnd;
		}

		// 是否启用长距离特殊搜路
		bool IsUseLongFind()
        {
	        return false;
        }

		/// <summary>
		/// 暂停,命令被备份时，会调用
		/// </summary>
		public void OnPause()
		{
			m_bNeedCallHorse = true;
		}

		public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
		{
			switch (wEventID)
			{
				case (int)EntityLogicDef.ENTITY_STOP_MOVE_OPCODE:
					{
						string str = pContext.ToString();

						uint uid = uint.Parse(str);
                        // 如果是瞬移当前积木块移动的对象，则停止该积木块指令
                        if (m_uidEntity != 0 && uid != 0 && m_uidEntity == uid)
                        {
                            m_isEnd = true;
                        }
                    }
					break;
				default: break;
			}

		}
	}
}