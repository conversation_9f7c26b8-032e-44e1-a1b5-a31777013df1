﻿#define Portrait_Screen_Model
/// <summary>
/// CBoot
/// </summary>
/// <remarks>
/// 2019.6.24: 创建. 谌安 <br/>
/// 游戏初始化管理<br/>
/// </remarks>
using GLib.Common;
using System;
using System.Collections;
using UnityEngine;
namespace GLib.Boot
{
    public class StateManagerModule : IStateManagerModule, IEventExecuteSink
    {
        public string ModuleName { get; set; }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress { get; set; }

        /// <summary>
        /// 模块同步创建.
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
            GlobalGame.Instance.EventEngine.Subscibe((IEventExecuteSink)this, (ushort)DGlobalEvent.EVENT_STATE_CHANGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "Game State Change");
            return true;
        }

        public void Release()
        {
            // 反注册事件
            if (GlobalGame.Instance.EventEngine == null)
            {
                TRACE.ErrorLn("CGameStateManagerModule failed! EventEngine == null.");
                return;
            }

            GlobalGame.Instance.EventEngine.UnSubscibe((IEventExecuteSink)this, (ushort)DGlobalEvent.EVENT_STATE_CHANGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
        }

        public void Update()
        {
        }

        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }

        public void ChangeState(LoginViewState state)
        {
        }

        public LoginViewState GetCurState()
        {
            return LoginViewState.None;
        }

        public bool IsStateChanging()
        {
            return false;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EVENT_STATE_CHANGE:
                    {
                        SEventGameStateChange stageData = (SEventGameStateChange)pContext;

                        if (stageData.nOldState == (int)GameState.Init && stageData.nNewState == (int)GameState.Login)
                        {
                        }

                        Debug.Log("OldState:" + stageData.nOldState + "  NewState:" + stageData.nNewState);
                    }
                    break;
            }
        }
    }
}
