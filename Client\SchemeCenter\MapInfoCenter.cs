﻿/// <summary>
/// MapInfoCenter
/// </summary>
/// <remarks>
/// 2019.7.19: 创建. 谌安 <br/>
/// 地图信息中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class MapInfoCenter : ISchemeNode, IMapInfoCenter
    {
        private const string MAP_INFO = "Scene";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, MapInfoDef> m_mapInfoByID;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public MapInfoCenter()
        {
            m_mapInfoByID = new Dictionary<int, MapInfoDef>();
        }

        ~MapInfoCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = MAP_INFO;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                MapInfoDef map = new MapInfoDef();

                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.SceneName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.BornPosition = GHelp.StringToVector3(pCSVReader.GetString(nRow, tmp_col++, ""));
                map.BornAngle = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                map.BackgroundMusic = pCSVReader.GetString(nRow, tmp_col++, "");
                map.CameraPosition = GHelp.StringToVector3(pCSVReader.GetString(nRow, tmp_col++, ""));
                map.CameraLookAt = GHelp.StringToVector3(pCSVReader.GetString(nRow, tmp_col++, ""));
                map.CameraFieldView = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                map.MapName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SceneOtherPrefab = pCSVReader.GetString(nRow, tmp_col++, "");
                map.SceneOtherPrefabPos = pCSVReader.GetString(nRow, tmp_col++, "");
                map.IsSceneLoadFinish = pCSVReader.GetString(nRow, tmp_col++, "");
                m_mapInfoByID.Add(map.Id, map);
            }
            //string s = ProductIni.Instance.GetString("StartUpScene", "0");
            // 配置文件加载完成，初次进行0号场景加载
            GHelp.ChangeScene(0, Vector3.zero);
            return true;
        }

        public void Release()
        {
            m_mapInfoByID.Clear();
            m_mapInfoByID = null;
        }

        public MapInfoDef GetMapInfoByID(int tID)
        {
            MapInfoDef info = null;

            m_mapInfoByID.TryGetValue(tID, out info);

            return info;
        }
    }
}
