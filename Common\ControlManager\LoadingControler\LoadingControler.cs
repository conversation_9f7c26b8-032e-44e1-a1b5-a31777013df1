﻿/// <summary>
/// LoadingController
/// </summary>
/// <remarks>
/// 2022/1/13 14:39:16: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// LoadingController
    /// </summary>
    public class LoadingControler : ILoadingControler
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        /// <summary>
        /// Loading状态
        /// </summary>
        private bool m_LoadState;
        public bool Create()
        {
            return true;
        }

        public void FixedUpdate()
        {
        }

        public bool GetLoadingState()
        {
            return m_LoadState;
        }

        public void LateUpdate()
        {
        }

        public void Release()
        {
        }

        public void SetLoadingState(bool bState)
        {
            m_LoadState = bState;
        }

        public void Update()
        {
        }
    }
}
