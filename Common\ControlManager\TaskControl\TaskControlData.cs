﻿/// <summary>
/// TaskControlDef
/// </summary>
/// <remarks>
/// 2021/7/23 18:29:27: 创建. 王正勇 <br/>
/// 任务模块数据
/// </remarks>
using game.scene;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// TaskControlDef
    /// </summary>
    public class TaskControlData
    {
        /// <summary>
        /// 任务数据
        /// </summary>
        private Dictionary<int, TaskInfo> m_dicTaskInfo;
        private Dictionary<string, int> m_dicTaskGuID;
        /// <summary>
        /// 注册任务UI
        /// </summary>
        private Dictionary<int, ITaskEvent> m_DicTaskEvent;
        /// <summary>
        /// 当前任务
        /// </summary>
        private TaskInfo m_NowTaskInfo;
        /// <summary>
        /// 通用任务事件
        /// </summary>
        private ITaskEvent m_GeneralTaskEvent;
        public TaskControlData()
        {
            m_DicTaskEvent = new Dictionary<int, ITaskEvent>();
            m_dicTaskInfo = new Dictionary<int, TaskInfo>();
            m_dicTaskGuID = new Dictionary<string, int>();
        }

        #region 事件数据
        public void AddTaskDataList(TaskInfo taskDef)
        {
            if (!m_dicTaskInfo.ContainsKey(taskDef.TaskId))
            {
                m_dicTaskInfo.Add(taskDef.TaskId, taskDef);
                m_dicTaskGuID.Add(taskDef.TaskInstanceId, taskDef.TaskId);
            }
        }
        /// <summary>
        /// 设置当前任务
        /// </summary>
        /// <param name="taskDef"></param>
        public void SetNowTaskInfo(TaskInfo taskDef)
        {
            m_NowTaskInfo = taskDef;
        }
        public TaskInfo GetTaskDefFrist()
        {
            return m_dicTaskInfo[0];
        }
        /// <summary>
        /// 获取制定任务信息
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns></returns>
        public TaskInfo GetTaskInfo(int taskId)
        {
            TaskInfo taskInfo = m_NowTaskInfo;
            if (m_dicTaskInfo == null || !m_dicTaskInfo.ContainsKey(taskId))
            {
                return taskInfo;
            }
            if (m_dicTaskInfo.ContainsKey(taskId))
            {
                taskInfo = m_dicTaskInfo[taskId];
            }
            return taskInfo;
        }
        /// <summary>
        /// 获取制定任务信息
        /// </summary>
        /// <param name="taskGuid">任务Guid</param>
        /// <returns></returns>
        public TaskInfo GetTaskInfo(string taskGuid)
        {
            TaskInfo taskInfo = null;
            if (m_dicTaskInfo == null || m_dicTaskInfo.Count <= 0)
            {
                return taskInfo;
            }
            taskInfo = FindTaskGuidData(taskGuid);
            return taskInfo;
        }
        /// <summary>
        /// 查找满足条件的数据
        /// </summary>
        /// <param name="taskGuid"></param>
        /// <returns></returns>
        private TaskInfo FindTaskGuidData(string taskGuid)
        {
            TaskInfo taskInfo = m_NowTaskInfo;
            int taskId = 0;
            if (m_dicTaskGuID.ContainsKey(taskGuid))
            {
                taskId = m_dicTaskGuID[taskGuid];
            }
            if (m_dicTaskInfo.ContainsKey(taskId))
            {
                taskInfo = m_dicTaskInfo[taskId];
            }
            return taskInfo;
        }

        /// <summary>
        /// 获取当前任务数据
        /// </summary>
        /// <returns></returns>
        public TaskInfo GetNowTaskInfo(int taskId)
        {
            if (m_NowTaskInfo == null)
            {
                if (m_dicTaskInfo.ContainsKey(taskId))
                {
                    m_NowTaskInfo = m_dicTaskInfo[taskId];
                }
            }
            return m_NowTaskInfo;
        }
        /// <summary>
        /// 移除任务数据
        /// </summary>
        /// <param name="taskid"></param>
        /// <returns></returns>
        public bool RemoveTaskInfo(int taskid)
        {
            if (m_NowTaskInfo.TaskId == taskid)
            {
                m_NowTaskInfo = null;
            }
            if (m_dicTaskInfo == null || !m_dicTaskInfo.ContainsKey(taskid))
            {
                return true;
            }
            m_dicTaskInfo.Remove(taskid);
            string strGuid = GetTaskGuID(taskid);
            if (!string.IsNullOrEmpty(strGuid))
            {
                m_dicTaskGuID.Remove(strGuid);
            }
            return true;
        }
        private string GetTaskGuID(int taskid)
        {
            string strGuid = string.Empty;
            foreach (KeyValuePair<string, int> keyValuePair in m_dicTaskGuID)
            {
                if (keyValuePair.Value == taskid)
                {
                    strGuid = keyValuePair.Key;
                }
            }
            return strGuid;
        }
        public void CloseTaskDefList()
        {
            m_dicTaskInfo.Clear();
            m_dicTaskGuID.Clear();
            m_NowTaskInfo = null;
        }
        #endregion

        #region 任务事件
        /// <summary>
        /// 添加任务订阅
        /// </summary>
        /// <param name="taskGuid"></param>
        /// <param name="taskEvent"></param>
        public void AddTaskEventList(int taskId, ITaskEvent taskEvent)
        {
            if (!m_DicTaskEvent.ContainsKey(taskId))
            {
                m_DicTaskEvent.Add(taskId, taskEvent);
            }
        }
        public void SetGeneralTaskEvent(ITaskEvent taskEvent)
        {
            m_GeneralTaskEvent = taskEvent;
        }
        /// <summary>
        /// 获取任务数据
        /// </summary>
        /// <param name="taskGuid"></param>
        /// <returns></returns>
        public ITaskEvent GetTaskEvent(int taskId)
        {
            ITaskEvent taskEvent = m_GeneralTaskEvent;
            if (m_DicTaskEvent == null || m_DicTaskEvent.Count <= 0)
            {
                return taskEvent;
            }
            if (m_DicTaskEvent.ContainsKey(taskId))
            {
                taskEvent = m_DicTaskEvent[taskId];
            }
            return taskEvent;
        }
        /// <summary>
        /// 移除注册事件
        /// </summary>
        /// <param name="taskGuid"></param>
        /// <returns></returns>
        public bool RemoveTaskEvent(int taskId)
        {
            m_GeneralTaskEvent = null;
            if (m_DicTaskEvent.ContainsKey(taskId))
            {
                m_DicTaskEvent.Remove(taskId);
            }
            return true;
        }
        public void CloseTaskEventList()
        {
            m_DicTaskEvent.Clear();
            m_GeneralTaskEvent = null;
        }
        #endregion
    }
}
