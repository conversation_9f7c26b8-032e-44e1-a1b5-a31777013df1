﻿/// <summary>
/// CommonApi
/// </summary>
/// <remarks>
/// 2021.4.26: 创建. 谌安 <br/>
/// 技能包装<br/>
/// </remarks>
using System;
using UnityEngine;
using GLib.Common;

namespace GLib.Common
{
    public class CEffectViewManager : IEffectViewManager
    {

        private float m_Progress;
        IEffectView m_EffectView;
        ISkillView m_SkillView;

        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName
        {
            get;
            set;
        }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState
        {
            get;
            set;
        }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress
        {
            get { return m_Progress; }
            set { m_Progress = value; }
        }

        /// <summary>
        /// 模块创建
        /// 如果是同步模块，Create成功就表示加载成功。
        /// 如果是异步模块, Create成功不表示加载成功，必须通过模块的ModuleLoadState属性来判断。
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
            m_EffectView = new CEffectView();
            m_SkillView = new CSkillView();
            return true;
        }

        /// <summary>
        /// 模块释放
        /// </summary>
        public void Release()
        {

        }

        ////////////////模块驱动基础接口//////////////////////

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {

        }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {

        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {

        }

        public IEffectView EffectView { get { return m_EffectView; } }

        public ISkillView SkillView { get { return m_SkillView; } }
    }
    
}
