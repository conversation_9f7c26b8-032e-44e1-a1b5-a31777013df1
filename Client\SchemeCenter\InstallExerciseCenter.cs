﻿/// <summary>
/// HeroInfoCenter
/// </summary>
/// <remarks>
/// 2021.5.8: 创建. 吴航 <br/>
/// 机器人目标(子物体)表数据处理中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;

namespace GLib.Client
{
    public class InstallExerciseCenter : ISchemeNode, IInstallExerciseCenter
    {
        /// <summary>
        /// 完成的标数据
        /// </summary>
        private InstallExerciseDef m_InstallExerciseFinish;

        private List<InstallExerciseDef> m_InstallExerciseList;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public InstallExerciseCenter()
        {
            m_InstallExerciseList = new List<InstallExerciseDef>();
        }

        ~InstallExerciseCenter()
        {
        }

        public bool Create()
        {
            return true;
        }

        public bool LoadScheme(string path)
        {
            m_InstallExerciseList.Clear();
            string strPath = path;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Data);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Data(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            //最后一行用来配置步骤完成相关东西 所以不算在步骤List里面
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                InstallExerciseDef data = new InstallExerciseDef();
                data.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.StepId = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.StepName = pCSVReader.GetString(nRow, tmp_col++, "");
                data.StepTip = pCSVReader.GetString(nRow, tmp_col++, "");
                data.Tool = pCSVReader.GetString(nRow, tmp_col++, "");
                data.OperateModel = pCSVReader.GetString(nRow, tmp_col++, "");
                data.OperateType = pCSVReader.GetString(nRow, tmp_col++, "");
                data.TipVoice = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.PrefabPath = pCSVReader.GetString(nRow, tmp_col++, "");
                data.MoveType = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.IsMiddlePos = pCSVReader.GetString(nRow, tmp_col++, "");
                data.StepScore = pCSVReader.GetString(nRow, tmp_col++, "");
                //预留字段
                data.IntValue1 = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.IntValue2 = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.IntValue3 = pCSVReader.GetInt(nRow, tmp_col++, 0);
                data.StrValue1 = pCSVReader.GetString(nRow, tmp_col++, "");
                data.StrValue2 = pCSVReader.GetString(nRow, tmp_col++, "");
                data.StrValue3 = pCSVReader.GetString(nRow, tmp_col++, "");
                if (nRow < nRecordCount - 1)
                {
                    m_InstallExerciseList.Add(data);
                }
                else
                {
                    m_InstallExerciseFinish = data;
                }
            }

            return true;
        }

        public void Release()
        {
            m_InstallExerciseList.Clear();
        }
      
        public List<InstallExerciseDef> GetAllInstallExerciseInfo()
        {
            return m_InstallExerciseList;
        }

        public InstallExerciseDef GetInstallExerciseInfoById(int idtype,int id)
        {
            switch (idtype)
            {
                case 1:
                    return m_InstallExerciseList.Find(item => item.Id == id);
                case 2:
                    return m_InstallExerciseList.Find(item => item.StepId == id);
            }
            return null;
          
        }

        public InstallExerciseDef GetInstallExerciseFinishInfo()
        {
            return m_InstallExerciseFinish;

        }
    }
}

