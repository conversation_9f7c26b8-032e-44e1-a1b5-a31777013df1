﻿/// <summary>
/// FileApiDef
/// </summary>
/// <remarks>
/// 2021/10/27 16:57:53: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public enum FileTypeDef
    {
        /// <summary>
        /// 录音
        /// </summary>
        app_uploaded_vocie,
        /// <summary>
        /// 头像
        /// </summary>
        avatar,
        /// <summary>
        /// screen_capture
        /// </summary>
        screen_capture,
        /// <summary>
        /// 意见反馈
        /// </summary>
        app_feedback
    }
}
