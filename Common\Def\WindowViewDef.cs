﻿/// <summary>
/// WindowViewDef
/// </summary>
/// <remarks>
/// 2019.7.33: 创建. 谌安 <br/>
/// </remarks>
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
    public delegate void WindowLoadCall(object parentObj, object currentObj);

    public delegate void WindowTemplateCall<T>(T currentObj) where T : Component;
    public enum WindowModel : int
    {
        None = 0,
        #region 基础窗口预留 100 个
        /// <summary>
        /// 提示window
        /// </summary>
        TipsWindow,
       
        CommandWindow,
        SubCommandWindow,
        Loading,
        /// <summary>
        /// 登录window
        /// </summary>
        LoginWindow,
        WaitingWin,
        /// <summary>
        /// 消息框窗口
        /// </summary>
        MessageBoxWindow,       
        /// <summary>
        /// 小地图窗口
        /// </summary>
        TinyMapWindow,
        /// </summary>
        /// 通用任务窗口
        /// </summary>
        GeneralTaskWindow,
        #endregion

        //后续增加窗口
        MainWindow ,   //主界面窗口

        ConstituteWindow=100,  //计算机构成window
        ComputerPutOnWindow,    //计算机上架window
        ComputerWiringWindow,   //计算机布线

        BaseCogWindow,   //基础认知窗口
        BuXIanWindow,//布线基础认知
        BuXIanOpWindow,//布线训练窗口

        #region 计算机运行与构成
        /// <summary>
        /// 安装练习窗口
        /// </summary>
        InstallExerciseWindow = 200,

        #endregion
        /// <summary>
        /// 计算机机房 上架window
        /// </summary>
        ComputerRoomPutOnWindow,
        /// <summary>
        /// 家庭场景 上架window
        /// </summary>
        HomePutOnWindow,

        /// <summary>
        /// 主页面搜索界面
        /// </summary>
        MainSearchWindow,
        /// <summary>
        /// 主页顶部界面
        /// </summary>
        MainBottomWindow,
        /// <summary>
        /// 主页界面
        /// </summary>
        MainPageWindow,
        /// <summary>
        /// 课程界面
        /// </summary>
        CourseWindow,
        /// <summary>
        /// 订阅界面
        /// </summary>
        SubscribeWindow,
        /// <summary>
        /// 个人中心界面
        /// </summary>
        PersonalCenterWindow,
        /// <summary>
        /// 课程界面
        /// </summary>
        SingleCourseWindow,
        /// <summary>
        /// 搜索课程结果界面
        /// </summary>
        CourseSearchResultWindow,
        /// <summary>
        /// 顶部菜单栏
        /// </summary>
        MainTopWindow,
        /// <summary>
        /// 课程首页界面
        /// </summary>
        CourseHomepageWindow,
        /// <summary>
        /// 分数界面
        /// </summary>
        ScoreWindow,
        /// <summary>
        /// 术后问答界面
        /// </summary>
        QuestionWindow,
        /// <summary>
        /// 患者信息界面
        /// </summary>
        PatientInfoWindow,
        /// <summary>
        /// 底部控制页面
        /// </summary>
        ControlStripWindow,
        /// <summary>
        /// 医生对话页面
        /// </summary>
        DoctorDialogueWindow,
        /// <summary>
        /// 答题页面
        /// </summary>
        AnswerUIWindow,
        /// <summary>
        /// 器械选择
        /// </summary>
        ApparatusSelectWindow,
        /// <summary>
        /// 确认窗口
        /// </summary>
        QualityWindow,
        /// <summary>
        /// Npc对话窗口
        /// </summary>
        NPCDialogueWindow,
        /// <summary>
        /// 穿刺页面
        /// </summary>
        PunctureWindow,
        /// <summary>
        /// 回收窗口
        /// </summary>
        RecycleWindow,
        /// <summary>
        /// 消毒窗口
        /// </summary>
        XiaoDuWindow,
        /// <summary>
        /// 人文关怀
        /// </summary>
        RenWenGuanHuai,
        /// <summary>
        /// 人文关怀窗口
        /// </summary>
        RenWenGuanHuaiWindow,
        /// <summary>
        /// 点位确定窗口
        /// </summary>
        DianWeiWindow,
        /// <summary>
        /// 穿脱手术衣窗口
        /// </summary>
        ShouShuYiMainWindow,

        WebViewWindow,

        /// <summary>
        /// 个人中心信息中心
        /// </summary>
        MessageCenterWindow,
        /// <summary>
        /// 新消毒窗口
        /// </summary>
        NewXiaoDuWindow,
        /// <summary>
        /// 转场UI
        /// </summary>
        TransitionWindow,
        /// <summary>
        /// 通用webUI页面
        /// </summary>
        CommonWebUIWindow = 235,
        /// <summary>
        /// 公告界面
        /// </summary>
        NoticeWindow = 237,
        /// <summary>
        /// 模考排行榜界面
        /// </summary>
        MockRankWindow = 238,
        /// <summary>
        /// 个人资料页面
        /// </summary>
        PersonalData = 239,
        /// <summary>
        /// 修改资料页面
        /// </summary>
        ModifyDataWindow,

        /// <summary>
        /// 账号及安全窗口
        /// </summary>
        AccountAndSecurityWindow,

        /// <summary>
        /// 意见反馈窗口
        /// </summary>
        OpinionFeedbackWindow,

        /// <summary>
        /// 考试模式分数提交确认窗口
        /// </summary>
        SubmitScoreWindow,

        /// <summary>
        /// 考核模式帮助页面
        /// </summary>
        SimulatHelpInfoWindow,
        /// <summary>
        /// 考核视角选择页面
        /// </summary>
        OperaTransWindow,

        /// <summary>
        /// 考核提示界面
        /// </summary>
        TestTipsWindow,
        /// <summary>
        /// 手术特写镜头界面
        /// </summary>
        OperationFeatureWindow,
        /// <summary>
        /// 长按进度条窗口
        /// </summary>
        LongPressWindow,

        /// <summary>
        /// 提示紫绀界面
        /// </summary>
        TipsZiGanWindow,

        /// <summary>
        /// 铺巾位置界面
        /// </summary>
        PuJinWeiZhiWindow,
        /// <summary>
        /// 滑动条窗口
        /// </summary>
        SliderWindow = 251,
        /// <summary>
        /// 帮助中心
        /// </summary>
        HelpCenterWindow = 252,
        /// <summary>
        /// 通用操作按钮
        /// </summary>
        OperateBtnWindow = 253,
        /// <summary>
        /// 横截面窗口
        /// </summary>
        OpenHengJieMian = 254,
        /// <summary>
        /// 通用选择照片界面
        /// </summary>
        CommonSelectPhotoWindow = 255,
        /// <summary>
        /// 时间流逝窗口
        /// </summary>
        TimeGoWindow = 256,
        /// <summary>
        /// 倒计时窗口
        /// </summary>
        TimeCountDownWindow = 257,
        /// <summary>
        /// 品质种类窗口
        /// </summary>
        QualityStyleWindow = 258,
        /// <summary>
        /// 清创术洗刷窗口
        /// </summary>
        XiShuaWindow = 259,
        /// <summary>
        /// 手上基本操作切开窗口
        /// </summary>
        QieKaiWindow = 260,

        /// <summary>
        /// 手上基本操作缝合窗口
        /// </summary>
        FengHeWindow = 261,
        /// <summary>
        /// 腹腔穿刺特写界面
        /// </summary>
        FQCCFeatureWindow = 262,

        /// <summary>
        /// 打开任务阶段选择窗口
        /// </summary>
        TaskSelectWindow = 263,

        /// <summary>
        /// 导尿window
        /// </summary>
        DaoNiaoWindow=264,

        /// <summary>
        /// 系统设置窗口
        /// </summary>
        SystemSettingsWindow,

        MessageSearchWindow,

        MessageDetailWindow,

        AddQuesWindow,

        XiYangSlider,
        
        YiXueSuYangWindow,
        
        ShuHouZongJieWindow,
        DCCEnergyWindow = 272,      //电除颤能量界面
        
        DCCBoXingTuWindow = 273,    //电除颤波形图界面

        RegisterWindow,
    }
    public class WindowViewCommand
    {
        public ushort cmdID;
        public int nParam;
        public string strParam;
        public object info;
    }


    public enum Window_Sorting : int
    {
        None = -1,
        //底层，Series,Course,UniWeb
        Root = 1,

        Course = 10,
        //底层及window中间层/
        RootEx = 20,
        //所有二级,三级界面/
        Window = 40,
        //引导
        Issue=800,
        //比如回城，控宝进度条
        Tips = 960,
        //--------------------------------以下层级为特殊处理层，非必要都使用上面的层级-------------------------------
        //指引
        Guider = 1000,
        //顶层  比如loading/
        SpecialTop = 1500,
        //分块下载层/
        DownloadLayer = 1700,
        //顶层  比如loading/
        LockScreen = 2000,
        HotUpdateWait = 2001,
        //内嵌网页
        UniWebView = 3000,
    }

    [System.Serializable]
    public class SComponentEx
    {
        public string strClass;
        public GameObject objCom;
    }

    [System.Serializable]
    public class SParamEx
    {
        public string strClass;
        public string valueCom;
    }
}
