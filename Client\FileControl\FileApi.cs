﻿/// <summary>
/// VoiceApi
/// </summary>
/// <remarks>
/// 2021/9/2 11:01:01: 创建. 王正勇 <br/>
/// 音频API
/// </remarks>
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Client
{

    /// <summary>
    /// VoiceApi
    /// </summary>
    public class FileApi : Singleton<FileApi>
    {
        public FileApi()
        { }
        public string SendSaveFileRes(string strFileName, FileTypeDef fileTypeDef, byte[] data, HTTP_Response_Handler hTTP_Response_Handler,bool bOverride=false)
        {
            return SendSaveFileRes(strFileName, fileTypeDef.ToString(), data, hTTP_Response_Handler,bOverride);
        }
            /// <summary>
            /// 发送保存文件通知
            /// </summary>
            /// <param name="strFileName">文件名【必须要包含后缀名】</param>
            /// <param name="fileTypeDef">文件类型</param>
            /// <param name="data">文件数据</param>
            /// <returns></returns>
            public string SendSaveFileRes(string strFileName, string fileTypeDef, byte[] data, HTTP_Response_Handler hTTP_Response_Handler, bool bOverride = false)
        {
            if (string.IsNullOrEmpty(strFileName))
            {
                TRACE.ErrorLn(string.Format("文件名不能为空：{0}", strFileName));
                return "";
            }
            bool m_IsHaveFileSub = GetSuffixName(strFileName);
            if (!m_IsHaveFileSub)
            {
                TRACE.ErrorLn(string.Format("您要上传的文件后缀不存在,您的文件名为：{0}", strFileName));
                return "";
            }
            string strUrl = string.Format("{0}", WebURL.PostSaveFileUrl.Trim());
            WWWForm from = GSpawnPool.Instance.GetObjectItem<WWWForm>();
            from.AddField("Content-Type", "multipart/form-data");
            from.AddField("FileContainerName", "course_resources");
            from.AddField("FileType", 2);
            from.AddField("ParentId", "");
            from.AddField("OwnerUserId", "");
            from.AddField("ParentFolderPath", "/" + fileTypeDef + "/");
            from.AddField("GenerateUniqueFileName", "false");
            from.AddField("OverrideExisting", bOverride.ToString().ToLower());
            from.AddBinaryData("File", data, strFileName);
            //TRACE.ErrorLn(string.Format("文件信息  文件名：{0}  文件大小:", strFileName,data.Length));
            //, GetFileType(fileType)
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, strUrl, hTTP_Response_Handler, from, GlobalGame.Instance.GameSDK.GetUserInfo().token);
            return strUrl;
        }
        /// <summary>
        /// 发送获取文件数据
        /// </summary>
        /// <param name="fileId">文件Id</param>
        /// <param name="hTTP_Response_Handler"></param>
        /// <returns></returns>
        public string SendGetFile(string fileId, HTTP_Response_Handler hTTP_Response_Handler)
        {
            if (string.IsNullOrEmpty(fileId))
            {
                TRACE.ErrorLn(string.Format("您要获取文件ID为空，请排查：{0}", fileId));
                return "";
            }
            TRACE.TraceLn(string.Format("File_获取文件ID为{0}", fileId));
            string strUrl = string.Format("{0}/{1}/download-info",
               WebURL.GainFileUrl.Trim(),
               fileId);
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, strUrl, hTTP_Response_Handler, null, GlobalGame.Instance.GameSDK.GetUserInfo().token, null);
            return strUrl;
        }
        private bool GetSuffixName(string fullFileNmae)
        {
            int i = fullFileNmae.LastIndexOf(".");
            if (i != -1 && i < fullFileNmae.Length - 1)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
