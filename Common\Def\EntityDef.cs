﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    // 实体状态
    public enum EntityState
    {
        None = -1,
        Stand = 0,
        Run,
        Dead,
        <PERSON>,//骑手
        Roration,//旋转
        Hurt,
        Fight,  //战斗
        MaxCount,
        Any
    };

    public enum EntityAppearance
    {
        None = -1,

        EA_Num_0,

        EA_Num_1,

        EA_Num_2,

        EA_Num_3,

        EA_Num_4,

        EA_Num_5,
    }

    public enum EMEntityType
    {
        typeUnknow = 0,     /// 未知实体
        typeActor,          /// 非主角
        typeRobot,          //机器人
        typeNpc,            /// Npc
        typeMonster,        /// 怪物
        typeGoods,          ///物品
        typePitfall,        ///陷阱
        typeTreasureBox,    ///宝箱
        typePortal,         ///传送门
        typeWall,           ///墙
        typeGoldCoin,       ///金币
        typeDiamond,        //钻石
        typeMaxCount,

        typeStatic,         /// 静态实体

        typeHero,           /// 主角
        typeBullet,         //子弹
        typeCreature = typeActor,
        typeEffect,         /// 光效
        typeAvatar,         /// 替身
    };


	//////////////////////////////部件ID定义///////////////////////////
	public enum EMENTITYPART
	{
		// 部件ID定义规则（ENTITYPART + 应用到实体层 + 功能涵义）
		ENTITYPART_ENTITY_INVALID = 0,      // 无效部件ID

		ENTITYPART_ENTITY_BUFF,             // buff部件
		ENTITYPART_PERSON_BROADCAST,        // 广播部件
        ENTITYPART_CREATURE_COMMON,			// 公共部件	

        ENTITYPART_MONSTER_AI,              // ＡＩ部件	

		ENTITYPART_CREATURE_MOVE,           // 移动部件	
		ENTITYPART_CREATURE_SKILL,          // 技能部件
        ENTITYPART_CREATURE_BULLET,         //子弹部件

        ENTITYPART_PERSON_TANK,             // 载具功能控制部件
        ENTITYPART_PERSON_FREEZE,           //冷却部件

        ENTITYPART_ENTITY_MAXID,            // 最大部件ID
	};

    /// <summary>
    /// 动画的最小控制单元的常量字段
    /// </summary>
    public class AnimatorConstField
    {
        public const float c_DEFAULT_TRANSITION = 0.18f;
        public const float c_INVALID_SPEEED = -9999.0f;
        public const float c_DEFAULT_STATE_LENGTH = 1.0f;
        public const float c_DEFAULT_NORMALIZED_TIME = 0.0f;
        public const int c_ANIMATORRES_MAX_IDLE_TIME = 10 * 1000;

    }//AnimatorConstField

    /// 实体部件
    /// 配置中使用数字来配，避免出错加上对应数字，加数字要对应改动max
    public enum EntityParts
    {
        EntityPart_Body = 0,                // 躯干，身体，裸体，整体(非生物一般只有躯干，如树，房子，地上物品)

        EntityPart_Manteau = 1,		        // 披风，背部

        EntityPart_SingleWeaponR = 2,		// 单武器右手武器，如刀，剑(角色类适用以上各部件)
        EntityPart_DoubleWeaponL = 9,		// 双武器左手武器，如盾
        EntityPart_DoubleWeaponR = 19,      // 双武器右手武器

        EntityPart_Max = EntityPart_Body + 1, //最后一个加入的枚举+1
    };

    public enum EntityHPColor : uint
    {
        EHPColor_None = 0,
        EHPColor_Red = 1,
        EHPColor_Green = 2,
        EHPColor_Blue = 3,
    }

    public enum WeaponAimState : byte
    {
        WAS_Aim_None = 0,
        //开始描准
        WAS_Aim_Start = 1,
        //描准结束
        WAS_Aim_End = 2,
    }

    /// <summary>
    /// 子弹运行状态
    /// </summary>
    public enum BulletRunState : byte
    {
        BRS_None = 0,
        BRS_Prepare,        //准备中
        BRS_Fly,            //飞行
        BRS_Collider,       //碰撞
        BRS_ColliderExit,   //碰撞退出事件
        BRS_Arrive,         //到达
    }

    public enum RobotParts
    {
        RP_None =0,
        RP_Fight,       //战斗组
        RP_Move,        //移动组
    }
}
