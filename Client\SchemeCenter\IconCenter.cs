﻿using game.schemes;
using GLib.Common;
/// <summary>
/// SchemeEntity
/// </summary>
/// <remarks>
/// 2019.7.3: 创建. 谌安 <br/>
/// 模型 <br/>
/// </remarks>
using System;
using System.Collections.Generic;

namespace GLib.Client
{
    public class IconCenter : ISchemeNode, IIconCenter
    {
        private Dictionary<int, Icon.Types.Item> m_dicIcons;

        private const string ENTITY_CONFIG = "Icon";

        private Icon m_icon;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public IconCenter()
        {
            m_icon = new Icon();
            m_dicIcons = new Dictionary<int, Icon.Types.Item>();
        }

        ~IconCenter() { }

        public bool Create()
        {
            if (!LoadScheme())
            {
                TRACE.ErrorLn("Icon load fail!");
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = ENTITY_CONFIG;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_Entity);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_Entity(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }
            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                Icon.Types.Item micon = new Icon.Types.Item();

                micon.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                micon.IconPath = pCSVReader.GetString(nRow, tmp_col++, "");
                micon.IconName = pCSVReader.GetString(nRow, tmp_col++, "");
                
#if UNITY_EDITOR
                if (m_dicIcons.ContainsKey(micon.Id))
                {
                    TRACE.ErrorLn("Model Id Error:" + micon.Id);
                }
#endif
                m_dicIcons.Add(micon.Id, micon);
                m_icon.Items.Add(micon);
            }
            GlobalGame.Instance.RenderLoader.SkinInit();
            return true;
        }

        public void Release()
        {
            m_dicIcons.Clear();
            m_dicIcons = null;
        }

        public Icon.Types.Item GetIconInfoByID(int IconID)
        {
            Icon.Types.Item info = null;

            m_dicIcons.TryGetValue(IconID, out info);
            if (info == null)
            {
                TRACE.ErrorLn(Api.NTR(string.Format("未找到图片Id:{0}", IconID)));
            }
            return info;
        }

        public Icon GetAllIcon()
        {
            return m_icon;
        }
    }
}
