﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6CB8686A-0479-49DC-8273-28FA39DE7E4A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Client</RootNamespace>
    <AssemblyName>Client</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG;UNITY_EDITOR;ENABLE_PROFILER;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseAndroid|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG;UNITY_ANDROID;</DefineConstants>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <WarningLevel>4</WarningLevel>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseIos|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG;UNITY_IPHONE;UNITY_IOS</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleasePc|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Basic, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Assets\Scripts\Basic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Common, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Assets\Scripts\Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\UnityDlls\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="UnityEngine">
      <HintPath>..\UnityDlls\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>..\UnityDlls\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>..\UnityDlls\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>..\UnityDlls\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>..\UnityDlls\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>..\UnityDlls\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CourseManger\CourseManger.cs" />
    <Compile Include="CourseManger\SingleCourse.cs" />
    <Compile Include="EntityClient\MessageSlot.cs" />
    <Compile Include="LuaHttpClient\LaHttp.cs" />
    <Compile Include="LuaHttpClient\LaHttpClient.cs" />
    <Compile Include="AssemblyInfo.cs" />
    <Compile Include="BuffClient\Buff.cs" />
    <Compile Include="BuffClient\BuffClient.cs" />
    <Compile Include="BuffClient\BuffPart.cs" />
    <Compile Include="EffectClient\EffectClient.cs" />
    <Compile Include="EntityClient\EntityClass.cs" />
    <Compile Include="EntityClient\EntityClient.cs" />
    <Compile Include="EntityClient\EntityPart\3DMoveManager.cs" />
    <Compile Include="EntityClient\EntityPart\CreatureCommonPart.cs" />
    <Compile Include="EntityClient\EntityWorld.cs" />
    <Compile Include="EntityClient\Entity\Box.cs" />
    <Compile Include="EntityClient\Entity\Hero.cs" />
    <Compile Include="EntityClient\Entity\Mast.cs" />
    <Compile Include="EntityClient\Entity\Monster.cs" />
    <Compile Include="EntityClient\Entity\Person.cs" />
    <Compile Include="EntityClient\Entity\Puppet.cs" />
    <Compile Include="EntityClient\Entity\Tank.cs" />
    <Compile Include="EntityClient\MessageHandler.cs" />
    <Compile Include="OperateLogManger\IsOperateEnd\IsOperateEnd.cs" />
    <Compile Include="OperateLogManger\OperateLogDate.cs" />
    <Compile Include="OperateLogManger\OperateLogLogicBase.cs" />
    <Compile Include="OperateLogManger\OperateLogManger.cs" />
    <Compile Include="OperateLogManger\OperationStringToTernary.cs" />
    <Compile Include="OperateLogManger\ValueType\GourdSend.cs" />
    <Compile Include="OperateLogManger\ValueType\ValueType.cs" />
    <Compile Include="PersonalDataManager\PersonalDataManager.cs" />
    <Compile Include="SceneClient\SceneClient.cs" />
    <Compile Include="SceneClient\SceneInfoClient.cs" />
    <Compile Include="SceneClient\SceneModelClient.cs" />
    <Compile Include="SchemeCenter\ApparatusSelectCenter.cs" />
    <Compile Include="SchemeCenter\AssistantNpcCenter.cs" />
    <Compile Include="SchemeCenter\OperaAreaTransformCenter.cs" />
    <Compile Include="SchemeCenter\CameraTransformCenter.cs" />
    <Compile Include="SchemeCenter\CAnswerUI.cs" />
    <Compile Include="SchemeCenter\CDoctorDialogue.cs" />
    <Compile Include="SchemeCenter\CDoctorPreperative.cs" />
    <Compile Include="SchemeCenter\CHandPoseCenter.cs" />
    <Compile Include="SchemeCenter\CMenuHelp.cs" />
    <Compile Include="SchemeCenter\COperateCondition.cs" />
    <Compile Include="SchemeCenter\COperationLog.cs" />
    <Compile Include="SchemeCenter\CScoreCondition.cs" />
    <Compile Include="SchemeCenter\CScoreInfo.cs" />
    <Compile Include="SchemeCenter\CTaskInfo.cs" />
    <Compile Include="SchemeCenter\CourseDataCenter.cs" />
    <Compile Include="SchemeCenter\CToolTransform.cs" />
    <Compile Include="SchemeCenter\HapticToolCenter.cs" />
    <Compile Include="SchemeCenter\InstallExerciseCenter.cs" />
    <Compile Include="SchemeCenter\DefaultConfigCenter.cs" />
    <Compile Include="SchemeCenter\AudioConfigCenter.cs" />
    <Compile Include="SchemeCenter\DepartmentCenter.cs" />
    <Compile Include="SchemeCenter\HelpCenterMenuCenter.cs" />
    <Compile Include="SchemeCenter\PersonalDataMenuCenter.cs" />
    <Compile Include="SchemeCenter\ScenePointCenter.cs" />
    <Compile Include="SchemeCenter\SchemeCenter.cs" />
    <Compile Include="SchemeCenter\VideoCenter.cs" />
    <Compile Include="ScoreManger\ScoreManger.cs" />
    <Compile Include="ScoreManger\SingleScore.cs" />
    <Compile Include="TaskClient\TaskClient.cs" />
    <Compile Include="TimelineClient\TimelineClient.cs" />
    <Compile Include="ExecuteModel\ExecuteModelPart.cs" />
    <Compile Include="ExecuteModel\ExecuteModelClient.cs" />
    <Compile Include="GameSDKClient\GameSDK.cs" />
    <Compile Include="GameSDKClient\JL\JLBase.cs" />
    <Compile Include="GameSDKClient\JL\JLSDKDev.cs" />
    <Compile Include="LoginModule\LoginModule.cs" />
    <Compile Include="ProductModule\ProductModule.cs" />
    <Compile Include="Message\CServerConnection.cs" />
    <Compile Include="Message\NetManager.cs" />
    <Compile Include="RenderViewProxy\RenderViewProxy.cs" />
    <Compile Include="SchemeCenter\IconCenter.cs" />
    <Compile Include="SchemeCenter\MapInfoCenter.cs" />
    <Compile Include="SchemeCenter\MonsterCenter.cs" />
    <Compile Include="SchemeCenter\SchemeBuff.cs" />
    <Compile Include="SchemeCenter\SchemeEffect.cs" />
    <Compile Include="SchemeCenter\SchemeEntity.cs" />
    <Compile Include="TankClient\PersonTankPart.cs" />
    <Compile Include="TankClient\TankClient.cs" />
    <Compile Include="FileControl\FileApi.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="EffectClient\Effect\" />
    <Folder Include="obj\Debug\TempPE\" />
    <Folder Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="obj\Debug\Client.csproj.AssemblyReference.cache" />
    <None Include="obj\Debug\Client.csproj.CoreCompileInputs.cache" />
    <None Include="obj\Debug\DesignTimeResolveAssemblyReferencesInput.cache" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="obj\Debug\Client.csproj.FileListAbsolute.txt" />
    <Content Include="obj\Debug\Client.dll" />
    <Content Include="obj\Debug\Client.pdb" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>