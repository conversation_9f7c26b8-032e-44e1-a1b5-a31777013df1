﻿/// <summary>
/// UpdateDef
/// </summary>
/// <remarks>
/// 2021.10.18: 创建. 谌安 <br/>
/// 版本更新器定义 <br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace GLib.Common
{
    public class UpdateDef 
    {
        //////////////////////////////////////////////////////////////////////////
        // 更新过程中的提示信息定义
        public static readonly string UPDATE_MSG_READ_SCHEME_FAILED=Api.NTR("更新失败,本地版本信息丢失。");

        public static readonly string UPDATE_MSG_CHECK_VERSION_START=Api.NTR("获取最新版本信息...");
        public static readonly string UPDATE_MSG_CHECK_VERSION_FAILED=Api.NTR("获取最新版本信息失败,重试中!请检查网络连接...");
        public static readonly string UPDATE_MSG_CHECK_VERSION_SUCCESS=Api.NTR("获取最新版本信息成功!");
        public static readonly string UPDATE_MSG_CHECK_VERSION_INVALID=Api.NTR("获取最新版本信息成功!");

        public static readonly string UPDATE_MSG_HTTP_REDIRECT=Api.NTR("URL地址重定向");

        public static readonly string UPDATE_MSG_CHECK_NEW_LIST=Api.NTR("获取更新列表...");
        public static readonly string UPDATE_MSG_NO_NEW_VERSION=Api.NTR("没有更新内容");

        public static readonly string UPDATE_MSG_CALLBACK_APPLICATION=Api.NTR("更新完成,回调应用程序...");
        public static readonly string UPDATE_MSG_DOWNLOAD_FILES=Api.NTR("开始下载文件...");
        public static readonly string UPDATE_MSG_EXECUTE_UPDATE=Api.NTR("正在准备安装更新内容,请稍等...");

        public static readonly string UPDATE_MSG_FAILED=Api.NTR("更新失败!");
        public static readonly string UPDATE_MSG_DOWNLOAD_TORRENT=Api.NTR("正在下载种子文件...");
        //public static readonly string UPDATE_MSG_DOWNLOAD_ERROR="下载过程中发生错误 [{0}]";
        public static readonly string UPDATE_MSG_DOWNLOAD_ERROR=Api.NTR("下载过程中发生错误 ,{0}");
        public static readonly string UPDATE_MSG_EXECUTE_ERROR = Api.NTR("安装更新内容过程中发生错误 [{0}]");
        public static readonly string UPDATE_MSG_CALLBACK_ERROR = Api.NTR("回调主程序出现错误 [{0}]");

        public static readonly string UPDATE_MSG_VERIFY_VERSION_START=Api.NTR("正在验证版本...");
        public static readonly string UPDATE_MSG_VERIFY_VERSION_SUCCESS=Api.NTR("验证版本成功...");
        public static readonly string UPDATE_MSG_VERIFY_VERSION_FAILED=Api.NTR("验证版本失败...");

        public static readonly string UPDATE_MSG_ROLL_BACK_ERROR = Api.NTR("回退版本失败! [{0}]");

        public static readonly string UPDATE_MSG_INIT_INSTALL = Api.NTR("正在拼命加载中，此过程不消耗任何流量.....");
        public static readonly string UPDATE_MSG_CHANGE_SCENE = Api.NTR("场景加载中,请稍等.....");

        public static readonly string UPDATE_MSG_UPDATE_BUILTINASSET = Api.NTR("开始更新内置资源.....");
        public static readonly string UPDATE_MSG_READBUILTINASSET_ERROR = Api.NTR("更新资源配置文件读取错误，请重新下载最新客户端.....");

        public static readonly string UPDATE_MSG_VALIDITY_ERROR = Api.NTR("文件校验错误，请重新下载最新客户端.....");

		public static readonly string UPDATE_SCHEME_FILE = "updatescheme";
        
        public static readonly string CHECK_NETWORK_DISCONNECT=Api.NTR("网络连接已经断开！请连接后重启");

        public static readonly string LOW_MEMORY=Api.NTR("您的设备当前可用内存较少\n\n为避免影响您的体验\n\n请重启手机释放更多内存后，再次尝试登录");
        
        //////////////////////////////////////////////////////////////////////////
        /// UpdateScheme 脚本中的字段定义
        public static readonly string UPDATE_ROOT_NAME="update_scheme";
        public static readonly string UPDATE_PROJECT_NAME="project";
        public static readonly string UPDATE_ATTR_NAME="name";
        public static readonly string UPDATE_ATTR_ALIAS="alias";
        public static readonly string UPDATE_ATTR_SHOWNAME="show_name";
        public static readonly string UPDATE_ATTR_REMOTEVER="remote_version";
        public static readonly string UPDATE_ATTR_BACKUPURL="backup_url";
        public static readonly string UPDATE_ATTR_URL="url";
        public static readonly string UPDATE_ATTR_LOCALVER="local_version";
        public static readonly string UPDATE_ATTR_VERIFIEDVER="verified_version";
        public static readonly string UPDATE_ATTR_VERSION="ver";
        public static readonly string UPDATE_ATTR_TORRENT="torrent_file";
        public static readonly string UPDATE_ATTR_TEMPDIR="temp_dir";
        public static readonly string UPDATE_ATTR_DIR="dir";
        public static readonly string UPDATE_ATTR_EXECUTE="execute";
        public static readonly string UPDATE_ATTR_COMMAND="command";
        public static readonly string UPDATE_ATTR_BANNER="banner";
        public static readonly string UPDATE_ATTR_CALLBACK="callback";
        public static readonly string UPDATE_ATTR_VERSIONINFO="version_info";
        public static readonly string UPDATE_ATTR_DISTRICT_LIMIT="district_limit";
        public static readonly string UPDATE_ATTR_DISTRICT_ALLOW="district_allow" ;
        public static readonly string UPDATE_ATTR_VALUE = "value";
        public static readonly string UPDATE_ATTR_PATCH = "patch";
        public static readonly string UPDATE_ATTR_SETUP_TYPE="setuptype";

        public static readonly string UPDATE_NODE_NAME = "project";
        public static readonly string UPDATE_NODE_VERSION = "version";
        public static readonly string UPDATE_NODE_METHOD = "update_method";
        public static readonly string UPDATE_NODE_TORRENT = "torrent_file";
        public static readonly string UPDATE_NODE_HTTPRES = "http_url";
        public static readonly string UPDATE_NODE_EXECUTE = "execute";
        public static readonly string UPDATE_NODE_FILE_SIZE = "file_size";
        public static readonly string UPDATE_NODE_FILE_HASH = "file_hash";
        public static readonly string UPDATE_NODE_ALL_VERSION = "all_version";
        public static readonly string UPDATE_NODE_BLOCK_HASH = "block_hash";
        public static readonly string UPDATE_NODE_DISTRICT_LIMIT = "district_limit";
        public static readonly string UPDATE_NODE_PARTNER_LIMIT = "limit_partner";
        public static readonly string UPDATE_NODE_PARTNER_ALLOW = "allow_partner";
        public static readonly string UPDATE_ATTR_SIZE = "szie";
        public static readonly string UPDATE_ATTR_PARTNER_ID = "id_list";
        public static readonly string UPDATE_ATTR_ALLOW="allow";
        public static readonly string UPDATE_ATTR_NOT_ALLOW="not_allow";
        public static readonly string UPDATE_ATTR_ROLL_BACK="roll_back";
        public static readonly string UPDATE_ATTR_CHANGE_VER="change_ver";
        public static readonly string UPDATE_NODE_DOWNLOAD= "download";
        public static readonly string UPDATE_NODE_MID= "mid";
        public static readonly string UPDATE_NODE_BLOCK= "block";
        public static readonly string UPDATE_NODE_QQ= "QQ";
        public static readonly string UPDATE_NODE_MINIPACK= "minipack";
        public static readonly string UPDATE_NODE_BILLBOARD= "billboard";
        public static readonly string UPDATE_NODE_SUBSCRIBE= "subscribe";
        public static readonly string UPDATE_NODE_MICRODOWNLOAD = "microdownload";
        public static readonly string UPDATE_NODE_IOS12PLAY = "ios12play";

        //////////////////////////////////////////////////////////////////////////
        // 更新计划环境变量定义
        public static readonly string SCHEME_WINDOW_DIR="%WINDOW_DIR%";			// Windows目录
        public static readonly string SCHEME_SYSTEM_DIR="%SYSTEM_DIR%";			// 系统目录
        public static readonly string SCHEME_CALLBACK_DIR="%CALLBACK_DIR%";		// 更新成功后回调主程序所在的目录
        public static readonly string SCHEME_CALLBACK_CMD="%CALLBACK_CMD%";		// 更新成功后执行的回调命令
        public static readonly string SCHEME_DOWNLOAD_DIR="%DOWNLOAD_DIR%";		// 下载临时文件防置的位置
        public static readonly string SCHEME_UPDATE_DIR = "%UPDATE_DIR%";       // 自动更新程序所在目录
        public static readonly string SCHEME_FILE_NAME="%FILE_NAME%";		    // 文件名
        public static readonly string SCHEME_MODULE_NAME="%MODULE_NAME%";		// 自动更新程序完整路径

        public static readonly string SCHEME_SERVER_LIST="%ShowServerList%";    // 是不是直接显示服务器列表界面


        public enum DataReporteEnum : byte
        {
            none = 0,
            //热更开始
            updateBegin,
            //热更结束
            updateEnd,
            //热更错误
            updateError,
            //强制更新
            forcedUpdate,
            //唤起SDK
            openSDK,
           
        }

        // 网址定义
        public static string INIT_URL_ANDROID_SERVICEENTRY
		{
			get
			{
				return ProductIni.Instance.GetString("INIT_URL_ANDROID_SERVICEENTRY", string.Empty);
			}
		}

		//安卓读入服务入口地址（新）
		public static string INIT_URL_ANDROID_BACKUPENTRY
		{
			get
			{
				return ProductIni.Instance.GetString("INIT_URL_ANDROID_BACKUPENTRY", string.Empty);	//安卓读入服务(备用)入口地址
			}
		}

		public static string INIT_URL_IOS_SERVICEENTRY
		{
			get
			{
				return ProductIni.Instance.GetString("INIT_URL_IOS_SERVICEENTRY", string.Empty);      	// iOS读入服务入口地址（新）
			}
		}


		public static string INIT_URL_IOS_BACKUPENTRY
		{
			get
			{
				return ProductIni.Instance.GetString("INIT_URL_IOS_BACKUPENTRY", string.Empty);			// iOS读入服务(备用)入口地址
			}
		}

		public static string ANDROID_UPDATE_SCHEME_FILE_SER
		{
			get
			{
				return ProductIni.Instance.GetString("ANDROID_UPDATE_SCHEME_FILE_SER", string.Empty);  //服务器配置UpdateScheme.xml(需要提供连接网址)
			}
		}

		public static string IOS_UPDATE_SCHEME_FILE_SER
		{
			get
			{
				return ProductIni.Instance.GetString("IOS_UPDATE_SCHEME_FILE_SER", string.Empty);		//服务器配置UpdateScheme.xml(需要提供连接网址)
			}
		}

		public static string INIT_URL_SERVERLIST
		{
			get
			{
				return ProductIni.Instance.GetString("INIT_URL_SERVERLIST", string.Empty);				//服务器下载最新的服务器列表
			}
		}
      
        public static int MAX_PATH = 260;
        /// 无效任务ID
        public const uint INVALID_TASK_ID = 0xFFFFFFFF;

        /** 加（解）密类型*/
        public const int  CRYPT_TYPE_EMPTY=0;		///< 不进行加（解）密
        public const int  CRYPT_TYPE_1=0x2;
        public const int  CRYPT_TYPE_2=0x4;
        public const int  CRYPT_TYPE_3=0x8;
        public const int  MAX_CRYPT_TYPE_COUNT=8;		///< 最大支持8组加（解）密算法（包括用户注册的4组在内）

        public const int  CRYPT_TYPE_MAP=CRYPT_TYPE_1;	/// 字节映射加密类型，对加密后进行的压缩损害小
        public const int  CRYPT_TYPE_XOR = CRYPT_TYPE_2;	/// 复杂异或加密类型，对加密后进行的压缩损害大

        public const float RATIO_WATERLINE = 0.95f;

        public readonly static string[] s_mpks = new string[8] 
	    {
		    "//Data//Shader.mpk",
		    "//Data//Music.mpk",
		    "//Data//DiBiao.mpk",
		    "//Data//Scp.mpk",
		    "//Data//UI.mpk",
		    "//Data//Creature.mpk",
		    "//Data//maps.mpk",
		    "//Data//mpw.mpk",
	    };

        public const int TIXML_DEFAULT_ENCODING=0;


    }

    public class UPDATE_PROJECT
	{
        public string strName;             // 项目名称
        public string strShowName;         // 对外显示的名称
        public string strAlias;            // 项目别名
        public string strRemoteUrl;        // 远程URL,用于检查更新内容
        public string strBackupUrl;        // 备用URL,防止网站不能访问
        public int nLocalVersion;          // 本地版本号
        public int nVerifiedVersion;       // 最后一次校验通过的版本号
        public string strTempDir;          // 本地临时目录
        public string strCallbackDir;      // 回调应用程序目录
        public string strTorrentFile;      // 本地版本对应的torrent文件,从torrent文件中可以查询每个文件的哈西值
        public string strBannerUrl;        // 广告位url
        public string strVersionInfo;      // 版本信息文件地址
        public string strDistrictLimit;    // 不允许更新的区
        public string strDistrictAllow;    // 只允许更新的区
        public int nNeedCheckVersion;

        public bool isCheck;                //是否检查
        
		public List<string> strExecuteList;       // 更新完成后需要执行的命令列表

        public   UPDATE_PROJECT()
        {
            strName=string.Empty;            
            strShowName=string.Empty;        
            strAlias=string.Empty;           
            strRemoteUrl=string.Empty;       
            strBackupUrl=string.Empty;       
            nLocalVersion=0;      
            nVerifiedVersion=0;   
            strTempDir=string.Empty;         
            strCallbackDir=string.Empty;     
            strTorrentFile=string.Empty;     
            strBannerUrl=string.Empty;       
            strVersionInfo=string.Empty;     
            strDistrictLimit=string.Empty;   
            strDistrictAllow=string.Empty;
            nNeedCheckVersion =0;
            isCheck = false;

            strExecuteList =new List<string>();     
        }
	};

    [StructLayout(LayoutKind.Sequential, Pack = 1, CharSet = CharSet.Unicode)]
    public	struct VERSION_VERIFY_DATA
	{
        public string strVersionInfoURL;
        public IVersionVerifyHandler pHandler;
        public int nVersion;
	};

    [StructLayout(LayoutKind.Sequential, Pack = 1, CharSet = CharSet.Unicode)]
    //////////////////////////////////////////////////////////////////////////
	/**
	@name : 从服务器上获取的版本信息
	@brief: 
	*/
	public class REMOTE_VERSION
	{
		public int				nVersion;			 // 版本ID
		public string           strName;             // 更新项目名称
		public string           strBanner;           // 广告条
		public string			strPatchUrl;         // 补丁地址 
		public string			nUpdateMehod;        // 更新方式
		public string			strTorrentFile;      // 种子地址
		public string           strVersionInfo;      // 版本信息文件地址
		public string           strFileHash;         // 文件哈希值
		public string           strBlockHash;        // 块哈西
		public string           strDistrictAllow;    // 区限制，允许哪些区
		public string           strDistrictNotAllow; // 区限制，不允许哪些区
		public int              nRollBackVer;        // 如果需要回退版本的话就跳到哪个版号
		public int              nChangeToVer;        // 更新完后跳到哪个版号? 
		public int              nFileSize;           // 文件大小

        public string           allVersion;          //所有项目服务器版本

        public List<string>     strExecuteList;		 // 更新完成后需要执行的命令列表
		public List<string>     strHttpResources;	 // HTTP的资源
		public List<int>		nLimitPartners;		 // 限制合作商
		public List<int>		nAllowPartners;		 // 允许合作商
		public bool				bPartnerJump;		 // 合作商是否跳过
        public int              nUdapteType;         // 允许更新的类型（EMUdapteType）
	};

    [StructLayout(LayoutKind.Sequential, Pack = 1, CharSet = CharSet.Unicode)]
    //////////////////////////////////////////////////////////////////////////
	/**
	@name : 从服务器上获取的安装包下载列表
	@brief: 
	*/
	public class REMOTE_DOWNLOAD
    {
        public string szID;
        public string szUrl;
    }

    /**
	@name : 命令参数对象
	@brief: 目前只支持整型和字符串类型
	*/
	public class  Parameter
	{
		public enum TYPE
		{
			enType_Nil  = 0,
			enType_Int     ,
			enType_String  ,
		};
		
		public string       m_strValue; // 字串参数值
		public int          m_nValue;   // 整型参数值

		public TYPE         m_Type;     // 参数类型
		public string       m_Desc;     // 参数描述
		public string       m_Detail;   // 详细描述
		public bool         m_IsOption; // 是否可选
		public bool         m_Selected; // 是否选择了这个参数

		public Parameter()
        { 
            m_Type=TYPE.enType_Nil;
            m_IsOption=true;
            m_Selected=false;
        }

		public Parameter(int val)
        { 
            m_Type=TYPE.enType_Int;
            m_nValue=val;
            m_IsOption=true;
            m_Selected=false;
        }

		public Parameter(string val)
        {
            m_Type = TYPE.enType_String;
            m_strValue=val;
            m_IsOption=true;
            m_Selected = false;
        }
	};





    public enum OPEN_MODE
    {
        READ = (int)0x01,  // 读文件
        WRITE = (int)0x02,  // 写文件
        OPEN = (int)0x04,  // 打开文件,如果不存在就创建
        ONLY_OPEN = (int)0x08, // 仅仅打开文件,如果不存在不要创建
        ALWAYS_CREATE = (int)0x10,// 每次都重新创建
    };

    public enum EMMessageType
    {
        EMMessageType_Confirm,      // 确认开始更新
        EMMessageType_ReStart,      // 确认重启
        EMMessageType_Message,      // 带URL跳转功能的信息提示
        EMMessageType_CheckMemory   // 检测内存是否低于最低要求的信息提示
    }

    public class DBootEvent
    {
        public const int EVENT_UPDATE_MESSAGEBOX = 1;
        public const int EVENT_SET_MESSAGESHOW = 2;
        public const int EVENT_SET_QQ = 3;
        public const int EVENT_SCENE_HOTUPDATE_START = 4;
        public const int EVENT_HOTUPDATE_FINISH = 5;
        /// <summary>
        /// 更新网速
        /// </summary>
        public const int EVENT_HOTUPDATE_NETSPEED = 6;
        /// <summary>
        /// 下载进度
        /// </summary>
        public const int EVENT_HOTUPDATE_DOWNLOAD_PROCESS = 7;
        /// <summary>
        /// 安装进度
        /// </summary>
        public const int EVENT_HOTUPDATE_SETUP_PROCESS = 8;
        /// <summary>
        /// 剩余空间不足
        /// </summary>
        public const int EVENT_HOTUPDATE_FREESPACE = 9;
        /// <summary>
        /// 网络状态弹框确认
        /// </summary>
        public const int EVENT_HOTUPDATE_WIFIBOX = 10;
    }

    public struct SUpdateMessage
    {
        public EMMessageType Type;
        public string szMessage;
        public string szUrl;
        public bool bQuit;
        public bool bLoc;
        public bool bNoClose;
    }

    public enum EMUpdateSchemeType
    {
        EMUpdateSchemeType_Update,            // 自动更新模式
        EMUpdateSchemeType_BlockDownLoad,     // 分块下载模式

        EMUpdateSchemeType_Max,               // 最大值 
    }

    // 更新包的类型
    // 用于区分：是第一次安装完成就必须安装的更新包还是正常的更新包
    public enum EMUdapteType
    {
        EMUdapteType_Update,             // 更新安装
        EMUdapteType_Frist,              // 首次安装
        EMUdapteType_All,                // 更新安装、首次安装都安装

        EMUdapteType_Max,                // 最大值 
    }

    /// <summary>
    /// 工程更新状态管理，只用于显示层是否请求管理 
    /// </summary>
    public class ProjectUpdateState
    {
        public string projectName;
        public EMUpdateState state;
    }

    public enum EMUpdateState : byte 
    {
        None = 0,
        //请求中
        Requesting,
        //更新完成
        UpdateFinish,
        //更新错误
        UpdateError,
    }
    
    public enum EMMarketType
    {
	    None = 0,
	    Honor,        // 荣耀应用市场
    }
}