﻿/// <summary>
/// COperationLog
/// </summary>
/// <remarks>
/// 2023/1/4 10:52:57: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class COperationLog : ISchemeNode, IOperationLog
    {
        public const string Task_Info = "OperationLog";
        Dictionary<int, Dictionary<string, OperationLogInfo>> m_DialogueInfoById;

        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }

        private Action<Dictionary<string, OperationLogInfo>> m_curLoadFinishCallback;

        private int m_CourseId;
        public COperationLog()
        {
            m_DialogueInfoById = new Dictionary<int, Dictionary<string, OperationLogInfo>>();

        }
        ~COperationLog()
        {
        }
        public bool Create()
        {
            return true;
        }

        public bool LoadScheme(int courseID, Action<Dictionary<string, OperationLogInfo>> action)
        {
            m_curLoadFinishCallback = action;
            m_CourseId = courseID;
            if (!m_DialogueInfoById.ContainsKey(courseID))
            {
                m_DialogueInfoById.Add(courseID, null);
                string strPath = "CourseCsv/" + courseID + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, courseID);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<string, OperationLogInfo> datas = new Dictionary<string, OperationLogInfo>();
                m_DialogueInfoById.TryGetValue(courseID, out datas);
                m_curLoadFinishCallback?.Invoke(datas);
            }
            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<string, OperationLogInfo> datas = null;
            m_DialogueInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<string, OperationLogInfo>();
                m_DialogueInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                OperationLogInfo dialogue = new OperationLogInfo();

                dialogue.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                dialogue.OperateName = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.ConditionDescription = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.OperateModelName = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.OperatedModelName = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.TaskID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                dialogue.ConditionID = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.ConditionDateType = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.LogicType = pCSVReader.GetString(nRow, tmp_col++, "");
                dialogue.NecessaryOperation = pCSVReader.GetInt(nRow, tmp_col++, 0);

                string dataID = string.Format("{0}:{1}:{2}", dialogue.OperateModelName, dialogue.OperatedModelName, dialogue.TaskID);
                if (!datas.ContainsKey(dataID))
                {
                    datas.Add(dataID, dialogue);
                }
            }

            m_curLoadFinishCallback?.Invoke(datas);
            return true;
        }
        public OperationLogInfo GetAllDialogue(string StepID)
        {
            OperationLogInfo info = null;
            Dictionary<string, OperationLogInfo> datas = new Dictionary<string, OperationLogInfo>();
            m_DialogueInfoById.TryGetValue(m_CourseId, out datas);
            datas.TryGetValue(StepID, out info);
            return info;
        }


        public int GetOperateDataCount()
        {
            return m_DialogueInfoById.Count;
        }

        public void Release()
        {
            m_DialogueInfoById.Clear();
            m_DialogueInfoById = null;
        }


    }
}