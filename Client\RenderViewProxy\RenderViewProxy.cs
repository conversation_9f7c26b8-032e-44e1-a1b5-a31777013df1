﻿/// <summary>
/// CRenderViewProxy
/// </summary>
/// <remarks>
/// 2019.6.26: 创建. 谌安 <br/>
/// </remarks>
using UnityEngine;
using GLib;
using GLib.Common;

namespace GLib.Client
{
    public class CRenderViewProxy : IRenderViewProxy
    {
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        /// <summary>
        /// 模块创建
        /// 如果是同步模块，Create成功就表示加载成功。
        /// 如果是异步模块, Create成功不表示加载成功，必须通过模块的ModuleLoadState属性来判断。
        /// </summary>
        /// <returns></returns>
        public bool Create()
        {
            return true;
        }

        /// <summary>
        /// 模块释放
        /// </summary>
        public void Release()
        {

        }

        ////////////////模块驱动基础接口//////////////////////

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {

        }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {

        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {

        }

        public void sendControllerCommand(int cmdID, int nParam, string strParam, object obj)
        {
            cmd_Base info = null;
            if (obj == null){
                info = GHelp.GetObjectItem<cmd_Base>();
            }else 
            {
                info = obj as cmd_Base;
            }

            info.strParam = strParam;
            info.nParam = nParam;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)cmdID, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, info);
        }

        public void sendEntityCommand(uint entityID, int cmdID, int nParam, string strParam, object obj)
        {
            cmd_EntityView info = null;
            if (obj == null || !(obj is cmd_EntityView))
            {
                info = GHelp.GetObjectItem<cmd_EntityView>();
            }
            else
            {
                info = obj as cmd_EntityView;
            }

            info.nParam = nParam;
            info.nViewID = entityID;
            info.strParam = strParam;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)cmdID, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, info);
        }
    }
}