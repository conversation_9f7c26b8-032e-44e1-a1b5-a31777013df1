﻿/// <summary>
/// CEntityPropMessageHandler
/// </summary>
/// <remarks>
/// 2019.7.4: 创建. 谌安 <br/>
/// 接受服务器关于实体相关命令的指令 <br/>
/// </remarks>
using game.common;
using game.proto;
using game.scene;
using Game.Entity;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;
using static Game.Entity.Entity_CreateEntity.Types;

namespace GLib.Client.Entity
{
    public class CEntityPropMessageHandler : IMessageHandler
    {
        CEntityPropMessageHandler g_pEntityPropMessageHandler;

        private List<string> m_dieGuids = new List<string>();
        public CEntityPropMessageHandler()
        {
            g_pEntityPropMessageHandler = this;
        }

        /** 消息处理
		@param msg 网络消息
		*/
        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {
#if OpenEntityClient_Profiler
			long fStartTimes = System.Diagnostics.Stopwatch.GetTimestamp();
#endif

            switch (head.wKeyAction)
            {
                case (int)MessageCodesOfEntity.CodeEntityCreateHero:
                    {
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_START("MessageHandler.OnCreateEntity");
#endif
                        OnCreateEntity(package, 1);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityCreateEntity:
                    {
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_START("MessageHandler.OnBatchCreateEntity");
#endif
                        OnBatchCreateEntity(package);
                        //OnBatchCreateEntity(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityDestroyEntity:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.OnDestroyEntity");
#endif
                        OnDestroyEntity(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntitySyncMove:
                    {//同步坐标，不包含主角自己,等于是场景同步  //此移动只能用于靠近某个实体，例如拾取物品、靠近宝箱、传送门，位置偏差较少的情况
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.SceneAction_SyncMove");
#endif
                        EntitySyncMove(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityMoveTo:
                    { //实体移动到目标点
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.MoveTo");
#endif
                        EntityMoveTo(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityTelesport:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.MoveTo");
#endif
                        EntityTelesport(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityAddBuff:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.AddBuff");
#endif
                        AddBuff(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityCancelBuff:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.CancelBuff");
#endif
                        CancelBuff(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntitySyncProp:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.SyncProp");
#endif
                        EntitySyncProp(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityDamage:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.Die");
#endif

#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                case (int)MessageCodesOfEntity.CodeEntityDie:
                    {
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.Die");
#endif
                        OnDie(package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    }
                    break;
                // 接收场景服的移动消息
                case (int)MessageCodesOfEntity.CodeMcmoveToSc:
#if OpenDebugInfo_Profiler
                        Api.PP_BY_NAME_START("MessageHandler.Die");
#endif
                    GHelp.FireExecute((ushort)DGlobalEvent.EXECUTE_MOVETO_SC, (byte)EMSOURCE_TYPE.SOURCE_TYPE_EXECUTEMODEL, 0, package);
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                    break;
                    // 消费完金币 传入消息box中处理回调
                case (int)MessageCodesOfEntity.CodeEntityExpendSc:
                    {
                        MessageBoxSend(package);
                    }
                    break;
                    // 停止移动的消息
                case (int)MessageCodesOfEntity.CodeEntityMoveStopSc:
                    {
                        EntityStop(package);
                    }
                    break;
                default:
                    {
                        int nLen = 0;
                        package.Pop(out nLen);
                        byte[] szValue = null;
                        package.ReadByteBuffer(out szValue, nLen);
                        GHelp.ToLaDataByFunction("OnEntityMessage", head, szValue);
                    }
                    break;
            }
            
        }

        /** 
       @param   
       @param   
       @return  
       */
        void OnCreateEntity(CPacketRecv data, int nIsHero)
        {
            // 构建实体
            IEntity pEntity = ((CEntityClient)GHelp.GetEntityClient()).BuildEntity((uint)EMtEntity_Class.tEntity_Class_Person,
                                                            data, 0, nIsHero);
            if (pEntity == null)
            {
                return;
            }
        }

        /** 
       @param   
       @param   
       @return  
       */
        void OnBatchCreateEntity(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);
            Entity_CreateEntity response = Entity_CreateEntity.Parser.ParseFrom(szValue);
            //对实体排序
            foreach (EntityInfo tempP in response.Items)
            {
                IEntity pEntity = ((CEntityClient)GHelp.GetEntityClient()).BuildEntity((uint)GHelp.GetClassByEntityType(tempP.EntityType), tempP);
                if (pEntity == null)
                {
                    continue;
                }
            }
        }

        /** 
        @param   
        @param   
        @return  
        */
        void OnDestroyEntity(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);
            Entity_DestroyEntity response = Entity_DestroyEntity.Parser.ParseFrom(szValue);
            foreach (string uid in response.Items)
            {
                if (!m_dieGuids.Contains(uid))
                {
                    DestroyEntity(Api.GuidCInt(uid));
                }
                else
                {
                    m_dieGuids.Remove(uid);
                }
            }
        }

        /** 释放某个对像
        @param   
        @param   
        @return  
        */
        public void DestroyEntity(Int64 uidEntity)
        {
            IEntity pEntity = GHelp.GetEntityClient().Get(uidEntity);
            if (pEntity == null)
            {
                return;
            }
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("DestroyEntity.FireExecute()");
#endif
            /*SEventEntityLeave9Grid_C eventleave9grid;
            eventleave9grid.uidEntity = pEntity.GetUID();
            GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_LEAVE9GRID, pEntity.GetEventSourceType(), (uint)UID_DATA.ANALYZEUID_SERIALNO(uidEntity), eventleave9grid);*/
#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_START("DestroyEntity.Release()");
#endif
            //目前只有怪物，非主角玩家才才支持Restore
            if (pEntity.GetEntityClass().IsMonster())
            {
                pEntity.Restore();
            }
            else if (pEntity.GetEntityClass().IsPerson())
            {
                IPerson person = pEntity as IPerson;
                if (person.IsHero())
                {
                    pEntity.Release();
                }
                else
                {
                    pEntity.Restore();
                }
            }
            else
            {
                pEntity.Release();
            }

#if OpenDebugInfo_Profiler
            Api.PP_BY_NAME_STOP();
#endif

            pEntity = null;
        }

        private void EntitySyncMove(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_SyncMove response = Entity_SyncMove.Parser.ParseFrom(szValue);
            cmd_creature_MoveTo moveTo = GHelp.GetObjectItem<cmd_creature_MoveTo>();
            moveTo.fPosition_x = response.Position.X;
            moveTo.fPosition_y = response.Position.Y;
            moveTo.fPosition_z = response.Position.Z;
            GHelp.sendEntityCommand(GHelp.GetEntityViewIDByUID(Api.GuidCInt(response.Guid)), (int)EntityLogicDef.ENTITY_TOVIEW_MOVETO, 0, "", moveTo);
        }
        /// <summary>
        /// 瞬移
        /// </summary>
        /// <param name="data"></param>
        private void EntityTelesport(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_Telesport_SC response = Entity_Telesport_SC.Parser.ParseFrom(szValue);
            IEntity entity = GHelp.GetEntityByUID(Api.GuidCInt(response.Playerguid));
            if (entity != null)
            {
                uint entityViewID = entity.GetEntityViewID();
                cmd_creature_rigidbody_sync telesport = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
                telesport.bNotGround = false;
                telesport.nEntityID = entityViewID;
                telesport.fPosition_x = response.Position.X;
                telesport.fPosition_y = response.Position.Y;
                telesport.fPosition_z = response.Position.Z;
                GHelp.sendEntityCommand(entityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", telesport);
                GHelp.FireExecute((int)EntityLogicDef.ENTITY_STOP_MOVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, entity.GetUID());
            }
        }
        private void EntityMoveTo(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_MoveTo_SC response = Entity_MoveTo_SC.Parser.ParseFrom(szValue);
            IEntity entity = GHelp.GetEntityByUID(Api.GuidCInt(response.Playerguid));

            //多段命令通过实体类型获取
            if (response.TargetType == game.common.eEntityType.TreasureBox)
            {
                GHelp.GetControlManager().GetHandleCommandManager().clear();
                SHandleCommand_Close close = new SHandleCommand_Close();
               // close.ptTargetTile = new Vector3(response.Position.X, response.Position.Y, response.Position.Z);
                close.fDistance = 1;
                close.bAutoFindPath = false;
                IHandleCommand pCmd = GHelp.GetControlManager().GetHandleCommandManager().getHandleCommandFactory().CreateCommand_Close(close);
                GHelp.GetControlManager().GetHandleCommandManager().appendCommandTail(pCmd);
            }
            else
            {
                if (entity == null)
                {//场景被销毁，还收到了服务器的移动消息
                    return;
                }
                switch (entity.GetEntityType())
                {
                    case EMEntityType.typeActor:
                    case EMEntityType.typeHero:
                    case EMEntityType.typeMonster:
                    case EMEntityType.typeRobot:
                        {

                            //List<Vector3> pathList = GlobalGame.Instance.NavigationManager.FindPath(entity.GetPosition(), new Vector3(response.Position.X, response.Position.Y, response.Position.Z));
                            cmd_MovePos moveTo = GHelp.GetObjectItem<cmd_MovePos>();
                            moveTo.listPath = new List<Vector3>();
                            for (int i = 0; i < response.Position.Count; i++)
                            {
                                moveTo.listPath.Add(new Vector3(response.Position[i].X, response.Position[i].Y, response.Position[i].Z));
                            }
                          //  moveTo.listPath = response.Position;// pathList;
                            // 发送命令的时间
                            moveTo.fSendCmdTime = Time.realtimeSinceStartup;

                            GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_MOVE_POS, 0, "", moveTo);
                        }
                        break;
                    default:
                        {
                           /* cmd_creature_MoveTo moveTo = GHelp.GetObjectItem<cmd_creature_MoveTo>();
                            moveTo.fPosition_x = response.Position.X;
                            moveTo.fPosition_y = response.Position.Y;
                            moveTo.fPosition_z = response.Position.Z;
                            GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_MOVETO, 0, "", moveTo);*/
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// 停止移动
        /// </summary>
        /// <param name="data"></param>
        private void EntityStop(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_MoveStop_SC response = Entity_MoveStop_SC.Parser.ParseFrom(szValue);
            IEntity entity = GHelp.GetEntityByUID(Api.GuidCInt(response.GameObjectId));
            // 如果对应的实体存在，则发送停止消息
            if (entity != null)
            {
                GlobalGame.Instance.MoveController.MoveEnd(entity.GetUID());
                GHelp.FireExecute((int)EntityLogicDef.ENTITY_STOP_MOVE_OPCODE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, entity.GetUID());
            }
        }

        #region Buff相关

        private void AddBuff(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_AddBuff_SC response = Entity_AddBuff_SC.Parser.ParseFrom(szValue);

            ICreature entity =(ICreature)GHelp.GetEntityByUID(Api.GuidCInt(response.Playerguid));
            if (entity != null)
            {
                IEntityPart pEntityPart = entity.GetEntityPart((uint)EMENTITYPART.ENTITYPART_ENTITY_BUFF);
                if (pEntityPart != null)
                {
                    (pEntityPart as IBuffPart).OnAddBuff(response);
                }
            }
        }

        private void CancelBuff(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_CancelBuff_SC response = Entity_CancelBuff_SC.Parser.ParseFrom(szValue);

            ICreature entity = (ICreature)GHelp.GetEntityByUID(Api.GuidCInt(response.Playerguid));
            if (entity != null)
            {
                IEntityPart pEntityPart = entity.GetEntityPart((uint)EMENTITYPART.ENTITYPART_ENTITY_BUFF);
                if (pEntityPart != null)
                {
                    (pEntityPart as IBuffPart).OnRemoveBuff(response);
                }
            }
        }

        #endregion

        #region 属性同步
        private void EntitySyncProp(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_SyncProp_SC response = Entity_SyncProp_SC.Parser.ParseFrom(szValue);

            IEntity entity = GHelp.GetEntityByUID(Api.GuidCInt(response.Playerguid));
            if (entity != null)
            {
                foreach (ProItem item in response.Items)
                {
                    if (item.PropType == eEntityProp.EEntityCurHp)
                    {
                        int curProp = entity.GetNumProp((uint)item.PropType);
                        if (curProp > int.Parse(item.PropValue))
                        {//受伤
                            IEffectViewManager pEffectViewManager = GHelp.GetEffectViewManager();
                            if (pEffectViewManager != null)
                            {
                                // 改变动作
                                if (entity.GetEntityViewID() > 0)
                                {
                                    PlayAnimationContext context = GHelp.GetObjectItem<PlayAnimationContext>();
                                    context.name = "beaten";
                                    GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_PLAY_ANIMATION, 0, "", context);
                                }
                            }
                        }
                    }
                    entity.SetStrProp((uint)item.PropType, item.PropValue);
                    OnSync(entity, item.PropType);
                }
            }
        }

        private void OnSync(IEntity entity, eEntityProp prop)
        {
            switch (prop)
            {
                case eEntityProp.EEntityCurHp:
                case eEntityProp.EEntityGoldCoin:
                case eEntityProp.EEntityDiamond:
                    {
                        byte bSrcType = entity.GetEventSourceType();
                        GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_UPDATEPROP, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(entity.GetUID()), null);
                    }
                    break;
                case eEntityProp.EEntityAngle:
                    {
                        uint entityViewId = entity.GetEntityViewID();
                        cmd_RotateAngle c_data = new cmd_RotateAngle()
                        {
                            Angle = entity.GetNumProp((uint)eEntityProp.EEntityAngle),
                            targetAngle = entity.GetNumProp((uint)eEntityProp.EEntityAngle),
                            timeCount = 1
                        };
                        //获取实体 然后旋转
                        GHelp.sendEntityCommand(entityViewId, (int)EntityLogicDef.ENTITY_ROTATION_BY_ONE, 0, "", c_data);
                    }
                    break;
                case eEntityProp.EEntityMoveSpeed:
                    {
                        cmd_SetThrust c_data = new cmd_SetThrust()
                        {
                            ThrustValue = entity.GetMoveSpeed()
                        };

                        GHelp.sendEntityCommand(entity.GetEntityViewID(), (int)EntityLogicDef.ENTITY_SETTHRUST, 0, "", c_data);
                    }
                    break;
            }
        }
        #endregion

        #region 死亡相关逻辑，如果发送死亡后，服务器再发送实体销毁则暂不处理，待死亡动画完成后再销毁

        private void OnDie(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Entity_Die_SC response = Entity_Die_SC.Parser.ParseFrom(szValue);

            for (int i = 0; i < response.DeathGuids.Count; i++)
            {
                m_dieGuids.Add(response.DeathGuids[i]);
                //TRACE.ErrorLn(response.DeathGuids[i]);
                IEntity entity = GHelp.GetEntityByUID(Api.GuidCInt(response.DeathGuids[i]));
                if (entity != null)
                {
                    ICreature creature = entity as ICreature;
                    if (creature != null)
                    {
                        //播放死亡相关逻辑
                        IEntityPart pEntityPart = (entity as ICreature).GetEntityPart((uint)EMENTITYPART.ENTITYPART_CREATURE_COMMON);
                        if (pEntityPart != null)
                        {
                            (pEntityPart as IEntityPart).OnDie(response);
                        }
                    }
                }
            }
        }

        #endregion

        private void MessageBoxSend(CPacketRecv data)
        {
            int nLen = 0;
            data.Pop(out nLen);
            byte[] szValue = null;
            data.ReadByteBuffer(out szValue, nLen);

            Expend_SC response = Expend_SC.Parser.ParseFrom(szValue);
            int isSuccess = response.IsSuccess ? 1 : 0;
            string onClickInfo = string.Format("CommonMsgBox({0},{1},{2})", response.MsgBoxModule, response.MsgBoxAction, isSuccess);
            GHelp.ItemClick(onClickInfo);
        }
    }
}
