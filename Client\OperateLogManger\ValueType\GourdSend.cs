﻿using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Client
{
    public  class GourdSend:OperateLogLogicBase
    {
        OperationLogInfo curinfo;
        UpdateOperateLogDate updataloginfo;
        public override void Init(OperationLogInfo info, UpdateOperateLogDate updateinfo)
        {
            base.Init(info, updateinfo);
            curinfo = info;
            updataloginfo = updateinfo;
        }

        public override int DisUpdate()
        {
          string[] data= updataloginfo.OperateDate.Split('_');
          curinfo.OperateName = string.Format(curinfo.OperateName, data);
         
           
            return 4;
        }
    }
}
