﻿/// <summary>
/// ReportDef
/// </summary>
/// <remarks>
/// 2021/8/4 21:14:37: 创建. 王正勇 <br/>
/// 
/// </remarks>
using game.schemes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{

    public class GainLearnReportDef : ErrorSerial
    {
        public int code;
        public string message;
        public bool isSuccess;
        public ReportLearnDef result;
    }
    /// <summary>
    /// ReportDef
    /// </summary>
    public class ReportCapacityDef
    {
        /// <summary>
        /// 图片ID
        /// </summary>
        public int m_ImgId;
        /// <summary>
        /// 标题名称
        /// </summary>
        public string m_TitleName;
        /// <summary>
        /// 能力类型
        /// </summary>
        public int m_CapacityType;
        /// <summary>
        /// 判定条件
        /// </summary>
        public int m_CapacityCondition;
        /// <summary>
        /// 能力知识点数量
        /// </summary>
        public int m_CapactiyNumber;
        /// <summary>
        /// 对比条件
        /// </summary>
        public float m_JudgingCriteria;
        /// <summary>
        /// 判定条件值
        /// </summary>
        public float m_JudgingCriteriaValue;
    }
    [Serializable]
    public class ReportLearnDef
    {
        /// <summary>
        /// 学习报告Id
        /// </summary>
        public string guid;
        /// <summary>
        /// 系列Id
        /// </summary>
        public int seriesId;
        /// <summary>
        /// 课程Id
        /// </summary>
        public int courseId;
        /// <summary>
        /// 用户ID
        /// </summary>
        public string userId;
        /// <summary>
        /// 练习标识
        /// </summary>
        public string exerciseIdent;
        /// <summary>
        /// 练习值
        /// </summary>
        public string exerciseValue;
        /// <summary>
        /// 需要统计时间的事件Index
        /// </summary>
        public string needTimeEventIndex;
        /// <summary>
        /// 知识标签
        /// </summary>
        public string knowledgeTag;
        /// <summary>
        /// 能力提升ID
        /// </summary>
        public string reportCapacityId;
        /// <summary>
        /// 能力知识点数量
        /// </summary>
        public string capactiyNumber;
        /// <summary>
        /// 判定条件值
        /// </summary>
        public string judgingCriteria;
        /// <summary>
        /// 判断条件
        /// </summary>
        public string judgingCondition;
        /// <summary>
        /// 判定类型
        /// </summary>
        public string judgingCriteriaValue;
        /// <summary>
        /// 击败百分比
        /// </summary>
        public string defeatPercentage;
        /// <summary>
        /// 评价等级
        /// </summary>
        public string evaluateGrade;
        /// <summary>
        /// 评级条件ID
        /// </summary>
        public string reportGradeId;
        /// <summary>
        /// 荣誉图片id
        /// </summary>
        public int honorImageId;
        /// <summary>
        /// 荣誉名称
        /// </summary>
        public string honorName;

        /// <summary>
        /// 练习名称
        /// </summary>
        public string ExerciseName { get; set; }
        /// <summary>
        /// 练习描述
        /// </summary>
        public string ExerciseDes { get; set; }
        /// <summary>
        /// 练习值
        /// </summary>
        public string ExerciseValues { get; set; }
        /// <summary>
        /// 练习提示
        /// </summary>
        public string ExerciseTis { get; set; }

        /// <summary>
        /// 能力名称
        /// </summary>
        public string CapacityName { get; set; }
        /// <summary>
        /// 能力图标
        /// </summary>
        public string CapactiyImage { get; set; }
        /// <summary>
        /// 能力描述
        /// </summary>
        public string CapactiyDes { get; set; }
        /// <summary>
        /// 完成知识点
        /// </summary>
        public string ComKnowledgePoints { get; set; }
        /// <summary>
        /// 所有知识点
        /// </summary>
        public string AllKnowLedgePoints { get; set; }

        public void Rest(ReportLearn.Types.Item item)
        {
            seriesId = item.SeriesId;
            courseId = item.CourseId;
            exerciseIdent = item.ExerciseIdent;
            exerciseValue = "";
            needTimeEventIndex = item.NeedTimeEventIndex;
            knowledgeTag = item.KnowledgeTag;
            reportCapacityId = item.ReportCapacityId;
            capactiyNumber = item.CapactiyNumber;
            judgingCriteria = item.JudgingCriteria;
            judgingCondition = item.JudgingCondition;
            judgingCriteriaValue = "";
            defeatPercentage = "90";
            evaluateGrade = "100017";
            reportGradeId = item.ReportGradeId;
            honorImageId = item.HonorImageId;
            honorName = item.HonorName;
        }
    }

    public class WebCapacityDto
    {
        /// <summary>
        /// 能力名称
        /// </summary>
        public string CapacityName { get; set; }
        /// <summary>
        /// 能力图标
        /// </summary>
        public string CapactiyImage { get; set; }
        /// <summary>
        /// 完成知识点
        /// </summary>
        public string ComKnowledgePoints { get; set; }
        /// <summary>
        /// 所有知识点
        /// </summary>
        public string AllKnowLedgePoints { get; set; }
    }
    public class WebExerciseDto
    {
        /// <summary>
        /// 练习名称
        /// </summary>
        public string ExerciseName { get; set; }
        /// <summary>
        /// 练习值
        /// </summary>
        public string ExerciseValues { get; set; }
        /// <summary>
        /// 练习提示
        /// </summary>
        public string ExerciseTis { get; set; }
    }
}
