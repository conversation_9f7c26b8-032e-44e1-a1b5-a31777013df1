﻿/// <summary>
/// ConfigControlDef
/// </summary>
/// <remarks>
/// 2019.7.31: 创建. 谌安 <br/>
/// 配置各系统模块枚举
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GLib.Common
{
    public class ConfigControlDef
    {
        //系统设置选项布尔类，按照数字0或1缓存到PlayerPrefs，重启后依然保留
        public enum ConfigOptionBool
        {
            //音乐开关
            MusicOn,
            //音效开关
            SoundOn,
        }
        
    }
}
