﻿/// <summary>
/// COpHandleCommandWaitTime
/// </summary>
/// <remarks>
/// 2023/5/11 10:15:53: 创建. 熊洋 <br/>
/// <br/>
/// </remarks>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

namespace GLib.Common
{
    public class COpHandleCommandWaitTime : IHandleCommand
    {

        private float m_Interval;
        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;
        private float m_nowTime = -9999;//开始时间
        private List<IHandleCommand> m_others;
        public COpHandleCommandWaitTime(SOpHandleCommand_WaitTime data)
        {
            m_Interval = data.interval;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpPlayAnimation;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_nowTime = -9999;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }
            if (m_nowTime < 0)
            {
                m_nowTime = Time.realtimeSinceStartup;
            }

            if (Time.realtimeSinceStartup - m_nowTime > m_Interval)
            {
                return true;
            }

            return false;
        }

        public void update()
        {
        }
    }
}

