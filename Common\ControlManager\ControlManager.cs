﻿
using System.Collections;
/// <summary>
/// ControlManager
/// </summary>
/// <remarks>
/// 2021.1.26: 创建. 谌安 <br/>
/// 控制器管理器<br/>
/// </remarks>
namespace GLib.Common
{
    public class CControlManager: IControlManager
    {
        /// 场景切换管理
        StageManager m_StageManager = null;

        // 命令管理
        CHandleCommandManager m_HandleCommandManager = null;
        CHandleMulitpleCommandManager m_HandleMulitpleCommandManager = null;

        CPickupObj m_PickupObj = null;
        /// <summary>
        /// 截屏功能
        /// </summary>
        CCaptureScreen m_CaptureScreen = null;

        //IThinkingAnalytics m_ThinkingAnalytics = null;
        IShareModule m_ShareModule=null;
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName
        {
            get;
            set;
        }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState
        {
            get;
            set;
        }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress
        {
            get;
            set;
        }

        public IPickupObj GetPickupObj()
        {
            return m_PickupObj;
        }

        //命令管理
        public IHandleCommandManager GetHandleCommandManager()
        {
            return m_HandleCommandManager;
        }

        public IHandleCommandManagerMulitple GetHandleMulitpleCommandManager()
        {
            return m_HandleMulitpleCommandManager;
        }

        public ICaptureScreen GetCaptureScreenController()
        {
            return m_CaptureScreen;
        }

        //public IThinkingAnalytics GetThinkingAnalytics()
        //{
        //    if (m_ThinkingAnalytics == null)
        //    {
        //        TRACE.ErrorLn("未初始化数数打点系统");
        //    }

        //    return m_ThinkingAnalytics;
        //}
        public IShareModule GetShareModule()
        {
            if (m_ShareModule == null)
            {
                TRACE.ErrorLn("未初始化分享系统");
            }
            return m_ShareModule;
        }
        //public void SetThinkingAnalytics(IThinkingAnalytics thinkingAnalytics)
        //{
        //    m_ThinkingAnalytics = thinkingAnalytics;
        //}
        public void SetShareModule(IShareModule shareModule)
        {
            m_ShareModule = shareModule;
        }
        public CControlManager()
        {
            m_StageManager = StageManager.Instance;
            m_HandleCommandManager = new CHandleCommandManager();
            m_HandleMulitpleCommandManager = new CHandleMulitpleCommandManager();
            m_PickupObj = new CPickupObj();
            m_CaptureScreen = new CCaptureScreen();
        }
        public bool Create()
        {
            if (!m_StageManager.Create())
            {
                TRACE.ErrorLn("CControlManager m_StageManager Create Fail !");
            }

            GlobalGame.Instance.StartCoroutineEx(DoCreate());
            return true;
        }

        private IEnumerator DoCreate()
        {
            if (!m_HandleCommandManager.Create())
            {
                TRACE.ErrorLn("CControlManager m_HandleCommandManager Create Fail !");
                yield break;
            }
            yield return null;

            if (!m_HandleMulitpleCommandManager.Create())
            {
                TRACE.ErrorLn("CControlManager m_HandleMulitpleCommandManager Create Fail !");
                yield break;
            }
            yield return null;

            if (!m_PickupObj.PickupObj_Load())
            {
                TRACE.ErrorLn("CControlManager m_PickupObj Create Fail !");
                yield break;
            }
            yield return null;

            if (!m_CaptureScreen.Create())
            {
                TRACE.ErrorLn("CControlManager m_CaptureScreen Create Fail !");
                yield return null;
            }
            yield return null;
        }
        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }

        public void Release()
        {
            m_HandleCommandManager.Release();
            m_HandleMulitpleCommandManager.Release();

            m_PickupObj.PickupObj_Unload();
        }

        public void Update()
        {
        }

        public bool GetMovieFinish()
        {
            return true;
        }
    }
}
