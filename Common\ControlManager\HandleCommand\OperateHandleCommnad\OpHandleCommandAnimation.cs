﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandAnimation : IHandleCommand
    {
        private Animator m_animator;
        private string m_animName;
        private float m_animationInterval;
        bool m_isEnd = false; // 是否不正确的执行完指令
        private float m_nowTime = -9999;//开始时间
        private bool m_isPlay = false;
        private float m_speed = -9999;
        private float m_oldSpeed = 1;
        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;
        private bool isReset;
        private string parametersType;
        private string parametersName;
        private string parametersOriginValue;
        private string parametersValue;
        private float parameterDampTime;
        public COpHandleCommandAnimation(SOpHandleCommand_Animation data)
        {
            m_animator = data.animator;
            m_animName = data.AnimationName;
            m_animationInterval = data.animationInterval;
            runInstance = data.runInstance;
            m_speed = data.animationSpeed;
            m_isPlay = false;
            m_others = data.otherCommand;
            isReset = data.isReset;
            parametersType = data.parametersType;
            parametersName = data.parametersName;
            parametersOriginValue = data.parametersOriginValue;
            parametersValue = data.parametersValue;
            if (!string.IsNullOrEmpty(data.parameterDampTime))
            {
                parameterDampTime = float.Parse(data.parameterDampTime);
            }
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpPlayAnimation;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_nowTime = -9999;
            m_animator = null;
            m_animName = "";
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_animator == null)
                {
                    m_animator = OperateHandleCommandHelp.FindTarget<Animator>(runInstance);
                }
            }
            if (m_animator == null)
            {
                m_isEnd = true;
                return false;
            }
            if (!m_isPlay)
            {
                //m_oldSpeed = m_animator.speed;
                if (m_speed > -9999)
                {
                    m_animator.speed = m_speed;
                }
                ///如果填了-1，默认获取当前动画片段时间
                if (m_animationInterval == -1)
                {
                    m_animationInterval = GetLengthByName(m_animName);
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                ///动画状态重置
                if (!string.IsNullOrEmpty(m_animName))
                {
                    if (isReset)
                    {
                        
                        m_animator.Play(m_animName, -1, 0f);
                    }
                    else
                    {
                        m_animator.Play(m_animName);
                    }
                }
                if (!string.IsNullOrEmpty(parametersType))
                {
                    switch (parametersType)
                    {
                        case "int":
                            m_animator.SetInteger(parametersName, int.Parse(parametersValue));
                            break;
                        case "bool":
                            bool state = false;
                            if (parametersValue.Equals("true"))
                            {
                                state = true;
                            }
                            m_animator.SetBool(parametersName, state);
                            break;
                    }
                }
            }

            if (m_nowTime < 0)
            {
                m_nowTime = Time.realtimeSinceStartup;
            }

            if (!string.IsNullOrEmpty(parametersType))
            {
                float value = float.Parse(parametersValue);
                float time = m_animationInterval > parameterDampTime ? parameterDampTime : m_animationInterval;
                float curTime = Time.realtimeSinceStartup - m_nowTime;
                float originValue = value - 1 ;
                if (!string.IsNullOrEmpty(parametersOriginValue))
                {
                     originValue = float.Parse(parametersOriginValue);
                }
                float currentValue = curTime / time * (value - originValue) + originValue;
                if (currentValue > value)
                    currentValue = value;
                switch (parametersType)
                {
                    case "float":
                        m_animator.SetFloat(parametersName, currentValue);
                        break;
                }
            }

            if (m_isPlay)
            {
                m_animator.speed = m_speed;
                //return true;
            }
            if (Time.realtimeSinceStartup - m_nowTime >= m_animationInterval)
            {
                m_animator.speed = m_oldSpeed;
                return true;
            }

            return false;
        }

        public float GetLengthByName(string name)
        {
            float length = 0;
            AnimationClip[] clips = m_animator.runtimeAnimatorController.animationClips;
            foreach (AnimationClip clip in clips)
            {
                if (clip.name.Equals(name))
                {
                    length = clip.length;
                    break;
                }
            }
            return length;
        }

        public void update()
        {
        }
    }
}
