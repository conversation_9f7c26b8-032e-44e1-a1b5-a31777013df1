﻿using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GLib.Client.JLSDKDev;
using static GLib.SocketTraceListener;

namespace GLib.Client
{
    public class IsOperateEnd: OperateLogLogicBase
    {
        
        public override void Init(OperationLogInfo info, UpdateOperateLogDate updatainfo)
        {
          base.Init(info, updatainfo); 

        }
        public override void ChangerConditionDate()
        {
            base.ChangerConditionDate();
              // 
            if (GlobalGame.Instance.OperaLogMagr.allTaskDatas.ContainsKey(m_operateLogInfo.TaskID))
            {
                m_updateOperateLogDate.OperateDate = "true";
            }
            else 
            {
                m_updateOperateLogDate.OperateDate = "false";
            }
            //{
            //    m_updateOperateLogDate.OperateDate = "0";
            //}
        }

       
    }
}
