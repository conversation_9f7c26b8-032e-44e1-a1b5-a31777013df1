﻿/// <summary>
/// HapticToolCenter
/// </summary>
/// <remarks>
/// 2022.12.9: 创建. 谌安 <br/>
/// 触觉工具配置<br/>
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class HapticToolCenter : ISchemeNode, IHapticToolCenter
    {
        private const string HAPTIC_INFO = "HapticTool";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, HapticToolDef> m_HapticToolByID;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        private float m_progress = 0.0f;
        public float Progress {
             get { return m_progress; }
            set { m_progress = value; } }

        public HapticToolCenter()
        {
            m_HapticToolByID = new Dictionary<int, HapticToolDef>();
        }

        ~HapticToolCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = HAPTIC_INFO;
            Progress = 0.0f;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                HapticToolDef map = new HapticToolDef();

                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.ToolName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.ModelID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.ToolDesc = pCSVReader.GetString(nRow, tmp_col++, ""); 
                map.Audio = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.SkinModelID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.IconID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                m_HapticToolByID.Add(map.Id, map);
            }
            Progress = 1.0f;
            return true;
        }

        public void Release()
        {
            m_HapticToolByID.Clear();
            m_HapticToolByID = null;
        }

        public HapticToolDef GetHapticToolByID(int toolID)
        {
            HapticToolDef info = null;

            m_HapticToolByID.TryGetValue(toolID, out info);

            return info;
        }
    }
}
