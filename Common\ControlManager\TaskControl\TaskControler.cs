﻿/// <summary>
/// TaskControler
/// </summary>
/// <remarks>
/// 2021/7/23 16:13:14: 创建. 王正勇 <br/>
/// 
/// </remarks>
using game.scene;
using game.schemes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    /// <summary>
    /// TaskControler
    /// </summary>
    public class CTaskControler : ITaskControler, IEventExecuteSink
    {
        private TaskControlData m_TaskControlData;
        private TaskExecute m_TaskExecute;
        private TaskSendServerInfo m_TaskSendServerInfo;
        private TaskEventScript m_TaskEventScript;
        private int m_NowTriggerTaskId = 0;
        private TaskRunState m_TaskRunState;
        /// <summary>
        /// 运行时是否显示Pin积木块组
        /// </summary>
        private bool m_IsShowPin;
        /// <summary>
        /// 任务视频播放状态
        /// </summary>
        private bool m_TaskVideoPlayState;
        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }
        /// <summary>
        /// 当前任务状态
        /// </summary>
        private TaskType m_NowTaskType;
        /// <summary>
        /// 当前触发任务数据
        /// </summary>
        private TriggerTaskDef m_NowTriggerTaskDef;
        private TaskIssueServerInfo m_TaskIssueServerInfo;
        public bool Create()
        {
            m_TaskRunState = new TaskRunState();
            m_TaskControlData = new TaskControlData();
            m_TaskExecute = new TaskExecute();
            m_TaskSendServerInfo = new TaskSendServerInfo();
            m_TaskEventScript = new TaskEventScript();
            m_TaskIssueServerInfo = new TaskIssueServerInfo();
            GlobalGame.Instance.RegisterModuleEvent(this, (int)EMModuleEvent.FixedUpdate);
            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_UPDATE_TRACK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "CTaskControler::通知任务数据更新");
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_INSTANCE_LIST_UPDTE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "CTaskControler::通知服务器通知客户端更新任务");
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_EXECUTE_SCRIPT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "CTaskControler::通知执行一段脚本");
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_DETAILED_MESSAGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "CTaskControler::通知启动任务结算");
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_FAIL_INFO, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "CTaskControler::通知任务模块VM运行超时");
                pEventEngine.Subscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_OVER_INFORM, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "CTaskControler::任务完成通知");
                pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "CTaskControler::监听退出通知");
                pEventEngine.Subscibe(this, (ushort)DGlobalEvent.INFORM_GO_BACK_MEUN_NOT_LOGINOUT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "CTaskControler::回到计划页但不退出");

                pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_RECONNECTED_REFRESH_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "CTaskControler::监听重联成功刷新场景");


            }
            return true;
        }

        public void FixedUpdate()
        {
        }
        public void SetNowTaskID(int taskId)
        {
            TRACE.TraceLn(string.Format("Task_设置当前任务：{0}", taskId));
            m_NowTriggerTaskId = taskId;
            m_NowTaskType = TaskType.None;
            m_TaskControlData.SetNowTaskInfo(null);
        }
        public int GetNowTaskID()
        {
            return m_NowTriggerTaskId;
        }
        public TaskType GetTaskType()
        {
            return m_NowTaskType;
        }
        public string GetNowTaskGuid()
        {
            if (m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId) == null)
            {
                return "";
            }
            return m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId).TaskInstanceId;
        }
        /// <summary>
        /// 通知触发任务
        /// </summary>
        /// <param name="taskId"></param>
        public void InformTriggerTask(TriggerTaskDef triggerTaskDef)
        {
            if (triggerTaskDef != null)
            {
                m_NowTaskType = TaskType.TaskStrike;
                TRACE.TraceLn(string.Format("Task_当前任务状态:{0}", m_NowTaskType));
                m_NowTriggerTaskDef = triggerTaskDef;
                m_NowTriggerTaskId = triggerTaskDef.taskId;
                m_TaskControlData.SetNowTaskInfo(null);
                m_TaskSendServerInfo.SendTriggerTask(triggerTaskDef);
            }

        }

        public void LateUpdate()
        {
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (int)ViewLogicDef.TASK_INSTANCE_LIST_UPDTE:
                    {
                        m_NowTaskType = TaskType.ReceiveTask;
                        TRACE.TraceLn(string.Format("Task_当前任务状态:{0}", m_NowTaskType));
                        InstanceUpdateTask(pContext);
                    }
                    break;
                //通知编程区加载积木块
                case (int)ViewLogicDef.TASK_UPDATE_TRACK:
                    {
                        m_NowTaskType = TaskType.TaskProceed;
                        TRACE.TraceLn(string.Format("Task_当前任务状态:{0}", m_NowTaskType));
                        UpdateTaskMess(pContext);
                    }
                    break;
                //执行脚本
                case (int)ViewLogicDef.TASK_EXECUTE_SCRIPT:
                    {
                        ExecuteTaskScript(pContext);
                    }
                    break;
                //任务结束
                case (int)ViewLogicDef.TASK_OVER_INFORM:
                    m_NowTaskType = TaskType.TaskOver;
                    TRACE.TraceLn(string.Format("Task_当前任务状态:{0}", m_NowTaskType));
                    NowTaskOverInfo(pContext);
                    break;
                //任务结算
                case (int)ViewLogicDef.TASK_DETAILED_MESSAGE:
                    {
                        m_NowTaskType = TaskType.TaskSettle;
                        TRACE.TraceLn(string.Format("Task_当前任务状态:{0}", m_NowTaskType));
                        TaskDetailtedMessage(pContext);
                    }
                    break;
                //任务VM运行超时
                case (int)ViewLogicDef.TASK_FAIL_INFO:
                    {
                        m_NowTaskType = TaskType.TaskLose;
                        TRACE.TraceLn(string.Format("Task_当前任务状态:{0}", m_NowTaskType));
                        TaskLoseMess(pContext);
                    }
                    break;
                //回到计划页，不退出登陆
                case (ushort)DGlobalEvent.INFORM_GO_BACK_MEUN_NOT_LOGINOUT:
                //退出和切换角色
                case (ushort)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE:
                    {
                        Rest();
                    }
                    break;
                //断线重连
                case (ushort)DGlobalEvent.EVENT_RECONNECTED_REFRESH_SCENE:
                    //如果任务还是触发任务状态，那么在重新触发一次任务
                    //if (m_NowTaskType == TaskType.TaskStrike)
                    //{
                    //    InformTriggerTask(m_NowTriggerTaskDef);
                    //}
                    break;

            }
        }
        #region 任务操作功能
        /// <summary>
        /// 设置任务列表数据
        /// </summary>
        /// <param name="pContext"></param>
        private void InstanceUpdateTask(object pContext)
        {
            byte[] szValue = pContext as byte[];
            Task_TaskInstanceListUpdate_SC task_Inst_SC = Task_TaskInstanceListUpdate_SC.Parser.ParseFrom(szValue);
            if (task_Inst_SC != null)
            {
                TaskInfo taskInfo = task_Inst_SC.TaskInstanceList[0];

                if (taskInfo != null)
                {
                    string resetScriptValue = null;
                    taskInfo.Properties.TryGetValue("resetScript", out resetScriptValue);
                    TRACE.TraceLn(string.Format("Task_收到任务接收通知,任务ID：{0},是否需要清除数据：{1}", taskInfo.TaskId.ToString(), resetScriptValue));
                    m_TaskRunState.m_RunTaskId = taskInfo.TaskId;
                    m_TaskRunState.m_IsTaskRun = true;
                    m_NowTriggerTaskId = taskInfo.TaskId;
                    m_TaskIssueServerInfo.SetNowTaskIssue();
                    m_TaskControlData.AddTaskDataList(taskInfo);
                    m_TaskControlData.SetNowTaskInfo(taskInfo);
                    SendTaskMessage(taskInfo, TaskType.ReceiveTask);

                }
            }
        }

        /// <summary>
        /// 更新任务内容
        /// </summary>
        /// <param name="pContext"></param>
        private void UpdateTaskMess(object pContext)
        {
            byte[] szValue = pContext as byte[];
            Task_UpdateTrackingMessage_SC task_SC = Task_UpdateTrackingMessage_SC.Parser.ParseFrom(szValue);
            if (task_SC != null)
            {
                //TaskInfo taskInfo = m_TaskControlData.GetTaskInfo(task_SC.TaskInstanceId);
                TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId);
                if (taskInfo != null)
                {
                    TRACE.TraceLn(string.Format("Task_收 到更新任务内容通知,任务ID：{0}", taskInfo.TaskId.ToString()));
                    taskInfo.TrackingMessage = task_SC.Message;
                    ITaskEvent taskEvent = m_TaskControlData.GetTaskEvent(taskInfo.TaskId);
                    if (taskEvent != null)
                    {
                        task_SC.Message = UBB.UBBStr(task_SC.Message);
                        m_TaskExecute.SendChangeTaskState(taskInfo.TaskInstanceId, taskEvent, TaskType.TaskProceed, task_SC);
                    }
                }
            }
        }
        /// <summary>
        /// 任务失败
        /// </summary>
        /// <param name="pContext"></param>
        private void TaskLoseMess(object pContext)
        {
            byte[] szValue = pContext as byte[];
            TaskFail_SC fail_SC = TaskFail_SC.Parser.ParseFrom(szValue);
            if (fail_SC != null)
            {
                TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId);
                if (taskInfo != null)
                {
                    ITaskEvent taskEvent = m_TaskControlData.GetTaskEvent(taskInfo.TaskId);
                    if (taskEvent != null)
                    {
                        m_TaskExecute.SendChangeTaskState(taskInfo.TaskInstanceId, taskEvent, TaskType.TaskLose, fail_SC);
                    }
                }
            }
        }
        /// <summary>
        /// 执行任务事件
        /// </summary>
        private void ExecuteTaskScript(object pContext)
        {
            byte[] szValue = pContext as byte[];
            ClientInvokeScript_SC client_SC = ClientInvokeScript_SC.Parser.ParseFrom(szValue);
            m_TaskEventScript.ExecuteTaskScript(client_SC.ScriptName);
        }

        private void TaskDetailtedMessage(object pContext)
        {
            byte[] szValue = pContext as byte[];
            TaskWindUpResult_SC result_SC = TaskWindUpResult_SC.Parser.ParseFrom(szValue);
            if (result_SC != null)
            {
                TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId);
                if (taskInfo != null)
                {
                    TRACE.TraceLn(string.Format("Task_收到任务结算通知{0}", taskInfo.TaskId));
                    ITaskEvent taskEvent = m_TaskControlData.GetTaskEvent(taskInfo.TaskId);
                    if (taskEvent != null)
                    {
                        m_TaskExecute.SendChangeTaskState(taskInfo.TaskInstanceId, taskEvent, TaskType.TaskSettle, result_SC);
                    }
                }
            }
        }
        private void NowTaskOverInfo(object pContext)
        {
            byte[] szValue = pContext as byte[];
            TaskFinished_SC taskfinish_SC = TaskFinished_SC.Parser.ParseFrom(szValue);
            if (taskfinish_SC != null)
            {

                TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId);
                if (taskInfo != null)
                {
                    TRACE.TraceLn(string.Format("Task_收到任务结束通知{0}", taskInfo.TaskId));
                    ITaskEvent taskEvent = m_TaskControlData.GetTaskEvent(taskInfo.TaskId);
                    if (taskEvent != null)
                    {
                        m_TaskExecute.SendChangeTaskState(taskInfo.TaskInstanceId, taskEvent, TaskType.TaskOver, taskfinish_SC);
                    }
                }
            }
        }
        #endregion
        private void SendTaskMessage(TaskInfo taskInfo, TaskType taskType)
        {
            if (taskInfo != null)
            {
                ITaskEvent taskEvent = m_TaskControlData.GetTaskEvent(taskInfo.TaskId);
                if (taskEvent != null)
                {
                    m_TaskExecute.SendChangeTaskState(taskInfo.TaskInstanceId, taskEvent, taskType, taskInfo);
                }
            }
        }

        /// <summary>
        /// 注册任务事件
        /// </summary>
        /// <param name="taskEvent"></param>
        /// <param name="taskGuid"></param>
        public void RegisterTask(ITaskEvent taskEvent, int taskId)
        {
            m_NowTriggerTaskId = taskId;
            m_TaskControlData.AddTaskEventList(taskId, taskEvent);
            TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId);
            SendTaskMessage(taskInfo, TaskType.ReceiveTask);
        }
        /// <summary>
        /// 注销任务事件
        /// </summary>
        /// <param name="taskGuid"></param>
        public void UnSubscibeTask(int taskId)
        {
            m_TaskControlData.RemoveTaskEvent(taskId);
            m_TaskRunState.Rest();
            TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(taskId);
            if (taskInfo != null)
            {
                TRACE.TraceLn(string.Format("Task_发出任务注销通知,注销任务ID为：{0}", taskInfo.TaskId));
                m_TaskSendServerInfo.SendStopTrackingTask(taskInfo.TaskInstanceId);
                m_TaskControlData.RemoveTaskInfo(taskId);
                m_NowTriggerTaskId = 0;
                m_NowTriggerTaskDef = null;
                m_NowTaskType = TaskType.None;
                m_TaskIssueServerInfo.Release();
            }

        }
        /// <summary>
        /// 设置通用任务
        /// </summary>
        /// <param name="taskEvent"></param>
        public void SetGeneralTaskEvent(ITaskEvent taskEvent)
        {
            m_TaskControlData.SetGeneralTaskEvent(taskEvent);
            //TaskInfo taskInfo = m_TaskControlData.GetNowTaskInfo(m_NowTriggerTaskId);
            //SendTaskMessage(taskInfo, TaskType.ReceiveTask);
        }
        public TaskRunState GetTaskRunState()
        {
            return m_TaskRunState;
        }
        public TaskIssueServerInfo GetTaskIssueServierInfo()
        {
            return m_TaskIssueServerInfo;
        }
        private void Rest()
        {
            m_TaskControlData.CloseTaskDefList();
            m_TaskControlData.CloseTaskEventList();
            m_NowTriggerTaskId = 0;
            m_TaskRunState.Rest();
        }
        public void Update()
        {
        }
        public void Release()
        {
            IEventEngine pEventEngine = GHelp.GetEventEngine();
            m_TaskRunState = null;
            m_IsShowPin = true;
            if (pEventEngine != null)
            {
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_UPDATE_TRACK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_INSTANCE_LIST_UPDTE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_EXECUTE_SCRIPT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_DETAILED_MESSAGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_FAIL_INFO, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)ViewLogicDef.TASK_OVER_INFORM, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)DGlobalEvent.EVENT_EXIT_GAMELOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                pEventEngine.UnSubscibe((IEventExecuteSink)this, (int)DGlobalEvent.INFORM_GO_BACK_MEUN_NOT_LOGINOUT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);

                GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_RECONNECTED_REFRESH_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);

            }
        }

        public void SetTaskVideoPlayState(bool bState)
        {
            m_TaskVideoPlayState = bState;
        }

        public bool GetTaskVideoPlayState()
        {
            return m_TaskVideoPlayState;
        }
        public bool GetTaskInfoShowPin()
        {
            return m_IsShowPin;
        }
        public void SetTaskInfoShowPin(bool bState)
        {
            m_IsShowPin = bState;
        }
    }
}
