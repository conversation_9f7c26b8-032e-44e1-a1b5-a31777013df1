﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandCreateEntity:IHandleCommand
    {
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private string entityModelID;
        private string saveEntityID;

        private uint entityViewID;
        private List<IHandleCommand> m_others;
        private GameObject m_targetParent;
        private SOpHandle_RunInstance runInstance;
        public COpHandleCommandCreateEntity(SOpHandleCommand_Entity data)
        {
            entityModelID = data.entityModelID;
            saveEntityID = data.saveEntityID;

            m_others = data.otherCommand;
            m_targetParent = data.targetParent;
            runInstance = data.runInstance;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpCreateEntity;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_targetParent == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_targetParent = t.gameObject;
                    }
                }

                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                if (!string.IsNullOrEmpty(saveEntityID) && !GHelp.OpIsContainsKey(saveEntityID))//相同的key不重复创建
                {
                    entityViewID = (uint)GHelp.GetCreateEntityViewID(GlobalGame.Instance.SchemeCenter.GetEntityInfo().GetEntityInfoByID(int.Parse(entityModelID)).PrefabPath, int.Parse(entityModelID), (obj) => { });
                    GHelp.AppendOpEntity(saveEntityID, entityViewID);
                }
                else if (!string.IsNullOrEmpty(saveEntityID))
                {
                    entityViewID = GHelp.GetOpEntityID(saveEntityID);
                }
                else
                {
                    entityViewID = (uint)GHelp.GetCreateEntityViewID(GlobalGame.Instance.SchemeCenter.GetEntityInfo().GetEntityInfoByID(int.Parse(entityModelID)).PrefabPath, int.Parse(entityModelID), (obj) => { });
                }
            }

            bool isCreate = GHelp.GetEntityFactory().EntityCreateIsFinish(entityViewID);
            if (isCreate)
            {
                if (m_targetParent)
                {
                    GameObject go = GHelp.GetEntityFactory().getEntityGameObjectByID(entityViewID);
                    go.transform.SetParent(m_targetParent.transform);
                }
                return true;
            }
              
            return false;
        }

        public void update()
        {
        }
    }
}
