﻿using game.proto;
using Game.Messages;
using GLib.Common;
using JWT;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CLoginModule : ILoginModule
    {
        //正在登陆中
        private bool m_bIsLogining = false;
        //正在注册中
        private bool m_bIsRegistering = false;

        private LoginState m_loginState = LoginState.Idle;
        //用户名
        private string m_strUserName = "";
        //密码
        private string m_strPassword = "";
        //手机号
        private long m_phoneNumber = 0;
        //验证码的token
        private string m_smsCodeToken = "";
        //验证码
        private int m_smsCode = 0;
        //登陆模式（密码登录，手机验证码登录）
        private EMGameLoginType m_loginType = EMGameLoginType.NONE;
        //开始连接网关
        private bool m_bStartConnect = false;

        // SDK登录
        private bool m_bIsSDKLogining = false;

        private GameState m_oldGameState = GameState.None;

        private GameState m_newGameState = GameState.None;
        const float MOVEI_TIMEOUT = 60f;  // CG超时时间
        const float SDK_LOGIN_TIMEOUT = 300.0f;  // 等待SDK登录认证超时时间
        const float NET_CONNECT_TIMEOUT = 10.0f;//等待网络连接超时
        const float VERIFY_TIMEOUT = 16.0f;//等待身份验证超时
        private bool m_nSDKLogin = false;

        const float SCENE_SERVER_READYTIME_TIMEOUT = 10f;

        //是否需要重连网关
        private bool m_bNeedReConnect = false;
        //登录服握手成功
        private bool m_bHandShakeOk = false;

        private EmAction m_curAction = EmAction.Login;

        //连接状态的进入时间
        private float[] m_fLoginStateTime = new float[(int)LoginState.Max];
        //状态的进入次数
        //在进入idle状态时清零，idle状态不需要统计次数
        private int[] m_nLoginStateEnterNum = new int[(int)LoginState.Max];
        //验证成功标志
        private bool m_bVerifySucc = false;
        //验证失败标志
        private bool m_bVerifyFail = false;
        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; }
        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress { get; set; }

        /// <summary>
        /// 用户名(开发版本登录用)
        /// </summary>
		public string UserName { get { return m_strUserName; } }
        /// <summary>
        /// 密码(开发版本登录用)
        /// </summary>
        public string Password { get { return m_strPassword; } }
        /// <summary>
        /// 手机号
        /// </summary>
        public long PhoneNumber { get { return m_phoneNumber; } }
        /// <summary>
        /// 验证码TOKEN
        /// </summary>
        public string SMSCodeToken { get { return m_smsCodeToken; } }
        /// <summary>
        /// 验证码
        /// </summary>
        public int SMSCode { get { return m_smsCode; } }
        /// <summary>
        /// 登录类别
        /// </summary>
        public EMGameLoginType LoginType { get { return m_loginType; } }
        enum EmAction
        {
            Login,
            Register,
        }
        /// <summary>
        /// 登录状态
        /// </summary>
        enum LoginState
        {
            Idle = 0,				//空闲


            Init,					//初始化(请求sdk登录)

            Movie,					// 播放CG

            SDKLogin,				//SDK登录

            WaitLogin,				//等待玩家登陆
            SDKLogin2,				//第二次SDK登录(初始化时SDK登录只有一次机会执行，这里可以有重试的机会)

            Connect,				//连接网关
            ConnectErr,				//连接失败
            Contected,				//连接成功开始登录

            Verify,					//开始验证
            VerifyErr,				//验证错误
            VerifySucc,				//验证成功

            SceneServerReady,       //场景服准备
            SceneServerReadyErr,    //场景服准备失败
            SceneServerReadySucc,    //场景服准备成功

            Register,               //开始注册
            RegisterErr,			//注册失败
            RegisterSucc,           //注册成功

            End,
            Exit,
            Max,
        }

        public bool Create()
        {
            return true;
        }

        public void FixedUpdate()
        {
            //更新登录状态
            OnLoginStateUpdate();
        }

        public void LateUpdate()
        {
        }

        public void Release()
        {
        }

        public void StartLoginBySMS(long phoneNumber, string token, int code)
        {
            if (m_bIsLogining)
                return;

            if (m_loginState != LoginState.WaitLogin)
            {
                TRACE.WarningLn("LoginModule::StartLogin m_loginState != LoginState.WaitLogin curState=" + m_loginState);
                return;
            }
            m_loginType = EMGameLoginType.SMS_LOGIN;
            m_phoneNumber = phoneNumber;
            m_smsCodeToken = token;
            m_smsCode = code;
            SetLoginState(LoginState.SDKLogin);
            m_bStartConnect = true;
            m_bIsLogining = true;
            m_curAction = EmAction.Login;
            TRACE.TraceLn("TestAW_LoginModule_181");
            //GHelp.addWait(Api.NTR("请等待"));
            GlobalGame.Instance.LoginManager.SetAutoLogin(false);
        }

        public void StartLoginByPassWord(string strUserName, string strPassword)
        {
            if (m_bIsLogining)
                return;

            if (m_loginState != LoginState.WaitLogin)
            {
                TRACE.WarningLn("LoginModule::StartLogin m_loginState != LoginState.WaitLogin curState=" + m_loginState);
                return;
            }

            // 获取配置
            EMGameSDKType sdkType = GHelp.GetConfigGameSDK();

            m_loginType = EMGameLoginType.PASSWORD_LOGIN;

            m_strUserName = strUserName;
            m_strPassword = strPassword;
            SetLoginState(LoginState.SDKLogin);
            m_bStartConnect = true;
            m_bIsLogining = true;
            m_curAction = EmAction.Login;
            TRACE.TraceLn("TestAW_LoginModule_208");
            GHelp.addWait(Api.NTR("请等待"));
            GlobalGame.Instance.LoginManager.SetAutoLogin(false);
        }

        /// <summary>
        /// 开始自动登录
        /// </summary>
        public void StartLoginByAuto(string accessToken)
        {
            if (m_bIsLogining)
                return;
            if (m_loginState != LoginState.WaitLogin)
            {
                TRACE.WarningLn("LoginModule::StartLogin m_loginState != LoginState.WaitLogin curState=" + m_loginState);
                return;
            }
            m_loginType = EMGameLoginType.AUTO_LOGIN;

            SetLoginState(LoginState.SDKLogin);
            m_bStartConnect = true;
            m_bIsLogining = true;
            m_curAction = EmAction.Login;

            //GHelp.addWait("请等待");
            GlobalGame.Instance.LoginManager.SetAutoLogin(true);
        }

        public void StartLoginByOneClick(string token)
        {
            if (m_bIsLogining)
                return;
            if (m_loginState != LoginState.WaitLogin)
            {
                TRACE.WarningLn("LoginModule::StartLogin m_loginState != LoginState.WaitLogin curState=" + m_loginState);
                return;
            }
            m_loginType = EMGameLoginType.ONE_CLICK;

            PlayerPrefs.SetString("OneClickToken", token);

            SetLoginState(LoginState.SDKLogin);
            m_bStartConnect = true;
            m_bIsLogining = true;
            m_curAction = EmAction.Login;

            //GHelp.addWait("请等待");
            GlobalGame.Instance.LoginManager.SetAutoLogin(false);
        }

        public void Update()
        {
        }

        private void OnLoginStateEnter(LoginState state, LoginState oldState)
        {
            switch (state)
            {
                case LoginState.Idle:
                    {

                    }
                    break;
                case LoginState.Init:
                    {
                        OnInit();
                    }
                    break;
                case LoginState.Movie:
                    {
                        //播放电影
                    }
                    break;
                case LoginState.SDKLogin:
                    {
                        if (!m_bIsLogining)
                        {
                            GlobalGame.Instance.SceneClient.CloseSceneServerReady();
                            //GHelp.addWait(Api.NTR("认证中"));

                            // 游戏SDK账号登录
                            GlobalGame.Instance.GameSDK.Login();
                        }
                    }
                    break;
                case LoginState.WaitLogin:
                    {
                        m_bIsLogining = false;
                        m_bIsRegistering = false;
                        //GHelp.HideWait();
                    }
                    break;
                case LoginState.Connect:
                    {
                        //*************  ************
                        //************
                        if (GHelp.GetIsUseSocket())
                        {
                            GlobalGame.Instance.NetManager.Connect(GHelp.GetIP(), GHelp.GetIPPort());
                            TRACE.TraceLn("===LoginModule::Connect,连接网络：" + GHelp.GetIP() + ":" + GHelp.GetIPPort());
                        }
                        else
                        {
                            SetLoginState(LoginState.VerifySucc);
                        }
                        //GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_LOGIN_CONNECT_BEGIN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                    }
                    break;
                case LoginState.ConnectErr:
                    {
                        //gamelogic_SelectAreaState info_areastate = new gamelogic_SelectAreaState();
                        //info_areastate.state = true;
                        //info_areastate.wModel = WindowModel.Login;
                        //GlobalGame.Instance.EventEngine.FireExecute((ushort)GameLogicDef.GVIEWCMD_SELECTAREA_DISABLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, info_areastate);
                        //GlobalGame.Instance.NetManager.Close();
                        m_bIsLogining = false;
                    }
                    break;
                case LoginState.Contected:
                    {
                    }
                    break;
                case LoginState.Register:
                    {
                    }
                    break;
                case LoginState.RegisterErr:
                    {
                        m_bIsRegistering = false;
                    }
                    break;
                case LoginState.RegisterSucc:
                    {
                        m_bIsRegistering = false;
                    }
                    break;
                case LoginState.Verify:
                    {
                        /*if (!GlobalGame.Instance.GameSDK.GetUserInfo().token.Equals(""))
                        {
                            TRACE.TraceLn("===LoginModule::Verify,发送验证消息");
                            AuthenticateRequest requset = new AuthenticateRequest();
                            requset.AclToken = GlobalGame.Instance.GameSDK.GetUserInfo().token;
                            requset.UserId = GlobalGame.Instance.GameSDK.GetUserInfo().access_token;
                            requset.RAMUserId = GlobalGame.Instance.GameSDK.GetUserInfo().userID;
                            requset.Properties["serverNodeName"] = GHelp.GetServerSceneNode();
                            requset.Properties["vmServerNodeName"] = GHelp.GetVMServerSceneNode();

                            if (!string.IsNullOrEmpty(PluginPlatform.Instance.Plugin().Radid()))
                            {
                                requset.Properties["radid"] = PluginPlatform.Instance.Plugin().Radid();
                            }
                            if (!string.IsNullOrEmpty(PluginPlatform.Instance.Plugin().Rsid()))
                            {
                                requset.Properties["rsid"] = PluginPlatform.Instance.Plugin().Rsid();
                            }
                            if (!string.IsNullOrEmpty(PluginPlatform.Instance.Plugin().GetMac()))
                            {
                                requset.Properties["mac"] = PluginPlatform.Instance.Plugin().GetMac();
                            }
                            if (!string.IsNullOrEmpty(ProductConfig.Version))
                            {
                                requset.Properties["version"] = ProductConfig.Version;
                            }
                            if (!string.IsNullOrEmpty(ResUtil.GetCurrentPlatformName()))
                            {
                                requset.Properties["platform"] = ResUtil.GetCurrentPlatformName();
                            }
                            string token = PlayerPrefs.GetString("RefreshToken", "");
                            if (!string.IsNullOrEmpty(token))
                            {
                                requset.Properties["refresh_token"] = token;
                            }

                            SGameMsgHead head;
                            head.SerialNumber = GHelp.GenSerialNumber();
                            head.SrcEndPoint = (int)ENDPOINT.Appclient;
                            head.DestEndPoint = (int)ENDPOINT.Gateway;
                            head.wKeyModule = (int)MSG_MODULEID.Gateway;
                            head.wKeyAction = (int)GatewayMessageCodes.AuthenticateRequest;

                            CPacketSend packet = new CPacketSend();

                            packet.Push<SGameMsgHead>(head);

                            packet.PushPB<AuthenticateRequest>(requset);

                            GlobalGame.Instance.NetManager.SendMessage(packet);
                            TRACE.TraceLn("===LoginModule::Verify,SendMessage发送成功");
                        }
                        else
                        {
                            TRACE.TraceLn("===LoginModule::Verify,token值为空");
                        }*/

                        if (!GlobalGame.Instance.GameSDK.GetUserInfo().token.Equals(""))
                        {
                            TRACE.TraceLn("===LoginModule::Verify,发送验证消息");
                            AuthenticateRequest requset = new AuthenticateRequest();

                            requset.AclToken = GlobalGame.Instance.GameSDK.GetUserInfo().token;// GlobalGame.Instance.GameSDK.GetUserInfo().token;
                            requset.UserId = GlobalGame.Instance.GameSDK.GetUserInfo().access_token;
                            string guidStr = "7bb28bc2-c72f-461f-bc17-48403763591a";
                            guidStr = guidStr.Substring(requset.UserId.Length, guidStr.Length - 1);
                            guidStr = requset.UserId + guidStr;
                            Guid g = Guid.Parse(guidStr);
                            requset.RAMUserId = g.ToString();

                            requset.Properties["serverNodeName"] = GHelp.GetServerSceneNode();
                            requset.Properties["vmServerNodeName"] = GHelp.GetVMServerSceneNode();


                            if (!string.IsNullOrEmpty(ProductConfig.Version))
                            {
                                requset.Properties["version"] = ProductConfig.Version;
                            }
                            if (!string.IsNullOrEmpty(ResUtil.GetCurrentPlatformName()))
                            {
                                requset.Properties["platform"] = ResUtil.GetCurrentPlatformName();
                            }

                            SGameMsgHead head;
                            head.SerialNumber = GHelp.GenSerialNumber();
                            head.SrcEndPoint = (int)ENDPOINT.Appclient;
                            head.DestEndPoint = (int)ENDPOINT.Gateway;
                            head.wKeyModule = (int)MSG_MODULEID.Gateway;
                            head.wKeyAction = (int)GatewayMessageCodes.AuthenticateRequest;

                            CPacketSend packet = new CPacketSend();

                            packet.Push<SGameMsgHead>(head);

                            packet.PushPB<AuthenticateRequest>(requset);

                            GlobalGame.Instance.NetManager.SendMessage(packet);
                            TRACE.TraceLn("===LoginModule::Verify,SendMessage发送成功");
                        }
                    }
                    break;
                case LoginState.VerifyErr:
                    {
                        m_bIsLogining = false;
                        m_bStartConnect = true;
                    }
                    break;
                case LoginState.VerifySucc:
                    {
                        // 为实现在loading中等待场景服的准备完毕事件，这里验证通过直接选角完毕
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_SELECTROLE_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                        m_bIsLogining = false;
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_LOGIN_HIDE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);

                        //SetLoginState(LoginState.SceneServerReady);
                    }
                    break;
                case LoginState.SceneServerReady:
                    {

                    }
                    break;
                case LoginState.SceneServerReadyErr:
                    {

                    }
                    break;
                case LoginState.SceneServerReadySucc:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_SELECTROLE_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                        m_bIsLogining = false;

                        GHelp.HideWait();
                    }
                    break;
                case LoginState.End:
                    {
                        m_bIsRegistering = false;
                        m_bIsLogining = false;
                    }
                    break;
                case LoginState.Exit:
                    {
                        OnLoginExit();
                    }
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 初始化
        /// </summary>
        private void OnInit()
        {
            if (m_oldGameState == GameState.Game)
            {
                if (GlobalGame.Instance.GameSDK.NeedLogout)
                {
                    // 登出
                    GlobalGame.Instance.GameSDK.Logout();
                    GlobalGame.Instance.GameSDK.NeedLogout = false;
                }

                //  隐藏所有窗口

            }
            //订阅fixedupdate事件
            GlobalGame.Instance.RegisterModuleEvent(this, (UInt32)EMModuleEvent.FixedUpdate);

            //GlobalGame.Instance.NetManager.RegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_GATEWAY, (IMessageHandler)this);


        }

        /// <summary>
        /// 设置连接状态
        /// </summary>
        /// <param name="newState"></param>
        private void SetLoginState(LoginState newState)
        {
            if (newState == m_loginState)
            {
                TRACE.WarningLn("LoginModule::SetLoginState SameState state=" + m_loginState);
                return;
            }

            // 旧的流程
            LoginState nOldState = m_loginState;

            //记录进入该状态的时间
            m_fLoginStateTime[(int)newState] = Time.realtimeSinceStartup;

            //记录一次进入该状态的次数
            m_nLoginStateEnterNum[(int)newState]++;

            // 当游戏流程退出
            OnLoginStateExit(nOldState, newState);

            //修改当前状态
            m_loginState = newState;

            // 当游戏流程进入
            OnLoginStateEnter(newState, nOldState);

#if OpenDebugInfo_LoginModule
			TRACE.TraceLn("LoginModule.SetLoginState():" + nOldState.ToString() + "->" + newState.ToString());
#endif
            SEventLoginStateChange_C data = new SEventLoginStateChange_C();
            data.nOldState = (int)nOldState;		// 老状态
            data.nNewState = (int)newState;			// 新状态
            GlobalGame.Instance.EventEngine.FireExecute(DGlobalEvent.EVENT_LOGIN_STATE_CHANGE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, data);
        }

        /// <summary>
        /// 开始登录流程
        /// </summary>
        public void OnEnter(GameState nState, GameState nOldState)
        {
#if OpenDebugInfo_LoginModule
        TRACE.WarningLn("登录模块进入");
#endif
            m_oldGameState = nOldState;
            if (m_oldGameState == GameState.Game)
            {
                //订阅fixedupdate事件
                GlobalGame.Instance.RegisterModuleEvent(this, (UInt32)EMModuleEvent.FixedUpdate);
                SetLoginState(LoginState.WaitLogin);
            }
            else
            {
                SetLoginState(LoginState.Init);
            }
            if (nOldState == GameState.Game)
            {

            }

            if (nOldState != GameState.Init && GlobalGame.Instance.NetManager.Connected)
            {
                GlobalGame.Instance.NetManager.Close();
            }

        }

        /// <summary>
        /// 登录是否完成
        /// </summary>
        /// <returns></returns>
        public bool IsLoginFinished()
        {
            return m_loginState == LoginState.VerifySucc;
            //return m_loginState == LoginState.SceneServerReadySucc;
        }

        /// <summary>
        /// 结束等咯流程
        /// </summary>
        public void OnExit(GameState nState, GameState nNewState)
        {
#if OpenDebugInfo_LoginModule
        TRACE.WarningLn("登录模块退出");
#endif
            m_newGameState = nNewState;
            //可以在此处反订阅事件等操作
            //todo
            //注销网关模块消息
            //GlobalGame.Instance.NetManager.UnRegisterMessageHandler(EMMSG_MODULEID.MSG_MODULEID_GATEWAY);
            //注销fixedupdate事件
            //GlobalGame.Instance.UnRegisterModuleEvent(this);
            SetLoginState(LoginState.Exit);
            m_bHandShakeOk = false;
            // 重置一下
            GlobalGame.Instance.GameSDK.SetUserLoginFlag(false);

            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_EXIT_LOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);

            //GHelp.HideWait();
        }

        private void OnLoginStateUpdate()
        {

            switch (m_loginState)
            {
                case LoginState.Idle:
                    {

                    }
                    break;
                case LoginState.Init:
                    {
                        // 暂时注释掉
                        // 是否已经播放过了CG
                        /*if (false)
                        {
                            int nValue = PlayerPrefs.GetInt("PLAY_YZ3D_START_MOVIE");
                            if (nValue == 0)
                            {
                                //	// 开始播放CG
                                SetLoginState(LoginState.Movie);
                                PlayerPrefs.SetInt("PLAY_YZ3D_START_MOVIE", 1);
                                break;
                            }
                        }*/

                        SetLoginState(LoginState.WaitLogin);
                    }
                    break;
                case LoginState.Movie:
                    {
                        bool bFinish = false;
                        //超时判断
                        if (Time.realtimeSinceStartup - m_fLoginStateTime[(int)LoginState.Movie] > MOVEI_TIMEOUT)
                        {
                            TRACE.WarningLn("LoginModule::OnLoginStateUpdate 电影播放超时");
                            bFinish = true;
                        }

                        if (!bFinish && GlobalGame.Instance.ControlManager.GetMovieFinish())
                        {
                            bFinish = true;
                        }

                        if (bFinish)
                        {
                            SetLoginState(LoginState.WaitLogin);
                        }
                    }
                    break;
                case LoginState.SDKLogin:
                    {
                        //超时判断
                        if (Time.realtimeSinceStartup - m_fLoginStateTime[(int)LoginState.SDKLogin] > SDK_LOGIN_TIMEOUT)
                        {
                            TRACE.WarningLn("LoginModule::OnLoginStateUpdate 平台账号登录超时");
                            break;
                        }
                        // 游戏SDK账号登录失败
                        if (GlobalGame.Instance.GameSDK.GetResult() == EMGameSDKResult.Failed)
                        {
                            //GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenCenterBottom, Api.NTR("您尚未登录,请先点击\"账号\"按钮登录"));
                            //SetLoginState(LoginState.ReqServiceEntry);
                            SetLoginState(LoginState.WaitLogin);
                            break;
                        }
                        else if (GlobalGame.Instance.GameSDK.GetResult() == EMGameSDKResult.Succee)
                        {
                            //并切换到创角界面,因为当前创角的api是由webapi获取的，授权时需要使用userid才能授权成功，所以登录成功后开启角色创建功能
                            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_LOGIN_STATE_LOGIN_SUCCESS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                            SetLoginState(LoginState.WaitLogin);

                            break;
                        }
                    }
                    break;
                case LoginState.WaitLogin:
                    {
                        // 如果sdk登录未成功，先进行SDK登录
                        if (m_bIsSDKLogining)
                        {
                            SetLoginState(LoginState.SDKLogin2);
                            m_bIsSDKLogining = false;
                            break;
                        }
                        else if (string.IsNullOrEmpty(GlobalGame.Instance.GameSDK.GetUserInfo().userID))
                        {//等待用户选择角色结束
                            break;
                        }


                        // sdk登录失败,需要重新登录
                        if (m_bStartConnect && GlobalGame.Instance.GameSDK.GetResult() == EMGameSDKResult.Failed)
                        {
                            m_bIsLogining = false;
                            m_bStartConnect = false;

                            GHelp.HideWait();
                            TRACE.ErrorLn("LoginModule::SDK登录失败，游戏登录被中断...");

                            //GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenCenterBottom, Api.NTR("您尚未登录,请先点击按钮登录"));
                            GHelp.addSystemTipsWithIcon(Api.NTR("您尚未登录,请先点击按钮登录"), EMFInfoIcon.Icon_False);
                        }

                        if (m_bStartConnect)
                        {
                            m_bStartConnect = false;

                            //判断网络是否已经连接
                            if (GlobalGame.Instance.NetManager.Connected)
                            {
                                //如果需要重连，或者握手未成功，就重连网关
                                if (m_bNeedReConnect || !m_bHandShakeOk)
                                {
                                    GlobalGame.Instance.NetManager.Close();
                                    SetLoginState(LoginState.Connect);
                                    break;
                                }
                                else
                                {
                                    //直接转连接成功状态
                                    SetLoginState(LoginState.Contected);
                                    break;
                                }
                            }
                            else
                            {
                                GlobalGame.Instance.NetManager.ResetReconnectCount();
                                SetLoginState(LoginState.Connect);
                            }
                        }
                    }
                    break;
                case LoginState.Connect:
                    {
                        //超时判断
                        if (Time.realtimeSinceStartup - m_fLoginStateTime[(int)LoginState.Connect] > NET_CONNECT_TIMEOUT)
                        {
                            TRACE.WarningLn("LoginModule::OnLoginStateUpdate 连接网关超时！！！");

                            //GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenCenterBottom, Api.NTR("连接服务器超时,请稍后重试！"));
                            GHelp.addSystemTipsWithIcon(Api.NTR("连接服务器超时,请稍后重试！"), EMFInfoIcon.Icon_False);
                            GlobalGame.Instance.NetManager.SetAllowReconnectBool(true);
                            SetLoginState(LoginState.ConnectErr);

                            if (GlobalGame.Instance.NetManager.Connected)
                            {//如果联接成功，但是超时有可能是与网关握手失败，需要关闭当前的socket联接
                                GlobalGame.Instance.NetManager.Close();
                            }

                            break;
                        }

                        //如果登录服握手成功，并且网关连接成功
                        if (m_bHandShakeOk && GlobalGame.Instance.NetManager.Connected)
                        {
                            SetLoginState(LoginState.Contected);
                            break;
                        }
                        //网关连接断开
                        if (GlobalGame.Instance.NetManager.ConnectErr)
                        {
                            //GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenCenterBottom, Api.NTR("连接网络失败,请检查网络连接是否正常！"));
                            GHelp.addSystemTipsWithIcon(Api.NTR("连接网络失败,请检查网络连接是否正常！"), EMFInfoIcon.Icon_False);
                            SetLoginState(LoginState.ConnectErr);
                            break;
                        }
                    }
                    break;
                case LoginState.ConnectErr:
                    {
                        SetLoginState(LoginState.WaitLogin);
                    }
                    break;
                case LoginState.Contected:
                    {
                        //登录流程
                        if (m_curAction == EmAction.Login)
                        {
                            //网关连接成功，开始发送登录验证信息
                            if (SendLogin())
                            {
                                //切换到Passward状态
                                SetLoginState(LoginState.Verify);
                            }
                            else
                            {
                                SetLoginState(LoginState.WaitLogin);
                            }
                            break;
                        }
                        //注册流程
                        if (m_curAction == EmAction.Register)
                        {
                            /*if (SendRegister())
                            {
                                SetLoginState(LoginState.Register);
                            }
                            else
                            {
                                SetLoginState(LoginState.WaitLogin);
                            }*/
                            break;
                        }
                    }
                    break;
                case LoginState.Verify:
                    {
                        //超时判断
                        if (Time.realtimeSinceStartup - m_fLoginStateTime[(int)LoginState.Verify] > VERIFY_TIMEOUT)
                        {
                            TRACE.WarningLn("LoginModule::OnLoginStateUpdate 验证用户信息超时！！！");
                            //GHelp.addSystemTips(EMInfoPos.InfoPos_ScreenCenterBottom, Api.NTR("用户信息校验超时,请稍后再试！"));
                            GHelp.addSystemTipsWithIcon(Api.NTR("用户信息校验超时,请稍后再试！"), EMFInfoIcon.Icon_False);
                            //需要重新连接网关
                            m_bHandShakeOk = false;

                            SetLoginState(LoginState.VerifyErr);
                            break;
                        }

                        if (m_bVerifySucc)
                        {
                            SetLoginState(LoginState.VerifySucc);
                            break;
                        }

                        if (m_bVerifyFail)
                        {
                            SetLoginState(LoginState.VerifyErr);
                            break;
                        }
                    }
                    break;
                case LoginState.VerifyErr:
                    {
                        SetLoginState(LoginState.WaitLogin);
                    }
                    break;
                case LoginState.VerifySucc:
                    {
                        //SetLoginState(LoginState.WaitLogin);
                    }
                    break;
                case LoginState.SceneServerReady:
                    {
                        //超时判断
                        if (Time.realtimeSinceStartup - m_fLoginStateTime[(int)LoginState.SceneServerReady] > SCENE_SERVER_READYTIME_TIMEOUT)
                        {
                            TRACE.WarningLn("LoginModule::OnLoginStateUpdate 场景服准备时间超时");
                            SetLoginState(LoginState.SceneServerReadyErr);
                            break;
                        }

                        if (GlobalGame.Instance.SceneClient.GetSceneServerReady())
                        {
                            SetLoginState(LoginState.SceneServerReadySucc);
                        }
                    }
                    break;
                case LoginState.SceneServerReadyErr:
                    {
                    }
                    break;
                case LoginState.SceneServerReadySucc:
                    {
                    }
                    break;
                case LoginState.Register:
                    {
                        /*if (m_bRegSucc)
                        {
                            SetLoginState(LoginState.RegisterSucc);
                            break;
                        }

                        if (m_bRegErr)
                        {
                            SetLoginState(LoginState.RegisterErr);
                            break;
                        }*/
                    }
                    break;
                case LoginState.RegisterErr:
                    {
                        SetLoginState(LoginState.WaitLogin);
                    }
                    break;
                case LoginState.RegisterSucc:
                    {
                        //转到注册成功界面
                        SetLoginState(LoginState.WaitLogin);
                    }
                    break;
                case LoginState.End:
                    {
                    }
                    break;
                case LoginState.Exit:
                    {
                        SetLoginState(LoginState.Idle);
                    }
                    break;
                default:
                    break;
            }
        }

        private void OnLoginExit()
        {
            //注销fixedupdate事件
            //GlobalGame.Instance.UnRegisterModuleEvent(this);
            SetLoginState(LoginState.Idle);
            m_bVerifySucc = false;
            m_bVerifyFail = false;
            //GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_EXIT_LOGIN_STATE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
        }

        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {
            switch (head.wKeyAction)
            {
                case (int)GatewayMessageCodes.ServerInfoResponse:         //	握手回应  //licc 只有开启短线重连机制才会回发
                    {
                        HandleLoginHandShakeMsg(package);
                    }
                    break;
                case (int)GatewayMessageCodes.AuthenticateResponse:
                    {
                        HandleVerifyMsg(package);
                    }
                    break;
            }
        }

        private void OnLoginStateExit(LoginState oldState, LoginState State)
        {
            switch (oldState)
            {
                case LoginState.Idle:
                    {
                    }
                    break;
                case LoginState.Init:
                    {

                    }
                    break;
                case LoginState.SDKLogin:
                    {
                        //GHelp.HideWait();
                    }
                    break;
                case LoginState.WaitLogin:
                    {
                    }
                    break;
                case LoginState.Connect:
                    {
                    }
                    break;
                case LoginState.ConnectErr:
                    {
                    }
                    break;
                case LoginState.Contected:
                    {
                    }
                    break;
                case LoginState.Verify:
                    {
                    }
                    break;
                case LoginState.VerifyErr:
                    {
                    }
                    break;
                case LoginState.VerifySucc:
                    {
                    }
                    break;
                case LoginState.Register:
                    {
                    }
                    break;
                case LoginState.RegisterErr:
                    {
                    }
                    break;
                case LoginState.RegisterSucc:
                    {

                    }
                    break;
                case LoginState.End:
                    {
                    }
                    break;
                case LoginState.Exit:
                    {
                    }
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 握手消息处理
        /// </summary>
        /// <param name="package"></param>
        public void HandleLoginHandShakeMsg(CPacketRecv package)
        {
            m_bHandShakeOk = true;
            int length = 0;
            package.Pop(out length);
            byte[] szValue = null;
            package.ReadByteBuffer(out szValue, length);
            ServerInfoResponse response = ServerInfoResponse.Parser.ParseFrom(szValue);
            if (response.AuthenticateRequest)
            {//需要授权
                SetLoginState(LoginState.Verify);
            }
            else
            {
                SetLoginState(LoginState.VerifySucc);
            }
        }

        public void HandleVerifyMsg(CPacketRecv package)
        {
            int length = 0;
            package.Pop(out length);
            byte[] szValue = null;
            package.ReadByteBuffer(out szValue, length);
            bool isEnter = true;
            AuthenticateResponse response = AuthenticateResponse.Parser.ParseFrom(szValue);
            if (response != null)
            {
                if (response.Result == AuthenticateResult.Success)
                {
                    if (response.Properties != null)
                    {
                        if (response.Properties.ContainsKey("access_token"))
                        {
                            // 将这次登录的token存进手机缓存中
                            PlayerPrefs.SetString("AccessToken", response.Properties["access_token"]);
                        }
                        if (response.Properties.ContainsKey("refresh_token"))
                        {
                            // 将刷新token也存进手机缓存当中
                            PlayerPrefs.SetString("RefreshToken", response.Properties["refresh_token"]);
                        }
                    }
                    isEnter = false;
                    m_bVerifySucc = true;
                }
            }
            if (isEnter)
            {//授权失败，用户需要重新登录
                PlayerPrefs.SetString("AccessToken", "");
                PlayerPrefs.SetString("RefreshToken", "");
                //GHelp.SetBreakLineOperation(true);
                //GHelp.ChangeScene(0, Vector3.zero);
                GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_LOGIN_HIDE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
                m_bVerifyFail = true;
            }
        }

        /// <summary>
        /// 发送登录
        /// </summary>
        private bool SendLogin()
        {
            //判断握手是否成功
            if (!m_bHandShakeOk)
            {
                TRACE.ErrorLn("LoginModule::SendLogin 还没有握手，发送登录请求失败");
                return false;
            }

            return GlobalGame.Instance.GameSDK.ReqVerify();
        }

        /// <summary>
        /// 判断当前状态是否是等待登录状态
        /// </summary>
        /// <returns></returns>
        public bool CheckLoginStateIsWaitLogin()
        {
            return m_loginState == LoginState.WaitLogin;
        }
    }
}
