﻿/// <summary>
/// Box
/// </summary>
/// <remarks>
/// 2021.3.26: 创建. 谌安 <br/>
/// 宝箱<br/>
/// </remarks>
using System.Collections.Generic;
using System;
using GLib.Common;
using GLib;
using System.Runtime.InteropServices;
using UnityEngine;
using GLib.Client.Entity;
using Google.Protobuf;
using game.schemes;
using game.common;
using game.scene;
using static Game.Entity.Entity_CreateEntity.Types;
using Game.Entity;

namespace GLib.Client.Entity
{

    // 定义
    public class CBox:IBox
    {
        // UID
	    Int64     					m_uid;
		//GUID
		string						m_guid;

	    // 名字
	    string						m_szName;

		string						m_nameColor;

	    // 数值属性
	    int[]						m_nNumProp;

	    // 物品清单
	    UInt16[]					m_GoodsIDList;

	    // 实体类型
	    CEntityClass				m_EntityClass;

		// 信号槽
		//CMessageSlot				m_MessageSlot;
		//实体工厂配置ID（服务器刷怪表ID）
		int m_entityFactoryConfigID = 0;
		// 实体视图ID
		uint                        m_nEntityViewID;

	    // 所有权限
	    List<uint>					m_setMaster;

	    // 客户端是否有权限打开
	    bool						m_bCanOpen;

	    // 上次点打开是什么时候,阻止不停拍空格键不停地
	    // 发拾取消息,客户端不停地冒此宝箱已打开
	    uint					   m_dwLastOpenTick;

		// 模型id
		int                        m_nSkinID;

		//能够拾取的时间，宝箱掉落之后，需要先停留一段时候之后，才能拾取。
		uint m_dwCanPickTick = 0;

		/// <summary>
		/// 3D坐标
		/// </summary>
		Vector3 m_Position;

		/// <summary>
		/// 出生位置
		/// </summary>
		Vector3 m_vBornPos;

		/// <summary>
		/// 是否共享类型
		/// </summary>
		byte m_bShareType;

		int m_nBaseLevel = 0;
		// 实体类型
		EMEntityType m_entitiyType;
		//实体ID
		int m_configID = 0;
		/// <summary>
		/// 销毁时播放拾取光效
		/// </summary>
		bool m_bDestroyPlayPickLighting = false;

        /** 
        @param   
        @param   
        @return  
        */
        public CBox()
        {
	        // UID
	        m_uid = DGlobalGame.INVALID_UID;

			m_guid = "";

			// 名字
			m_szName ="";
	
	        // 实体视图ID
            m_nEntityViewID = 0;
    
	        // 客户端是否有权限打开
	        m_bCanOpen = false;

	        // 上次点打开是什么时候,阻止不停拍空格键不停地
	        // 发拾取消息,客户端不停地冒此宝箱已打开
	        m_dwLastOpenTick = 0;

			m_bShareType = 0;

			m_setMaster = new List<uint>();

			m_nNumProp = new int[(int)eEntityProp.EEntityMax];

			m_GoodsIDList = new ushort[2];

			m_EntityClass = new CEntityClass();
			//m_MessageSlot = new CMessageSlot(DGlobalMessage.MSG_ACTION_MAXID);
        }

        public void Init()
        {   
			for (int i = 0; i < m_nNumProp.Length; i++ )
			{
				m_nNumProp[i] = 0;
			}			
			for (int i = 0; i < m_GoodsIDList.Length; i++)
			{
				m_GoodsIDList[i] = 0;
			}
        }

        /** 释放,会释放内存
        @param   
        @param   
        @return  
        */
        public void Release()
        {
	        // 发送事件
	        /*SEventEntityDestroryEntity_C eventdestroryentity;
	        eventdestroryentity.uidEntity = m_uid;	
	        byte bSrcType = GetEventSourceType();
            GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType,
							           (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
							           eventdestroryentity);*/

	        // 从实体世界中移除
            ((CEntityClient)GHelp.GetEntityClient()).Remove(this);

	        // 从场景中移除掉
            if (m_nEntityViewID != 0)
	        {
				//播放拾取光效
				if(m_bDestroyPlayPickLighting)
				{
					IPerson person = GHelp.GetHero();
					if (person != null)
					{
						int nEffectID = 1;// GHelp.GetGoodsEffectID(GetBoxGoodsBaseLevel());
						LightingEffectContext context = GHelp.GetObjectItem<LightingEffectContext>();
						context.id = (uint)nEffectID;  // 效果Id;
						context.ptCenter = m_Position;
						context.src = m_nEntityViewID;
						context.target = person.GetEntityViewID();      // 技能效果发起者
						GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_ADD_LIGHTING, nEffectID, "", context);
					}
				}

				// 移除实体光效
				int nRayResID = 1;// GetNumProp((uint)EMBOX_PROP.BOX_PROP_RAYRESID);
                if (nRayResID > 0)
                {
                    IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
                    if (pEntityFactory != null)
                    {
						GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_REMOVE_LIGHTING, nRayResID, "", null);
                    }
                }
                
                EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
                Destroy.ENTITY_ID=GetEntityViewID();
				Destroy.ENTITY_UID = m_uid;
				// 移除实体视图
				GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);
	        }

	        // 所有权限
	        m_setMaster.Clear();

	        // 客户端是否有权限打开
	        m_bCanOpen = false;	

	        // 消息槽
	        //m_MessageSlot.Close();

			// UID
			m_uid = 0;

			m_guid = "";

			// 名字
			m_szName = null;

			// 数值属性
			for (int i = 0; i < m_nNumProp.Length; i++)
			{
				m_nNumProp[i] = 0;
			}		

			// 物品清单
			for (int i = 0; i < m_GoodsIDList.Length; i++)
			{
				m_GoodsIDList[i] = 0;
			}

			// 实体视图ID
			m_nEntityViewID = 0;

			// 上次点打开是什么时候,阻止不停拍空格键不停地
			// 发拾取消息,客户端不停地冒此宝箱已打开
			m_dwLastOpenTick = 0;

			//能够拾取的时间，宝箱掉落之后，需要先停留一段时候之后，才能拾取。
			m_dwCanPickTick = 0;

			m_Position = Vector3.zero;

			/// <summary>
			/// 出生位置
			/// </summary>
			m_vBornPos = Vector3.zero;

			/// <summary>
			/// 是否共享类型
			/// </summary>
			m_bShareType = 0;

			m_nBaseLevel = 0;

			m_bDestroyPlayPickLighting = false;

	    }

		/// <summary>
		/// 还原,不释放对象，只将状态还原到创建时状态
		/// </summary>
		public void Restore()
		{
			Release();
			//回收
			GlobalGame.Instance.EntityClient.RecycleEntity(this);
		}

        /** 创建
        @param   
        @param   
        @return  
        */
        public bool Create()
        {
			// 实体类型
			m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Box);

	        // 信号槽
	        //m_MessageSlot.Init(10);
            
            // 此客户端是否存在打开此宝箱权限
            IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			/*IPerson pHero = GHelp.GetHero();
            if(pHero != null)
            {
                uint dwClientPDBID = (uint)pHero.GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_PDBID);
				if (m_setMaster.Count == 0 || m_setMaster.Find(item => item == dwClientPDBID) != 0)
                {
                    m_bCanOpen = true;
                }
            }*/

			//如果主角没有权限打开,并且是共享物品,那么就不创建view对象
			if (!m_bCanOpen && m_bShareType == 1)
			{
				//不用创建表现对象
			}
			else
			{
				//设置宝箱停留时间
				if(m_bCanOpen )
				{
					//uint dwStayTime = (m_bShareType == 1) ? (uint)SHARE_BOX_STAY_TIME : (uint)BOX_STAY_TIME;
				//	m_dwCanPickTick = (uint)Api.GetTickCount() + dwStayTime;
				}

				int nMonsterID = m_configID;

				bool isShowName = false;


				// 创建实体视图
				if (!CreateView())
					return false;

				if(pEntityFactory!=null)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawHP);
					pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagSelectable); // 实体标志  EntityFlags.flagSelectable  // 可选择
				}

				//播放出身光效
				int nRayResID = 1;//GetNumProp((uint)EMBOX_PROP.BOX_PROP_RAYRESID);

				//可拾取
				if (nRayResID > 0 && m_bCanOpen) //
				{
                    SkillEffectContext context = GHelp.GetObjectItem<SkillEffectContext>();
					context.id = (uint)nRayResID;  // 效果Id;
					context.target = context.src = GetEntityViewID();      // 技能效果发起者
					GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ADD_LIGHTING, nRayResID, "", context);
				}

				

				if (pEntityFactory != null)
				{
					pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagSelectable);// | (uint)EntityFlags.flagNoShadow
					if (isShowName)
					{
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName);
					}
				}

				// 设置宝箱名字
				//if (m_GoodsIDList != null && m_GoodsIDList.Length > 0 /*&& GetNumProp((uint)EMBOX_PROP.BOX_PROP_TYPE) != (uint)EMBOX_TYPE.BOX_TYPE_PACK*/)
				/*{
					int nGoodID = m_GoodsIDList[0];

					SGoodsSchemeInfo goodsSchemeInfo = GlobalGame.Instance.SchemeCenter.GetGoodsSchemeInfo(nGoodID);
					if (goodsSchemeInfo != null)
					{
						m_nBaseLevel = goodsSchemeInfo.lBaseLevel;
						if (pEntityFactory != null)
						{
							pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawTitle);
						}

                        cmd_titlepart_visiable visiable = GHelp.GetObjectItem<cmd_titlepart_visiable>();
						visiable.visiable = true;
						//显示头顶文字
						GHelp.sendEntityCommand(m_nEntityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_TITLEPART_VISIABLE, 1, "", visiable);

						string szNameAndColor;
						szNameAndColor = goodsSchemeInfo.szName + ";" + DEntityProp.DROP_EQUIPMENT_BASE_LEVEL_COLOR[m_nBaseLevel] + ";";
						ITitleClient pTitleClient = GHelp.GetTitleClient();
						if (pTitleClient != null)
						{
							pTitleClient.SetEntityTitle(this, (int)TitleIndexType.TitleIndexType_Name, (int)ETitleEffect.ETitleEffect_BoxName, szNameAndColor);
						}
					}
				}*/
			}

			// 添加到实体世界中
			if (!((CEntityClient)GHelp.GetEntityClient()).Add(this))
			{
				TRACE.ErrorLn("Box::GetEntityClient.add failed");
				return false;
			}

	        return true;
        }

		/// <summary>
		/// 获取宝箱内物品的品阶
		/// </summary>
		/// <returns></returns>
		public int GetGooodsBaseLevel()
		{
			return m_nBaseLevel;
		}



        /** 取得实体类型
        @param   
        @param   
        @return  
        */
        public IEntityClass  GetEntityClass()
        {
	        return m_EntityClass;
        }

		public EMEntityType GetEntityType()
		{
			return m_entitiyType;
		}

        /** 取得UID
        @param   
        @param   
        @return  
        */
        public Int64 GetUID()
        {
	        return m_uid;
        }

		public string GetStrGUID()
		{
			return m_guid;
		}

		/// <summary>
		/// 获取3D坐标
		/// </summary>
		/// <returns></returns>
		public Vector3 GetPosition()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
			Vector3 pos = pEntityFactory.GetPosition(m_nEntityViewID);
			return pos;
		}

		/// <summary>
		/// 设置3D坐标
		/// </summary>
		public void SetPosition(Vector3 vPos)
		{
		}

		/// <summary>
		/// 获取是否正在跳跃
		/// </summary>
		/// <returns></returns>
		public EntityJumpDef GetJumpState()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return EntityJumpDef.None;
			EntityJumpDef isJump = pEntityFactory.GetJumpState(m_nEntityViewID);
			return isJump;
		}

		/// <summary>
		/// 设置跳跃状态
		/// </summary>
		/// <param name="state"></param>
		/// <returns></returns>
		public bool SetJumpState(EntityJumpDef state)
        {
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return false;
			pEntityFactory.SetJumpState(m_nEntityViewID, state);
			return true;
        }

		/// <summary>
		/// 获取朝向
		/// </summary>
		/// <returns></returns>
		public Vector3 GetForward()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
			Vector3 forward = pEntityFactory.GetForward(m_nEntityViewID);
			return forward;
		}

		/// <summary>
		/// 设置朝向
		/// </summary>
		/// <param name="vForward"></param>
		public void SetForward(Vector3 vForward)
		{
		}

		/// <summary>
		/// 获取移动速度
		/// </summary>
		/// <returns></returns>
		public float GetMoveSpeed()
		{
			return GetNumProp((uint)eEntityProp.EEntityMoveSpeed) / DGlobalGame.FLOAT_SCALE_SIZE;
		}

        /** 设置数值属性
        @param   nValue ：属性值
        @param   
        @return  
        */
        public bool SetNumProp(uint dwPropID, int nValue)
        {
			if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
			{
				return false;
			}

			m_nNumProp[dwPropID] = nValue;

			return true;
        }

        /** 取得数值属性
        @param   
        @param   
        @return  
        */
        public int GetNumProp(uint dwPropID)
        {
            if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
	        {
		        return 0;
	        }

	        return m_nNumProp[dwPropID];
        }

        /** 设置字符属性
        @param   pszValue ：属性值
        @param   
        @return  
        */
        public bool SetStrProp(uint dwPropID, string pszValue)
        {
			/* switch(dwPropID)
			 {
				 case (uint)EMBOX_PROP.BOX_PROP_POPEDOM:
				 {
					 SyncBoxPopedom(pszValue);
				 }
				 break;
				 case (uint)EMBOX_PROP.BOX_PROP_GOODSLIST:
				 {
					 SyncGoodsList(pszValue);
				 }
				 break;
			 default:break;
			 }*/

			int _value = 0;
			GHelp.ConvertEntityPropInt((eEntityProp)dwPropID, pszValue, ref _value);
			m_nNumProp[dwPropID] = _value;

			return false;
        }

		

        /** 批量更新属性
        @param   
        @param   
        @return  
        */
        public bool BatchUpdateProp(IMessage pszProp, int nLen)
        {
			int nInLen = nLen;
			EntityInfo other = pszProp as EntityInfo;
			m_uid = Api.GuidCInt(other.Guid);
			m_guid = other.Guid;
			m_Position = new UnityEngine.Vector3(other.Position.X, other.Position.Y, other.Position.Z);
			m_szName = other.Name;
			m_entitiyType = (EMEntityType)other.EntityType;
			m_configID = other.ConfigId;
			m_entityFactoryConfigID = other.EntityFactoryConfigID;
			// 数值属性
			//pszProp.ReadToArray<int>(ref m_nNumProp, 4 * (int)EMBOX_PROP.BOX_PROP_BROADCAST);
			foreach (ProItem item in other.Props)
			{
				SetStrProp((uint)item.PropType, item.PropValue);
			}
			return true;
        }

        /** 消息
        @param   
        @param   
        @return  true：正常执行；false：被否决  
        */
        public bool OnMessage(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
        {
			return true;// m_MessageSlot.Fire(dwMsgID, pGameMsgHead, pszMsg);
        }

        /** 订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageVoteSink  pVoteSink, string pszDesc)
        {
			return true;//  m_MessageSlot.Subscibe(dwMsgID, pVoteSink, pszDesc);
		}

        /** 取消订阅投票消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageVoteSink  pVoteSink)
        {
			return true;//  m_MessageSlot.UnSubscibe(dwMsgID, pVoteSink);
		}

        /** 订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool Subscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink, string pszDesc)
        {
			return true;//  m_MessageSlot.Subscibe(dwMsgID, pExecuteSink, pszDesc);
		}

        /** 取消订阅执行消息
        @param   
        @param   
        @return  
        */
        public bool UnSubscibe(uint dwMsgID, IMessageExecuteSink  pExecuteSink)
        {
			return true;//  m_MessageSlot.UnSubscibe(dwMsgID, pExecuteSink);
		}

        /** 取得名字
        @param   
        @param   
        @return  
        */
        public string GetName()
        {
	        return m_szName;
        }

        /** 是否可以自动打开
        @param   
        @param   
        @return  
        */
        public bool CanAutoVisit()
        {
	       /* if(!m_bCanOpen)
	        {
		        return false;
	        }
		   */
	        return true;
        }




        /** 
        @param   
        @param   
        @return  
        */
        public void AutoVisit()
        {
	        m_dwLastOpenTick =(uint)Api.GetTickCount();
        }

		/// <summary>
		/// 是否是共享宝箱
		/// </summary>
		/// <returns></returns>
		public bool IsShareType()
		{
			return m_bShareType > 0;
		}

		/// <summary>
		/// 是否能打开
		/// </summary>
		/// <returns></returns>
		public bool CanOpen()
		{
			return m_bCanOpen;
		}

        /** 取得实体视图ID接口
        @param   
        @param   
        @return  
        */
        public UInt32 GetEntityViewID()
        {
	        return m_nEntityViewID;
        }

        /** 取得事件源类型,SOURCE_TYPE_PERSON, SOURCE_TYPE_MONSTER ... ...
        @param   
        @param   
        @return  
        */
        public byte GetEventSourceType()
        {
            return (byte)EMSOURCE_TYPE.SOURCE_TYPE_BOX;
        }

        /** 
        @param   
        @param   
        @return  
        */
        /*public void Visit(IItemVisitorController  pVisitor)
        {
	        if(pVisitor == null)
	        {
		        return;
	        }
	        pVisitor.VisitBox(this);
        }*/

        /** 
        @param   
        @param   
        @return  
        */
        void SyncBoxPopedom(CPacketRecv pszValue)
        {             
        }

        /** 
        @param   
        @param   
        @return  
        */
        public void SyncGoodsList(CPacketRecv pszValue)
        {
        }

        /** 取得宝箱是挂的物品清单，是给自动打怪使用的
        @param   
        @param   
        @return  返回的是长度为DICE_BOX_MAXGOODNUM的WORD数组
        */
        public  UInt16[]  GetGoodsIDList()
        {
	        return m_GoodsIDList;
        }


        /** 属性是否变化
        @param   
        @param   
        @return  
        */
        public bool IsNumPropChanged(uint dwPropID)
        {
	        return false;
        }


        /** 属性是否变化处理结束,清理标识
        @param   
        @param   
        @return  
        */
        public bool ClsNumPropChanged()
        {
	        return false;
        }

        // 创建显示层实体
        public bool CreateView()
        {
            EntityViewItem item = GHelp.GetObjectItemEx<EntityViewItem>();
            if (!GetBasicViewInfo(ref item))
            {
                TRACE.ErrorLn("Box::CreateView getBasicViewInfo failed!");
                return false;
            }

            // 创建EntityView
            GHelp.sendControllerCommand ((int)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, 0, "", item);
            m_nEntityViewID = item.EntityViewID;
            if (m_nEntityViewID == 0)
            {
                TRACE.ErrorLn("Box::CreateView invalid viewID=" + m_uid);
                return false;
            }

            // 设置出生坐标
            cmd_creature_rigidbody_sync data = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
            data.bNotGround = false;
            data.nEntityID = GetEntityViewID();
            data.fPosition_x = m_Position.x;
			data.fPosition_y = m_Position.y;
			data.fPosition_z = m_Position.z;
            GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", data);

            // 加载皮肤模型
            GHelp.ChangeEntitiyPart(GetEntityViewID(), EntityParts.EntityPart_Body,item.nSkinID);

            GHelp.RecycleObjectItemEx<EntityViewItem>(item);
            //TRACE.TraceLn("createView id=" + m_uid);
            return true;
        }

		public int GetEntityFactoryConfigID()
		{
			return m_entityFactoryConfigID;
		}

		public bool GetBasicViewInfo(ref EntityViewItem item)
        {
			Model.Types.Item modelItem = GHelp.GetModelItem(m_entitiyType, m_configID);
			item.EntityType = (byte)m_entitiyType;// EMEntityType.typeTreasureBox;
            item.EntityViewID = 0;
            item.UID = m_uid;
		
            item.byIsHero = 0;
			//item.prefabPath = modelItem.PrefabPath;
			m_szName = "";//goodsSchemeInfo.szName;
            item.szName = m_szName;
			m_nSkinID = item.nSkinID = modelItem.Id;
			item.ConfigID = m_configID;
			item.nameColor = m_nameColor;
			item.Angle = GetNumProp((int)eEntityProp.EEntityAngle);
			item.fMoveSpeed = GetMoveSpeed();
			item.EntityFactoryConifgID = m_entityFactoryConfigID;
			return true;
        }

		public IMessage GetConfigInfo()
		{
			return GHelp.GetConfigItem(m_entitiyType,m_configID);
		}

		public int GetModelID()
		{
			return m_nSkinID;
		}

		public int GetConfigID()
		{
			return m_configID;
		}

		// 发送命令,命令与事件不同，命令是外界通知实体做某件事情，实体做了，再发出事件
		public void sendCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
        {
        }

		/// <summary>
		/// 设置实体称号
		/// </summary>
		/// <param name="nIndex">TitleIndexType 称号位置</param>
		/// <param name="nEffectID">ETitleEffect 格式化效果ID,	0表示无,可在szTitleName中字定义格式化字串</param>
		/// <param name="szTitleName">称号名字</param>
		/// <returns></returns>
		public bool SetEntityTitle(int nIndex, int nEffectID, string szTitleName, bool bUpdateView = true)
		{
			return false;
		}

		/// <summary>
		/// 播放掉落动画
		/// </summary>
		public void PlayDropAnimation()
		{
			//通知表现层播放宝箱掉落动画
            PlayAnimationContext context = GHelp.GetObjectItem<PlayAnimationContext>();
            context.name = "open";
            GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_PLAY_ANIMATION, 0, "", context);
            //GHelp.PlaySharedSound(AudioAssetConstId.EquipFall);
		}

		/// <summary>
		/// 获取宝箱中物品的品阶
		/// </summary>
		/// <returns></returns>
		private int GetBoxGoodsBaseLevel()
		{
			//if (m_GoodsIDList != null && m_GoodsIDList.Length > 0 /*&& GetNumProp((uint)EMBOX_PROP.BOX_PROP_TYPE) != (uint)EMBOX_TYPE.BOX_TYPE_PACK*/)
			/*{
				int nGoodID = m_GoodsIDList[0];

				SGoodsSchemeInfo goodsSchemeInfo = GlobalGame.Instance.SchemeCenter.GetGoodsSchemeInfo(nGoodID);
				if (goodsSchemeInfo != null)
				{
					return goodsSchemeInfo.lBaseLevel;
				}
			}*/

			return 0;
		}
    }
}
