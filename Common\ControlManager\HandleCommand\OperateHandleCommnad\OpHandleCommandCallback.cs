﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

namespace GLib.Common
{
    public class COpHandleCommandCallback : IHandleCommand
    {

        private float m_callbackInterval;
        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;
        private OpHandleCalll m_callback = null;
        private float m_nowTime = -9999;//开始时间
        private List<IHandleCommand> m_others;
        public COpHandleCommandCallback(SOpHandleCommand_Callback data)
        {
            m_callbackInterval = data.callbackInterval;
            m_callback = data.callback;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpPlayAnimation;
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_nowTime = -9999;
            //m_callback = null;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }
            if (m_callback == null)
            {
                m_isEnd = true;
                return false;
            }
            if (m_nowTime < 0)
            {
                m_nowTime = Time.realtimeSinceStartup;
            }

            if (Time.realtimeSinceStartup - m_nowTime > m_callbackInterval)
            {
                CoroutineHolderManager.Instance.StartCoroutine(AppendCoroutine());
                //m_callback.Invoke();
                return true;
            }

            return false;
        }

        private IEnumerator AppendCoroutine()
        {
            yield return new WaitForEndOfFrame();
            m_callback.Invoke();
            m_callback = null;
        }

        public void update()
        {
        }
    }
}
