﻿/// <summary>
/// Function
/// </summary>
/// <remarks>
/// 2021.5.12: 创建. 谌安 <br/>
/// 游戏全局通用函数<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
	public static class Function
    {
		//计算2维向量的角度
		public static float VectorAngle(Vector2 from, Vector2 to)
		{
			float angle;
			Vector3 cross = Vector3.Cross(from, to);
			angle = Vector2.Angle(from, to);
			return cross.z > 0 ? angle : -angle;
		}

		/// <summary>
		/// 2维向量和世界坐标X轴正方向的角度(-180,180)
		/// </summary>
		/// <param name="x"></param>
		/// <param name="y"></param>
		/// <returns></returns>
		public static float Vector2AngleWithXAxis(float x, float y)
		{
			Vector2 to;
			to.x = x;
			to.y = y;
			Vector2 from;
			from.x = 1;
			from.y = 0;
			return VectorAngle(from, to);
		}
	}
}
