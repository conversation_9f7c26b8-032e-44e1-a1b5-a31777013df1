﻿/// <summary>
/// CustomListBlockDef
/// </summary>
/// <remarks>
/// 2021/7/16 20:17:30: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// CustomListBlockDef
    /// </summary>
    public class CustomListData
    {
        /// <summary>
        /// 列表积木块ID
        /// </summary>
        public string m_CustomListId;
        /// <summary>
        /// 用户自定义类型
        /// </summary>
        public string m_Type;
        /// <summary>
        /// 列表积木块名称
        /// </summary>
        public string m_Name;
        /// <summary>
        /// 变量积木块类型,0表示全局，1表示当前角色
        /// </summary>
        public string m_VariateType;
        /// <summary>
        /// 自定义列表值
        /// </summary>
        public List<CustomListValue> m_CustomListValue;
        public void Reast()
        {
            m_CustomListId = "";
            m_Type = "";
            m_Name = "";
            m_VariateType = "";
        }
    }
    /// <summary>
    /// 自定义列表值
    /// </summary>
    public class CustomListValue
    {
        /// <summary>
        /// 自定义列表值ID
        /// </summary>
        public string m_ListValId;
        /// <summary>
        /// 自定义列表值名称
        /// </summary>
        public string m_ListValName;
        /// <summary>
        ///自定义列表值
        /// </summary>
        public string m_ListValValue;
    }
}
