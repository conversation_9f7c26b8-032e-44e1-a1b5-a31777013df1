﻿/// <summary>
/// MeunNavigationControl
/// </summary>
/// <remarks>
/// 2021/11/25 11:43:31: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// MeunNavigationControl
    /// </summary>
    public class MeunNavigation
    {
        private static MeunNavigation _Instance;
        private WindowModel m_CreateWindowModel;
        private bool m_MeunNavigationShowState;
        /// <summary>
        /// 主界面是否加载完成
        /// </summary>
        private bool m_MainUILoadOver = true;
        public static MeunNavigation Instance
        {
            get
            {
                if (_Instance == null)
                {
                    _Instance = new MeunNavigation();
                }
                return _Instance;
            }
        }


        /// <summary>
        /// 设置当前窗口
        /// </summary>
        /// <param name="windowModel"></param>
        public void SetNowWindowModel(WindowModel windowModel)
        {
            m_CreateWindowModel = windowModel;
        }
        /// <summary>
        /// 设置当前主菜单的显示状态
        /// </summary>
        /// <param name="bState"></param>
        public void SetMeunNavigationShowState(bool bState)
        {
            m_MeunNavigationShowState = bState;
        }

        public void SetMainUILoadOver(bool boState)
        {
            m_MainUILoadOver = boState;
        }
        public WindowModel GetNowWindowModel()
        {
            return m_CreateWindowModel;
        }
        /// <summary>
        /// 获取当前主菜单的显示状态。
        /// </summary>
        /// <returns></returns>
        public bool GetMeunNavigationShowState()
        {
            return m_MeunNavigationShowState;
        }
        public bool GetMainUILoadOver()
        {
            return m_MainUILoadOver;
        }
        public void Release()
        {
            m_CreateWindowModel = WindowModel.None;
            _Instance = null;
        }
    }
}
