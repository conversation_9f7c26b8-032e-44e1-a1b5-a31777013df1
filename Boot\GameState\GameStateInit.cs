﻿/// <summary>
/// GameStateInit
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 客户端状态-初始化<br/>
/// </remarks>
#define OpenDebugInfo_GameStateInit
using GLib.Common;

namespace GLib.Boot
{
	// 游戏客户端状态-初始化 Init
	public class GameStateInit : IGameState
	{
		// 版本检查 内部流程状态
		private enum EMCheckVerState
		{
			None = 0,
			Create,			// 初始化
			Enter,			// 进入
			Wait,			// 等待
			Exit,			// 退出
			Release,		// 关闭
			Error,			// 错误

			MAX,
		}
		////// 模块方法 //////////////////////////////////////////

		private bool m_IsEnd = false;
		// 流程是否结束
		public bool IsEnd
		{
			set { m_IsEnd = value; }
			get { return m_IsEnd; }
		}

		/// <summary>
		/// 状态创建.
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
			return true;
		}

		/// <summary>
		/// 状态释放
		/// </summary>
		public void Release()
		{

		}

		/// <summary>
		/// 当游戏流程进入
		/// </summary>
		/// <param name="nState">新状态</param>
		/// <param name="nOldState">旧状态</param>
		public void OnEnter(GameState nState, GameState nOldState)
		{

			IsEnd = true;
		}

		/// <summary>
		/// 当游戏流程退出
		/// </summary>
		/// <param name="nState">旧状态</param>
		/// <param name="nNewState">新状态</param>
		public void OnExit(GameState nState, GameState nNewState)
		{

		}

		/// <summary>
		/// 每逻辑帧
		/// </summary>
		public void OnFixedUpdate()
		{

		}

	}
}



