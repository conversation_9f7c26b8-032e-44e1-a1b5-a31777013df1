﻿/// <summary>
/// MainEventDef
/// </summary>
/// <remarks>
/// 2019.7.12: 创建. 谌安 <br/>
/// </remarks>
using System;


namespace GLib.Common
{
    public enum MainEventState
    {
        none = 0,
        //隐藏
        hide,
        //灰色
        gray,
        //全部效果开启
        alleffect,
        //移除事件，不对效果进行处理
        EventRemove,
        //清除事件
        ClearEvent,
    }

    /// <summary>
    /// 主事件通用状态
    /// </summary>
    public enum MainEventGeneralState
    {
        none = 0,

        show,

        hide
    }
}
