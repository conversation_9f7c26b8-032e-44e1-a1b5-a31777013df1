﻿/// <summary>
/// MapInfoCenter
/// </summary>
/// <remarks>
/// 2019.7.19: 创建. 谌安 <br/>
/// 地图信息中心
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CameraTransformCenter : ISchemeNode, ICameraTransformCenter
    {
        private const string MAP_INFO = "DefaultTransform";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, CameraTransformDef> m_InfoByID;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        public float Progress { get; set; }

        public CameraTransformCenter()
        {
            m_InfoByID = new Dictionary<int, CameraTransformDef>();
        }

        ~CameraTransformCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = MAP_INFO;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                CameraTransformDef map = new CameraTransformDef();

                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.Pos = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Rorate = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Scale = pCSVReader.GetString(nRow, tmp_col++, "");
                map.DurationTime = pCSVReader.GetFloat(nRow, tmp_col++, 0);
                map.ImageName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Desc = pCSVReader.GetString(nRow, tmp_col++, "");
                m_InfoByID.Add(map.Id, map);
            }
            return true;
        }

        public void Release()
        {
            m_InfoByID.Clear();
            m_InfoByID = null;
        }

        public CameraTransformDef GetCameraTransformInfoByID(int tID)
        {
            CameraTransformDef info = null;

            m_InfoByID.TryGetValue(tID, out info);

            return info;
        }

        public void GetHeroMoveInfoByID(int tID)
        {
            CameraTransformDef info = null;
            m_InfoByID.TryGetValue(tID, out info);
            if (info==null)
            {
                return;
            }
            SEventHeroMove heroMove = new SEventHeroMove();
            heroMove.vTragetPos = GHelp.StringToVec3(info.Pos);
            heroMove.vTragetEul = GHelp.StringToVec3(info.Rorate);
            heroMove.fAnimationDuration = info.DurationTime;
            if (info.DurationTime == 0)
            {
                heroMove.bAnimation = false;
            }
            else
            {
                heroMove.bAnimation = true;
            }
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_UPDATEHEROMOVEID, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, tID);
            GHelp.FireExecute(DGlobalEvent.EVENT_HERO_MOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, heroMove);
        }


        public void GetTouchMoveinfoByID(int tID)
        {
            CameraTransformDef info = null;
            m_InfoByID.TryGetValue(tID, out info);
            if (info == null)
            {
                return;
            }
            HapticDevice_Transform touchMove = new HapticDevice_Transform();
            touchMove.pos = GHelp.StringToVec3(info.Pos);
            touchMove.rotation = GHelp.StringToVec3(info.Rorate);
            touchMove.scale = GHelp.StringToVec3(info.Scale);
            touchMove.handType = EMHandType.All;
           
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_CHANGE_HAPTICDEVICE_TRANSFORM, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, touchMove);
        }

    }
}
