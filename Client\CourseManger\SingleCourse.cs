﻿/// <summary>
/// SingleCourse
/// </summary>
/// <remarks>
/// 2023/2/15 13:50:35: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using GLib.LitJson;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using static GLib.Client.JLSDKDev;

namespace GLib.Client
{
    public class SingleCourse : HTTP_Response_Handler
    {
        /// <summary>
        /// 任务数据
        /// </summary>
        private Dictionary<int, OperateInfo> m_TaskInfo;
        public Dictionary<int, OperateInfo> TaskInfo
        {
            get { return m_TaskInfo; }
        }
        private Dictionary<int, string> m_SpecialDate;
        /// <summary>
        /// 表格配置ID
        /// </summary>
        private Dictionary<int,List<string>> m_AllSpeciaID;
        /// <summary>
        /// 特殊任务ID
        /// </summary>
        public List<string> m_SpecialID;
        /// <summary>
        /// 已经用过的特殊任务ID
        /// </summary>
        private List<string> m_UseSpecialID;
        /// <summary>
        /// 任务阶段表
        /// </summary>

        public List<TaskStageInfoDef> TaskStageInfoDefs;
        /// <summary>
        /// 日志
        /// </summary>
        private Dictionary<string, OperationLogInfo> m_OperateInfo;
        public Dictionary<string, OperationLogInfo> OperateInfo
        {
            get { return m_OperateInfo; }
        }
        /// <summary>
        ///  NPC表格
        /// </summary>
        private Dictionary<int, AssistantNpcDef> m_assistantNpcDef = null;

        public Dictionary<int, AssistantNpcDef> AssistantNpcDef
        {
            get { return m_assistantNpcDef; }
        }

        /// <summary>
        /// 条件数据
        /// </summary>
        private Dictionary<int, OperateConditionInfo> m_OperateCondition;

        public Dictionary<int, OperateConditionInfo> OperateCondition
        {
            get { return m_OperateCondition; }
        }
        /// <summary>
        ///  器械选择
        /// </summary>
        private List<ApparatusSelectDef> m_ApparatusSelectDef;
        public List<ApparatusSelectDef> ApparatusSelectDef
        {
            get { return m_ApparatusSelectDef; }
        }
        /// <summary>
        /// 分数数据
        /// </summary>
        private Dictionary<int, ScoreInfo> m_ScoreInfoDic;
        private Dictionary<int, MenuHelp> m_MenuHelpDic;

        private Dictionary<int, DoctorDialogueInfo> m_DoctorDialogueInfo;
        public Dictionary<int, DoctorDialogueInfo> DoctorDialogueInfo
        {
            get { return m_DoctorDialogueInfo; }
        }

        private List< DoctorPreperativeInfo> m_DoctorPreperativeInfo;
        public List<DoctorPreperativeInfo> DoctorPreperativeInfo
        {
            get { return m_DoctorPreperativeInfo; }
        }
        private Dictionary<int, AnswerUIInfo> m_AnswerUIInfo;
        public Dictionary<int, AnswerUIInfo> AnswerUIInfo
        {
            get { return m_AnswerUIInfo; }
        }
        public Dictionary<int, ScoreInfo> ScoreInfo
        {
            get { return m_ScoreInfoDic; }
        }
        public Dictionary<int, MenuHelp> MenuHelp
        {
            get { return m_MenuHelpDic; }
        }

        public CourseData C_NowCourseDate;
        /// <summary>
        /// 当前选择的病例
        /// </summary>
        public CaseInfo m_NowCaseInfo;
        /// <summary>
        /// 当前模式模式
        /// </summary>
        public CourseState m_CourseState;

        /// <summary>
        /// 服务器获取的当前课程的病例数据 
        /// </summary>
        public List<CaseInfo> m_CaseDatas;


        /// <summary>
        /// 实训ID获取
        /// </summary>
        public TrainDate m_CurrendTrainDate;

        /// <summary>
        /// 训练场景ID
        /// </summary>
        public string m_trainScnenID;
        /// <summary>
        /// 获取任务数据
        /// </summary>
        public CourseDataReturn node;
        public Dictionary<int, ToolTransform> C_ToolTransform { get { return m_ToolTransform; } }



        private Dictionary<int, ToolTransform> m_ToolTransform;

        /// <summary>
        /// 日志
        /// </summary>
        private Dictionary<int, CameraTransformDef> m_OperaAreaTrans;
        public Dictionary<int, CameraTransformDef> OperaAreaTrans
        {
            get { return m_OperaAreaTrans; }
        }

        #region 场景数据管理
        /// <summary>
        /// 病人实体id
        /// </summary>
        private CourseSceneInfo m_patientInfo;
        /// <summary>
        /// 所有场景中的记录信息
        /// </summary>
        private Dictionary<string, CourseSceneInfo> m_sceneInfo;

        private IPatientPlay m_patientPlay;

        private int currentIndex = -1;
        #endregion

        public void InitCourseData(int courseId)
        {
            C_NowCourseDate = GlobalGame.Instance.SchemeCenter.GetCourseDate().GetCourseDateByID(courseId);
            m_SpecialDate = new Dictionary<int, string>();
            m_AllSpeciaID = new Dictionary<int, List<string>>();
            m_SpecialID = new List<string>();
            m_UseSpecialID = new List<string>();
            m_SpecialDate.Clear();
            if (!string.IsNullOrEmpty(C_NowCourseDate.AssessmentTask))
            {
                string[] temp = C_NowCourseDate.AssessmentTask.Split('&');
                for (int i = 0; i < temp.Length; i++)
                {
                    m_SpecialDate.Add(i, temp[i]);
                }
                currentIndex = -1;
            }
        }

        public void Init(CourseData courseData,int patientId,int index)
        {
            //int courseID = courseData.Id;
            //int courseID = patientId == 0 ? courseData.Id : courseData.Id * 100 + patientId;
            int courseID = GlobalGame.Instance.CourseMgr.GetCurCoursePatientId(C_NowCourseDate.Id,patientId);
            if (GlobalGame.Instance.CourseMgr.GetCourseState() == CourseState.MockTest)
            {
                SetSpecialDate(index);
            }
            GlobalGame.Instance.SchemeCenter.GetTaskInfo().LoadScheme(courseID, (data) =>
            {
                m_TaskInfo = data;
            });
            GlobalGame.Instance.SchemeCenter.GetApparatusSelectCenter().LoadScheme(courseID, () =>
            {
                m_ApparatusSelectDef = GlobalGame.Instance.SchemeCenter.GetApparatusSelectCenter().GetApparatusSelectInfos(courseID);
            });
            GlobalGame.Instance.SchemeCenter.GetOperationLog().LoadScheme(courseID, (date) =>
            {
                m_OperateInfo = date;
            });
            GlobalGame.Instance.SchemeCenter.GetOperateCondition().LoadScheme(courseID, (date) =>
            {
                m_OperateCondition = date;
            });
            GlobalGame.Instance.SchemeCenter.GetAssistantNpcCenter().LoadScheme(courseID, () =>
            {
                m_assistantNpcDef = GlobalGame.Instance.SchemeCenter.GetAssistantNpcCenter().GetAssistantNpcTaskInfos(courseID);
            });
            GlobalGame.Instance.SchemeCenter.GetDoctorDialogue().LoadScheme(courseID, (date) =>
            {
                m_DoctorDialogueInfo = date;
            });
            GlobalGame.Instance.SchemeCenter.GetDoctorPreperativ().LoadScheme(courseID, (date) =>
            {
                m_DoctorPreperativeInfo = date;
            });
            GlobalGame.Instance.SchemeCenter.GetAnswerUI().LoadScheme(courseID, (date) =>
            {
                m_AnswerUIInfo = date;
            });
            GlobalGame.Instance.SchemeCenter.GetToolTransform().LoadScheme(courseID, (date) =>
            {
                m_ToolTransform = date;
            });
            GlobalGame.Instance.OperaLogMagr.LoadDatable();
            GlobalGame.Instance.SchemeCenter.GetMenuHelp().LoadScheme(courseID, (date) =>
            {
                m_MenuHelpDic = date;
            });
            GlobalGame.Instance.SchemeCenter.GetOperaAreaTransformCenter().LoadScheme(courseID, (date) =>
            {
                m_OperaAreaTrans = date;
            });
        }


        public void SetSpecialDate(int index)
        {
            m_SpecialID.Clear();
            if (m_SpecialDate.Count!=0)
            {
                if (currentIndex == index)
                {
                    foreach (var item in m_AllSpeciaID.Values)
                    {
                        List<string> temStr = new List<string>();
                        for (int i = 0; i < item.Count; i++)
                        {
                            if (!m_UseSpecialID.Contains(item[i]))
                            {
                                temStr.Add(item[i]);
                            }
                        }
                        if (temStr.Count == 0)
                        {
                            foreach (var tem in item)
                            {
                                m_UseSpecialID.Remove(tem);
                            }
                            int temIndex = Random.Range(0, item.Count);
                            m_SpecialID.Add(item[temIndex]);
                            m_UseSpecialID.Add(item[temIndex]);
                        }
                        else
                        {
                            int temIndex = Random.Range(0, temStr.Count);
                            m_SpecialID.Add(temStr[temIndex]);
                            m_UseSpecialID.Add(temStr[temIndex]);
                        }
                    }
                }
                else
                {
                    m_AllSpeciaID.Clear();
                    m_UseSpecialID.Clear();
                    if (!string.IsNullOrEmpty(m_SpecialDate[index]))
                    {
                        string[] m_tempStrs = m_SpecialDate[index].Split('_');
                        for (int i = 0; i < m_tempStrs.Length; i++)
                        {
                            string[] temspecials = m_tempStrs[i].Split(';');
                            int temIndex = Random.Range(0, temspecials.Length);
                            m_SpecialID.Add(temspecials[temIndex]);
                            m_UseSpecialID.Add(temspecials[temIndex]);
                            m_AllSpeciaID.Add(i, temspecials.ToList());
                        }
                    }
                }
                currentIndex = index;
            }
        }
        #region 获得医生弹窗

        public List<DoctorDialogueInfo> GetAllDialogueByStepID(int stepID)
        {
           return GlobalGame.Instance.SchemeCenter.GetDoctorDialogue().GetAllDialogue(stepID);
        }

        #endregion

        #region 获得答题UI

        public List<AnswerUIInfo> GetAllAnswereByStepID(int stepID)
        {
            List<AnswerUIInfo> tempAll = new List<AnswerUIInfo>();
            foreach (var item in m_AnswerUIInfo.Values)
            {
                if (item.StepID == stepID)
                {
                    tempAll.Add(item);
                }
            }
            return tempAll;
        }

        public List<DoctorDialogueInfo> GetAllGetDoctorDialogueInfoByStepID(int stepID)
        {

            List<DoctorDialogueInfo> tempAll = new List<DoctorDialogueInfo>();
            foreach (var item in m_DoctorDialogueInfo.Values)
            {
                if (item.StepID == stepID)
                {
                    tempAll.Add(item);
                }
            }
            return tempAll;
        }

        public List<DoctorPreperativeInfo> GetAllDoctorPreperative()
        {

            return m_DoctorPreperativeInfo;
        }

        public List<CameraTransformDef> GetAllOperaAreaTrans()
        {
            List<CameraTransformDef> datas=new List<CameraTransformDef>() ;
            foreach (var item in m_OperaAreaTrans.Values)
            {
                datas.Add(item);           
            }
            return datas;
        }

        #endregion

        /// <summary>
        /// 更新课程数据
        /// </summary>
        public void UpCourseDate()
        {

        }
        public void Release()
        {
            m_CourseState = CourseState.None;
            ReleaseCurSceneData();
            m_NowCaseInfo = null;
        }

        public void GetTrainID(bool isExam)
        {
            string webPath = string.Format("{0}{1}/{2}/{3}/{4}", WebURL.TrainID, GlobalGame.Instance.GameSDK.GetUserInfo().userID, C_NowCourseDate.Id, m_NowCaseInfo.id, "Android");
            string endExam = "0";
            if (isExam)
            {
                endExam = "1";
            }
            webPath = string.Format("{0}/{1}", webPath, endExam);
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this, null, "");
        }


        public void OnResponse(byte[] pContent, uint dwLen, string url)
        {
            string data = Encoding.UTF8.GetString(pContent);
        }

        public void OnError(uint dwError, string url)
        {
            Debug.Log("====== : ScoreOnError ");
        }

        public void OnLocation(string new_url, string url)
        {
            Debug.Log("====== :ScoreOnLocation ");
        }

        public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
        {
            if (pData == null)
            {
                return true;
            }
            string data = Encoding.UTF8.GetString(pData);
            m_CurrendTrainDate = JsonMapper.ToObject<TrainDate>(data);
            return true;
        }

        public EMHTTP_METHOD GetCurrentHttpMethod()
        {
            return EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB;
        }

        #region 场景数据管理

        public void RegPatientPlay(IPatientPlay patientPlay)
        {
            m_patientPlay = patientPlay;
        }

        public IPatientPlay GetPatientPlay()
        {
            return m_patientPlay;
        }

        public void SetPatientEntityID(uint eID, int scenePointID)
        {
            if(m_patientInfo == null)
            {
                m_patientInfo = new CourseSceneInfo();
            }
            m_patientInfo.EntityID = eID;
            m_patientInfo.ScenePointID = scenePointID;
        }

        public CourseSceneInfo GetPatient
        {
            get { return m_patientInfo; }
        }

        public void SetOtherModelEntityID(uint eID, int scenePointID, string modelID)
        {
            if (m_sceneInfo == null)
            {
                m_sceneInfo = new Dictionary<string, CourseSceneInfo>();
            }
            CourseSceneInfo course = new CourseSceneInfo();
            course.EntityID = eID;
            course.ScenePointID = scenePointID;
            m_sceneInfo.Add(modelID + "", course);
        }

        public CourseSceneInfo GetOtherModelByID(string modelID)
        {
            CourseSceneInfo courseSceneInfo = null;
            if (m_sceneInfo == null)
                return courseSceneInfo;
            m_sceneInfo.TryGetValue(modelID + "", out courseSceneInfo);
            return courseSceneInfo;
        }

        private void ReleaseCurSceneData()
        {
            m_patientInfo = null;
            if(m_sceneInfo != null)
                m_sceneInfo.Clear();
            m_sceneInfo = null;
            m_patientPlay = null;
        }
        #endregion
    }
}