﻿/// <summary>
/// AudioDef
/// </summary>
/// <remarks>
/// 2019.7.31: 创建. 谌安 <br/>
/// 音效相关定义<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace GLib.Common
{
    //所有的音效的定义全部在这里....
    //  这里定义游戏中公用的音效资源ID,范围 1 ~ 10000
    public static class AudioAssetConstId
    {
        // 通用
        public const int ButtonClick1 = 1;  // 按钮点击通用音1
        public const int ButtonClick2 = 2;  // 按钮点击通用音2
        public const int ButtonClick3 = 3;  // 按钮点击通用音3
        public const int ButtonClick4 = 4;  // 按钮点击通用音4
        public const int ButtonClick5 = 5;  // 按钮点击通用音5

        public const int Grap = 101;  // 抓取音效
        public const int Teleport = 102;  // 传送音效

        public const int MainSystemBG = 1001;   // 主场景背景音效
    }

    public static class AudioTagClass
    {
        //音乐与音效Tag  必须占据Tag1 这是一个大类型Tag1,其他的分类可以Tag2 Tag3
        public static readonly string AudioMusic = "AudioMusic";//音乐Tag
        public static readonly string AudioSound = "AudioSound";//音效Tag
        
        public static readonly string DialogVoice = "DialogVoice";
        public static readonly string SceneBackground = "SceneBackground";
        public static readonly string VideoSound = "VideoSound";
        public static readonly string TaskAudio = "TaskAudio";//任务
        public static readonly string VoiceSound = "VoiceSound";
        public static readonly string BlockSound = "BlockSound";//积木块
        public static readonly string ButtonAudio = "ButtonAudio";
        public static readonly string OPCodeAudio = "OPCodeAudio";
        public static readonly string StepAudio = "StepAudio";//步骤
        public static readonly string UIAudio = "UIAudio";//UI
    }

    // 逻辑层调用显示层播放音乐
    // Tag1 , Tag2 , Tag3 为 分类标签 , 如果不设置底层不会进行记录，强制作为临时音效播放
    public class gamelogic_PlayMusic : cmd_Base
    {
        public int audioAssetId = 0   ;                     //  音效资源ID AudioAssetObject
        public AudioClip clip = null;                       //用于临时播放的声音片段,如果非null则忽略audioAssetId

        public bool loop        = false;                    //是否循环
        public float volume     = 1.0f ;                    //音量
        public float pitch      = 1.0f;                     //这里暂时保留
        public int priority     = 0;                        //这里暂时保留
        public float fadeInTime = 0f;                     //淡入时间  这里是否需要FadeOut参数...
        public bool followListener = false;                 //是否跟随监听器 true 则followTransform为监听器的tranform  false则不跟随 或者自定的跟随物体
        public Transform followTransform = null;            //用于跟随或者放置音效..
        public Vector3 playPosition = Vector3.zero;
        public string Tag1      = AudioTagClass.AudioSound;  //必填  其余不可占用...
        public string Tag2      = ""   ;  // 标签[可选]
        public string Tag3      = ""   ;  // 标签[可选]
        public bool exludeMutiply = false;

        public override void ReSet() {//方便缓存池对象调用...
            audioAssetId = 0;                     //  音效资源ID AudioAssetObject
            clip = null;                       //用于临时播放的声音片段,如果非null则忽略audioAssetId

            loop = false;                    //是否循环
            volume = 1.0f;                    //音量
            pitch = 1.0f;                     //这里暂时保留
            priority = 0;                        //这里暂时保留
            fadeInTime = 0.5f;                     //淡入时间  这里是否需要FadeOut参数...
            followListener = false;                 //是否跟随监听器 true 则followTransform为监听器的tranform  false则不跟随 或者自定的跟随物体
            followTransform = null;            //用于跟随或者放置音效..
            playPosition = Vector3.zero;
            Tag1 = AudioTagClass.AudioSound;  //必填  其余不可占用...
            Tag2 = "";  // 标签[可选]
            Tag3 = "";  // 标签[可选]
            exludeMutiply = false;
    }
        public bool isMusic {
            get { 
                return Tag1 == AudioTagClass.AudioMusic;
            }
        }
        public bool isSound {
            get
            {
                return Tag1 == AudioTagClass.AudioSound;
            }
        }

#if UNITY_EDITOR
        public bool resourceAvailid{
            get {
                return (isMusic || isSound);
            }
        }
#endif
        

        private bool hasTag
        {
            get
            {
                return !string.IsNullOrEmpty(Tag1) || !string.IsNullOrEmpty(Tag2) && !string.IsNullOrEmpty(Tag3) ;
            }
        }
    }


    //  暂停某类音乐或某个音乐,如果 audioAssetId 不为 0 则为具体的音乐,如果audioAssetId为-1，则为所有音乐
    public class gamelogic_PauseMusic : cmd_Base
    {
        public int audioAssetId = 0;   //  音效资源ID AudioAssetObject
        public string Tag = "";   // 筛选条件,满足此条件的音乐都会被暂停
        public bool checkLoop = true;  //  是否检查循环

        public override void ReSet()
        {
            audioAssetId = 0;   //  音效资源ID AudioAssetObject
            Tag = "";   // 筛选条件,满足此条件的音乐都会被暂停
            checkLoop = true;  //  是否检查循环
        }

        public bool all
        {
            get
            {
                return -1 == audioAssetId;
            }
        }
    }

    //  恢复某类音乐或某个音乐,如果 audioAssetId 不为 0 则为具体的音乐
    public class gamelogic_ResumeMusic : cmd_Base
    {
        public int audioAssetId = 0;   //  音效资源ID AudioAssetObject
        public string Tag = "";   // 筛选条件,满足此条件的音乐都会被恢复

        public override void ReSet()
        {
            audioAssetId = 0;   //  音效资源ID AudioAssetObject
            Tag = "";   // 筛选条件,满足此条件的音乐都会被恢复
        }

        public bool all
        {
            get
            {
                return -1 == audioAssetId;
            }
        }
    }


    //  停止某类音乐或某个音乐,如果 audioAssetId 不为 0 则为具体的音乐,如果audioAssetId为-1，则为所有音乐
    public class gamelogic_StopMusic : cmd_Base
    {
        public int audioAssetId = 0;   //  音效资源ID AudioAssetObject
        public string Tag       = ""     ;   // 筛选条件,满足此条件的音乐都会被停止
        public float fadeOutTime = -1.0f  ;   // 淡出时间,小于0表示立即停止
        public bool destory = false;         // 是否销毁

        public override void ReSet()
        {
            audioAssetId = 0;   //  音效资源ID AudioAssetObject
            Tag = "";   // 筛选条件,满足此条件的音乐都会被停止
            fadeOutTime = -1.0f;   // 淡出时间,小于0表示立即停止
            destory = false;         // 是否销毁
        }

        public bool all
        {
            get
            {
                return -1 == audioAssetId;
            }
        }
    }

    public class gamelogic_SetAudioState: cmd_Base{

        public string tag = "";//状态的Tag Tag1的名称
        public bool bOn = false;//是否开启
        public override void ReSet()
        {
            tag = "";//状态的Tag Tag1的名称
            bOn = false;//是否开启
        }

    }

    public class gamelogic_AudioIsPlay : cmd_Base
    {
        public int assetId = 0;//资源ID
        public bool bPlay = false;//是否播放,透传参数
    }

    //  设置某类音乐或某个音乐的音量,如果 audioAssetId 不为 0 则为具体的音乐
    public class gamelogic_SetSoundVolume : cmd_Base
    {
        public int audioAssetId = 0;   //  音效资源ID AudioAssetObject
        public string Tag = "";   // 筛选条件,满足此条件的音乐都会被停止
        public float volume = 1.0f;  // 音量 volume 为 -1 时表示恢复音量


        public override void ReSet()
        {
            audioAssetId = 0;   //  音效资源ID AudioAssetObject
            Tag = "";   // 筛选条件,满足此条件的音乐都会被停止
            volume = 1.0f;  // 音量 volume 为 -1 时表示恢复音量
        }
        public bool all
        {
            get
            {
                return -1 == audioAssetId;
            }
        }
    }


    public class gamelogic_FadeAudio : cmd_Base
    {
        public int audioAssetId;
        public int fadeAssetId = 0;
        public string Tag = "";
        public float fadeMutiply = 1;
        public int excludeAssetId = 0;
        public string excludeTag = "";
    
        public bool all
        {
            get
            {
                return -1 == fadeAssetId;
            }
        }
        public override void ReSet()
        {
            fadeAssetId = 0;
            Tag = "";
            fadeMutiply = 1;
            excludeAssetId = 0;
            excludeTag = "";
        }
    }
}
