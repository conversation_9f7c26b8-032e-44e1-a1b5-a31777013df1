﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public  static class OperateHandleCommandHelp
    {
        public static T FindTarget<T>(SOpHandle_RunInstance runInstance) where T : Component
        {
            GameObject target = null;
            if (runInstance != null)
            {
                if (runInstance.targetKind.Equals("7"))
                {
                    uint eID = GHelp.GetOpEntityID(runInstance.scenePointModelIdOrEntityKey);
                    GameObject tmp = GHelp.GetEntityFactory().getEntityGameObjectByID(eID);
                    if (!string.IsNullOrEmpty(runInstance.targetData) && tmp != null)
                    {
                        Transform tt = tmp.transform.Find(runInstance.targetData);
                        if (tt != null)
                        {
                            tmp = tt.gameObject;
                        }
                        else
                        {
                            tmp = null;
                        }
                    }
                    target = tmp;
                }
            }
            if (target != null)
            {
                return target.GetComponent<T>();
            }
            return null;
        }

        public static void AppendCommand(List<IHandleCommand> handleCommands)
        {
            if (handleCommands != null)
            {
                CoroutineHolderManager.Instance.StartCoroutine(AppendCoroutine(handleCommands));               
            }
        }

        private static IEnumerator AppendCoroutine(List<IHandleCommand> handleCommands)
        {
            yield return new WaitForEndOfFrame();
            Guid guid = Guid.NewGuid();
            string commandGuid = guid.ToString();
            for (int i = 0; i < handleCommands.Count; i++)
            {
                if (handleCommands[i] != null)
                    GHelp.GetControlManager().GetHandleMulitpleCommandManager().appendCommandTail(commandGuid, handleCommands[i]);
            }
        }
    }
}
