﻿/// <summary>
/// TaskSendServerInfo
/// </summary>
/// <remarks>
/// 2021/7/24 10:53:16: 创建. 王正勇 <br/>
/// 任务模块想服务器发送消息
/// </remarks>
using game.proto;
using game.scene;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    /// <summary>
    /// TaskSendServerInfo
    /// </summary>
    public class TaskSendServerInfo
    {
        /// <summary>
        /// 发送触发任务
        /// </summary>
        /// <param name="taskId"></param>
        /// <returns></returns>
        public bool SendTriggerTask(TriggerTaskDef triggerTaskDef)
        {
            SGameMsgHead head = GetSGameMsgHead((int)TaskAction.TriggerTaskCs);
            CPacketSend packet = new CPacketSend();
            packet.Push<SGameMsgHead>(head);
            Task_TriggerTask_CS requset = new Task_TriggerTask_CS();
            requset.TaskId = triggerTaskDef.taskId;
            requset.ReTrigger = triggerTaskDef.IsRestTask;
            packet.PushPB<Task_TriggerTask_CS>(requset);
            GlobalGame.Instance.NetManager.SendMessage(packet);
            TRACE.TraceLn(string.Format("Task_发出任务触发通知,触发任务ID为：{0}", triggerTaskDef.taskId));
            return true;
        }
        /// <summary>
        /// 发送停止任务
        /// </summary>
        /// <param name="strTaskGuid"></param>
        /// <returns></returns>
        public bool SendStopTrackingTask(string strTaskGuid)
        {
            SGameMsgHead head = GetSGameMsgHead((int)TaskAction.StopTrackingTaskCs);
            CPacketSend packet = new CPacketSend();
            packet.Push<SGameMsgHead>(head);
            Task_StopTrackingTask_CS requset = new Task_StopTrackingTask_CS();
            requset.TaskInstanceId = strTaskGuid;
            packet.PushPB<Task_StopTrackingTask_CS>(requset);
            GlobalGame.Instance.NetManager.SendMessage(packet);
            TRACE.TraceLn(string.Format("Task_发出任务注销通知,注销任务GUID为：{0}", strTaskGuid));
            return true;
            
        }
        private SGameMsgHead GetSGameMsgHead(int iKeyAction)
        {
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Task;
            head.wKeyAction = iKeyAction;
            return head;
        }
    }
}
