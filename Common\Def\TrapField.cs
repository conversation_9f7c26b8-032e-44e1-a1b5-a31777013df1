﻿/// <summary>
/// TrapField
/// </summary>
/// <remarks>
/// 2021.3.24: 创建. 王正勇 <br/>
/// 管理所有组件用到的类型和枚举
/// </remarks>
namespace GLib.Common
{
    /// <summary>
    /// 陷阱类型
    /// </summary>
    public enum TrapType
    {
        /// <summary>
        /// 地刺
        /// </summary>
        Sunkens,
        /// <summary>
        /// 黑雾
        /// </summary>
        BlackGog,

    }
    /// <summary>
    /// 陷阱触发类型
    /// </summary>
    public enum TriggerMode
    {
        /// <summary>
        /// 主动触发
        /// </summary>
        Initiative,
        /// <summary>
        /// 碰撞体触发
        /// </summary>
        Collision,
    }
}
