﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public struct PermissionDef
    {
#if UNITY_ANDROID
        public const string Camera = "android.permission.CAMERA";

        public const string Microphone = "android.permission.RECORD_AUDIO";

        public const string FineLocation = "android.permission.ACCESS_FINE_LOCATION";

        public const string CoarseLocation = "android.permission.ACCESS_COARSE_LOCATION";

        public const string ExternalStorageRead = "android.permission.READ_EXTERNAL_STORAGE";

        public const string ExternalStorageWrite = "android.permission.WRITE_EXTERNAL_STORAGE";
#elif UNITY_IPHONE || UNITY_IOS
        public const string Camera = "Camera";

        public const string Microphone = "Microphone";

        public const string FineLocation = "FineLocation";

        public const string CoarseLocation = "CoarseLocation";

        public const string ExternalStorageRead = "ExternalStorageRead";

        public const string ExternalStorageWrite = "ExternalStorageWrite";
#else
        public const string Camera = "Camera";

        public const string Microphone = "Microphone";

        public const string FineLocation = "FineLocation";

        public const string CoarseLocation = "CoarseLocation";

        public const string ExternalStorageRead = "ExternalStorageRead";

        public const string ExternalStorageWrite = "ExternalStorageWrite";
#endif
    }

    [Serializable]
    public class S2C_BaiduVoice
    {
        public string err_msg;
        public int err_no;
        public string[] result;
    }

    [Serializable]
    public class C2S_BaiduVoice
    {
        public string format;
        public int rate;
        public int channel;
        public string cuid;
        public string token;
        public int dev_pid;
        public string speech;
        public int len;
    }

    [Serializable]
    public class S2C_BaiduVoiceToken
    {
        public string refresh_token;
        public int expires_in;
        public string session_key;
        public string access_token;
        public string scope;
        public string session_secret;
    }
}
