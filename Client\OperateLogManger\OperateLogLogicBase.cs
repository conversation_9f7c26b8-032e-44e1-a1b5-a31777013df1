﻿
using GLib.Common;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
//罗皓东
namespace GLib.Client
{
    public class OperateLogLogicBase
    {
        protected OperationLogInfo m_operateLogInfo;
        protected UpdateOperateLogDate m_updateOperateLogDate;
        public virtual void Init(OperationLogInfo info, UpdateOperateLogDate updateinfo)
        {
            m_operateLogInfo = info;
            m_updateOperateLogDate=updateinfo;
            //   DisUpdate(m_operateLogInfo, m_updateOperateLogDate);
           // DisUpdate();
        }
        public virtual void ChangerConditionDate()
        {
            
        }

        public virtual int DisUpdate()
        {
           ChangerConditionDate();
           OperateConditionInfo conditionInfo = GlobalGame.Instance.OperaLogMagr.m_OperateCondition[int.Parse(m_operateLogInfo.ConditionID)];

           int munTpye = OperationStringToTernary.StringToTernary(conditionInfo, m_updateOperateLogDate.OperateDate, m_operateLogInfo);
            return munTpye;
        }

        public virtual void Release()
        { 
         
        }
    }
}
