﻿using game.common;
using game.proto;
using game.scene;
using game.schemes;
using GLib.LitJson;
using Google.Protobuf;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Script.Serialization;
using TMPro;
using UnityEngine;
using UnityEngine.Android;
using UnityEngine.Events;
/// <summary>
/// ITimerManager
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 项目帮助<br/>
/// </remarks>
namespace GLib.Common
{
    public class GHelp
    {
        #region 临时测试数据
        private static string TempIp = "";
        private static string ServerSceneNode = "";
        private static string VMServerSceneNode = "";
        /// <summary>
        /// 首页网址
        /// </summary>
        private static string LoadWebUrl = "";

        private static string ClusterName = "";
        private static int TempPort = 0;
        private static int ReleaseVer = 0;
        private static string UserAgreementUrl = "";
        private static string UserPrivacyUrl = "";
        private static Dictionary<string, object> PropertiesDic = new Dictionary<string, object>();
        private static Dictionary<string, string> PhoneAgreementUrl = new Dictionary<string, string>();
        private static bool LoadWebConfigSet = false;  // 是否通过记事本设置
        private static string LoginUrl = "";  // 登录地址
        private static string ShuQianAIKey = "";//术前AIkey
        private static string BaiduAPIKey = "";     // 百度语音API Key
        private static string BaiduSecretKey = "";     // 百度语音Secret Key
        private static Dictionary<string, object> NacigationProperties = new Dictionary<string, object>();
        /// <summary>
        /// 各个api的地址
        /// </summary>
        private static Dictionary<string, string> WebApiUrls = new Dictionary<string, string>();
        private static string DefaultWebUrl = "";
        private static string TrainingWebUrl = "";
        /// <summary>
        /// 当前布局模式
        /// </summary>
        private static LayoutModel NowLayoutModel = LayoutModel.VerticalModel;
        /// <summary>
        /// 竖屏分辨率
        /// </summary>
        private static Vector2 m_VerticalResolution;
        /// <summary>
        /// 应用市场类型
        /// </summary>
        private static EMMarketType m_marketType = EMMarketType.None;
        /// <summary>
        /// 请求ServerConfig.ini是否完成
        /// </summary>
        public static bool IsServerConfiginiRequest = false;
        const string EmojiPattern = @"\uD83D[\uDE00-\uDE4F\uDC00-\uDDFF]|\uD83C[\uDF00-\uDFFF\uDDE6-\uDDFF]|\uD83E[\uDD00-\uDDFF\uDD10-\uDD3F]|[\u2600-\u26FF\u2700-\u27BF]";

        public static bool IsEventEngine;
        public static void SetDefaultWebUrl(string url)
        {
            if (DefaultWebUrl != url && !string.IsNullOrEmpty(url))
            {
                DefaultWebUrl = url;
            }
        }

        public static void SetTrainingWebUrl(string url)
        {
            if (TrainingWebUrl != url && !string.IsNullOrEmpty(url))
            {
                TrainingWebUrl = url;
            }
        }


        public static string GetTrainingWebUrl()
        {
            if (!string.IsNullOrEmpty(TrainingWebUrl))
            {
                return TrainingWebUrl;
            }
            else
            {
                return GetDefaultWebUrl();
            }
        }

        public static void SetWebApiUrl(string key, string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return;
            }
            // 当存储的地址里不包含时才存入进去，即每个地址只存一次
            if (!WebApiUrls.ContainsKey(key))
            {
                WebApiUrls[key] = url;
            }
        }

        public static void SetTempIP(string t)
        {
            if (string.IsNullOrEmpty(TempIp))
            {
                TempIp = t;
            }
        }

        public static void SetTempPort(int t)
        {
            if (TempPort == 0)
            {
                TempPort = t;
            }
        }

        public static void SetServerSceneNode(string t)
        {
            if (string.IsNullOrEmpty(ServerSceneNode))
            {
                ServerSceneNode = t;
            }
        }

        public static void SetVMServerSceneNode(string t)
        {
            if (string.IsNullOrEmpty(VMServerSceneNode))
            {
                VMServerSceneNode = t;
            }
        }
        public static void SetLoadWebUrl(string t, bool isConfigSet = false)
        {
            if (string.IsNullOrEmpty(t))
            {
                return;
            }
            // 如果是记事本设置
            if (isConfigSet)
            {
                LoadWebConfigSet = isConfigSet;
            }
            if (!LoadWebConfigSet || isConfigSet)
            {
                LoadWebUrl = t;
                TRACE.TraceLn("===首页地址：" + LoadWebUrl);
                if (!string.IsNullOrEmpty(t))
                {
                    Uri uri = new Uri(t);
                    if (uri != null)
                    {
                        WebURL.LoadDomainName = string.Format("{0}://{1}", uri.Scheme, uri.Host);
                    }
                }
            }
        }

        public static void SetClusterName(string t)
        {
            ClusterName = t;
        }

        public static void SetReleaseVer(int i)
        {
            ReleaseVer = i;
        }

        public static void SetUserAgreementUrl(string t)
        {
            UserAgreementUrl = t;
        }

        public static void SetUserPrivacyUrl(string t)
        {
            UserPrivacyUrl = t;
        }

        public static void SetPropertiesDic(Dictionary<string, object> d)
        {
            PropertiesDic = d;
        }

        public static void SetPhoneAgreementUrl(Dictionary<string, string> d)
        {
            PhoneAgreementUrl = d;
        }

        public static void SetLoginUrl(string t)
        {
            if (LoginUrl != t && !string.IsNullOrEmpty(t))
            {
                LoginUrl = t;
            }
        }

        /// <summary>
        /// 设置当前布局模式
        /// </summary>
        /// <param name="layoutModel"></param>
        public static void SetLayoutModel(LayoutModel layoutModel)
        {
            NowLayoutModel = layoutModel;
        }
        public static void SetVerticalWidthOrHeight(Vector2 vecVertical)
        {
            m_VerticalResolution = vecVertical;
        }

        public static void SetNavigationProperties(Dictionary<string, object> pro)
        {
            if (pro != null)
            {
                NacigationProperties = pro;
            }
        }
        
        public static void SetMarketType(int type)
        {
            m_marketType = (EMMarketType)type;
        }
        
        public static EMMarketType GetMarketType()
        {
            return m_marketType;
        }

        public static string GetDefaultWebUrl()
        {
            if (!string.IsNullOrEmpty(DefaultWebUrl))
            {
                return DefaultWebUrl;
            }
            else
            {
                return WebURL.webUrl;
            }
        }

        public static string GetWebApiUrl(string key)
        {
            // 如果服务器有下发，则取服务器的
            if (WebApiUrls.ContainsKey(key))
            {
                if (!string.IsNullOrEmpty(WebApiUrls[key]))
                {
                    return WebApiUrls[key];
                }
            }

            // 如果没有对应的key，取服务器下发的默认地址
            if (!string.IsNullOrEmpty(DefaultWebUrl))
            {
                return DefaultWebUrl;
            }
            // 连默认地址都没有则取写死的地址
            else
            {
                return WebURL.webUrl;
            }
        }

        public static string GetIP()
        {
            if (string.IsNullOrEmpty(TempIp))
            {
                return "************";
            }
            return TempIp;
        }

        public static int GetIPPort()
        {
            if (TempPort == 0)
            {
                TempPort = 9931;
            }
            return TempPort;
        }
        public static string GetServerSceneNode()
        {
            if (string.IsNullOrEmpty(ServerSceneNode))
            {
                return "";
            }
            return ServerSceneNode;
        }

        public static string GetVMServerSceneNode()
        {
            if (string.IsNullOrEmpty(VMServerSceneNode))
            {
                return "";
            }
            return VMServerSceneNode;
        }
        public static string GetLoadWebUrl()
        {
            if (string.IsNullOrEmpty(LoadWebUrl))
            {
#if UNITY_IPHONE || UNITY_IOS
                return WebURL.AppIOSUrl;
#else
                return WebURL.AppUrl;
#endif
            }
            return LoadWebUrl;
        }

        public static string GetClusterName()
        {
            return ClusterName;
        }

        public static int GetReleaseVer()
        {
            return ReleaseVer;
        }

        /// <summary>
        /// 获取用户协议
        /// </summary>
        /// <returns></returns>
        public static string GetUserAgreementUrl()
        {
            if (string.IsNullOrEmpty(UserAgreementUrl))
            {
                return WebURL.UserAgreementUrl;
            }
            return UserAgreementUrl;
        }

        /// <summary>
        /// 获取隐私协议
        /// </summary>
        /// <returns></returns>
        public static string GetUserPrivacyUrl()
        {
            if (string.IsNullOrEmpty(UserPrivacyUrl))
            {
                return WebURL.UserPrivacyUrl;
            }
            return UserPrivacyUrl;
        }

        public static Dictionary<string, object> GetPropertiesDic()
        {
            return PropertiesDic;
        }

        public static Dictionary<string, string> GetPhoneAgreementUrl()
        {
            return PhoneAgreementUrl;
        }

        /// <summary>
        /// 获取登录地址
        /// </summary>
        /// <returns></returns>
        public static string GetLoginUrl()
        {
            if (!string.IsNullOrEmpty(LoginUrl))
            {
                return LoginUrl;
            }
            else
            {
                return WebURL.LoginUrl;
            }
        }
        public static Dictionary<string, object> GetNacigationProperties()
        {
            return NacigationProperties;
        }

        public static object GetNavigatProByKey(string key)
        {
            object value = null;
            if (NacigationProperties != null && NacigationProperties.ContainsKey(key))
            {
                value = NacigationProperties[key];
            }
            return value;
        }

        private static string tempTargetID = "";
        public static void SetTempTargetID(string t)
        {
            tempTargetID = t;
        }

        public static string TempTargetID()
        {
            return tempTargetID;
        }
        /// <summary>
        /// 获取当前布局模式
        /// </summary>
        /// <returns></returns>
        public static LayoutModel GetLayoutModel()
        {
            return NowLayoutModel;
        }
        public static Vector2 GetVerticalResolution()
        {
            return m_VerticalResolution;
        }
        #endregion
        //是否审
        private static bool g_aud_version = false;

        #region SerialNumber  序列number,服务器所需值，每次叠加

        private static int nSerialNumber = 0;
        public static int GenSerialNumber()
        {
            nSerialNumber++;
            return nSerialNumber;
        }

        #endregion

        public static IControlManager GetControlManager()
        {
            return GlobalGame.Instance.ControlManager;
        }

        public static IUpdateEngine GetUpdateEngine()
        {
            return ((IUpdateEngine)Api.GetUpdateEngine());
        }

        private static IG_CombinactionManager m_CombinactionManager;
        public static void SetCombinactionManager(IG_CombinactionManager g_CombinactionManager)
        {
            m_CombinactionManager = g_CombinactionManager;
        }

        /// <summary>
        /// 是否连接了力反馈设备
        /// </summary>
        private static bool IsHaptic = false;
        public static void SetIsHaptic(int isHaptic)
        {
            IsHaptic = isHaptic == 1;
        }

        public static bool GetIsHaptic()
        {
            return IsHaptic;
        }

        /// <summary>
        /// 是否需要开启切换步骤的按钮
        /// </summary>
        private static bool IsHideChangeStep = false;
        public static void SetIsHideChangeStep(int isHideChangeStep)
        {
            IsHideChangeStep = isHideChangeStep == 1;
        }

        public static bool GetIsHideChangeStep()
        {
            return IsHideChangeStep;
        }
        private static string CaseSkip;
        public static void SetCaseSkip(string temCase)
        {
            CaseSkip = temCase;
        }

        public static List<int> GetCaseSkipID()
        {
            List<int> temLiist = new List<int>();
            if (!string.IsNullOrEmpty(CaseSkip))
            {
                string[] temStrs = CaseSkip.Split('_');
                foreach (var item in temStrs)
                {
                    temLiist.Add(int.Parse(item));
                }
            }
            return temLiist;
        }
        public static IG_CombinactionManager GetCombinactionManager()
        {
            return m_CombinactionManager;
        }

        //当前系统平台
        private static SystemPlatform m_curPlatform = SystemPlatform.PC;
        //当前系统平台
        public static void SetSystemPlatform(SystemPlatform p)
        {
            m_curPlatform = p;
        }

        public static SystemPlatform GetSystemPlatform()
        {
            return m_curPlatform;
        }

        //是否使用socket 连接
        private static bool m_useSocket = true;
        public static void SetUseSocket(bool isuse)
        {
            m_useSocket = isuse;
        }

        public static bool GetIsUseSocket()
        {
            return m_useSocket;
        }

        private static bool m_isUpdateDefine = false;
        /// <summary>
        /// 设置是否是热更新
        /// </summary>
        /// <param name="isUpdateDefine"></param>
        public static void SetUpdateDefine(bool isUpdateDefine)
        {
            m_isUpdateDefine = isUpdateDefine;
        }
        /// <summary>
        /// 获取是否是热更新
        /// </summary>
        /// <returns></returns>
        public static bool GetUpdateDefine()
        {
            int only = ProductIni.Instance.GetInt("UpdateOnlyStart", 0);
            if (only == 1)
                return false;//暂时不开热更新，为了强更版本

            return m_isUpdateDefine;
        }

        public static bool SetAudState(string p)
        {
            bool bReturn = false;
            if (!string.IsNullOrEmpty(p))
            {
                string[] s = p.Split(';');
                foreach (string tmp in s)
                {
                    string curVersion = ProductConfig.Version;
                    if (!ResUtil.GetCurrentPlatformName().Equals("IOS"))
                    {
                        curVersion = curVersion + "_" + PluginPlatform.Instance.Plugin().Radid() + "_" + PluginPlatform.Instance.Plugin().Rsid();
                        TRACE.TraceLn(curVersion + "  server:" + tmp);
                    }
                    if (curVersion.Equals(tmp))
                    {
                        g_aud_version = true;
                        bReturn = g_aud_version;
                        break;
                    }
                }
            }
            return bReturn;
        }
        /// <summary>
        /// 当前状态   审
        /// </summary>
        /// <returns></returns>
        public static bool GetAudState()
        {
#if UNITY_EDITOR
            string isAud = PlayerPrefs.GetString("EditorisAud");
            if (string.IsNullOrEmpty(isAud))
            {
                return g_aud_version;
            }
            else
            {
                return isAud.Equals("true");
            }
#else
         return g_aud_version;
#endif
        }

        /** 取得事件引擎
		@param   
		@param   
		@return  
		*/
        public static IEventEngine GetEventEngine()
        {
            if (GlobalGame.Instance == null)
            {
                return null;
            }

            return GlobalGame.Instance.EventEngine;
        }

        /** 获取网络模块
        @param   
        @param   
        @return  
        */
        public static INetManager GetNetManager()
        {
            if (GlobalGame.Instance == null)
            {
                return null;
            }

            return GlobalGame.Instance.NetManager;
        }



        public static IEntityFactory GetEntityFactory()
        {
            return GlobalGame.Instance.EntityFactory;
        }

        public static IEffectViewManager GetEffectViewManager()
        {
            return GlobalGame.Instance.EffectViewManager;
        }

        /** 取得BUFF客户端
		@param   
		@param   
		@return  
		*/
        public static IBuffClient GetBuffClient()
        {
            return GlobalGame.Instance.BuffClient;
        }

        /** 取得效果客户端
		@param   
		@param   
		@return  
		*/
        public static IEffectClient GetEffectClient()
        {
            return GlobalGame.Instance.EffectClient;
        }
        /** 取得实体客户端
		@param   
		@param   
		@return  
		*/
        public static IEntityClient GetEntityClient()
        {
            if (GlobalGame.Instance == null)
            {
                return null;
            }

            return GlobalGame.Instance.EntityClient;
        }

       
        public static bool CheckAppFristRun()
        {
            return PlayerPrefs.GetInt("AppFristRun", 0) != 1;
        }

        /** 发送执行事件
		@param   
		@param   
		@return  
		*/
        public static void FireExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            if (GlobalGame.Instance == null)
            {
                return;
            }
            IEventEngine pEventEngine = GlobalGame.Instance.EventEngine;
            if (pEventEngine == null)
            {
                return;
            }
            // 发送执行事件
            pEventEngine.FireExecute(wEventID, bSrcType, dwSrcID, pContext);
        }

        /** 发送否决事件
		@param   
		@param   
		@return  
		*/
        public static bool FireVote(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            IEventEngine pEventEngine = GlobalGame.Instance.EventEngine;
            if (pEventEngine == null)
            {
                return false;
            }

            return pEventEngine.FireVote(wEventID, bSrcType, dwSrcID, pContext);
        }


        /// <summary>
		/// 逻辑层发送消息到UI层
		/// </summary>
		/// <param name="wEventID">事件ID</param>
		/// <param name="bSrcType">发送源类型</param>
		/// <param name="dwSrcID">发送源标识（实体为UID中"序列号"部份，非实体就为0）</param>
		/// <param name="pContext">上下文</param>
		public static void sendUIAysncCommand(WindowModel pModel, ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            cmd_AsyncBase info = null;
            if (pContext == null || !(pContext is cmd_AsyncBase))
            {
                info = new cmd_AsyncBase();
            }
            else
            {
                info = pContext as cmd_AsyncBase;
            }

            info.wModel = pModel;

            GlobalGame.Instance.EventEngine.FireExecute(wEventID, bSrcType, dwSrcID, info);
        }

        /// <summary>
        /// 获得实体移动路径信息
        /// </summary>
        /// <returns></returns>
        public static List<Vector3> GetEntiyiPathInfoList(long uid)
        {
            UInt32 ViewID = GlobalGame.Instance.EntityClient.Get(uid).GetEntityViewID();

            IEntityFactory pEntityFactory = GetEntityFactory();
            if (pEntityFactory == null)
            {
                return null;
            }

            return pEntityFactory.GetPathList(ViewID);
        }

        public static T GetObjectItem<T>() where T : cmd_Base, new()
        {
            if (GSpawnPool.Instance == null)
            {
                return null;
            }
            return GSpawnPool.Instance.GetObjectItem<T>();
        }

        /// <summary>
        /// 获取缓存对象（可用于发送多次命令，使用完后需要调用RecycleObjectCmd自行回收）
        /// 特别提醒：返回的对象只能用于发送命令，外部不能对其缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T GetObjectItemEx<T>() where T : cmd_Base, new()
        {
            T obj = GSpawnPool.Instance.GetObjectItem<T>();

            // 引用计数加1
            if (obj is cmd_Base)
            {
                ((cmd_Base)obj).IncRef();
            }

            return obj;
        }

        /// <summary>
        /// 回收命令对象（配合GetObjectCmd成对使用）
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="item"></param>
        public static void RecycleObjectItemEx<T>(T item) where T : cmd_Base, new()
        {
            if (item is cmd_Base)
            {
                // 引用计数减1
                ((cmd_Base)item).DecRef();

                // 将对象回收到缓冲池
                if (((cmd_Base)item).GetRefCount() <= 0)
                {
                    // 相关的内容未改完，暂关闭
                    ((cmd_Base)item).ReSet();
                    GSpawnPool.Instance.RecycleObjectItemToCaches<T>(item);
                }
            }
        }
        /// <summary>
		/// 逻辑层发送消息到显示层（Render 和 UI）
		/// </summary>
		/// <param name="cmdID">消息码 GameLogicDef枚举中定义</param>
		/// <param name="nParam"></param>
		/// <param name="strParam"></param>
		/// <param name="obj">obj必须为继承cmd_Base的类或者为null</param>
		public static void sendControllerCommand(int cmdID, int nParam, string strParam, object obj)
        {
            GlobalGame.Instance.RenderViewProxy.sendControllerCommand(cmdID, nParam, strParam, obj);
        }

        /// <summary>
        /// 逻辑层发送消息到显示层（EntityView）
        /// </summary>
        /// <param name="entityID">EntityView ID</param>
        /// <param name="cmdID">消息码 EntityLogicDef枚举中定义</param>
        /// <param name="nParam"></param>
        /// <param name="strParam"></param>
        /// <param name="obj">obj必须为继承cmd_EntityView的类或者为null</param>
        public static void sendEntityCommand(uint entityID, int cmdID, int nParam, string strParam, object obj)
        {
            GlobalGame.Instance.RenderViewProxy.sendEntityCommand(entityID, cmdID, nParam, strParam, obj);
        }

        /// <summary>
        /// 切换场景
        /// </summary>
        /// <param name="mapID">地图ID</param>
        /// <param name="reloadIfAlreadyLoad">果场景已经加载，是否强制重新加载</param>
        public static void ChangeScene(uint mapID, Vector3 pos, bool reloadIfAlreadyLoad = false)
        {
            //先眨眼，眨眼结束后在跳场景
            BlinkStart blinkStart = new BlinkStart();
            blinkStart.mapID = mapID;
            blinkStart.pos = pos;
            blinkStart.reloadIfAlreadyLoad = reloadIfAlreadyLoad;
            FireExecute((ushort)DGlobalEvent.EVENT_HERO_BLINK_START, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, blinkStart);
            //BlinkEndChangeScene(mapID, pos, reloadIfAlreadyLoad);
        }

        public static void BlinkEndChangeScene(uint mapID, Vector3 pos, bool reloadIfAlreadyLoad = false)
        {
            MapInfoDef item = GlobalGame.Instance.SchemeCenter.GetMapInfo().GetMapInfoByID((int)mapID);
            GHelp.GetEventEngine().FireExecute((ushort)ViewLogicDef.GVIEWCMD_LOADINGWIN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
            gamelogic_LoadSceneInfo loadInfo = new gamelogic_LoadSceneInfo();
            loadInfo.strSceneName = item.SceneName;
            loadInfo.nMapID = (uint)item.Id;
            loadInfo.mPosition = pos;
            StageManager.Instance.OnUpdateFinish(loadInfo, true);
            GHelp.RecycleObjectItemEx<gamelogic_LoadSceneInfo>(loadInfo);
        }

        /// <summary>
        /// 发送退出场景通知
        /// </summary>
        /// <param name="CurSceneID">当前场景</param>
        /// <param name="CurSceneName">当前场景名称</param>
        public static void FindQuitScene(uint CurSceneID, string CurSceneName)
        {
            #region 发送退出场景通知
            SEventSceneQuit sEventSceneQuit = new SEventSceneQuit();
            sEventSceneQuit.nMapId = CurSceneID;
            sEventSceneQuit.strSceneName = CurSceneName;
            GHelp.FireExecute((ushort)DGlobalEvent.EVENT_QUIT_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, sEventSceneQuit);
            #endregion
        }

        #region 光效相关
        public static int CreateLightingEffect(uint effectID, Vector3 ptCenter, Transform parent = null)
        {
            if (effectID <= 0)
            {
                return -1;
            }

            int nID = GetUILightingEffectID();
            LightingEffectContext context = GHelp.GetObjectItem<LightingEffectContext>();
            context.id = effectID;  // 效果Id;
            context.ptCenter = ptCenter;
            context.parent = parent;
            context.feedbackID = nID;
            GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_ADD_LIGHTING, (int)effectID, "", context);
            return nID;
        }

        /// <summary>
        /// 关闭UI光效
        /// </summary>
        /// <param name="efftectID">光效ID</param>
        public static void CloseLightingEffect(uint efftectID, int feedbackID)
        {
            if (efftectID > 0)
            {
                LightingEffectStopContext cmdLeffect = GHelp.GetObjectItem<LightingEffectStopContext>();
                cmdLeffect.id = (uint)efftectID;
                cmdLeffect.feedbackID = feedbackID;

                sendControllerCommand((int)ViewLogicDef.GVIEWCMD_REMOVE_LIGHTING, (int)efftectID, "", cmdLeffect);
            }
        }

        private static int m_nCurGetUILightingEffectID = 0;


        private static int GetUILightingEffectID()
        {
            if (m_nCurGetUILightingEffectID > 65535)
            {
                m_nCurGetUILightingEffectID = 0;
            }

            m_nCurGetUILightingEffectID++;

            return m_nCurGetUILightingEffectID;
        }
        #endregion

        #region 音效相关
        /// <summary>
        /// 播放音乐或音效
        /// </summary>
        /// <param name="audioID">资源ID</param>
        /// <param name="volume">音量</param>
        /// <param name="isLoop">是否循环</param>
        /// <param name="followListener">是否跟随AudioListener</param>
        /// <param name="audioTag1">音乐的标签AudioTagClass.AudioMusic（音乐）或者AudioTagClass.AudioSound(音效)</param>
        /// <param name="audioTag2">其它标志例如AudioTagClass.SceneBackground</param>
        public static void PlayMusic(int audioID, string audioTag1, string audioTag2 = "", float volume = 1.0f, bool isLoop = false, bool followListener = true)
        {
            SendPlayMusic(audioID, null, audioTag1, audioTag2, volume, isLoop, followListener);
        }
        /// <summary>
        /// 播放音乐或音效
        /// </summary>
        /// <param name="audioID">资源ID</param>
        /// <param name="audioClip">音频资源</param>
        /// <param name="volume">音量</param>
        /// <param name="isLoop">是否循环</param>
        /// <param name="followListener">是否跟随AudioListener</param>
        /// <param name="audioTag1">音乐的标签AudioTagClass.AudioMusic（音乐）或者AudioTagClass.AudioSound(音效)</param>
        /// <param name="audioTag2">其它标志例如AudioTagClass.SceneBackground</param>
        public static void PlayMusic(int audioID, AudioClip audioClip, string audioTag1, string audioTag2 = "", float volume = 1.0f, bool isLoop = false, bool followListener = true)
        {
            SendPlayMusic(audioID, audioClip, audioTag1, audioTag2, volume, isLoop, followListener);
        }
        /// <summary>
        /// 发送播放视频
        /// </summary>
        /// <param name="audioID">资源ID</param>
        /// <param name="audioClip">音频资源</param>
        /// <param name="audioTag1">音乐的标签AudioTagClass.AudioMusic（音乐）或者AudioTagClass.AudioSound(音效)</param>
        /// <param name="audioTag2">其它标志例如AudioTagClass.SceneBackground</param>
        /// <param name="volume">音量</param>
        /// <param name="isLoop">是否循环</param>
        /// <param name="followListener">是否跟随AudioListener</param>
        private static void SendPlayMusic(int audioID, AudioClip audioClip, string audioTag1, string audioTag2, float volume, bool isLoop, bool followListener)
        {
            gamelogic_PlayMusic playMusic = GSpawnPool.Instance.GetObjectItem<gamelogic_PlayMusic>();
            playMusic.audioAssetId = audioID;
            if (audioClip != null)
            {
                playMusic.clip = audioClip;
            }
            playMusic.fadeInTime = 0.1f;
            playMusic.loop = isLoop;
            if (followListener)
            {
                playMusic.followListener = followListener;
            }
            playMusic.Tag1 = audioTag1;
            playMusic.Tag2 = audioTag2;
            playMusic.volume = volume;
            sendControllerCommand((int)ViewLogicDef.GVIEWCMD_PLAY_MUSIC, 0, "", playMusic);
        }

        /// <summary>
        /// 停止音乐  tag:筛选条件,满足此条件的音乐都会被停止参照AudioTagClass类的定义
        /// </summary>
        /// <param name="audioID">资源ID</param>
        /// <param name="tag">筛选条件,满足此条件的音乐都会被停止参照AudioTagClass类的定义</param>
        /// <param name="fadeOutTime">淡出时间,小于0表示立即停止</param>
        /// <param name="destory">是否销毁</param>
        public static void StopMusic(int audioID, string tag, float fadeOutTime = 0f, bool destory = false)
        {
            gamelogic_StopMusic stopMusic = GetObjectItem<gamelogic_StopMusic>();
            stopMusic.audioAssetId = audioID;
            stopMusic.Tag = tag;
            stopMusic.fadeOutTime = fadeOutTime;
            stopMusic.destory = destory;
            sendControllerCommand((int)ViewLogicDef.GVIEWCMD_STOP_MUSIC, 0, "", stopMusic);
        }

        /// <summary>
        /// 暂停音乐  
        /// </summary>
        /// <param name="audioID">audioAssetId 不为 0 则为具体的音乐,如果audioAssetId为-1，则为所有音乐</param>
        /// <param name="tag">筛选条件,满足此条件的音乐都会被停止参照AudioTagClass类的定义</param>
        public static void PauseMusic(int audioID, string tag)
        {
            gamelogic_PauseMusic pm = GetObjectItem<gamelogic_PauseMusic>();
            pm.audioAssetId = audioID;
            pm.Tag = tag;
            sendControllerCommand((int)ViewLogicDef.GVIEWCMD_PAUSE_MUSIC, 0, "", pm);
        }

        /// <summary>
        /// 恢复音乐  
        /// </summary>
        /// <param name="audioID">audioAssetId 不为 0 则为具体的音乐,如果audioAssetId为-1，则为所有音乐</param>
        /// <param name="tag">筛选条件,满足此条件的音乐都会被停止参照AudioTagClass类的定义</param>
        public static void ResumeMusic(int audioID, string tag)
        {
            gamelogic_ResumeMusic rm = GetObjectItem<gamelogic_ResumeMusic>();
            rm.audioAssetId = -1;
            rm.Tag = tag;
            sendControllerCommand((int)ViewLogicDef.GVIEWCMD_RESUME_MUSIC, 0, "", rm);
        }

        /// <summary>
        /// 设置tag对应的音乐状态
        /// </summary>
        /// <param name="tag">筛选条件,满足此条件的音乐都会被停止参照AudioTagClass类的定义</param>
        /// <param name="bOn">状态</param>
        public static void SetAudioState(string tag, bool bOn)
        {
            gamelogic_SetAudioState setStateMusic = GetObjectItem<gamelogic_SetAudioState>();
            setStateMusic.tag = tag;
            setStateMusic.bOn = bOn;
            sendControllerCommand((int)ViewLogicDef.GVIEWCMD_SET_AUDIO_STATE, 0, "", setStateMusic);
        }

        public static bool AudioIsPlay(int assetId)
        {
            gamelogic_AudioIsPlay audio = GetObjectItem<gamelogic_AudioIsPlay>();
            audio.assetId = assetId;
            sendControllerCommand((int)ViewLogicDef.GVIEWCMD_AUDIO_ISPLAY, 0, "", audio);
            return audio.bPlay;
        }
        /// <summary>
        /// 根据路径获取音频
        /// </summary>
        /// <param name="strPath"></param>
        /// <param name="actionBack"></param>
        public static void GetAudioClip(string strPath, Action<AudioClip> actionBack)
        {
            GResources.LoadAsync<AudioClip>(strPath, (path, asset) =>
            {
                if (actionBack != null)
                {
                    actionBack.Invoke(asset);
                }
            });
        }
        #endregion

        #region TextMeshPro相关
        public static void SetSelectColor(TextMeshProUGUI meshpro)
        {
            string str = meshpro.text;
            if (str.Contains("<color=#FF9A31>"))
                str = str.Replace("<color=#FF9A31>", "");
            if (str.Contains("</color>"))
                str = str.Replace("</color>", "");
            if (str.Contains("<color=#D84837>"))
                str = str.Replace("<color=#D84837>", "");
            meshpro.text = string.Format("<color=#FF9A31>{0}</color>", str);
        }
        public static void SetUnSelectColor(TextMeshProUGUI meshpro)
        {
            string str = meshpro.text;
            if (str.Contains("<color=#FF9A31>"))
                str = str.Replace("<color=#FF9A31>", "");
            if (str.Contains("</color>"))
                str = str.Replace("</color>", "");
            if (str.Contains("<color=#D84837>"))
                str = str.Replace("<color=#D84837>", "");
            meshpro.text = string.Format("<color=#D84837>{0}</color>", str);
        }
        #endregion

        #region 实体管理
        /// <summary>
        /// 创建实体返回实体ID
        /// </summary>
        /// <param name="path"></param>预制体路径
        /// <returns></returns>
        public static int GetCreateEntityViewID(string path)
        {
            EntityViewItem entityViewItem = GHelp.GetObjectItemEx<EntityViewItem>();
            entityViewItem.prefabPath = path;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, entityViewItem);
            return (int)entityViewItem.EntityViewID;
        }

        /// <summary>
        /// 创建实体返回实体ID
        /// </summary>
        /// <param name="path"></param>预制体路径
        /// <returns></returns>
        public static int GetCreateEntityViewID(string path, int SkinID, UnityAction<GameObject> unityEvent = null)
        {
            EntityViewItem entityViewItem = GHelp.GetObjectItemEx<EntityViewItem>();
            entityViewItem.prefabPath = path;
            entityViewItem.EntityType = (byte)EMEntityType.typeActor;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, entityViewItem);
            // 加载皮肤模型
            ChangeEntitiyPart(entityViewItem.EntityViewID, EntityParts.EntityPart_Body, SkinID, "", unityEvent);
            return (int)entityViewItem.EntityViewID;
        }

        public static void ChangeEntitiyPart(UInt32 nEntityView, EntityParts part, int resID, string targetID = "", UnityAction<GameObject> unityEvent = null)
        {
            ChangePartContextEx context = GHelp.GetObjectItem<ChangePartContextEx>();
            context.partID = part;
            context.resId = resID;
            context.strTargetID = targetID;
            context.unityEvent = unityEvent;
            GHelp.sendEntityCommand(nEntityView, (int)EntityLogicDef.ENTITY_TOVIEW_CHANGE_SKIN, 0, "", context);
        }

        /// <summary>
        /// 销毁实体
        /// </summary>
        /// <param name="EntityID"></param>
        public static void DestroyEntity(uint EntityID)
        {
            EntryView_Destroy entryView_Destroy = new EntryView_Destroy();
            entryView_Destroy.ENTITY_ID = EntityID;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, entryView_Destroy);
        }
        /// <summary>
        /// 实体同步位置
        /// </summary>
        /// <param name="EntityID"></param>
        /// <param name="V3pos"></param>
        public static void EntityToviewSyncPos(uint EntityID, Vector3 V3pos)
        {
            cmd_creature_rigidbody_sync pos = new cmd_creature_rigidbody_sync();
            pos.nEntityID = EntityID;
            pos.fPosition_x = V3pos.x;
            pos.fPosition_y = V3pos.y;
            pos.fPosition_z = V3pos.z;
            GHelp.sendEntityCommand(EntityID, (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", pos);
        }

        /// <summary>
        /// 发送获取实体事件
        /// </summary>
        /// <param name="EntityID"></param>
        public static void EntityObjEvent(int EntityID)
        {
            cmd_creature_game cmd_get_game = new cmd_creature_game();
            cmd_get_game.EntityId = EntityID;
            GHelp.sendEntityCommand((uint)EntityID, (int)EntityLogicDef.ENEITY_GAME, 0, "", cmd_get_game);
        }

        public static IEntity GetEntityByUID(Int64 UID)
        {
            if (UID == DGlobalGame.INVALID_UID)
            {
                return null;
            }

            return GlobalGame.Instance.EntityClient.Get(UID);
        }

        public static uint GetEntityViewIDByUID(long UID)
        {
            IEntity pEntity = GetEntityClient().Get(UID);
            if (pEntity == null)
            {
                return 0;
            }
            return pEntity.GetEntityViewID();
        }


        /** 取得主角
		@param   
		@param   
		@return  
		*/
        public static IPerson GetHero()
        {
            IEntityClient pEntityClient = GetEntityClient();
            if (pEntityClient == null)
            {
                return null;
            }

            return pEntityClient.GetHero();
        }

        public static EMtEntity_Class GetClassByEntityType(eEntityType t)
        {
            EMtEntity_Class c = EMtEntity_Class.tEntity_Class_Person;
            switch (t)
            {
                case eEntityType.Player:
                    c = EMtEntity_Class.tEntity_Class_Person;
                    break;
                case eEntityType.Monster:
                case eEntityType.Npc:
                    c = EMtEntity_Class.tEntity_Class_Monster;
                    break;
                case eEntityType.Goods:
                case eEntityType.TreasureBox:
                case eEntityType.GoldCoin:
                    c = EMtEntity_Class.tEntity_IsClass_Goods;
                    break;
                case eEntityType.Wall:
                case eEntityType.Pitfall:
                    c = EMtEntity_Class.tEntity_Class_Trap;
                    break;
                case eEntityType.Portal:
                    c = EMtEntity_Class.tEntity_Class_Mast;
                    break;
                case eEntityType.Robot:
                    c = EMtEntity_Class.tEntity_Class_Tank;
                    break;
            }
            return c;
        }

        public static int GetEntityNumProp(Int64 UID, uint dwPropID)
        {
            if (UID == DGlobalGame.INVALID_UID)
            {
                return 0;
            }

            IEntity pEntity = GlobalGame.Instance.EntityClient.Get(UID);
            if (pEntity == null)
            {
                return 0;
            }

            return pEntity.GetNumProp(dwPropID);
        }

        /// <summary>
        /// 找到全部路径从英雄
        /// </summary>
        /// <param name="targetPos"></param>
        /// <param name="entityGuid"></param>
        /// <returns></returns>
        public static List<Vector3> FindPathByHero(Vector3 targetPos, ref string entityGuid)
        {
            List<Vector3> paths = null;

            Vector3 srcPos = Vector3.zero;
            IPerson person = GHelp.GetHero();
            if (person != null)
            {
                srcPos = person.GetPosition();
                entityGuid = person.GetStrGUID();
                IPersonTankPart pTankPart = (IPersonTankPart)person.GetEntityPart((uint)EMENTITYPART.ENTITYPART_PERSON_TANK);
                if (pTankPart != null)
                {
                    if (pTankPart.IsOnTank())
                    {
                        srcPos = pTankPart.GetTank().GetPosition();
                        entityGuid = pTankPart.GetTank().GetStrGUID();
                    }
                }

                paths = GlobalGame.Instance.NavigationManager.FindPath(srcPos, targetPos);
            }

            return paths;
        }
        #endregion

        #region waitwindow

        public static void addWait(string msg)
        {
            TRACE.TraceLn(Api.NTR("打开Wait"));
            gamelogic_Waiting cmd = GetObjectItem<gamelogic_Waiting>();
            cmd.szTips = msg;
            cmd.wModel = WindowModel.WaitingWin;
            cmd.aCommandState = AsyncCommandState.CreateCommmand;
            FireExecute((ushort)ViewLogicDef.GVIEWCMD_WAITING_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd);
        }

        public static void HideWait()
        {
            TRACE.TraceLn(Api.NTR("关闭Wait"));
            cmd_AsyncBase cmd = GetObjectItem<cmd_AsyncBase>();
            cmd.wModel = WindowModel.WaitingWin;
            cmd.aCommandState = AsyncCommandState.UpdateCommand;
            FireExecute((ushort)ViewLogicDef.GVIEWCMD_WAITING_HIDE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd);
        }

        #endregion

        #region Tips
        /// <summary>
        /// 显示提示框
        /// </summary>
        /// <param name="pos">提示框类型</param>
        /// <param name="text">提示文字</param>
        public static void addSystemTips(EMInfoPos pos, string text)
        {
            addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, text, Color.white);
        }
        public static void addSystemTips(EMInfoPos pos, string text, Color curColor)
        {
            if (text == string.Empty)
            {
                return;
            }

            if (pos == 0 || (uint)pos >= (uint)EMInfoPos.MaxInfoPosCount)
            {
                return;
            }

            gamelogic_SAddSystemTips tips = GHelp.GetObjectItem<gamelogic_SAddSystemTips>();
            tips.text = text;
            tips.pos = (uint)pos;
            tips.color = curColor;
            //if (((uint)pos & (uint)EMInfoPos.InfoPos_ScreenCenterBottom) > 0)
            //{
            //    tips.wModel = WindowModel.TipsWindow;
            //}
            tips.wModel = WindowModel.TipsWindow;
            tips.aCommandState = AsyncCommandState.CreateCommmand;
            GHelp.GetEventEngine().FireExecute((ushort)ViewLogicDef.GVIEWCMD_ADD_SYSTEMTIPS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, tips);
        }

        public static void addSystemTips(EMInfoPos pos, string text, Color curColor, bool isSuccess)
        {
            if (text == string.Empty)
            {
                return;
            }

            if (pos == 0 || (uint)pos >= (uint)EMInfoPos.MaxInfoPosCount)
            {
                return;
            }

            gamelogic_SAddSystemTips tips = GHelp.GetObjectItem<gamelogic_SAddSystemTips>();
            tips.text = text;
            tips.pos = (uint)pos;
            tips.color = curColor;
            if (isSuccess)
            {
                tips.icon = EMFInfoIcon.Icon_True;
            }
            else
            {
                tips.icon = EMFInfoIcon.Icon_False;
            }

            //if (((uint)pos & (uint)EMInfoPos.InfoPos_ScreenCenterBottom) > 0)
            //{
            //    tips.wModel = WindowModel.TipsWindow;
            //}
            tips.wModel = WindowModel.TipsWindow;
            tips.aCommandState = AsyncCommandState.CreateCommmand;
            tips.parent3DUI = true;
            GHelp.GetEventEngine().FireExecute((ushort)ViewLogicDef.GVIEWCMD_ADD_SYSTEMTIPS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, tips);
        }

        /// <summary>
        /// 带图标的系统提醒,仅使用正中央已设定好的位置
        /// by 王康阳
        /// </summary>
        /// <param name="text">文案</param>
        /// <param name="icon">图标</param>
        public static void addSystemTipsWithIcon(string text, EMFInfoIcon icon = EMFInfoIcon.None)
        {
            if (text == string.Empty)
            {
                return;
            }

            gamelogic_SAddSystemTips tips = GHelp.GetObjectItem<gamelogic_SAddSystemTips>();
            if (tips != null)
            {
                tips.text = text;
                tips.pos = (uint)EMInfoPos.InfoPos_ScreenCenterGradual;
                tips.color = new Color(1, 1, 1, 1);
                tips.icon = icon;
                tips.wModel = WindowModel.TipsWindow;
                tips.aCommandState = AsyncCommandState.CreateCommmand;
                if (GHelp.GetEventEngine() != null)
                {
                    GHelp.GetEventEngine().FireExecute((ushort)ViewLogicDef.GVIEWCMD_ADD_SYSTEMTIPS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, tips);
                }
            }
        }

        #endregion

        /// <summary>
		/// 获取GameSDK的配置
		/// </summary>
		/// <returns></returns>
		public static EMGameSDKType GetConfigGameSDK()
        {
            EMGameSDKType gameSDKType;
            if (Application.platform == RuntimePlatform.Android)
            {
                gameSDKType = EMGameSDKType.JL_DEV;
            }
            else if (Application.platform == RuntimePlatform.IPhonePlayer)
            {
                gameSDKType = EMGameSDKType.JL_DEV;
            }
            else
            {
                gameSDKType = EMGameSDKType.JL_DEV;
            }

            return gameSDKType;
        }

        public static void SetUserInfoByLogin(string userid, string nickName)
        {

            GlobalGame.Instance.GameSDK.SetUserID(userid);
            GlobalGame.Instance.GameSDK.GetUserInfo().username = nickName;
        }

        /** 启用定时器
		@param   
		@param   
		@return  
		*/
        public static void SetTimer(Int32 timerID, Int32 interval, ITimerHandler handler, int callCount, string desc)
        {
            GlobalGame.Instance.TimerManager.AddTimer(handler, timerID, interval, callCount, desc);
        }

        /** 启用定时器
  @param   
  @param   
  @return  
  */
        public static bool SetTimer(Int32 timerID, Int32 interval, ITimerHandler handler, string desc)
        {
            GlobalGame.Instance.TimerManager.AddTimer(handler, timerID, interval, desc);
            return true;
        }

        /** 销毁定时器
		@param   
		@param   
		@return  
		*/
        public static void KillTimer(Int32 timerID, ITimerHandler handler)
        {
            GlobalGame.Instance.TimerManager.RemoveTimer(handler, timerID);
        }

        /** 销毁定时器
		@param   
		@param   
		@return  
		*/
        public static void KillAllTimer(ITimerHandler handler)
        {
            GlobalGame.Instance.TimerManager.RemoveTimer(handler);
        }

        public static string setCreatureTextWithDefaultStyle(string text, string fgColor)
        {
            string tmp = "FFFFFFFF";

            if (!string.IsNullOrEmpty(fgColor))
            {
                if (fgColor.Length == 1)
                {//等于1位代表是id
                    tmp = GetColorByID(int.Parse(fgColor));
                }
                else
                {//颜色值
                    tmp = fgColor;
                }
            }

            return string.Format("<label text='{0}' font='1' fgcolor='{1}' size='20'>", text, tmp);
        }

        public static string GetColorByID(int colorID)
        {
            string tmp = "FFFFFFFF";

            switch (colorID)
            {
                case 1://绿色
                    tmp = "00FF00FF";
                    break;
                case 2://蓝色
                    tmp = "0000FFFF";
                    break;
                case 3://紫色
                    tmp = "800080FF";
                    break;
                case 4://橙色
                    tmp = "FFA500FF";
                    break;
                case 5://橙红色
                    tmp = "FF4500FF";
                    break;
                case 6://深红色
                    tmp = "8B0000FF";
                    break;
                case 7://金色
                    tmp = "FFD700FF";
                    break;
            }

            return tmp;
        }

        /// <summary>  
        /// 对相机截图,返回图片Base64字符串 
        /// </summary>  
        /// <returns>The screenshot2.</returns>  
        /// <param name="camera">Camera.要被截屏的相机</param>  
        /// <param name="rect">Rect.截屏的区域</param>  
        public static string CaptureCameraToBase64(Camera camera, Rect rect)
        {
            Texture2D screenShot = CaptureCameraToTexture(camera, rect);
            // 最后将这些纹理数据，成一个png图片文件  
            byte[] bytes = screenShot.EncodeToPNG();
            string basestr = Convert.ToBase64String(bytes);
            return basestr;
        }

        /// <summary>
        /// 对相机截图,返回图片纹理
        /// </summary>
        /// <param name="camera">Camera.要被截屏的相机</param>
        /// <param name="rect">Rect.截屏的区域</param>
        /// <returns></returns>
        public static Texture2D CaptureCameraToTexture(Camera camera, Rect rect)
        {
            // 创建一个RenderTexture对象  
            RenderTexture rt = new RenderTexture((int)rect.width, (int)rect.height, 0);
            // 临时设置相关相机的targetTexture为rt, 并手动渲染相关相机  
            camera.targetTexture = rt;
            camera.Render();
            //ps: --- 如果这样加上第二个相机，可以实现只截图某几个指定的相机一起看到的图像。  
            //ps: camera2.targetTexture = rt;  
            //ps: camera2.Render();  
            //ps: -------------------------------------------------------------------  

            // 激活这个rt, 并从中中读取像素。  
            RenderTexture.active = rt;

            int width = (int)(rect.width) / 2;

            int height = (int)(rect.height) / 2;
            Texture2D screenShot = new Texture2D((int)rect.width / 2, (int)rect.height / 2, TextureFormat.RGB24, false);
            //左下角为原点（0, 0）
            float leftBtmX = rect.xMin;
            float leftBtmY = rect.yMin;
            screenShot.ReadPixels(new Rect((int)rect.width / 3, (int)rect.height / 4, (int)rect.width / 2, (int)rect.height / 2), 0, 0);// 注：这个时候，它是从RenderTexture.active中读取像素  
            screenShot.Apply();

            // 重置相关参数，以使用camera继续在屏幕上显示  
            camera.targetTexture = null;
            //ps: camera2.targetTexture = null;  
            RenderTexture.active = null; // JC: added to avoid errors  
            GameObject.Destroy(rt);
            return screenShot;
        }

        /// <summary>
        /// 保存图片纹理进IO中
        /// </summary>
        /// <param name="texture"></param>
        /// <returns></returns>
        public static string SaveTexture2D(Texture2D texture)
        {
            byte[] bytes = texture.EncodeToPNG();
            string fileName = GlobalGame.Instance.ControlManager.GetCaptureScreenController().GenCaptureName();
            //保存  
            try
            {
                File.WriteAllBytes(fileName, bytes);
            }
            catch (Exception e)
            {
                TRACE.ErrorLn(e.Message);
            }
            return fileName;
        }

        public static bool SaveTexture2D(Texture2D png, string save_file_name)
        {
            byte[] bytes = png.EncodeToPNG();
            string directory = Path.GetDirectoryName(save_file_name);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);
            using (FileStream file = File.Open(save_file_name, FileMode.Create))
            {
                using (BinaryWriter writer = new BinaryWriter(file))
                {
                    writer.Write(bytes);
                }
            }
            return true;
        }

        /// <summary>
        /// 计算模型的中心点 并且移动该物体(隐藏的物体不算)
        /// </summary>
        /// <param name="tt"></param>多物体的父物体
        /// <returns></returns>
        public static void GetCenter(Transform tt)
        {
            Transform parent = tt;
            Vector3 postion = parent.position;
            Quaternion rotation = parent.rotation;
            Vector3 scale = parent.localScale;
            parent.position = Vector3.zero;
            parent.rotation = Quaternion.Euler(Vector3.zero);
            parent.localScale = Vector3.one;


            Vector3 center = Vector3.zero;
            Renderer[] renders = parent.GetComponentsInChildren<Renderer>();
            int noActiveNum = 0;
            foreach (Renderer child in renders)
            {
                if (CheckParentsActive(child.transform))
                {
                    center += child.bounds.center;
                }
                else
                {
                    noActiveNum++;
                }
            }
            center /= (parent.GetComponentsInChildren<Transform>().Length - noActiveNum);
            Bounds bounds = new Bounds(center, Vector3.zero);
            foreach (Renderer child in renders)
            {
                bounds.Encapsulate(child.bounds);
            }

            parent.position = postion;
            parent.rotation = rotation;
            parent.localScale = scale;

            foreach (Transform t in parent)
            {
                t.position = t.position - bounds.center;
            }
            parent.transform.position = bounds.center + parent.position;
        }

        /// <summary>
        /// 一直往上检查 如果父物体是隐藏的 则返回false
        /// </summary>
        /// <param name="child"></param>
        /// <returns></returns>
        public static bool CheckParentsActive(Transform child)
        {
            bool isActive = true;
            if (child.parent != null)
            {
                if (child.parent.gameObject.activeSelf)
                {
                    isActive = CheckParentsActive(child.parent);
                }
            }
            return isActive;
        }

        // 获取当前是否为WIFI连接
        public static bool CheckWIFIConnect()
        {
            //当用户使用WiFi时    
            if (Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork)
            {
                return true;
            }

            return false;
        }

        #region 设置物体layer
        /// <summary>
        /// 功能性物体，比如射线检测物体、碰撞物体，这些在LOD子节点是不会出现，LOD父节点会出现
        /// </summary>
        public static string[] FunctionChildObjNames = new string[] { "ScreenCastLayer", "ColliderShape", "SkinDataObj" };
        public static void SetLayer(int layer, GameObject go, bool bIncludeChild = false, string[] IngoreObjName = null)
        {
            if (bIncludeChild)
            {
                Transform[] trs = go.GetComponentsInChildren<Transform>();
                foreach (Transform t in trs)
                {
                    if (IngoreObjName != null)
                    {
                        bool bNeedToChangeLayer = true;
                        foreach (string IngoreStr in FunctionChildObjNames)
                        {
                            if (t.gameObject.name.Equals(IngoreStr))
                            {
                                bNeedToChangeLayer = false;
                                break;
                            }
                        }
                        if (bNeedToChangeLayer)
                        {
                            t.gameObject.layer = layer;
                        }
                    }
                    else
                    {
                        t.gameObject.layer = layer;
                    }

                }
                trs = null;
            }
            else
            {
                go.layer = layer;
            }
        }
        #endregion

        #region 通過EntityType獲得path

        public static string GetModelPathByType(EMEntityType type, int entityID)
        {
            string path = "";
            Model.Types.Item item = GetModelItem(type, entityID);

            if (item != null)
            {
                path = item.PrefabPath;
            }
            path = path.Replace("\\", "/");

            return path;
        }

        public static IMessage GetConfigItem(EMEntityType type, int configID)
        {
            IMessage message = null;
            switch (type)
            {
                case EMEntityType.typeNpc:
                    {
                    }
                    break;
                case EMEntityType.typeMonster:
                    {
                        message = GlobalGame.Instance.SchemeCenter.GetMonsterInfo().GetMonsterInfoByID(configID);
                    }
                    break;
                case EMEntityType.typeGoods:
                    {
                    }
                    break;
                case EMEntityType.typePitfall:
                    {
                    }
                    break;
                case EMEntityType.typeTreasureBox:
                    {
                    }
                    break;
                case EMEntityType.typePortal:
                    {
                    }
                    break;
                case EMEntityType.typeWall:
                    {
                    }
                    break;
                case EMEntityType.typeGoldCoin:
                    {
                    }
                    break;
            }
            return message;
        }

        public static Model.Types.Item GetModelItem(EMEntityType type, int infoID)
        {
            int modelID = 0;
            switch (type)
            {
                case EMEntityType.typeNpc:
                    {
                    }
                    break;
                case EMEntityType.typeMonster:
                    {
                        Monster.Types.Item i = GlobalGame.Instance.SchemeCenter.GetMonsterInfo().GetMonsterInfoByID(infoID);
                        modelID = i.ModelId;
                    }
                    break;
                case EMEntityType.typeGoods:
                    {
                    }
                    break;
                case EMEntityType.typePitfall:
                    {
                    }
                    break;
                case EMEntityType.typeTreasureBox:
                    {
                    }
                    break;
                case EMEntityType.typePortal:
                    {
                    }
                    break;
                case EMEntityType.typeWall:
                    {
                    }
                    break;
                case EMEntityType.typeGoldCoin:
                    {
                    }
                    break;
                case EMEntityType.typeBullet:
                    {
                    }
                    break;
            }

            return GlobalGame.Instance.SchemeCenter.GetEntityInfo().GetEntityInfoByID(modelID);
        }

        #endregion

        public static PBVec3 StringToPBVec3(string strPBVec)
        {
            string[] s = strPBVec.Split(';');
            PBVec3 v = new PBVec3();
            if (s.Length >= 3)
            {
                v.X = float.Parse(s[0]);
                v.Y = float.Parse(s[1]);
                v.Z = float.Parse(s[2]);
            }
            return v;
        }

        public static Vector3 StringToVector3(string strVec)
        {
            string[] s = strVec.Split(';');
            Vector3 v = Vector3.zero;
            if (s.Length >= 3)
            {
                v.x = float.Parse(s[0]);
                v.y = float.Parse(s[1]);
                v.z = float.Parse(s[2]);
            }
            return v;
        }

        #region 属性同步逻辑

        public static void ConvertEntityPropInt(eEntityProp _prop, string s, ref int _value)
        {
            switch (_prop)
            {
                case eEntityProp.EEntitySex:
                case eEntityProp.EEntityCurHp:
                case eEntityProp.EEntityMaxHp:
                case eEntityProp.EEntityMoveSpeed:
                case eEntityProp.EEntityFaceId:
                case eEntityProp.EEntityLevel:
                case eEntityProp.EEntityGoldCoin:
                case eEntityProp.EEntityDiamond:
                case eEntityProp.EEntityAttack:
                case eEntityProp.EEntityDefense:
                case eEntityProp.EEntityViewRange:
                case eEntityProp.EEntityAngle:
                case eEntityProp.EEntityCamp:
                    {
                        if (_prop == eEntityProp.EEntityMoveSpeed)
                        {//服务器下发的速度是float值，所以在增加的时候附加速度因子
                            _value = (int)(float.Parse(s) * DGlobalGame.FLOAT_SCALE_SIZE);
                        }
                        else
                        {
                            _value = int.Parse(s);
                        }
                    }
                    break;
            }
        }

        #endregion

        /// <summary>
        /// int值用;隔开的字符串 返回int[]
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public static int[] StringSplitIntArray(string info)
        {
            string[] strArr = info.Split(';');
            int[] intArr = new int[strArr.Length];
            if (strArr.Length > 0)
            {
                for (int i = 0; i < strArr.Length; i++)
                {
                    intArr[i] = (int.Parse(strArr[i]));
                }
            }
            return intArr;
        }

        /// <summary>
        /// 包含;的字符串转化为Vector3
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public static Vector3 StringSplitVector3(string info)
        {
            string[] strArr = info.Split(';');
            int[] intArr = new int[strArr.Length];
            if (strArr.Length > 0)
            {
                for (int i = 0; i < strArr.Length; i++)
                {
                    intArr[i] = (int.Parse(strArr[i]));
                }
            }
            return new Vector3(intArr[0], intArr[1], intArr[2]);
        }

        /// <summary>
        ///string转V3 带括号的有负数的(-90;-90;-90)
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static Vector3 StringToVec3(string str)
        {
            str = str.Replace("(", "");
            str = str.Replace(")", "");
            string[] s = str.Split(';');
            Vector3 v = new Vector3();
            if (s.Length >= 3)
            {
                v.x = float.Parse(s[0]);
                v.y = float.Parse(s[1]);
                v.z = float.Parse(s[2]);
            }
            return v;
        }

        /// <summary>
        ///string转V2 带括号的有负数的(-90;-90)
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static Vector2 StringToVec2(string str)
        {
            str = str.Replace("(", "");
            str = str.Replace(")", "");
            string[] s = str.Split(';');
            Vector2 v = new Vector2();
            if (s.Length >= 2)
            {
                v.x = float.Parse(s[0]);
                v.y = float.Parse(s[1]);
            }
            return v;
        }

        /// <summary>
        /// 括号加下划线的转为V2List
        /// (361;350)_(1258;350)
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static List<Vector2> GetBracketsUnderLineV2(string text)
        {
            List<Vector2> V2List = new List<Vector2>();
            string[] posArray = text.Split('_');
            for (int i = 0; i < posArray.Length; i++)
            {
                V2List.Add(StringToVec2(posArray[i]));
            }
            return V2List;
        }

        /// <summary>
        /// 根据屏幕分辨率 计算模型缩放大小 默认为1920*1080
        /// </summary>
        public static float ScreenModelScale()
        {
            int screenWidth = Screen.width;
            int screenHeight = Screen.height;
            float m_minX = 0;
            float m_minY = 0;
            if (GetLayoutModel() == LayoutModel.AcrossModel)
            {
                m_minX = screenWidth / 1920f;
                m_minY = screenHeight / 1080f;
            }
            else
            {
                m_minX = screenWidth / 1080f;
                m_minY = screenHeight / 1920f;
            }

            if (m_minX <= m_minY)
            {
                return m_minX;
            }
            else
            {
                return m_minY;
            }
        }

        #region 碰撞检测计算
        /// <summary>
        /// 判断圆形和矩形是否相交 参考算法https://www.zhihu.com/question/24251545
        /// </summary>
        /// <param name="region"></param>
        /// <param name="pt"></param>
        /// <param name="fRadius"></param>
        /// <returns></returns>
        public static bool CircleIntersectRegion(Vector3[] region, Vector3 pt, float fRadius)
        {
            if (region == null || region.Length != 4)
                return false;

            // 计算圆心
            Vector2 vStart;
            vStart.x = region[0].x;
            vStart.y = region[0].z;

            Vector2 vEnd;
            vEnd.x = region[2].x;
            vEnd.y = region[2].z;

            Vector2 vDiagonal = vEnd - vStart;
            //对角线长度
            float fLength = vDiagonal.magnitude;
            //
            vDiagonal.Normalize();
            // 中心点坐标
            Vector2 vCenter = vStart + vDiagonal * fLength * 0.5f;

            // 第1步：转换至第1象限
            Vector2 vPt;
            vPt.x = pt.x;
            vPt.y = pt.z;
            // 求绝对值
            Vector2 vV = vPt - vCenter;
            vV.x = Mathf.Abs(vV.x);
            vV.y = Mathf.Abs(vV.y);

            // 计算第一象限的h点
            bool bFind = false;
            Vector2 vH = default(Vector2);
            for (int i = 0; i < region.Length; i++)
            {
                if (region[i].x - vCenter.x > 0.00001f && region[i].z - vCenter.y > 0.00001f)
                {
                    vH.x = region[i].x - vCenter.x;
                    vH.y = region[i].z - vCenter.y;
                    bFind = true;
                    break;
                }
            }

            if (bFind == false)
                return false;
            // 第2步：求圆心至矩形的最短距离矢量
            Vector2 vU = vV - vH;
            vU.x = Mathf.Max(vU.x, 0);
            vU.y = Mathf.Max(vU.y, 0);

            float fLen = vU.sqrMagnitude;

            // 第3步：长度平方与半径平方比较
            return fLen < fRadius * fRadius;
        }

        /// <summary>
		/// 判断点是否在多边形区域内，暂时不考虑y轴
		/// 注释：如果是每帧调用，会不会太耗性能，待优化
		/// </summary>
		/// <param name="region"></param>
		/// <param name="pt"></param>
		/// <returns></returns>
		public static bool PtInRegion(List<Vector3> region, Vector3 pt)
        {
            //TRACE.ErrorLn("call PtInRegion count=" + region.Count + ",pos="+pt);
            if (region == null || region.Count <= 2)
                return false;

            //判断边长是否和x轴和z轴平行,如果是的话可优化判断
            //配置规则是从左下角逆时针配置
            if (region.Count == 4)
            {
                if ((FloatIsEqual(region[0].z, region[1].z) && FloatIsEqual(region[2].z, region[3].z)
                    && FloatIsEqual(region[0].x, region[3].x) && FloatIsEqual(region[1].x, region[2].x))
                    || (FloatIsEqual(region[0].x, region[1].x) && FloatIsEqual(region[2].x, region[3].x)
                    && FloatIsEqual(region[0].z, region[3].z) && FloatIsEqual(region[1].z, region[2].z)))
                {

                    float fMinX = Mathf.Min(Mathf.Min(region[0].x, region[1].x), Mathf.Min(region[2].x, region[3].x));
                    float fMaxX = Mathf.Max(Mathf.Max(region[0].x, region[1].x), Mathf.Max(region[2].x, region[3].x));

                    float fMinZ = Mathf.Min(Mathf.Min(region[0].z, region[1].z), Mathf.Min(region[2].z, region[3].z));
                    float fMaxZ = Mathf.Max(Mathf.Max(region[0].z, region[1].z), Mathf.Max(region[2].z, region[3].z));


                    if (pt.x < fMinX || pt.x > fMaxX || pt.z < fMinZ || pt.z > fMaxZ)
                    {
                        //区域外
                        return false;
                    }
                    else
                    {
                        //区域内
                        return true;
                    }
                }
            }


            float fminx = 0.0f;
            float fminy = 0.0f;
            float fmaxx = 0.0f;
            float fmaxy = 0.0f;
            //计算AABB包围盒
            if (CalMaxRect(region, ref fminx, ref fminy, ref fmaxx, ref fmaxy))
            {
                //不在包围盒里面
                if (InRect(fminx, fminy, fmaxx, fmaxy, pt) == false)
                {
                    return false;
                }
            }


            bool ret = false;
            int nCount = region.Count;
            int i = 0;
            int j = nCount - 1;
            for (; i < nCount; j = i++)
            {
                if ((((region[i].z <= pt.z) && (pt.z < region[j].z)) ||
                    ((region[j].z <= pt.z) && (pt.z < region[i].z)))
                    && (pt.x < (region[j].x - region[i].x) * (pt.z - region[i].z) / (region[j].z - region[i].z) + region[i].x))
                {
                    ret = !ret;
                }
            }
            return ret;
        }

        /// <summary>
        /// 判断点是否在多边形区域内，暂时不考虑y轴
        /// 注释：如果是每帧调用，会不会太耗性能，待优化
        /// </summary>
        /// <param name="region"></param>
        /// <param name="pt"></param>
        /// <returns></returns>
        public static bool PtInRegion(Vector3[] region, Vector3 pt)
        {
            //TRACE.ErrorLn("call PtInRegion count=" + region.Count + ",pos="+pt);
            if (region == null || region.Length <= 2)
                return false;

            //判断边长是否和x轴和z轴平行,如果是的话可优化判断
            //配置规则是从左下角逆时针配置
            if (region.Length == 4)
            {
                if ((FloatIsEqual(region[0].z, region[1].z) && FloatIsEqual(region[2].z, region[3].z)
                    && FloatIsEqual(region[0].x, region[3].x) && FloatIsEqual(region[1].x, region[2].x))
                    || (FloatIsEqual(region[0].x, region[1].x) && FloatIsEqual(region[2].x, region[3].x)
                    && FloatIsEqual(region[0].z, region[3].z) && FloatIsEqual(region[1].z, region[2].z)))
                {

                    float fMinX = Mathf.Min(Mathf.Min(region[0].x, region[1].x), Mathf.Min(region[2].x, region[3].x));
                    float fMaxX = Mathf.Max(Mathf.Max(region[0].x, region[1].x), Mathf.Max(region[2].x, region[3].x));

                    float fMinZ = Mathf.Min(Mathf.Min(region[0].z, region[1].z), Mathf.Min(region[2].z, region[3].z));
                    float fMaxZ = Mathf.Max(Mathf.Max(region[0].z, region[1].z), Mathf.Max(region[2].z, region[3].z));


                    if (pt.x < fMinX || pt.x > fMaxX || pt.z < fMinZ || pt.z > fMaxZ)
                    {
                        //区域外
                        return false;
                    }
                    else
                    {
                        //区域内
                        return true;
                    }
                }
            }

            float fminx = 0.0f;
            float fminy = 0.0f;
            float fmaxx = 0.0f;
            float fmaxy = 0.0f;
            //计算AABB包围盒
            if (CalMaxRect(region, ref fminx, ref fminy, ref fmaxx, ref fmaxy))
            {
                //不在包围盒里面
                if (InRect(fminx, fminy, fmaxx, fmaxy, pt) == false)
                {
                    return false;
                }
            }

            bool ret = false;
            int nCount = region.Length;
            int i = 0;
            int j = nCount - 1;
            for (; i < nCount; j = i++)
            {
                if ((((region[i].z <= pt.z) && (pt.z < region[j].z)) ||
                    ((region[j].z <= pt.z) && (pt.z < region[i].z)))
                    && (pt.x < (region[j].x - region[i].x) * (pt.z - region[i].z) / (region[j].z - region[i].z) + region[i].x))
                {
                    ret = !ret;
                }
            }
            return ret;
        }

        // 判断两个float是否相等
        public static bool FloatIsEqual(float f1, float f2)
        {
            if (Math.Abs(f1 - f2) < 0.0001f)
            {
                return true;
            }
            return false;
        }

        /** 计算点集合的AABB包围盒
    @param   poly	:	多边形的点数组
    @param   nCount	:	多边形点的数量
    @param   minx	:	左下角X坐标（输出)
    @param   miny	:	左下角Y坐标（输出)
    @param   maxx	:	右上角X坐标（输出)
    @param   maxy	:	右上角Y坐标（输出)
    @return			:	计算成功返回true
    */
        public static bool CalMaxRect(List<Vector3> poly, ref float minx, ref float miny, ref float maxx, ref float maxy)
        {
            if (null == poly)
                return false;
            int nCount = poly.Count;
            minx = poly[0].x;
            miny = poly[0].z;
            maxx = poly[0].x;
            maxy = poly[0].z;
            for (int i = 1; i < nCount; i++)
            {
                if (minx > poly[i].x)
                    minx = poly[i].x;
                if (miny > poly[i].z)
                    miny = poly[i].z;
                if (maxx < poly[i].x)
                    maxx = poly[i].x;
                if (maxy < poly[i].z)
                    maxy = poly[i].z;
            }
            return true;
        }

        /** 计算点集合的AABB包围盒
		@param   poly	:	多边形的点数组
		@param   nCount	:	多边形点的数量
		@param   minx	:	左下角X坐标（输出)
		@param   miny	:	左下角Y坐标（输出)
		@param   maxx	:	右上角X坐标（输出)
		@param   maxy	:	右上角Y坐标（输出)
		@return			:	计算成功返回true
		*/
        public static bool CalMaxRect(Vector3[] poly, ref float minx, ref float miny, ref float maxx, ref float maxy)
        {
            if (null == poly)
                return false;
            int nCount = poly.Length;
            minx = poly[0].x;
            miny = poly[0].z;
            maxx = poly[0].x;
            maxy = poly[0].z;
            for (int i = 1; i < nCount; i++)
            {
                if (minx > poly[i].x)
                    minx = poly[i].x;
                if (miny > poly[i].z)
                    miny = poly[i].z;
                if (maxx < poly[i].x)
                    maxx = poly[i].x;
                if (maxy < poly[i].z)
                    maxy = poly[i].z;
            }
            return true;
        }

        /** 判断点是否在标准矩阵内  
		@param   minx	:	左下角X坐标
		@param   miny	:	左下角Y坐标
		@param   maxx	:	右上角X坐标
		@param   maxy	:	右上角Y坐标
		@param   p		:	点坐标
		@return			:	在矩形区域内返回true
		*/
        public static bool InRect(float minx, float miny, float maxx, float maxy, Vector3 p)
        {
            if (p.x >= minx && p.x <= maxx && p.z >= miny && p.z <= maxy)
                return true;
            return false;
        }

        // 判断点P(x, y)与有向直线P1P2的关系. 小于0表示点在直线左侧，等于0表示点在直线上，大于0表示点在直线右侧  
        // 参考算法 http://blog.csdn.net/zaffix/article/details/25005057
        public static float EvaluatePointToLine(float x, float y, float x1, float y1, float x2, float y2)
        {
            float a = y2 - y1;
            float b = x1 - x2;
            float c = x2 * y1 - x1 * y2;
            return a * x + b * y + c;
        }

        // 圆与线段碰撞检测   参考算法 http://blog.csdn.net/zaffix/article/details/25160505
        // 圆心p(x, y), 半径r, 线段两端点p1(x1, y1)和p2(x2, y2)  
        public static bool IsCircleIntersectLineSeg(float x, float y, float r, float x1, float y1, float x2, float y2)
        {
            float vx1 = x - x1;
            float vy1 = y - y1;
            float vx2 = x2 - x1;
            float vy2 = y2 - y1;


            // len = v2.length()  
            float len = Mathf.Sqrt(vx2 * vx2 + vy2 * vy2);

            // v2.normalize()  
            vx2 /= len;
            vy2 /= len;

            // u = v1.dot(v2)  
            // u is the vector projection length of vector v1 onto vector v2.  
            float u = vx1 * vx2 + vy1 * vy2;

            // determine the nearest point on the lineseg  
            float x0 = 0.0f;
            float y0 = 0.0f;
            if (u <= 0)
            {
                // p is on the left of p1, so p1 is the nearest point on lineseg  
                x0 = x1;
                y0 = y1;
            }
            else if (u >= len)
            {
                // p is on the right of p2, so p2 is the nearest point on lineseg  
                x0 = x2;
                y0 = y2;
            }
            else
            {
                // p0 = p1 + v2 * u  
                // note that v2 is already normalized.  
                x0 = x1 + vx2 * u;
                y0 = y1 + vy2 * u;
            }

            return (x - x0) * (x - x0) + (y - y0) * (y - y0) <= r * r;
        }

        #endregion

        #region 技能上下文解析
        /// <summary>
        /// 解析外观数据
        /// </summary>
        /// <param name="pszMsg">消息体</param>
        /// <param name="formData">外观数据</param>
        public static bool PopSkillContext(CPacketRecv pszMsg, ref SkillContext skillContext)
        {
            if (pszMsg == null)
                return false;
            pszMsg.Pop(out skillContext.uidOperator); 			/// 使用技能的操作者（不一定是发起者，如载具上面的人），如果填0，表示操作者为技能部件拥有者 实体序号
			pszMsg.Pop(out skillContext.uidTarget);				/// 使用技能时鼠标点击的位置上的实体对象 实体序号
			pszMsg.Pop(out skillContext.fPosX);					/// 使用技能的目标位置X
			pszMsg.Pop(out skillContext.fPosY);					/// 使用技能的目标位置Y
			pszMsg.Pop(out skillContext.fPosZ);					/// 使用技能的目标位置Z
			pszMsg.Pop(out skillContext.fForwardX);				/// 使用技能的朝向X
			pszMsg.Pop(out skillContext.fForwardY);				/// 使用技能的朝向Y
			pszMsg.Pop(out skillContext.fForwardZ);				/// 使用技能的朝向Z
			pszMsg.Pop(out skillContext.fAngle);				/// 使用技能的角度
			pszMsg.Pop(out skillContext.skillid);				/// 技能大类ID
			pszMsg.Pop(out skillContext.skillsubid);			/// 技能子ID
			pszMsg.Pop(out skillContext.result);				/// 技能结果(SkillResult)
			pszMsg.Pop(out skillContext.hitTicks);              /// 击中时间（由客户端技能系统发起者自行计算）
            pszMsg.Pop(out skillContext.pre_count);				/// 预选目标数(用于像闪电链之类的技能)
			pszMsg.Pop(out skillContext.viewid);				/// 视图ID(客户端自行随机确定)
			pszMsg.Pop(out skillContext.tick);					/// 客户端tick
			pszMsg.Pop(out skillContext.arrived_tick);			/// 到达服务器时的tick
			pszMsg.Pop(out skillContext.breakFlag);				/// 当前技能的打断标志
			pszMsg.Pop(out skillContext.dwZoneID);              /// 场景ID

            //检测选中目标列表
            skillContext.listHitEntity = new List<SSkillHitEntity>();
            for (int i = 0; i < SkillCommon.SKILL_HIT_MAX_COUNT; i++)
            {
                SSkillHitEntity hitEntity = default(SSkillHitEntity);
                pszMsg.Pop<SSkillHitEntity>(ref hitEntity);
                if (hitEntity.dwEntityID == 0)
                {
                    break;
                }
                skillContext.listHitEntity.Add(hitEntity);
            }

            return true;
        }

        /// <summary>
        /// 解析外观数据
        /// </summary>
        /// <param name="pszMsg">消息体</param>
        /// <param name="formData">外观数据</param>
        public static bool PushSkillContext(CPacketSend pszMsg, ref SkillContext skillContext)
        {
            if (pszMsg == null)
                return false;
            pszMsg.Push(skillContext.uidOperator); 			/// 使用技能的操作者（不一定是发起者，如载具上面的人），如果填0，表示操作者为技能部件拥有者 实体序号
			pszMsg.Push(skillContext.uidTarget);				/// 使用技能时鼠标点击的位置上的实体对象 实体序号
			pszMsg.Push(skillContext.fPosX);					/// 使用技能的目标位置X
			pszMsg.Push(skillContext.fPosY);					/// 使用技能的目标位置Y
			pszMsg.Push(skillContext.fPosZ);					/// 使用技能的目标位置Z
			pszMsg.Push(skillContext.fForwardX);				/// 使用技能的朝向X
			pszMsg.Push(skillContext.fForwardY);				/// 使用技能的朝向Y
			pszMsg.Push(skillContext.fForwardZ);				/// 使用技能的朝向Z
			pszMsg.Push(skillContext.fAngle);				/// 使用技能的角度
			pszMsg.Push(skillContext.skillid);				/// 技能大类ID
			pszMsg.Push(skillContext.skillsubid);			/// 技能子ID
			pszMsg.Push(skillContext.result);				/// 技能结果(SkillResult)
			pszMsg.Push(skillContext.hitTicks);             /// 击中时间（由客户端技能系统发起者自行计算）
            pszMsg.Push(skillContext.pre_count);				/// 预选目标数(用于像闪电链之类的技能)
			pszMsg.Push(skillContext.viewid);				/// 视图ID(客户端自行随机确定)
			pszMsg.Push(skillContext.tick);					/// 客户端tick
			pszMsg.Push(skillContext.arrived_tick);			/// 到达服务器时的tick
			pszMsg.Push(skillContext.breakFlag);			/// 当前技能的打断标志
			pszMsg.Push(skillContext.dwZoneID);             /// 场景ID

            // 压入目标列表
            int nHitCount = 0;
            if (skillContext.listHitEntity != null)
            {
                nHitCount = skillContext.listHitEntity.Count;
                for (int i = 0; i < nHitCount; i++)
                {
                    pszMsg.Push<SSkillHitEntity>(skillContext.listHitEntity[i]);
                }
            }

            int nLeftCount = SkillCommon.SKILL_HIT_MAX_COUNT - nHitCount;
            for (int i = 0; i < nLeftCount; i++)
            {
                SSkillHitEntity hitEntity = default(SSkillHitEntity);
                pszMsg.Push<SSkillHitEntity>(hitEntity);
            }

            return true;
        }

        public static bool PushUserSkillPB(CPacketSend pszMsg, Skill_UseSkill skillContext)
        {

            pszMsg.PushPB<Skill_UseSkill>(skillContext);

            return true;
        }
        #endregion

        #region BoxMessage
        private static Dictionary<string, Action<string, CLuaParam>> MSGCMD = new Dictionary<string, Action<string, CLuaParam>>();
        public static void OpenMessageBox(string msg, int nButtonID, bool isHideClose = false, bool isForceShowCloseBtn = false)
        {
            gamelogic_MessageBox cmd = GetObjectItem<gamelogic_MessageBox>();
            cmd.wModel = WindowModel.MessageBoxWindow;
            cmd.aCommandState = AsyncCommandState.CreateCommmand;
            cmd.message = msg;
            cmd.isHideClose = isHideClose;
            cmd.id = nButtonID;
            cmd.isForceShowCloseBtn = isForceShowCloseBtn;
            FireExecute((ushort)ViewLogicDef.GVIEWCMD_MESSAGEBOX_OPEN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="showMessageBoxData"></param>
        /// <param name="isHideClose"></param>
        /// <param name="isForceShowCloseBtn"></param>不管横竖屏 强制显示关闭按钮
        public static void InformShowMessageBox(ShowMessageBoxData showMessageBoxData, bool isHideClose = false, bool isForceShowCloseBtn = false)
        {
            string MsgName = Api.NTR(showMessageBoxData.title);
            string MsgText = Api.NTR(showMessageBoxData.content);
            // 模块 执行 按钮ID

            string strButtenTemp = string.Empty;
            if (showMessageBoxData.dicTemp != null)
            {
                foreach (KeyValuePair<int, string> keyValuePair in showMessageBoxData.dicTemp)
                {
                    strButtenTemp += "<a href='client::CommonMsgBox({0},{1}," + keyValuePair.Key + ")'>" + Api.NTR(keyValuePair.Value) + "</a>";
                }
            }
            string strTemp = strButtenTemp;
            string MsgButtons = string.Format(strTemp, (int)showMessageBoxData.msgBoxModule, (int)showMessageBoxData.msgBoxAction);

            string msg = MsgText + ';' + MsgButtons + ';' + MsgName;
            int nButtonID = (int)showMessageBoxData.messageBoxID;
            OpenMessageBox(msg, nButtonID, isHideClose, isForceShowCloseBtn);
        }
        /// <summary>
        /// 通用确定提示框，只提示内容，用户点击按钮以后进行隐藏，不做任何操作。
        /// </summary>
        /// <param name="title">提示标题</param>
        /// <param name="showMessage">提示内容</param>
        /// <param name="btnName">按钮名称</param>
        public static void InformMessageConfirm(string title, string showMessage, string btnName)
        {
            ShowMessageBoxData showMessageBoxData = new ShowMessageBoxData();
            showMessageBoxData.messageBoxID = MessageBoxID.COMMON;
            showMessageBoxData.msgBoxModule = MsgBoxModule.GENERAL;
            showMessageBoxData.msgBoxAction = MsgBoxAction.MESSAGE_CONFIRM;
            showMessageBoxData.title = title;
            showMessageBoxData.content = showMessage;
            showMessageBoxData.dicTemp = new Dictionary<int, string>();
            showMessageBoxData.dicTemp.Add(0, btnName);
            GHelp.InformShowMessageBox(showMessageBoxData);
        }
        public static void ItemClick(string info)
        {
            int comIndex = info.IndexOf("(");
            // 获取方法名
            string comName = info.Remove(comIndex, info.Length - comIndex);
            CLuaParam tempParam = new CLuaParam();
            List<string> temp = MidStrEx(info, "(", ")");
            string str;
            if (temp.Count > 0)
            {
                str = temp[0];
            }
            else
            {
                return;
            }
            string[] sArray = Regex.Split(str, ",", RegexOptions.IgnoreCase);
            foreach (string i in sArray)
            {
                tempParam.Push(i);
            }
            MSGCMD[comName].Invoke("textName", tempParam);
        }

        // 提取字符
        public static List<string> MidStrEx(string sourse, string startstr, string endstr)
        {
            List<string> temp = new List<string>();
            string orMsg = sourse;
            while (orMsg.Length > 0)
            {
                string result = string.Empty;
                int startindex, endindex;
                try
                {
                    startindex = orMsg.IndexOf(startstr);
                    if (startindex == -1)
                        break;
                    string tmpstr = orMsg.Substring(startindex + startstr.Length);
                    endindex = tmpstr.IndexOf(endstr);
                    if (endindex == -1)
                        break;
                    result = tmpstr.Remove(endindex);

                    orMsg = tmpstr.Remove(0, endstr.Length);
                }
                catch (Exception ex)
                {
                    Debug.Log("MidStrEx Err:" + ex.Message);
                    break;
                }

                // 间隔不能是0
                if (endindex < 1)
                {
                    continue;
                }
                temp.Add(result);
            }
            return temp;
        }

        /// <summary>
        /// 过滤绝大部分Emoji字符
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        public static string FilterEmoji(string content)
        {
            // 正则表达式匹配 笑脸Emoji 
            //string pattern = @"\uD83D[\uDE00-\uDE4F]";
            // 覆盖 99% 的 Emoji 符号（包含表情、手势、物体、旗帜等）
            // 测试使用①替换匹配到的符号
            return Regex.Replace(content, EmojiPattern, "");
        }

        public static void RegisterLuaCmd(string name, Action<string, CLuaParam> key, string text)
        {
            //MSGCMD.Add(name, key);
        }

        /// <summary>
        /// light不能直接等于主light 这会导致主light变了 我们新建的light也改变了 所以只能参数赋值
        /// </summary>
        /// <param name="light"></param>
        /// <returns></returns>
        public static Light GetLight(Light light, Light newlight)
        {
            newlight.color = light.color;
            newlight.type = light.type;
            newlight.intensity = light.intensity;
            newlight.cullingMask = light.cullingMask;

            //m_mainLight.lightmapBakeType = sceneLight.lightmapBakeType;
            newlight.transform.localPosition = light.transform.localPosition;
            newlight.transform.localEulerAngles = light.transform.localEulerAngles;
            // sceneLight.gameObject.SetActive(false);

            newlight.shadows = light.shadows;
            if (light.shadows == LightShadows.Soft || light.shadows == LightShadows.Hard)
            {
                newlight.shadowStrength = light.shadowStrength;
                newlight.shadowBias = light.shadowBias;
                newlight.shadowNormalBias = light.shadowNormalBias;
                newlight.shadowNearPlane = light.shadowNearPlane;
                newlight.shadowResolution = light.shadowResolution;
            }
            return newlight;
        }

        /// <summary>
        /// 生成唯一ID
        /// </summary>
        /// <returns></returns>
        public static string GenerateStringID()
        {
            long i = 1;
            foreach (byte b in Guid.NewGuid().ToByteArray())
            {
                i *= ((int)b + 1);
            }
            return string.Format("{0:x}", i - DateTime.Now.Ticks);
        }

        #endregion

        /// <summary>
        /// 时间秒数转化为00:00 超过1小时转化为00:00:00
        /// </summary>
        /// <param name="timeValue"></param>单位：秒
        /// <returns></returns>
        public static string GetTimeStringBySecond(float timeValue, bool isMs = true)
        {
            if (isMs)
            {
                timeValue /= 1000;
            }
            TimeSpan ts = new TimeSpan(0, 0, Convert.ToInt32(timeValue));
            string str = "";
            if (ts.Hours > 0)
            {
                str = ts.Hours.ToString("00") + ":" + ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00");
            }
            if (ts.Hours == 0 && ts.Minutes > 0)
            {
                str = ts.Minutes.ToString("00") + ":" + ts.Seconds.ToString("00");
            }
            if (ts.Hours == 0 && ts.Minutes == 0)
            {
                str = "00:" + ts.Seconds.ToString("00");
            }
            return str;
        }
        #region ToLua
        /** 取得Lua模块
		@param   
		@param   
		@return  
		*/
        public static ILuaManager GetLuaManager()
        {
            return GlobalGame.Instance.LuaManager;
        }
        /// <summary>
        /// 往La层发送相应的数据
        /// </summary>
        public static void ToLaDataByFunction(string func, params object[] data)
        {
            //GlobalToLa.Instance.LaDataByFunction(func, data);
        }


        /// <summary>
        /// 往La层发送相应的数据
        /// </summary>
        public static void LaDoString(string str)
        {
            //GlobalToLa.Instance.LaDoString(str);
        }
        #endregion


        /// <summary>
        /// 屏幕上2个点的夹角是否在angel范围内
        /// </summary>
        /// <param name="after"></param>后面的点
        /// <param name="before"></param>前面的点
        /// <param name="angel"></param>
        ///  <returns></returns>
        public static bool IsScreenAngel(Vector2 after, Vector2 before, float angel)
        {
            float tempAngel = (float)(Mathf.Atan((after.y - before.y) / (after.x - before.x)) * (180 / Math.PI));
            //第一、二、三、四象限的角度
            if ((tempAngel < angel && tempAngel >= 0) || (tempAngel > -angel && tempAngel <= 0) || (tempAngel > 180 - angel && tempAngel < 180)
                || (tempAngel > 180 && tempAngel < 180 + angel))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取小于360的角度
        /// </summary>
        /// <param name="angle"></param>
        /// <returns></returns>
        public static float GetLess360Angel(float angle)
        {
            if (angle > 360)
            {
                angle = angle - 360;
                GetLess360Angel(angle);
            }
            return angle;
        }

        /// <summary>
        /// 获取当前时间戳
        /// 目前本地 之后改成服务器时间
        /// </summary>
        /// <returns></returns>
        public static long GetCurTimeStamp()
        {
            return (DateTime.Now.ToLocalTime().Ticks - 621355968000000000) / 10000;
        }


        /// <summary>
        /// 通过时间戳获取时间int数据 年 月 日 时 分 秒
        /// </summary>
        /// <returns></returns>
        /// <param name="timeStamp"></param>时间戳
        /// <param name="isMS"></param>单位是否是ms
        /// <returns></returns>
        public static TimeIntData GetTimeIntDataByTimeStamp(long timeStamp, bool isMS = true)
        {
            if (isMS)
            {
                timeStamp = timeStamp / 1000;
            }
            TimeIntData intTimeArray = new TimeIntData();
            //1970.1.1时间戳
            DateTime startTime = new DateTime(1970, 1, 1);
            DateTime dt = startTime.AddSeconds(timeStamp);
            intTimeArray.year = dt.Year;
            intTimeArray.month = dt.Month;
            intTimeArray.day = dt.Day;
            intTimeArray.hour = dt.Hour;
            intTimeArray.minute = dt.Minute;
            intTimeArray.second = dt.Second;
            intTimeArray.weekStr = CaculateWeekDay(dt.DayOfWeek);
            return intTimeArray;
        }


        public static string CaculateWeekDay(DayOfWeek dayOfWeek)
        {
            string weekstr = "";
            switch (dayOfWeek)
            {
                case DayOfWeek.Monday: weekstr = "周一"; break;
                case DayOfWeek.Tuesday: weekstr = "周二"; break;
                case DayOfWeek.Wednesday: weekstr = "周三"; break;
                case DayOfWeek.Thursday: weekstr = "周四"; break;
                case DayOfWeek.Friday: weekstr = "周五"; break;
                case DayOfWeek.Saturday: weekstr = "周六"; break;
                case DayOfWeek.Sunday: weekstr = "周日"; break;
            }
            return weekstr;
        }

        public static string CaculateWeekDay(int dayOfWeek)
        {
            string weekstr = "";
            switch (dayOfWeek)
            {
                case 1: weekstr = "周一"; break;
                case 2: weekstr = "周二"; break;
                case 3: weekstr = "周三"; break;
                case 4: weekstr = "周四"; break;
                case 5: weekstr = "周五"; break;
                case 6: weekstr = "周六"; break;
                case 7: weekstr = "周日"; break;
            }
            return weekstr;
        }

        /// <summary>
        /// 通过分;秒;毫秒 的字符串 转为为float 毫秒
        /// </summary>
        /// <param name="strTime"></param>
        /// <returns></returns>
        public static float GetTimeMsByString(string strTime)
        {
            string[] strTimeArray = strTime.Split(';');
            if (strTimeArray.Length != 3)
            {
                TRACE.ErrorLn("时间填写错误");
            }
            float minute = float.Parse(strTimeArray[0]);
            float second = float.Parse(strTimeArray[1]);
            float ms = float.Parse(strTimeArray[2]);
            return minute * 60000 + second * 1000 + ms;
        }

        /// <summary>
        /// 获取字符串keyWord在字符text所出现的次数
        /// </summary>
        /// <param name="text"></param>
        /// <param name="keyWord"></param>
        /// <returns></returns>
        public static int GetKeyInTextNum(string text, string keyWord)
        {
            int index = 0;
            int count = 0;
            while ((index = text.IndexOf(keyWord, index)) != -1)
            {
                count++;
                index = index + keyWord.Length;
            }
            return count;
        }
        #region HtmlCheck

        public static void HtmlItemClick(string linkInfo)
        {
            //client::moveto(1,1,1)
            if (string.IsNullOrEmpty(linkInfo))
            {
                return;
            }

            // 多个函数以';'号分隔
            List<string> linkList = new List<string>();
            Api.split(ref linkList, linkInfo, ";");

            for (int i = 0; i < linkList.Count; i++)
            {
                string link = linkList[i];

                int pos = link.IndexOf(":");
                if (pos < 0)
                {
                    GlobalGame.Instance.LuaManager.ClientRunScript(linkInfo);
                }
                else
                {
                    // 如果是HTTP协议则
                    if (link.IndexOf("http://") > -1 || link.IndexOf("https://") > -1)
                    {
                        GlobalGame.Instance.LuaManager.ClientRunScript(link);
                    }
                    else
                    {
                        string proc = link.Substring(pos + 2);
                        GlobalGame.Instance.LuaManager.ClientRunScript(proc);
                    }
                }
            }
        }

        /// <summary>
        /// 产生多个发送到后台的边界逻辑
        /// </summary>
        /// <param name="picByte"></param>
        /// <param name="jsonEntity"></param>
        /// <param name="dataName1">avatar头像 | pic意见反馈</param>
        /// <param name="dataName2">profile头像 | newFeed意见反馈</param>
        /// <param name="imgType">图片类型</param>
        /// <param name="imgName">保存图片</param>
        /// <returns></returns>
        public static byte[] GenHttpBoundary(byte[] picByte, string jsonEntity, ref string boundary, string dataName1, string dataName2, string imgType = "png", string imgName = "")
        {
            var nowTick = System.DateTime.Now.Ticks.ToString("x");
            boundary = "------------------------" + nowTick;
            StringBuilder postData = new StringBuilder();
            postData.Append("--" + boundary + "\r\n");
            string picName = string.IsNullOrEmpty(imgName) ? nowTick : imgName;
            postData.Append(string.Format("Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}.{2}\"\r\n", dataName1, picName, imgType));// avatar头像,pic意见反馈
            postData.Append(string.Format("Content-Type: image/{0}\r\n\r\n", imgType));
            postData.Append(string.Format("data:image/{0};base64,{1}", imgType, System.Convert.ToBase64String(picByte)));
            postData.Append("\r\n");

            postData.Append("--" + boundary + "\r\n");
            postData.Append(string.Format("Content-Disposition: form-data; name=\"{0}\"; filename=\"blob\"\r\n", dataName2)); // profile,newFeed
            postData.Append("Content-Type: application/json\r\n\r\n");
            postData.Append(jsonEntity);
            postData.Append("\r\n");

            postData.Append("--" + boundary + "--");

            byte[] postDataBytes = Encoding.UTF8.GetBytes(postData.ToString());

            return postDataBytes;
        }

        public static byte[] GenHttpBoundaryNoImg(byte[] picByte, string jsonEntity, ref string boundary, string dataName1, string dataName2, string imgType = "png", string imgName = "")
        {
            var nowTick = System.DateTime.Now.Ticks.ToString("x");
            boundary = "------------------------" + nowTick;
            StringBuilder postData = new StringBuilder();
            postData.Append("--" + boundary + "\r\n");
            //string picName = string.IsNullOrEmpty(imgName) ? nowTick : imgName;
            //postData.Append(string.Format("Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}.{2}\"\r\n", dataName1, picName, imgType));// avatar头像,pic意见反馈
            //postData.Append(string.Format("Content-Type: image/{0}\r\n\r\n", imgType));
            //postData.Append(string.Format("data:image/{0};base64,{1}", imgType, System.Convert.ToBase64String(picByte)));
            //postData.Append("\r\n");

            postData.Append("--" + boundary + "\r\n");
            postData.Append(string.Format("Content-Disposition: form-data; name=\"{0}\"; filename=\"blob\"\r\n", dataName2)); // profile,newFeed
            postData.Append("Content-Type: application/json\r\n\r\n");
            postData.Append(jsonEntity);
            postData.Append("\r\n");

            postData.Append("--" + boundary + "--");

            byte[] postDataBytes = Encoding.UTF8.GetBytes(postData.ToString());

            return postDataBytes;
        }
        #endregion

        #region Android
        public static void SetBuglyUserID(string userID)
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                using (AndroidJavaClass ajc = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
                {
                    using (AndroidJavaObject ajo = ajc.GetStatic<AndroidJavaObject>("currentActivity"))
                    {
                        ajo.Call("SetBuglyUserID", userID);
                    }
                }
            }
            catch (Exception e)
            { }
#endif
        }


        public static void HideSplash()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                using (AndroidJavaClass ajc = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
                {
                    using (AndroidJavaObject ajo = ajc.GetStatic<AndroidJavaObject>("currentActivity"))
                    {
                        ajo.Call("onHideSplash");
                    }
                }
            }
            catch (Exception e)
            { }
#endif
        }
        #endregion

        public static Vector3 CVector(PBVec3 pb)
        {
            return new Vector3(pb.X, pb.Y, pb.Z);
        }

        /// <summary>
        /// 2个RectTransform重叠面积计算
        /// </summary>
        /// <param name="r1"></param>
        /// <param name="r2"></param>
        /// <returns></returns>
        public static float RectangleArea(Rect r1, Rect r2)
        {
            float ex = Math.Max(r1.xMax, r2.xMax); ///右上角x坐标
            float cx = Math.Min(r1.xMin, r2.xMin); ///左下角的x坐标
            float rW = ex - cx;//重叠后的边长
            float or1W = r1.xMax - r1.xMin;  //原r1边长
            float or2W = r2.xMax - r2.xMin;  //原r2边长
            float xArea = (Mathf.Abs(or1W) + Mathf.Abs(or2W)) - Mathf.Abs(rW);
            float ey = Math.Max(r1.yMax, r2.yMax);///右上角y坐标
            float cy = Math.Min(r1.yMin, r2.yMin); ///左下角y坐标
            float rH = ey - cy;//重叠后的边长
            float or1H = r1.yMax - r1.yMin;  //原r1边长
            float or2H = r2.yMax - r2.yMin;  //原r2边长
            float yArea = (Mathf.Abs(or1H) + Mathf.Abs(or2H)) - Mathf.Abs(rH);

            return (xArea * yArea);
        }

        /// <summary>
        /// m中随机取n
        /// </summary>
        /// <param name="m"></param>
        /// <param name="n"></param>
        /// <returns></returns>
        public static int[] RandomNum(int m, int n)
        {
            int[] result = new int[n]; //--随机取n;
            int[] index = new int[m];
            for (int i = 0; i < m; i++)
            {
                index[i] = i;
            };
            int size = m;
            int id;
            for (int j = 0; j < n; j++)
            {
                id = UnityEngine.Random.Range(0, size - 1);
                result[j] = index[id];
                index[id] = index[size - 1];
                size--;
            }
            return result;
        }

        /// <summary>
        /// json字符串转Dictionary
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public static Dictionary<string, object> GetValue(string json)
        {
            //解析json对象
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            return (Dictionary<string, object>)serializer.DeserializeObject(json);
        }
        /// <summary>
        /// 获取两个时间的间隔
        /// </summary>
        /// <param name="stratDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <param name="iType">获取类型，1表示秒，2：分，3：小时,4：天,5:毫秒</param>
        /// <returns></returns>
        public static int GetTwoDateTimeInterval(DateTime stratDateTime, DateTime endDateTime, int iType)
        {
            int iInterval = 0;
            if (stratDateTime.Ticks > 0)
            {
                TimeSpan StartTimeSpan = new TimeSpan(stratDateTime.Ticks);
                TimeSpan EndTimeSpan = new TimeSpan(endDateTime.Ticks);
                TimeSpan timeSpan = StartTimeSpan.Subtract(EndTimeSpan).Duration();

                switch (iType)
                {
                    case 1:
                        iInterval = Convert.ToInt32(timeSpan.TotalSeconds);
                        break;
                    case 2:
                        iInterval = Convert.ToInt32(timeSpan.TotalMinutes);
                        break;
                    case 3:
                        iInterval = Convert.ToInt32(timeSpan.TotalHours);
                        break;
                    case 4:
                        iInterval = Convert.ToInt32(timeSpan.TotalDays);
                        break;
                    case 5:
                        iInterval = Convert.ToInt32(timeSpan.TotalMilliseconds);
                        break;
                }
            }
            return iInterval;
        }

        /// <summary>
        /// 加载项目外的图片，包括网络图片，返回Texture2D
        /// </summary>
        /// <param name="path"></param>
        /// <param name="call"></param>
        public static void LoadFileTexture(string path, Action<Texture2D> call)
        {
            CUnityWebLoadTexture.Instance.LoadFileTexture(path, call);
        }

        /// <summary>
        /// 有的地方调用不到 Texture2D.LoadImage()，在这里中转一下
        /// </summary>
        /// <returns></returns>
        public static Texture2D ImageConversionLoadImage(Texture2D tex, byte[] data)
        {
            tex.LoadImage(data);
            return tex;
        }

        /// <summary>
        /// 将目标Texture转为其他格式Texture
        /// </summary>
        /// <param name="tex">需要转换的Texture</param>
        /// <param name="format">目标格式</param>
        /// <param name="bMipmap">是否生成 Mipmaps</param>
        /// <returns></returns>
        public static Texture2D ConvertToTargetFormatTexture(Texture2D tex, TextureFormat format = TextureFormat.RGBA32, bool bMipmap = false)
        {
            Texture2D uncompressedTexture = new Texture2D(
                tex.width,
                tex.height,
                format,                 // 目标格式
                bMipmap                 // 是否生成 Mipmaps
            );
            // 复制像素数据
            uncompressedTexture.SetPixels(tex.GetPixels());
            uncompressedTexture.Apply();
            return uncompressedTexture;
        }
        #region 请求权限
        /// <summary>
        /// 请求权限
        /// </summary>
        /// <param name="permission">请求类型</param>
        /// <param name="gainJurisdictionBack">请求成功以后返回的操作</param>
        public static void RequestUserAuthorization(string permission, Action gainJurisdictionBack, Action failBack = null)
        {
#if UNITY_ANDROID || UNITY_IPHONE || UNITY_IOS
            TRACE.TraceLn(string.Format("GHelp_进入到获取权限功能：{0}", permission));
            CheckPermissionCurrency(permission, (backNum) =>
            {
                TRACE.TraceLn(String.Format("GHelp_当前获取权限放回结果：{0}", backNum));
                if (backNum == 3)
                {
                    if (gainJurisdictionBack != null)
                    {
                        gainJurisdictionBack.Invoke();
                    }
                }
                else if (backNum == 2)
                {
                    PopupAuthorizedTips();
                    //string strMessage = "您需要自行去开启录音机使用权限";
                    //InformMessageConfirm(Api.TR("自行授权"), strMessage, Api.TR("知道了"));
                    failBack?.Invoke();
                }
                 else if (backNum == 1)
                {
                   failBack?.Invoke();
                }
            });
#endif
        }
        /// <summary>
        /// 弹出自行授权提醒
        /// </summary>
        public static void PopupAuthorizedTips()
        {
            string strTipInfo = "获取相应权限被拒绝，请您自行开启权限";
            if (GetLayoutModel() == LayoutModel.AcrossModel)
            {
                addSystemTips(EMInfoPos.InfoPos_ScreenTopCenterStop, strTipInfo);
            }
            else
            {
                addSystemTipsWithIcon(strTipInfo);
            }
        }
        #endregion

        #region 退出登录

        public static void ExitToMainWindow()
        {
        }

        public static void ExitModuleToMainWindow(bool isTips = true)
        {
            if (isTips)
            {
                ShowMessageBoxData showMessageBoxData = new ShowMessageBoxData();
                showMessageBoxData.messageBoxID = MessageBoxID.COMMON;
                showMessageBoxData.msgBoxModule = MsgBoxModule.LOGIN_OUT;
                showMessageBoxData.msgBoxAction = MsgBoxAction.EXIT_GOTOMAINWINDOW;
                showMessageBoxData.title = Api.TR("退出提醒");
                showMessageBoxData.content = Api.TR("获取环节数据失败，请重新进入!");
                showMessageBoxData.dicTemp = new Dictionary<int, string>();
                showMessageBoxData.dicTemp.Add(0, Api.TR("确定"));
                GHelp.InformShowMessageBox(showMessageBoxData, true);
            }
            else
            {
                //GHelp.addSystemTipsWithIcon("网络异常，请稍后再试", EMFInfoIcon.Icon_False);
                //回到计划页面不退出登陆
                SetBreakLineOperation(false);
            }
        }
        /// <summary>
        /// 设置异常登录
        /// </summary>
        public static void SetAbnormalLoginOut()
        {
            SetAbnormalLoginOut("您的账号已经在其他地方登录，将为您跳转到我的页面，您可以重新进行登录");
        }
        public static void SetAbnormalLoginOut(string msg)
        {
            //发出强制登出提醒
            FireExecute((ushort)ViewLogicDef.INFORM_FORCED_LOGIN_OUT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
            ShowMessageBoxData showMessageBoxData = new ShowMessageBoxData();
            showMessageBoxData.messageBoxID = MessageBoxID.COMMON;
            showMessageBoxData.msgBoxModule = MsgBoxModule.LOGIN_OUT;
            showMessageBoxData.msgBoxAction = MsgBoxAction.ANOMALYLOGINOUT;
            showMessageBoxData.title = Api.TR("退出提醒");
            showMessageBoxData.content = Api.TR(msg);
            showMessageBoxData.dicTemp = new Dictionary<int, string>();
            showMessageBoxData.dicTemp.Add(0, Api.TR("确定"));
            GHelp.InformShowMessageBox(showMessageBoxData, true);
        }
        public static void SetBreakLineOperation(bool boState)
        {
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_CLEAR_TIPS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");

            GHelp.FireExecute((int)ViewLogicDef.EVENT_REC_FINALSCORE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "");
            GHelp.SetAudioState("AudioSound", false);
            GHelp.SetAudioState("AudioMusic", false);
            GHelp.StopMusic(AudioAssetConstId.ButtonClick1, AudioTagClass.AudioSound);
            //销毁场景
            FireExecute((ushort)ViewLogicDef.DESTROY_MAIN_INTERFACE_CHANGE_SCENE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
            //退出登录，true表示要退出登陆，false表示回到计划页面不退出登陆
            //FireExecute((ushort)ViewLogicDef.BACK_IN_LOGIN_WINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, boState);
            //重刷主界面的显示
            FireExecute((ushort)ViewLogicDef.HEASVY_BRUSH_MEUN_SHOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
            //运行返回
            GHelp.FireExecute((ushort)ViewLogicDef.COURCE_RETURN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
            ChangeScene(0, Vector3.zero);
            GlobalGame.Instance.EntityFactory.DestroyAllEntity();
        }
        public static List<T> GetListJson<T>(string json)
        {
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            return serializer.Deserialize<List<T>>(json);
        }

        public static T GetJsonArg<T>(string json)
        {
            JavaScriptSerializer serializer = new JavaScriptSerializer();
            return serializer.Deserialize<T>(json);
        }
        #endregion


        /// <summary>
        /// 获取手机上是否有微信
        /// </summary>
        /// <returns></returns>
        public static bool GetPhoneHaveWeChatApp()
        {
            bool bHaveApp = false;
            int iHaveApp = PlayerPrefs.GetInt("HaveWeChatApp");
            //如果有APP直接返回
            if (iHaveApp == 1)
            {
                bHaveApp = true;
                return bHaveApp;
            }
#if UNITY_ANDROID && !UNITY_EDITOR
            bHaveApp = PluginPlatform.Instance.Plugin().FindPhoneHaveAssignApp("com.tencent.mm");
#elif UNITY_IPHONE && !UNITY_EDITOR
            //IOS目前还不知道传什么参数
            bHaveApp = PluginPlatform.Instance.Plugin().FindPhoneHaveAssignApp("");
#endif
            if (bHaveApp)
            {
                PlayerPrefs.SetInt("HaveWeChatApp", 1);
            }
            return bHaveApp;
        }
        /// <summary>
        /// 获取手机上是否有支付宝
        /// </summary>
        /// <returns></returns>
        public static bool GetPhoneHaveAlipayApp()
        {
            bool bHaveApp = false;
            int iHaveApp = PlayerPrefs.GetInt("HaveWeChatApp");
            //如果有APP直接返回
            if (iHaveApp == 1)
            {
                bHaveApp = true;
                return bHaveApp;
            }
#if UNITY_ANDROID && !UNITY_EDITOR
            bHaveApp = PluginPlatform.Instance.Plugin().FindPhoneHaveAssignApp("com.eg.android.AlipayGphone");
#elif UNITY_IPHONE && !UNITY_EDITOR
            //IOS目前还不知道传什么参数
            bHaveApp = PluginPlatform.Instance.Plugin().FindPhoneHaveAssignApp("");
#endif
            if (bHaveApp)
            {
                PlayerPrefs.SetInt("HaveWeChatApp", 1);
            }
            return bHaveApp;
        }


        /// <summary>
        /// UI是否是宽缩放
        /// </summary>
        /// <returns></returns>
        public static bool IsWidthSacle()
        {
            if (GetLayoutModel() == LayoutModel.AcrossModel)
            {
                if (Screen.width / 1920f == Screen.height / 1080f) return false;
                return Screen.width / 1920f >= Screen.height / 1080f;
            }
            else
            {
                if (Screen.width / 1920f == Screen.height / 1080f) return false;
                return Screen.width / 1080f >= Screen.height / 1920f;
            }
        }

        #region 检查所有权限
        /// <summary>
        /// 权限检查通用方法
        /// </summary>
        /// <param name="permission">权限名:例Permission.ExternalStorageWrite</param>
        /// <param name="permissionState">1:本次拒绝 2:拒绝并不允许 3:已获得权限</param>
        public static void CheckPermissionCurrency(string permission, Action<int> permissionState)
        {
            TRACE.TraceLn("GHelp_进入到CheckPermissionCurrency");
            PluginPlatform.Instance.Plugin().CheckPermission(permission, permissionState);
        }

        #endregion
        public static StringBuilder AccountToJson(string account, string keyword)
        {
            StringBuilder sb = new StringBuilder();

            JsonWriter jsonWriter = new JsonWriter(sb);
            jsonWriter.WriteObjectStart();
            jsonWriter.WritePropertyName("username");
            jsonWriter.Write(account);
            jsonWriter.WritePropertyName("password");
            jsonWriter.Write(keyword);
            //把设备信息带入，首次登录自动绑定设备
            jsonWriter.WritePropertyName("deviceID");
            jsonWriter.Write(SystemInfo.deviceUniqueIdentifier);
            jsonWriter.WritePropertyName("deviceName");
            jsonWriter.Write(SystemInfo.deviceName);
            jsonWriter.WriteObjectEnd();
            return sb;
        }
        public static T ToObject<T>(string str)
        {
            var obj = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(str);
            return obj;
        }

        public static void PlayAnimation(uint entityId, string AnimationName)
        {
            PlayAnimationContext context = GHelp.GetObjectItem<PlayAnimationContext>();
            context.name = AnimationName;
            GHelp.sendEntityCommand(entityId, (int)EntityLogicDef.ENTITY_TOVIEW_PLAY_ANIMATION, 0, "", context);
        }

        public static Vector3 OutputInpectorEulers(Transform transform)
        {
            Vector3 angle = transform.eulerAngles;
            float x = angle.x;
            float y = angle.y;
            float z = angle.z;

            if (Vector3.Dot(transform.up, Vector3.up) >= 0f)
            {
                if (angle.x >= 0f && angle.x <= 90f)
                {
                    x = angle.x;
                }
                if (angle.x >= 270f && angle.x <= 360f)
                {
                    x = angle.x - 360f;
                }
            }
            if (Vector3.Dot(transform.up, Vector3.up) < 0f)
            {
                if (angle.x >= 0f && angle.x <= 90f)
                {
                    x = 180 - angle.x;
                }
                if (angle.x >= 270f && angle.x <= 360f)
                {
                    x = 180 - angle.x;
                }
            }

            if (angle.y > 180)
            {
                y = angle.y - 360f;
            }

            if (angle.z > 180)
            {
                z = angle.z - 360f;
            }
            return angle;
        }

        /// <summary>
        /// 瞬移
        /// </summary>
        /// <param name="vTragetPos"></param>
        /// <param name="vTragetEul"></param>
        public static void HeroMove(Vector3 vTragetPos, Vector3 vTragetEul)
        {
            SEventHeroMove heroMove = new SEventHeroMove();
            heroMove.vTragetPos = vTragetPos;
            heroMove.vTragetEul = vTragetEul;
            GHelp.FireExecute((ushort)DGlobalEvent.EVENT_HERO_MOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, heroMove);
        }

        /// <summary>
        /// 平滑移动
        /// </summary>
        /// <param name="vTragetPos"></param>
        /// <param name="vTragetEul"></param>
        public static void CameraMove(Vector3 vTragetPos, Vector3 vTragetEul)
        {
            SEventHeroMove heroMove = new SEventHeroMove();
            heroMove.vTragetPos = vTragetPos;
            heroMove.vTragetEul = vTragetEul;
            heroMove.bAnimation = true;
            GHelp.FireExecute((ushort)DGlobalEvent.EVENT_HERO_MOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, heroMove);
        }

        #region 操作UI
        public static void LoginWindow()
        {
            cmd_LoginWindow cmd_Login = new cmd_LoginWindow();
            cmd_Login.wModel = WindowModel.LoginWindow;
            cmd_Login.aCommandState = AsyncCommandState.CreateCommmand;
            FireExecute((ushort)ViewLogicDef.EVENT_CREATE_LOGINWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Login);
        }

        /// <summary>
        /// 病例选择
        /// </summary>
        public static void CreateCaseSeclectWindow()
        {
            //先注释 自己去定义
            //cmd_CaseSelection cmd_CaseInfo = new cmd_CaseSelection();
            //cmd_CaseInfo.wModel = WindowModel.CaseSelectWindow;
            //cmd_CaseInfo.aCommandState = AsyncCommandState.CreateCommmand;
            //FireExecute((ushort)ViewLogicDef.EVENT_CREAE_CASESCLECT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_CaseInfo);
        }

        public static void CloseChangeTiWeiWindow()
        {
            cmd_CreateChangeTiWeiWindow cmd_AsyncBase = new cmd_CreateChangeTiWeiWindow();
            //自己定义
            //cmd_AsyncBase.wModel = WindowModel.ChangeTiWeiWindow;
            FireExecute((ushort)ViewLogicDef.EVENT_CLOSE_CHANGETIWEIWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_AsyncBase);
        }

        public static void CreateChangeTiWeiWindow(UnityEngine.Events.UnityAction<bool> unityAction)
        {
            cmd_CreateChangeTiWeiWindow cmd_AsyncBase = new cmd_CreateChangeTiWeiWindow();
            //自己定义
           // cmd_AsyncBase.wModel = WindowModel.ChangeTiWeiWindow;
            cmd_AsyncBase.aCommandState = AsyncCommandState.CreateCommmand;
            cmd_AsyncBase.unityAction = unityAction;
            FireExecute((ushort)ViewLogicDef.EVENT_CREATE_CHANGETIWEIWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_AsyncBase);
        }

        public static void CloseAnswerUIWindow()
        {
            cmd_AnswerWindow cmd_Help = new cmd_AnswerWindow();
            cmd_Help.wModel = WindowModel.AnswerUIWindow;
            cmd_Help.aCommandState = AsyncCommandState.UpdateCommand;
            FireExecute((ushort)ViewLogicDef.EVENT_CLOSE_ANSWERWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Help);
        }

        #region

        /// <summary>
        /// 随机真假
        /// </summary>
        /// <returns></returns>
        public static bool GetRandomBool()
        {
            bool mbool = UnityEngine.Random.Range(0, 2) == 1 ? true : false;
            return mbool;
        }
        public static string GetRendomDate()
        {
            int temYear = UnityEngine.Random.Range(DateTime.Now.Year - 2, DateTime.Now.Year + 1);
            int temMonth = UnityEngine.Random.Range(1, 13);
            if (temYear == DateTime.Now.Year)
            {
                temMonth = UnityEngine.Random.Range(1, DateTime.Now.Month);
            }
            int maxNumber = GetDayMax(temYear, temMonth);
            if(temYear == DateTime.Now.Year && temMonth == DateTime.Now.Month)
            {
                maxNumber = DateTime.Now.Day;
            }
            int temDay = UnityEngine.Random.Range(1, maxNumber);
            string temMonthStr = temMonth.ToString();
            string temDayStr = temDay.ToString();
            if (temMonth <= 9)
            {
                temMonthStr = string.Format("0{0}", temMonth);
            }
            if (temDay <= 9)
            {
                temDayStr = string.Format("0{0}", temDay);
            }
            string temTime = string.Format("{0}/{1}/{2}", temYear, temMonthStr, temDayStr);
            return temTime;
        }
        public static string GetInvalidDate(string firstTime, int qualityMonth)
        {
            var pauseT = Convert.ToDateTime(firstTime);
            int year = pauseT.Year;
            int month = pauseT.Month;
            int day = pauseT.Day;
            month = pauseT.Month + qualityMonth;
            if (pauseT.Month + qualityMonth > 12)
            {
                year += 1;
                month = (pauseT.Month + qualityMonth) % 12;
                if (month == 0)
                {
                    month = pauseT.Month;
                }
            }

            int maxDay = GetDayMax(year, month) - 1;
            if (maxDay < day)
            {
                month += 1;
                day = day - maxDay;
            }
            string temMonth = month.ToString();
            string temDay = day.ToString();
            if (month <= 9)
            {
                temMonth = string.Format("0{0}", temMonth);
            }
            if (day <= 9)
            {
                temDay = string.Format("0{0}", temDay);
            }
            string endTime = string.Format("{0}/{1}/{2}", year, temMonth, temDay);
            return endTime;
        }
        private static int GetDayMax(int temYear, int temMonth)
        {
            int maxNumber;
            if (temMonth == 2)
            {
                if (temYear % 4 == 0)
                {
                    maxNumber = 30;
                }
                else
                {
                    maxNumber = 29;
                }
            }
            else if (temMonth < 8)
            {
                if (temMonth % 2 == 1)
                {
                    maxNumber = 32;
                }
                else
                {
                    maxNumber = 31;
                }
            }
            else
            {
                if (temMonth % 2 == 0)
                {
                    maxNumber = 32;
                }
                else
                {
                    maxNumber = 31;
                }
            }

            return maxNumber;
        }
        public static bool GetDay(string firstTime)
        {
            var pauseT = Convert.ToDateTime(firstTime);
            var resumeT = DateTime.Now;
            var ts1 = new TimeSpan(pauseT.Ticks);
            var ts2 = new TimeSpan(resumeT.Ticks);
            var tsSub = ts1.Subtract(ts2);
            return tsSub.Days >= 0 ? true : false;
        }
        public static bool GetExpiredOrNot(int month, int IntervalDays)
        {
            if (month != 0 && IntervalDays > month * 30)
            {
                return false;
            }
            return true;
        }
        #endregion

        #endregion


        public static int GenRandom(int rep, int min, int max)
        {
            int cur = Api.RangeRand(min, max);
            if (rep > 0)
            {//剔除number
                if (rep == cur)
                {//需要递归
                    cur = RecursionRandom(rep, min, max);
                }
            }
            return cur;
        }

        private static int RecursionRandom(int rep, int min, int max)
        {
            int cur = Api.RangeRand(min, max);
            if (cur == rep)
            {
                return RecursionRandom(rep, min, max);
            }
            return cur;
        }

       static Dictionary<int, int> handIDDic = new Dictionary<int, int>();
        public static int SetHandID(int handType,int id)
        {
            if (!handIDDic.ContainsValue(id))
            {
                if (handIDDic.ContainsKey(handType))
                {
                    handIDDic[handType] = id;
                }
                else
                {
                    handIDDic.Add(handType ,id);
                }
            } 
            return 0;
        }

        /// <summary>
        /// 0有无手套，1右手，2左手，返回iconID
        /// </summary>
        /// <param name="handType"></param>
        /// <returns></returns>
        public static int GetHandID(int handType)
        {
            if (handIDDic.ContainsKey(handType))
            {
                int handID =  handIDDic[handType];
                return handID;
            }
            return 0;
        }

        #region Attribute logic

        private static Dictionary<int, Type> m_operateAttribute = new Dictionary<int, Type>();

        public static void SetAllOperateAttribute(int taskID,Type type)
        {
            if (m_operateAttribute.ContainsKey(taskID))
                TRACE.ErrorLn("重复添加 TaskOperateEnum id = " + taskID + " type = " + type);
            else
                m_operateAttribute.Add(taskID, type);
        }

        public static Type GetOperateClass(int taskID)
        {
            Type tmp = null;
            m_operateAttribute.TryGetValue(taskID, out tmp);
            return tmp;
        }
        #endregion

        #region OperateCommand Mgr

        private static Dictionary<string, uint> opEntityMgr;

        public static void AppendOpEntity(string key, uint entityID)
        {
            if (opEntityMgr == null)
            {
                opEntityMgr = new Dictionary<string, uint>();
            }
            if(!opEntityMgr.ContainsKey(key))
                opEntityMgr.Add(key, entityID);
        }

        public static uint GetOpEntityID(string key)
        {
            uint tmp = 0;
            if (opEntityMgr != null)
            {
                opEntityMgr.TryGetValue(key, out tmp);
            }

            return tmp;
        }

        public static bool OpIsContainsKey(string key)
        {
            if (opEntityMgr != null)
            {
                return opEntityMgr.ContainsKey(key);
            }
            return false;
        }

        public static void ReleaseOpEntityMgr()
        {
            if(opEntityMgr != null)
                opEntityMgr.Clear();
        }

        #endregion

        private static bool m_IsLock = true;
        public static void SetLock(bool isLock)
        {
            m_IsLock = isLock;
        }
        public static bool GetLock()
        {
            return m_IsLock ;
        }

        public static int GetFirstSimulat()
        {

            return PlayerPrefs.GetInt("IsFirstSimulat");
        }

        public static void SetFirstSimulat(int value)
        {

             PlayerPrefs.SetInt("IsFirstSimulat", value);
             PlayerPrefs.Save();
        }

        public static int GetFirstTest()
        {
            return PlayerPrefs.GetInt("IsFirstTest");
        }

        public static void SetFirstTest(int value)
        {
             PlayerPrefs.SetInt("IsFirstTest", value);
             PlayerPrefs.Save();
        }

        public static string GetTipOpenBool()
        {
            return PlayerPrefs.GetString("OpenTipBool");
        }

        /// <summary>
        /// 0 关闭  1 开启
        /// </summary>
        /// <param name="value"></param>
        public static void SetTipOpenBool(String value)
        {
            PlayerPrefs.SetString("OpenTipBool", value);
            PlayerPrefs.Save();
        }

        public static void CameraMove(CameraTransformDef cameraTransform) {
            if (cameraTransform == null)
            {
                return;
            }
            SEventHeroMove heroMove = new SEventHeroMove();
            heroMove.vTragetPos = GHelp.StringToVec3(cameraTransform.Pos);
            heroMove.vTragetEul = GHelp.StringToVec3(cameraTransform.Rorate);
            heroMove.fAnimationDuration = cameraTransform.DurationTime;
            if (cameraTransform.DurationTime == 0)
            {
                heroMove.bAnimation = false;
            }
            else
            {
                heroMove.bAnimation = true;
            }
            GHelp.FireExecute(DGlobalEvent.EVENT_HERO_MOVE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, heroMove);
        }

        /// <summary>
        /// 分割带=号的字符串 返回key和value
        /// </summary>
        /// <param name="Line"></param>
        /// <param name="Key"></param>
        /// <param name="Value"></param>
        /// <returns></returns>
        public static bool ParseKeyValuePair(string Line, ref string Key, ref string Value)
        {
            // *** Check for key+value pair ***
            int i;
            if ((i = Line.IndexOf('=')) <= 0) return false;

            int j = Line.Length - i - 1;
            Key = Line.Substring(0, i).Trim();
            if (Key.Length <= 0) return false;

            Value = (j > 0) ? (Line.Substring(i + 1, j).Trim()) : ("");
            return true;
        }

        /// <summary>
        /// 更新日志
        /// </summary>
        public static void Update_DirectOperateLog(string operateName,int necessaryOperation,string time,int result)
        {
            OperateLogDataDisPose operateLogDataDisPose = new OperateLogDataDisPose();
            operateLogDataDisPose.OperateName = operateName;
            operateLogDataDisPose.NecessaryOperation = necessaryOperation;
            operateLogDataDisPose.Time = time;
            operateLogDataDisPose.Result = result;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_UPDATE_DIRECTOPERATELOG, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 2, operateLogDataDisPose);
        }

        public static void OpenTimeGoWindowsUI()
        {
            cmd_TimeGoWindow cmd_Help = new cmd_TimeGoWindow();
            cmd_Help.wModel = WindowModel.TimeGoWindow;
            cmd_Help.aCommandState = AsyncCommandState.CreateCommmand;
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CREATE_TIMEGOWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Help);
        }

        private void OpenOperateBtnUI(string text)
        {
            cmd_OperateBtnWindow cmd_Help = new cmd_OperateBtnWindow();
            cmd_Help.wModel = WindowModel.OperateBtnWindow;
            cmd_Help.aCommandState = AsyncCommandState.CreateCommmand;
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CREATE_OPERATEBTNWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Help);
        }

        public static Color CreateColor(int r, int g, int b, float alpha = 1)
        {
            return new Color(r / 255f, g / 255f, b / 255f, alpha);
        }

        public static Color CreateColor(int hex, float alpha = 1)
        {
            hex &= 0xFFFFFF;
            return CreateColor(hex >> 16, (hex & 0xFFFF) >> 8, hex & 0xFF, alpha);
        }
        
           public static string PathForFile(string filename)
        {
            if (Application.platform == RuntimePlatform.IPhonePlayer)
            {
                string path = Application.persistentDataPath.Substring(0, Application.persistentDataPath.Length - 5);
                path = path.Substring(0, path.LastIndexOf('/'));
                path = Path.Combine(path, "Documents");
                path = Path.Combine(path, "Cache");
                path = Path.Combine(path, "TopImg");
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                return Path.Combine(path, filename);
            }
            else if (Application.platform == RuntimePlatform.Android)
            {
                string path = Application.persistentDataPath;
                path = Path.Combine(path, "Cache");
                path = Path.Combine(path, "TopImg");

                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                return Path.Combine(path, filename);
            }
            else
            {
                string path =  Application.streamingAssetsPath;
                path = Path.Combine(path, "Cache");
                path = Path.Combine(path, "TopImg");
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                return Path.Combine(path, filename);
            }

        }

        
        /// <summary>
        /// 在本地保存文件
        /// </summary>
        /// <param name="bytes"></param>
        /// <param name="path"></param>
        public static void SaveNativeFile(byte[] bytes, string path)
        {
            FileStream fs = new FileStream(path, FileMode.Create);
            fs.Write(bytes, 0, bytes.Length);
            fs.Flush();
            fs.Close();
        }

        /// <summary>
        /// 获取到本地的图片
        /// </summary>
        /// <param name="path"></param>
        public static Texture2D[] GetNativeFile(string[] path,float m_with,float m_height)
        {
            Texture2D[] textures = new Texture2D[path.Length];
            for (int i = 0; i < path.Length; i++)
            {
                try
                {
                    var pathName = PathForFile(path[i]);
                    var bytes = ReadFile(pathName);
                    textures[i] = new Texture2D((int)m_with, (int)m_height);
                    textures[i].LoadImage(bytes);
                }
                catch (Exception c)
                {
                }
            }
            return textures;
        }
        
        public static byte[] ReadFile(string filePath)
        {
            var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            fs.Seek(0, SeekOrigin.Begin);
            var binary = new byte[fs.Length];
            fs.Read(binary, 0, binary.Length);
            fs.Close();
            return binary;
        }

        /// <summary>
        /// 判断文件是否存在
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static bool FileExists(string[] filePath)
        {
            for (int i = 0; i < filePath.Length; i++)
            {
                if ( !File.Exists(filePath[i]))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 存入本地图片名
        /// </summary>
        /// <param name="value"></param>
        public static void SetTopImageName(string[] value)
        {
            PlayerPrefs.SetString("TopImageName", value.ToString());
            PlayerPrefs.Save();
        }
        
        public static string GetTopImageName()
        {
            return PlayerPrefs.GetString("TopImageName");
        }
        
        /// <summary>
        /// 存入本地图片地址
        /// </summary>
        /// <param name="value"></param>
        public static void SetTopImageUrl(string[] value)
        {
            PlayerPrefs.SetString("TopImageUrl", string.Join(",",value));
            PlayerPrefs.Save();
        }
        
        public static string[] GetTopImageUrl()
        {
            return PlayerPrefs.GetString("TopImageUrl").Split(',');
        }

        public static void SetShuQianAIKey(string key)
        {
            if (ShuQianAIKey != key && !string.IsNullOrEmpty(key))
            {
                ShuQianAIKey = key;
            }
        }

        public static string GetShuQianAIKey()
        {
            if (!string.IsNullOrEmpty(ShuQianAIKey))
            {
                return ShuQianAIKey;
            }
            
            return ShuQianAIKey;
        }

        public static void SetBaiduKey(string apiKey, string secretKey)
        {
            SetBaiduAPIKey(apiKey);
            SetBaiduSecretKey(secretKey);
        }

        public static void SetBaiduAPIKey(string key)
        {
            BaiduAPIKey = key;
        }

        public static void SetBaiduSecretKey(string key)
        {
            BaiduSecretKey = key;
        }

        public static string GetBaiduAPIKey()
        {
            return BaiduAPIKey;
        }

        public static string GetBaiduSecretKey()
        {
            return BaiduSecretKey;
        }

        public static void SetEventEngineStatus(bool status)
        {
            IsEventEngine = status;
        }

        public static bool GetEventEngineStatus()
        {
            return IsEventEngine;
        }
    }
}
