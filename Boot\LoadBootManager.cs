﻿/// <summary>
/// LoadBootManager
/// </summary>
/// <remarks>
/// 2019.6.24: 创建. 谌安 <br/>
/// </remarks>
//#define Update
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System;
using GLib.Common;
using System.Text;

namespace GLib.Boot
{

    //游戏启动管理

    public class LoadBootManager : MonoBehaviourEX<LoadBootManager>, HTTP_Response_Handler
    {
        public LoadMode m_LoadMode = LoadMode.ModeAssetBundle;

        private static readonly string _LOAD_GAMEMANAGER_PATH = "Prefab/Boot/_Manager";

        public SystemPlatform m_platform = SystemPlatform.PC;

        public bool m_useSocket = true;

        private List<string> m_szInitFileName;
        private List<string> m_szFileName;

        private string m_tempP = "";
        private string m_tempWebIp = "";
        private string m_tempVMWebIp = "";
        private string m_tempOSSWebIp = "";
        private string m_sceneServerNode = "";
        private string m_sceneVMServerNode = "";
        private string m_LoadWebUrl = "";
        private string m_clusterName = "";
        private int m_releaseVer = 0;
        private string m_courseServerApi = "";
        private string m_sceneServerApi = "";
        private string m_loginServerApi = "";
        private string m_addressKey = "";
        private string m_addressTraining = "";
        private string m_serverConfigAddress = "";
        private int m_tempPort = 0;
        private int m_IsHideChangeStep = 0;
        private int m_marketType = 0;
        private string CaseSkip = "";
        public DateTime m_StartDateTime;
        public DateTime m_CBootDateTime;
        public DateTime m_CGameDateTime;

        // 读取后台ServerConfig配置数据
        private const string ShuQianAIKey = "SHUQIANAIKey";
        private const string m_BaiDuVoiceAPIKey = "BaiDuVoiceAPIKey";
        private const string m_BaiDuVoiceSecretKey = "BaiDuVoiceSecretKey";

        /// <summary>
        /// 请求多少次失败就不在请求了
        /// </summary>
        public int m_serverConfigRequestMaxNum = 3;
        private int m_serverConfigCurRequestNum = 0;

        public override void Awake()
        {


            m_StartDateTime = DateTime.Now;
            base.Awake();

            ProductTime.Init();
            Api.Init(); 
            GHelp.SetSystemPlatform(m_platform);
            GHelp.SetUseSocket(m_useSocket);
            m_szInitFileName = new List<string>();

            //如果分辨率宽度小于等于高度，那么宽便是竖屏分辨率的宽。否则相反。
            /*int screenWidth = Screen.width;
            int screenHeight = Screen.height;
            //如果宽大于高，那么目前获取到的分辨率是横屏
            if (Screen.width> Screen.height)
            {
                screenWidth = Screen.height;
                screenHeight = Screen.width;
                
            }
            //如果宽度大于高度
            if (screenWidth < 1080)
            {
                screenWidth = 1080;
                screenHeight = 1920;
            }
            else if(screenWidth > 2500)   // 设个最大值，最大不会大于这个值
            {
                screenWidth = 2500;
                screenHeight = (1920 * 2500) / 1080;
            }
            GHelp.SetVerticalWidthOrHeight(new Vector2(screenWidth, screenHeight));*/

            m_szFileName = new List<string>();
            CHttp.Instance.Create(new CoroutinePoolExecutor(this), this);
            DontDestroyOnLoad(gameObject);
            StartCoroutine(__LoadGameManager());
            //CutScreenToPortrait();

        }

        private IEnumerator __LoadGameManager()
        {
            LoadAssets();
            yield return CopyFile();

            ProductIni.Instance.Open();

            ResourceConfigManager.Instance.Create();
            ResourceConfigManager.Instance.Init();

            InitAB();

            yield return GResources.Init(GResourceHelp.GenAssetLoader(gameObject, m_LoadMode), gameObject, m_LoadMode);

            GameObject obj = GResources.Load<GameObject>(_LOAD_GAMEMANAGER_PATH);

            GameObject _gameObject = GameObject.Instantiate(obj);
            _gameObject.transform.SetParent(gameObject.transform);

            m_tempP = ProductIni.Instance.GetString("ServerIP", ""); // ************
            m_tempWebIp = ProductIni.Instance.GetString("ServerWebIP", "");  // "http://************:9900"
            m_tempVMWebIp = ProductIni.Instance.GetString("VMServerWebIP", "");  // "http://************:9900"
            m_tempOSSWebIp = ProductIni.Instance.GetString("FileServerWebIP", "");
            m_sceneServerNode = ProductIni.Instance.GetString("ServerNode", "");  // scene_chenan
            m_sceneVMServerNode = ProductIni.Instance.GetString("vmServerNodeName", "");
            m_LoadWebUrl = ProductIni.Instance.GetString("loadWebUrl", ""); // index.html
            m_clusterName = ProductIni.Instance.GetString("ClusterName", "pro");
            m_releaseVer = ProductIni.Instance.GetInt("ReleaseVer", 1);
            m_courseServerApi = ProductIni.Instance.GetString("CourseServerApi", ""); 
            m_sceneServerApi = ProductIni.Instance.GetString("SceneServerApi", "");
            m_loginServerApi = ProductIni.Instance.GetString("LoginServerApi", "");
            m_addressKey = ProductIni.Instance.GetString("AddressKey", "");
            m_addressTraining = ProductIni.Instance.GetString("AddressTraining", "");
            m_serverConfigAddress = ProductIni.Instance.GetString("ServerConfigAddress", "");
            m_tempPort = ProductIni.Instance.GetInt("ServerPort", 0);
            CaseSkip = ProductIni.Instance.GetString("CaseSkip", "");
            m_IsHideChangeStep = ProductIni.Instance.GetInt("IsHideChangeStep", 0);
            m_marketType = ProductIni.Instance.GetInt("MarketType", 0);
            //m_IsHaptic = ProductIni.Instance.GetInt("IsHaptic", 1);
            GHelp.SetTempIP(m_tempP);
            GHelp.SetTempPort(m_tempPort);
            GHelp.SetDefaultWebUrl(m_tempWebIp);
            GHelp.SetServerSceneNode(m_sceneServerNode);
            GHelp.SetVMServerSceneNode(m_sceneVMServerNode);
            GHelp.SetLoadWebUrl(m_LoadWebUrl, true);
            GHelp.SetClusterName(m_clusterName);
            GHelp.SetReleaseVer(m_releaseVer);
            GHelp.SetLoginUrl(m_loginServerApi);
            GHelp.SetIsHaptic(1);
            GHelp.SetCaseSkip(CaseSkip);
            GHelp.SetIsHideChangeStep(m_IsHideChangeStep);
            GHelp.SetMarketType(m_marketType);
            if (!string.IsNullOrEmpty(m_addressKey) && !string.IsNullOrEmpty(m_serverConfigAddress) && !string.IsNullOrEmpty(m_addressTraining))
            {
                GetServerConfig();
            }
#if Update
            IUpdateEngine updateEngine = null;
            do
            {
                yield return null;
                updateEngine = GHelp.GetUpdateEngine();
            } while (updateEngine == null || !updateEngine.GetInitFinish());
            GHelp.SetUpdateDefine(true);
#endif
        }

        // 生成需要从内包复制到外包的AssetBundle配置文件列表
        private void LoadAssets()
        {
            // 添加AssetBundle配置文件到列表中去
            for (int i = 0; i < ResourceConfigManager.m_AssetFileName.Length; i++)
            {
                m_szFileName.Add(ResourceConfigManager.m_AssetFileName[i]);
            }
            for (int i = 0; i < ResourceConfigManager.m_FixedFileName.Length; i++)
            {
                m_szFileName.Add(ResourceConfigManager.m_FixedFileName[i]);
            }
        }

        /// <summary>
        /// 拷贝目录下的所有文件到目的目录。
        /// </summary>
        /// <param >源路径</param>
        /// <param >目的路径</param>
        private IEnumerator CopyFile()
        {
            string szSrcFileName = string.Empty;
            string szDesFileName = string.Empty;


            string nLastVer = UpdateIni.Instance.GetString("Last", "ProductVer", "");
            // Debug.LogError("CopyFile nLastVer:" + nLastVer);
            for (int i = 0; i < m_szFileName.Count; i++)
            {
                GetABPath(m_szFileName[i], ResUtil.Location.Internal, out szSrcFileName);

#if UNITY_EDITOR || UNITY_IPHONE
                szSrcFileName = string.Format("file:///{0}", szSrcFileName);
#endif
                GetABPath(m_szFileName[i], ResUtil.Location.External, out szDesFileName);
                if (File.Exists(szDesFileName))
                {
                    //版本判断
                    if (m_szFileName[i].Equals("ProductConfig.ini"))
                    {
                        //continue;
                    }
                    { 
                        string nCurVer = ProductConfig.Version;
                        // Debug.LogError("CopyFile nCurVer:" + nCurVer+ "  szDesFileName:"+ szDesFileName);
                        //暂时不用管版本信息
                        if (nCurVer.Equals(nLastVer))
                        {
                          //  continue;
                        }
                        //    else
                        //    {
                        //    }
                    }
                }
                //Debug.LogError("CopyFile  File:" + m_szFileName[i]);
                m_szInitFileName.Add(m_szFileName[i]);

                string sz_Path = szDesFileName.Substring(0, szDesFileName.LastIndexOf("/"));

                if (!Directory.Exists(sz_Path))
                {
                    try
                    {
                        Directory.CreateDirectory(sz_Path);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError(e.Message);
                    }
                }
                using (var www = new WWW(szSrcFileName))
                {
                    yield return www;
                    if (string.IsNullOrEmpty(www.error))
                    {
                        try
                        {
                            System.IO.File.WriteAllBytes(szDesFileName, www.bytes);
                        }
                        catch (System.Exception e)
                        {
                            Debug.LogError(e.Message);
                        }
                    }
                    else
                    {
                        Debug.LogWarning(www.error + " :" + m_szFileName[i]);
                    }

                    www.Dispose();
                }

                yield return null;
            }

            UpdateIni.Instance.SetString("Last", "ProductVer", ProductConfig.Version);
            m_szFileName.Clear();
            Resources.UnloadUnusedAssets();

            GC.Collect();
        }

        /// <summary>
        /// Get AssetBundle路径
        /// </summary>
        /// <param name="name"></param>
        /// <param name="loc"></param>
        /// <param name="sz_Path"></param>
        private void GetABPath(string name, ResUtil.Location loc, out string sz_Path)
        {
            string szRootPath;
            if (loc == ResUtil.Location.External)
            {
                szRootPath = ResUtil.GetAssetBundleRootPathExternal();
            }
            else
            {
                szRootPath = ResUtil.GetAssetBundleRootPathInternal();
            }

            sz_Path = string.Format("{0}/{1}", szRootPath, name);
        }

        private void InitAB()
        {
            for (int i = 0; i < m_szInitFileName.Count; i++)
            {
                int nName = 0;
                if (!int.TryParse(m_szInitFileName[i], out nName))
                    continue;

                AssetBundleListInfo info = ResourceConfigManager.Instance.GetAssetBundleListData(nName);
                if (info == null)
                {
                    info = new AssetBundleListInfo();
                }

                info.szName = nName;
                info.nVer = 0;
                info.nExternal = 1;
                info.nLocal = 1;

                ResourceConfigManager.Instance.AddAssetBundleListData(info);
            }
        }
        private void CutScreenToPortrait()
        {
            GHelp.SetLayoutModel(LayoutModel.VerticalModel);
            Screen.orientation = ScreenOrientation.Portrait;
            Screen.autorotateToPortrait = true;
            //竖屏上下颠倒
            Screen.autorotateToPortraitUpsideDown = false;
            Screen.autorotateToLandscapeLeft = false;
            Screen.autorotateToLandscapeRight = false;
        }
        private void OnDestroy()
        {
        }

        public void OnResponse(byte[] pContent, uint dwLen, string url)
        {
            string data = Encoding.UTF8.GetString(pContent);
        }

        public void OnError(uint dwError, string url)
        {
            Debug.Log("====== : ScoreOnError ");
            m_serverConfigCurRequestNum++;
            if (m_serverConfigCurRequestNum < m_serverConfigRequestMaxNum)
            {
                CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, m_serverConfigAddress, this, null, "");
                return;
            }
            if (url.Contains(m_serverConfigAddress))
            {
                GHelp.IsServerConfiginiRequest = true;
                GHelp.HideWait();
            }
        }

        public void OnLocation(string new_url, string url)
        {
            Debug.Log("====== :ScoreOnLocation ");
        }

        public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
        {
            if (pData == null)
            {
                return true;
            }
            string data = Encoding.UTF8.GetString(pData);
            //Debug.Log("Outnode.data=" + data);
            if (url.Contains(m_serverConfigAddress))
            {
                string[] datas = data.Split('\n');
                int num = 0;
                for (int i = 0; i < datas.Length; i++)
                {
                    if (datas[i].Contains("="))
                    {
                        string key = "", value = "";
                        GHelp.ParseKeyValuePair(datas[i], ref key, ref value);
                        if (key.Equals(m_addressKey))
                        {
                            if (!string.IsNullOrEmpty(value))
                            {
                                GHelp.SetDefaultWebUrl(value);
                                GHelp.SetLoginUrl(string.Format("{0}/api/vr/student/login", value));
                            }
                            else
                            {
                                TRACE.TraceLn(string.Format("{0}:的value为空", key));
                            }
                            num++;
                        }
                        else if (key.Equals(m_addressTraining))
                        {
                            num++;
                            GHelp.SetTrainingWebUrl(value);
                        }
                        else if (key.Equals(ShuQianAIKey))
                        {
                            num++;
                            GHelp.SetShuQianAIKey(value);
                        }
                        else if (key.Equals(m_BaiDuVoiceAPIKey))
                        {
                            num++;
                            GHelp.SetBaiduAPIKey(value);
                        }
                        else if (key.Equals(m_BaiDuVoiceSecretKey))
                        {
                            num++;
                            GHelp.SetBaiduSecretKey(value);
                        }
                    }
                }
                if (num < 2)
                {
                    TRACE.TraceLn(string.Format("{0}没有找到key:{1}或者{2}", m_serverConfigAddress, m_addressKey,m_addressTraining));
                }
                GHelp.IsServerConfiginiRequest = true;
                GHelp.HideWait();
            }
            return true;
        }

        public EMHTTP_METHOD GetCurrentHttpMethod()
        {
            return EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB;
        }

        public void GetServerConfig()
        {
            GHelp.addWait("服务器连接中...");
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, m_serverConfigAddress, this, null, "");
        }
    }
}

