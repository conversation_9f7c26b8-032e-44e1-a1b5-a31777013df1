﻿/// <summary>
/// DGlobalMisc
/// </summary>
/// <remarks>
/// 2020.8.17: 创建. 谌安 <br/>
/// 全局其它事件管理<br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace GLib.Common
{
    public class DGlobalMisc
    {

        public const Int32 MAX_BUILDCONTEXT_COUNT = 8;
    }

    public class ILoginProtocl
    {
        //服务器响应
        public const int MSG_SERVERINFO_RESPONSE = 50; 
    }

    ////////////////////房间事件操作结果码/////////////////////////////
    public enum EMRoomEvent
    {
        None = 0,
        //加入房间
        MSG_FIGHT_JOINROOM,
        //退出房间
        MSG_FIGHT_EXITROOM,
        //加入房间成功
        MSG_FIGHT_JOINROOMSUC,
    }

    ///////////////////////// 攻击或治疗原因 //////////////////////////
    public enum EMEPK_AttackCureResult
    {
        EPK_AttackCureResult_Success = 0,               // 成功

        EPK_AttackCureResult_InvalidArgs,               // 无效参数（当不能识别时用）
        EPK_AttackCureResult_Unknow,                    // 未知原因
        EPK_AttackCureResult_NoAttackSelf,              // 不能攻击自己
        EPK_AttackCureResult_NoNPC,                     // 不能攻击/治疗NPC

        EPK_AttackCureResult_InvaludTarget,             // 无效的目标
        EPK_AttackCureResult_Max
    };

    /////////////////////3D移动标志////////////////////////////
    public enum EM3DMoveFlag
    {
        EM3DMoveFlag_FollowPath = 0x01,         //后续路径
        EM3DMoveFlag_SpeedUp = 0x08,            //服务器移动加速
        EM3DMoveFlag_ServerSpeedUp = 0x40,      //服务器加速移动，只有服务器驱动怪物走路才有效
        EM3DMoveFlag_ServerFrameSyn = 0x80,     //服务器帧同步方式
    };

    [StructLayout(LayoutKind.Sequential, Pack = 1, CharSet = CharSet.Unicode)]
    public struct SMiscBuildContext
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = DGlobalMisc.MAX_BUILDCONTEXT_COUNT)]
        public Int32[] nValue1;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = DGlobalMisc.MAX_BUILDCONTEXT_COUNT)]
        public Int32[] nValue2;
        public Int32 nPlusValue;											// 附加数值
    };
}
