﻿using game.common;
using game.proto;
using game.scene;
using game.schemes;
using Game.Entity;
using Google.Protobuf;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using static Game.Entity.Entity_CreateEntity.Types;

namespace GLib.Client.Entity
{
    public class CMonster : IMonster
    {
		Int64 m_uid;

		string m_guid;

		// 数值属性
		int[] m_nNumProp;

		// 怪物称号
		string m_szTitle;

		// 实体类型
		CEntityClass m_EntityClass;

		// 实体部件
		IEntityPart[] m_pEntityPart;

		// 实体技能部件
		Dictionary<long, IEntityPart> m_pSkillPart;

		// 实体视图ID
		UInt32 m_nEntityViewID;

		// 死亡标志
		bool m_bDieFlag;

		// 模型id
		int m_nSkinID;

		// 主人
		Int64 m_uidLeader;

		// 是否为虚拟怪物
		bool m_bIsDeceitFlag;

		//是否开启visit
		bool m_bEnableVisit;

		// 是否是自定义怪物
		//bool						m_bCustom;

		// 怪物等级
		int m_lLevel;

		// 实体类型
		EMEntityType m_entitiyType;
		//配置ID
		int m_configID = 0;
		//实体工厂配置ID（服务器刷怪表ID）
		int m_entityFactoryConfigID = 0;

		// 怪物名字：这个在挑战系统中用到
		string m_szName;
		/// <summary>
		/// 名字颜色
		/// </summary>
		private string m_nameColor;

		// 3D坐标位置
		Vector3 m_Position;

		//3D朝向
		Vector3 m_Forward;
		/// <summary>
		/// 3D移动管理器
		/// </summary>
		protected C3DMoveManager m_3DMoveManager;


		private Monster.Types.Item m_monsterInfo;

		private NPC.Types.Item m_npcInfo;

		private Wall.Types.Item m_wallInfo;

		private Pitfall.Types.Item m_pitfallInfo;
		/** 
        @param   
        @param   
        @return  
        */
		public CMonster()
		{
			// UID
			m_uid = 0;

			m_guid = "";

			// 实体视图ID
			m_nEntityViewID = 0;

			// 死亡标志
			m_bDieFlag = false;

			// 怪物称号
			m_szTitle = "";

			// 怪物称号
			m_szName = string.Empty;

			m_EntityClass = new CEntityClass();

			// 数值属性
			m_nNumProp = new int[(int)eEntityProp.EEntityMax];

			// 实体部件
			m_pEntityPart = new IEntityPart[(int)EMENTITYPART.ENTITYPART_ENTITY_MAXID];

			m_pSkillPart = new Dictionary<long, IEntityPart>();

			//移动管理器
			m_3DMoveManager = new C3DMoveManager();

			for (int i = 0; i < m_nNumProp.Length; i++)
			{
				m_nNumProp[i] = 0;
			}
			for (int i = 0; i < m_pEntityPart.Length; i++)
			{
				m_pEntityPart[i] = null;
			}

		}

		public void Init()
		{

			m_3DMoveManager.Create(this);
		}

		/// <summary>
		/// 获取3D移动实体管理器
		/// </summary>
		/// <returns></returns>
		public I3DMoveManager Get3DMoveManager()
		{
			return m_3DMoveManager;
		}


		/** 释放,会释放内存
        @param   
        @param   
        @return  
        */
		public void Release()
		{
			{
				///////////////////////////////////////////////////////////////////
				// 发送事件
				/*SEventEntityDestroryEntity_C eventdestroryentity;
				eventdestroryentity.uidEntity = m_uid;
				byte bSrcType = GetEventSourceType();
				GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
											eventdestroryentity);*/

				///////////////////////////////////////////////////////////////////
				// 释放部件
				for (int i = 0; i < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; i++)
				{
					if (m_pEntityPart[i] != null)
					{
						m_pEntityPart[i].Release();
						m_pEntityPart[i] = null;
					}
				}

				foreach (KeyValuePair<long, IEntityPart> skillPart in m_pSkillPart)
				{
					skillPart.Value.Release();
				}
				m_pSkillPart.Clear();

				///////////////////////////////////////////////////////////////////
				// 从实体世界中移除
				((CEntityClient)GHelp.GetEntityClient()).Remove(this);

				// 移去多占位
				DisposeMultiBlock(false);

				EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
				Destroy.ENTITY_ID = m_nEntityViewID;
				Destroy.ENTITY_UID = m_uid;

				// 移除实体视图
				GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);


				m_nEntityViewID = 0;


			}

			m_szTitle = "";

			for (int i = 0; i < m_nNumProp.Length; i++)
			{
				m_nNumProp[i] = 0;
			}
		}
		public byte GetEventSourceType()
		{
			return (byte)EMSOURCE_TYPE.SOURCE_TYPE_MONSTER;
		}

		/// <summary>
		/// 还原,不释放对象，只将状态还原到创建时状态
		/// </summary>
		public void Restore()
		{
			{
				///////////////////////////////////////////////////////////////////
				// 发送事件
				/*SEventEntityDestroryEntity_C eventdestroryentity;
				eventdestroryentity.uidEntity = m_uid;
				byte bSrcType = GetEventSourceType();
				GHelp.GetEventEngine().FireExecute((ushort)DGlobalEvent.EVENT_ENTITY_DESTROYENTITY, bSrcType, (uint)UID_DATA.ANALYZEUID_SERIALNO(m_uid),
											eventdestroryentity);*/

				///////////////////////////////////////////////////////////////////
				// 回收部件
				for (int i = 0; i < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; i++)
				{
					if (m_pEntityPart[i] != null)
					{
						m_pEntityPart[i].Restore();
					}
				}
				foreach (KeyValuePair<long, IEntityPart> skillPart in m_pSkillPart)
				{
					skillPart.Value.Restore();
				}

				///////////////////////////////////////////////////////////////////
				// 从实体世界中移除
				((CEntityClient)GHelp.GetEntityClient()).Remove(this);

				// 移去多占位
				DisposeMultiBlock(false);

				EntryView_Destroy Destroy = GHelp.GetObjectItem<EntryView_Destroy>();
				Destroy.ENTITY_ID = m_nEntityViewID;
				Destroy.ENTITY_UID = m_uid;

				// 移除实体视图
				GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_DESTROY_ENTRY, 0, "", Destroy);

				m_nEntityViewID = 0;

				// 是否为虚拟怪物
				m_bIsDeceitFlag = false;

				// 怪物称号
				m_szName = string.Empty;

				//回收移动管理器
				m_3DMoveManager.Restore();

				for (int i = 0; i < m_nNumProp.Length; i++)
				{
					m_nNumProp[i] = 0;
				}
				m_szTitle = "";
			}

			// 添加到缓冲池中
			GlobalGame.Instance.EntityClient.RecycleEntity(this);
		}

		/** 创建
        @param   
        @param   
        @return  
        */
		public bool Create()
		{
			// 怪物ID
			int nMonsterID = m_configID;
			{
				m_monsterInfo = GlobalGame.Instance.SchemeCenter.GetMonsterInfo().GetMonsterInfoByID(nMonsterID);
				// 取得怪物配置
				if (m_monsterInfo == null)
				{
					TRACE.ErrorLn("CMonster: Create m_pSchemeInfo == null ,MonsterID=" + nMonsterID);
					return false;
				}
				m_nameColor = m_monsterInfo.NameColor;
			}

			// 实体类型
			m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Monster);

			// 创建实体视图
			if (!CreateView())
			{
				return false;
			}

			// 添加到实体世界中
			if (!((CEntityClient)GHelp.GetEntityClient()).Add(this))
			{
				return false;
			}

			// 移动速度
			ChangeMoveStyle();

			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory != null)
			{
				if (m_entitiyType == EMEntityType.typeNpc)
				{

				}
				else if (m_entitiyType == EMEntityType.typeWall)
				{
					if (m_wallInfo.ShowHP == 1)
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawHP);
					if (m_wallInfo.ShowName == 1)
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName);
				}
				else if (m_entitiyType == EMEntityType.typePitfall)
				{
					if (m_pitfallInfo.ShowHP == 1)
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawHP);
					if(m_pitfallInfo.ShowName == 1)
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName);
				}
				else
				{
					if (m_monsterInfo.ShowHP == 1)
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawHP);
					if (m_monsterInfo.ShowName == 1)
						pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName);
				}
				/*
				if (m_pSchemeInfo.nForbidAngleChanged == 1)
				{
					pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagForbidAngleChanged);
				}

				if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_Hide) == (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_Hide)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagVisible);
				}

				if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_NoSelectable) == (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_NoSelectable)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagSelectable);
				}

				if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_HideName) == (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_HideName)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawName);
				}

				if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_HideHP) == (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_HideHP)
				{
					pEntityFactory.removeFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawHP);
				}

				if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_HideShadow) == (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_HideShadow)
				{
					pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagNoShadow);
				}

				//血条显示逻辑更改，默认不显示血条，表中如果配置长显示血条，需要增加此逻辑/
				if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_LongShowHP) == (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_LongShowHP)
				{
					pEntityFactory.addFlag(m_nEntityViewID, (uint)EntityFlags.flagDrawHP);
				}*/
			}

			return true;
		}

		/// <summary>
		/// 正在飞行
		/// </summary>
		/// <returns></returns>
		public bool IsFlying()
		{
			return false;
		}


		/** 构建虚拟怪物
        @param   
        @param   
        @return  
        */
		public bool Deceit(Int64 uid)
		{
			if (uid == 0)
			{
				return false;
			}

			// UID
			m_uid = uid;

			// 实体类型
			m_EntityClass.SetClass((uint)EMtEntity_Class.tEntity_Class_Monster);


			return true;
		}

		/** 
        @param   
        @param   
        @return  
        */
		public string GetName()
		{
			if (m_szName != string.Empty/*这样判断最快*/)
			{
				return m_szName;
			}

			return "";// m_pSchemeInfo.szName;
		}

		private bool IsOnStrick()
		{
			/*ISchemeCenter pSchemeCenter = GHelp.GetSchemeCenter();
			IPerson pHero = GHelp.GetHero();
			if (pSchemeCenter == null || pHero == null)
			{
				return false;
			}
			// 完全按照阵营规则来判断敌对状态
			ICampPart pMCampPart = (ICampPart)GetEntityPart((uint)EMENTITYPART.ENTITYPART_CREATURE_CAMP);
			ICampPart pTCampPart = (ICampPart)pHero.GetEntityPart((uint)EMENTITYPART.ENTITYPART_CREATURE_CAMP);
			if (pMCampPart == null || pTCampPart == null)
			{
				return false;
			}
			int nMCampID = pMCampPart.GetCampID();
			int nTCampID = pTCampPart.GetCampID();
			if (nMCampID == 0 && nTCampID == 0)
			{
				return true;
			}
			int nInitEnmity = pSchemeCenter.GetInitEnmity(pMCampPart.GetCampID(), pTCampPart.GetCampID());
			if (nInitEnmity <= 0)
			{
				return false;
			}*/
			return true;
		}

		/** 
        @param   
        @param   
        @return  
        */
		public void ShowEntityName()
		{
			if (m_nEntityViewID == 0)
			{
				return;
			}

			/*uint dwMapID = GHelp.GetMapID();
			IPerson pPerson = GHelp.GetHero();
			ISchemeCenter pSchemeCenter = GHelp.GetSchemeCenter();
			SMapSchemeInfo pMapSchemeInfo = pSchemeCenter.GetMapSchemeInfo((int)dwMapID);
			if (pMapSchemeInfo == null || pPerson == null)
			{
				return;
			}*/

			string pszName = GetName();
			if (string.IsNullOrEmpty(pszName))
			{
				return;
			}

			//int dwHeroMask = pPerson.GetNumProp((uint)EMCREATURE_PROP.CREATURE_PROP_DEGREEMASK);    // 身份标识

		}

		/** 调整怪物速度
        @param   
        @param   
        @return  
        */
		public void ChangeMoveStyle()
		{
			//获取移动速度
			float fSpeed = GetNumProp((uint)eEntityProp.EEntityMoveSpeed) / DGlobalGame.FLOAT_SCALE_SIZE;
			//设置移动速度
			//GHelp.sendEntityCommand(m_nEntityViewID, (int)EntityLogicDef.ENTITY_TOVIEW_SET_SPEED, (int)(fSpeed * 100), "", null);
		}

		/** 
        @param   
        @param   
        @return  
        */
		public Int64 GetLeader()
		{
			return m_uidLeader;
		}

		
		/** 取得实体类型
        @param   
        @param   
        @return  
        */
		public IEntityClass GetEntityClass()
		{
			return m_EntityClass;
		}

		public EMEntityType GetEntityType()
		{
			return m_entitiyType;
		}

		/** 取得UID
        @param   
        @param   
        @return  
        */
		public Int64 GetUID()
		{
			return m_uid;
		}


		public string GetStrGUID()
		{
			return m_guid;
		}
		/// <summary>
		/// 获取3D坐标
		/// </summary>
		/// <returns></returns>
		public Vector3 GetPosition()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
			Vector3 pos = pEntityFactory.GetPosition(GetEntityViewID());
			return pos;
		}

		/// <summary>
		/// 设置3D坐标
		/// </summary>
		public void SetPosition(Vector3 vPos)
		{
		}

		/// <summary>
		/// 获取是否正在跳跃
		/// </summary>
		/// <returns></returns>
		public EntityJumpDef GetJumpState()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return EntityJumpDef.None;
			EntityJumpDef isJump = pEntityFactory.GetJumpState(m_nEntityViewID);
			return isJump;
		}

		/// <summary>
		/// 设置跳跃状态
		/// </summary>
		/// <param name="state"></param>
		/// <returns></returns>
		public bool SetJumpState(EntityJumpDef state)
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return false;
			pEntityFactory.SetJumpState(m_nEntityViewID, state);
			return true;
		}

		/// <summary>
		/// 获取朝向
		/// </summary>
		/// <returns></returns>
		public Vector3 GetForward()
		{
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return Vector3.zero;
			Vector3 forward = pEntityFactory.GetForward(GetEntityViewID());
			return forward;
		}

		/// <summary>
		/// 设置朝向
		/// </summary>
		/// <param name="vForward"></param>
		public void SetForward(Vector3 vForward)
		{
			m_Forward = vForward;
		}

		/// <summary>
		/// 获取移动速度
		/// </summary>
		/// <returns></returns>
		public float GetMoveSpeed()
		{
			return GetNumProp((uint)eEntityProp.EEntityMoveSpeed) / DGlobalGame.FLOAT_SCALE_SIZE;
		}
		/** 设置数值属性
        @param   nValue ：属性值
        @param   
        @return  
        */
		public bool SetNumProp(uint dwPropID, int nValue)
		{
			m_nNumProp[dwPropID] = nValue;
			return true;
		}

		/** 取得数值属性
        @param   
        @param   
        @return  
        */
		public int GetNumProp(uint dwPropID)
		{
			// 要转成内部MonsterID
			if (dwPropID < 0 || dwPropID >= (uint)eEntityProp.EEntityMax)
			{
				return 0;
			}
			return m_nNumProp[dwPropID];
		}

		/** 设置字符属性
        @param   pszValue ：属性值
        @param   
        @return  
        */
		public bool SetStrProp(uint dwPropID, string pszValue)
		{
			int _value = 0;
			GHelp.ConvertEntityPropInt((eEntityProp)dwPropID, pszValue, ref _value);
			m_nNumProp[dwPropID] = _value;
			return true;
		}
		public int GetEntityFactoryConfigID()
		{
			return m_entityFactoryConfigID;
		}
		/** 批量更新属性
        @param   
        @param   
        @return  
        */
		public bool BatchUpdateProp(IMessage pszProp, int nLen)
		{
			int nInLen = nLen;
			EntityInfo other = pszProp as EntityInfo;
			m_uid = Api.GuidCInt(other.Guid);
			m_Position = new UnityEngine.Vector3(other.Position.X, other.Position.Y, other.Position.Z);
			m_szName = other.Name;
			m_entitiyType = (EMEntityType)other.EntityType;
			m_configID = other.ConfigId;
			m_guid = other.Guid;
			m_entityFactoryConfigID = other.EntityFactoryConfigID;

			foreach (ProItem item in other.Props)
			{
				SetStrProp((uint)item.PropType, item.PropValue);
			}

			return true;
		}

		/** 消息
        @param   
        @param   
        @return  true：正常执行；false：被否决  
        */
		public bool OnMessage(uint dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			return true;// m_MessageSlot.Fire(dwMsgID, pGameMsgHead, pszMsg);
		}

		/** 订阅投票消息
        @param   
        @param   
        @return  
        */
		public bool Subscibe(uint dwMsgID, IMessageVoteSink pVoteSink, string pszDesc)
		{
			return true;// m_MessageSlot.Subscibe(dwMsgID, pVoteSink, pszDesc);
		}

		/** 取消订阅投票消息
        @param   
        @param   
        @return  
        */
		public bool UnSubscibe(uint dwMsgID, IMessageVoteSink pVoteSink)
		{
			return true;// m_MessageSlot.UnSubscibe(dwMsgID, pVoteSink);
		}

		/** 订阅执行消息
        @param   
        @param   
        @return  
        */
		public bool Subscibe(uint dwMsgID, IMessageExecuteSink pExecuteSink, string pszDesc)
		{
			return true;// m_MessageSlot.Subscibe(dwMsgID, pExecuteSink, pszDesc);
		}

		/** 取消订阅执行消息
        @param   
        @param   
        @return  
        */
		public bool UnSubscibe(uint dwMsgID, IMessageExecuteSink pExecuteSink)
		{
			return true;//m_MessageSlot.UnSubscibe(dwMsgID, pExecuteSink);
		}

		/** 增加实体部件
        @param   
        @param    
        @return  
        */
		public bool AddEntityPart(IEntityPart pEntityPart)
		{
			if (pEntityPart == null)
			{
				return false;
			}

			uint nPartID = pEntityPart.GetPartID();
			if (nPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || nPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
			{
				return false;
			}

			if (m_pEntityPart[nPartID] != null)
			{
				return false;
			}

			m_pEntityPart[nPartID] = pEntityPart;

			return true;
		}

		/** 移除实体部件
        @param   
        @param   
        @return  
        */
		public bool RemoveEntityPart(uint dwPartID)
		{
			if (dwPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || dwPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
			{
				return false;
			}

			m_pEntityPart[dwPartID] = null;

			return true;
		}

		/** 取得实体部件
        @param   
        @param   
        @return  
        */
		public IEntityPart GetEntityPart(uint dwPartID)
		{
			if (dwPartID <= (uint)EMENTITYPART.ENTITYPART_ENTITY_INVALID || dwPartID >= (uint)EMENTITYPART.ENTITYPART_ENTITY_MAXID)
			{
				return null;
			}

			return m_pEntityPart[dwPartID];
		}

		public IEntityPart GetSkillPart(long skillID = 1)
		{
			IEntityPart part = null;

			m_pSkillPart.TryGetValue(skillID, out part);

			return part;
		}

		/// <summary>
		/// 获得全部的技能部件
		/// </summary>
		/// <returns></returns>
		public Dictionary<long, IEntityPart> GetAllSkillParts()
		{
			return m_pSkillPart;
		}

		/// <summary>
		/// 获得或者创建技能部件
		/// </summary>
		/// <param name="dwPartID"></param>
		/// <param name="uid"></param>
		/// <returns></returns>
		public IEntityPart GetOrCreateSkillPart(long skillID = 1)
		{
			IEntityPart part = null;

			return part;
		}


		public bool AddSkillPart(IEntityPart pEntityPart, long skillID = 1)
		{
			if (pEntityPart as ISkillPart == null)
			{
				return false;
			}

			if (m_pSkillPart.ContainsKey(skillID))
			{
				return false;
			}

			m_pSkillPart.Add(skillID, pEntityPart);

			return true;
		}

		/** 是否正在移动
        @param   
        @param   
        @return  
        */
		public bool IsMoving()
		{
			if (m_nEntityViewID == 0)
			{
				return false;
			}
			IEntityFactory pEntityFactory = GHelp.GetEntityFactory();
			if (pEntityFactory == null)
				return false;
			return pEntityFactory.isMoving(m_nEntityViewID);
		}

	
		/** 取得实体视图ID接口
        @param   
        @param   
        @return  
        */
		public UInt32 GetEntityViewID()
		{
			return m_nEntityViewID;
		}

		public void LockVisitEnable(bool lock_visit)
		{
			m_bEnableVisit = lock_visit;
		}

		public Monster.Types.Item GetMonsterInfo()
		{
			return m_monsterInfo;
		}

		public Wall.Types.Item GetWallInfo()
		{
			return m_wallInfo;
		}

		public Pitfall.Types.Item GetPitfallInfo()
		{
			return m_pitfallInfo;
		}

		public NPC.Types.Item GetNpcInfo()
		{
			return m_npcInfo;
		}

		/** 属性是否变化
        @param   
        @param   
        @return  
        */
		public bool IsNumPropChanged(uint dwPropID)
		{
			return false;
		}


		/** 属性是否变化处理结束,清理标识
        @param   
        @param   
        @return  
        */
		public bool ClsNumPropChanged()
		{
			return false;
		}

		/** 设置死亡标志
        @param   
        @param   
        @return  
        */
		public void SetDieFlag()
		{
			m_bDieFlag = true;
		}

		/** 处理多占位
        @param   
        @param   
        @return  
        */
		public void DisposeMultiBlock(bool bSetBlock)
		{
			
		}

		/**
        @purpose          : 定时器触发后回调,你可以在这里编写处理代码
        @param	 dwTimerID: 定时器ID,用于区分是哪个定时器
        @return		      : empty
        */
		public void OnTimer(TimerInfo ti)
		{
			/*if ((uint)EMETimerID.ETimerID_RemedyBlock != ti.timerId)
			{
				return;
			}*/

			DisposeMultiBlock(true);
		}

		// 创建显示层实体
		public bool CreateView()
		{
			//TRACE.ErrorLn("pos:" + m_Position);
			// 不是飞行模式就贴地
			//if ((m_pSchemeInfo.nBehaveStrategy & (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_Fly) != (int)EMEMonster_BehaveStrategy.EMonster_BehaveStrategy_Fly)
			//{
			//    //怪物服务器和客户端导航网格高度不一致
			//    float fHeiht = GlobalGame.Instance.NavigationManager.GetHeight(m_Position);
			//    if ((fHeiht > -10000) && Mathf.Abs(m_Position.y - fHeiht) < 5)
			//    {
			//        m_Position.y = fHeiht;
			//    }
			//}
			//TRACE.ErrorLn("pos1:" + m_Position);
			EntityViewItem item = GHelp.GetObjectItemEx<EntityViewItem>();
			if (!GetBasicViewInfo(ref item))
			{
				TRACE.ErrorLn("Monster::CreateView getBasicViewInfo failed!");
				return false;
			}
			//角度
			//item.Angle = 0;// GetNumProp((uint)EMCREATURE_PROP.CREATURE_MONSTER_ANGLE);

			// 创建EntityView
			GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_CREATE_ENTRY, 0, "", item);
			m_nEntityViewID = item.EntityViewID;
			if (m_nEntityViewID == 0)
			{
				TRACE.ErrorLn("Monster::CreateView invalid viewID=" + m_uid);
				return false;
			}

			// 设置出生坐标
			cmd_creature_rigidbody_sync data = GHelp.GetObjectItem<cmd_creature_rigidbody_sync>();
			data.bNotGround = false;
			data.nEntityID = GetEntityViewID();
			data.fPosition_x = m_Position.x;
            data.fPosition_y = m_Position.y;
			data.fPosition_z = m_Position.z;
			GHelp.sendEntityCommand(GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_SYNC_POS, 0, "", data);

			//TRACE.WarningLn("&&&BuildMonster:" + GetName() + " EntityID:" + GetEntityViewID()+" time:"+Api.GetTickCount());
			// 加载皮肤模型
			GHelp.ChangeEntitiyPart(GetEntityViewID(), EntityParts.EntityPart_Body, item.nSkinID);

			//TRACE.TraceLn("createView id=" + m_uid);
			GHelp.RecycleObjectItemEx<EntityViewItem>(item);
			return true;
		}

		public bool GetBasicViewInfo(ref EntityViewItem item)
		{
			Model.Types.Item modelItem = GHelp.GetModelItem(m_entitiyType, m_configID);
			/*SMonsterSchemeInfo pScheme = getMonsterScheme();
			if (null == pScheme)
			{
				TRACE.ErrorLn("Monster::GetBasicViewInfo Don't find the monster[" + m_szName + "] config data!");
				return false;
			}*/

			item.EntityViewID = 0;
			item.UID = m_uid;
			m_nSkinID = item.nSkinID = modelItem.Id;
			item.EntityType = (byte)m_entitiyType;
			item.ConfigID = m_configID;

			item.byIsHero = 0;

			item.szName = m_szName;
			item.nameColor = m_nameColor;
			item.Angle = GetNumProp((int)eEntityProp.EEntityAngle);
			item.fMoveSpeed = GetMoveSpeed();
			item.EntityFactoryConifgID = m_entityFactoryConfigID;
			return true;
		}

		public IMessage GetConfigInfo()
		{
			return GHelp.GetConfigItem(m_entitiyType, m_configID);
		}

		public int GetModelID()
		{
			return m_nSkinID;
		}

		public int GetConfigID()
        {
			return m_configID;
		}
		//获得阵营
		public int GetCamp()
		{
			return GetNumProp((uint)eEntityProp.EEntityCamp);
		}

		public void sendCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
		{
			// 部件接收命令
			for (int id = 0; id < (int)EMENTITYPART.ENTITYPART_ENTITY_MAXID; ++id)
			{
				if (m_pEntityPart[id] != null)
				{
					if (m_pEntityPart[id].onCommand(cmdid, nParam, strParam, ptrParam))
					{
						return;
					}
				}
			}
		}
	}
}
