﻿using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Client
{
    public class SceneInfoClient : IEventExecuteSink, ISceneInfoClient
    {
        public EMModuleLoadState ModuleLoadState { get; set; }

        public string ModuleName { get; set; }

        public float Progress { get; set; }

        public SceneInfoClient()
        {
        }

        public bool Create()
        {
            GHelp.GetEventEngine().Subscibe(this, DGlobalEvent.EVENT_SCENE_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "SceneInfoClient");
            return true;
        }

        public void Release()
        {
            GHelp.GetEventEngine().UnSubscibe(this, DGlobalEvent.EVENT_SCENE_LOAD_FINISH, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            if (wEventID == DGlobalEvent.EVENT_SCENE_LOAD_FINISH)
            {
                if (pContext == null)
                {
                    return;
                }
            }
        }

        public void Update()
        {
        }

        public void FixedUpdate()
        {
        }

        public void LateUpdate()
        {
        }
    }
}
