﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8EF6822C-2C91-44C1-AA10-6BE6422EFFE4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Start</RootNamespace>
    <AssemblyName>Start</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG;UNITY_EDITOR;ENABLE_PROFILER;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseAndroid|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG;UNITY_ANDROID;</DefineConstants>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <WarningLevel>4</WarningLevel>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleaseIos|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG;UNITY_IPHONE;UNITY_IOS</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'ReleasePc|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\Bin\Assets\Scripts\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <DebugType>portable</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Basic, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Assets\Scripts\Basic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Client, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Client\SeventyYears\Assets\Scripts\Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Common, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Assets\Scripts\Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Haptic, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Assets\Scripts\Haptic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Render, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Client\SeventyYears\Assets\Scripts\Render.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="UI, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Bin\Assets\Scripts\UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>..\UnityDlls\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>..\UnityDlls\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine">
      <HintPath>..\UnityDlls\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>..\UnityDlls\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>..\UnityDlls\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>..\UnityDlls\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>..\UnityDlls\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>..\UnityDlls\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\UnityDlls\UnityEngine.JSONSerializeModule.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>..\UnityDlls\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>..\UnityDlls\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssemblyInfo.cs" />
    <Compile Include="CBoot.cs" />
    <Compile Include="CGame.cs" />
    <Compile Include="GameClient.cs" />
    <Compile Include="GameLoader.cs" />
    <Compile Include="GameState\GameStateCreate.cs" />
    <Compile Include="GameState\GameStateGame.cs" />
    <Compile Include="GameState\GameStateInit.cs" />
    <Compile Include="GameState\GameStateLogin.cs" />
    <Compile Include="LoadBootManager.cs" />
    <Compile Include="StateSwitchMgr.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="obj\Debug\TempPE\" />
    <Folder Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="obj\Debug\DesignTimeResolveAssemblyReferencesInput.cache" />
    <None Include="obj\Debug\Start.csproj.CoreCompileInputs.cache" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="obj\Debug\Start.csproj.FileListAbsolute.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>