﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandOperateUI : IHandleCommand, IEventExecuteSink
    {
        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;
        private GameObject target;
        private int operateUIKind;
        private string text;
        private bool m_isOver;
        private List<IHandleCommand> m_others;
        public COpHandleCommandOperateUI(SOpHandleCommand_OperateUI data)
        {
            target = data.target;
            operateUIKind = data.operateUIKind;
            text = data.text;
            m_isPlay = false;
            m_isOver = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpOpreateUI;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)ViewLogicDef.EVENT_OVERCLICK_OPERATEUI:
                    {
                        GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)ViewLogicDef.EVENT_OVERCLICK_OPERATEUI, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                        m_isOver = true;
                    }
                    break;
                default:
                    break;
            }
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isOver = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                switch (operateUIKind)
                {
                    case 1:///点击
                        GHelp.FireExecute((ushort)ViewLogicDef.EVENT_ADDCLICK_OPERATEUI, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, target);
                        GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)ViewLogicDef.EVENT_OVERCLICK_OPERATEUI, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
                        break;
                    case 2:///设置文本
                        cmd_OperateUISetText cmd = new cmd_OperateUISetText(target, text);
                        GHelp.FireExecute((ushort)ViewLogicDef.EVENT_SETTEXT_OPERATEUI, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd);
                        m_isOver = true;
                        break;
                }
               
                m_isPlay = true;
            }

            if (m_isOver)
            {
                return true;
            }
            return false;
        }

        public void update()
        {
        }
    }
}
