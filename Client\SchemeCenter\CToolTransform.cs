﻿using game.scene;
using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using static GLib.Client.JLSDKDev;

namespace GLib.Client
{
    public struct SToolTransform
    {
        //移动目标
        public Vector3 vPos;
        /// <summary>
        /// 移动角度
        /// </summary>
        public Vector3 vEul;
        /// 移动角度
        /// </summary>
        public Vector3 vScale;
    };
    public class CToolTransform : ISchemeNode, IToolTransform
    {
        public const string strPath = "ToolTransform";
        private Dictionary<int, Dictionary<int, ToolTransform>> m_TaskInfoById;
        Dictionary<int, ToolTransform> datas;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Action<Dictionary<int, ToolTransform>> m_action;
        private int m_courseId;
        public CToolTransform()
        {
            m_TaskInfoById = new Dictionary<int, Dictionary<int, ToolTransform>>();
        }
        ~CToolTransform()
        {
        }
        public bool Create()
        {
            //if (!LoadScheme())
            //{
            //    return false;
            //}
            return true;
        }
        public bool LoadScheme(int CourseId, Action<Dictionary<int, ToolTransform>> action = null)
        {
            m_action = action;
            m_courseId = CourseId;
            if (!m_TaskInfoById.ContainsKey(CourseId))
            {
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                datas = new Dictionary<int, ToolTransform>();
                m_TaskInfoById.TryGetValue(CourseId, out datas);
                m_action?.Invoke(datas);
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            m_TaskInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, ToolTransform>();
                m_TaskInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                ToolTransform toolTransform = new ToolTransform();

                toolTransform.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                toolTransform.ChooseID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                toolTransform.ToolID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                toolTransform.ToolName = pCSVReader.GetString(nRow, tmp_col++, "");
                toolTransform.Pos = pCSVReader.GetString(nRow, tmp_col++, "");
                toolTransform.Rotation = pCSVReader.GetString(nRow, tmp_col++, "");
                toolTransform.Scale = pCSVReader.GetString(nRow, tmp_col++, "");
                datas.Add(toolTransform.ID, toolTransform);
            }
            m_action?.Invoke(datas);
            return true;
        }
        public void Release()
        {
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
        }

     

        public ToolTransform GetToolTransformByID(int tID)
        {
            ToolTransform toolTransform = null;
            datas = new Dictionary<int, ToolTransform>();
            if (datas == null)
            {
                return null;
            }
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            datas.TryGetValue(tID, out toolTransform);
            return toolTransform;
        }      
    }
}
