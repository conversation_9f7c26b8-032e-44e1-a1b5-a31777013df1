﻿//#define Update
/// <summary>
/// CBoot
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 启动器，过渡使用暂时用于进入游戏<br/>
/// </remarks>
using game.proto;
using GLib.Common;
using GLib.Render;
using System;
using UnityEngine;
using System.Collections;
using GLib.UI;

namespace GLib.Boot
{
    public class CBoot : MonoBehaviourEX<CBoot>, IBoot
    {
        public override void Awake()
        {
            base.Awake();

#if Update
           
                Screen.sleepTimeout = SleepTimeout.NeverSleep;

                DontDestroyOnLoad(gameObject);

                gameObject.AddComponent<Dependence>();

                ResourceConfigManager.Instance.Create();

                m_GlobalBoot = new CGlobalBoot();
                GameObject globalManager = GameObject.Find("_Manager(Clone)");
                if (!m_GlobalBoot.Create(globalManager))
                {
                    return;
                }

                StartCoroutine(CreateLauncher());
#else
            {
                Screen.sleepTimeout = SleepTimeout.NeverSleep;
            }
#endif
        }

        private void Start()
        {

            LoadBootManager.Instance.m_CBootDateTime = DateTime.Now;
            
#if !Update
            {
                UIManager.Instance.CreateAsyncWindow(WindowModel.WaitingWin, (object parentObj, object currentObj) =>
                {
                    gameObject.AddComponent<CGame>();
                    (currentObj as GameObject).GetComponent<UIWindow>().Show();
                    //StartCoroutine(Temp((currentObj as GameObject)));
                    CreatePlatform();
                    TRACE.TraceLn(string.Format("进入到LoadBoot的时间：{0}，进入到CGame的时间：{1}，进入到CBoot的时间：{2}", LoadBootManager.Instance.m_StartDateTime, LoadBootManager.Instance.m_CGameDateTime, LoadBootManager.Instance.m_CBootDateTime));
                });
            }
#endif
        }

        IEnumerator  Temp(GameObject cur)
        {
            yield return new WaitForSeconds(1.0f);

            cur.GetComponent<RectTransform>().localPosition = new Vector3(61.2f, 3.74f, 93.1f);
        }

        /// <summary>
        /// ??????????
        /// </summary>
        private IEnumerator CreateLauncher()
        {
            TRACE.TraceLn("CBoot CreateLauncher.....");

            yield return null;
#if Update
            {
                IUpdateEngine pUpdateEngine = GHelp.GetUpdateEngine();
                if (pUpdateEngine == null)
                {
                    yield break;
                }

                while (!pUpdateEngine.GetInitFinish())
                {
                    yield return null;
                }

                TRACE.TraceLn("CBoot UpdateEngine InitFinish.....");


                gameObject.AddComponent<UpdateUIManager>();
                UpdateUIManager.Instance.Init();
                UpdateUIManager.Instance.GetUpdateWidget().Init(this);

                TRACE.TraceLn("CBoot Set_Update_CallBack.....");

                Api.Set_Update_CallBack(Update_CallBack);
            }
#endif
        }

        /// <summary>
        /// ????????
        /// </summary>
        public void CloseLauncher()
        {
            TRACE.TraceLn("CBoot CloseLauncher.....");
#if Update
            {
                UIManager.Instance.CreateAsyncWindow(WindowModel.MeunNavigationWindow, (object parentObj, object currentObj) =>
                {
                    gameObject.AddComponent<CGame>();
                    (currentObj as GameObject).GetComponent<UIWindow>().Show();
                });
            }
#else
            {
                gameObject.AddComponent<CGame>();
            }
#endif
        }

        public void Update()
        {
#if Update
            {
                CallbackDispatcher.Instance.DispatchCallback(100);
            }
#endif
        }

        public void Update_CallBack()
        {
            TRACE.TraceLn("CBoot Update_CallBack.....");
#if Update
            {
                IUpdateEngine pUpdateEngine = GHelp.GetUpdateEngine();
                if (pUpdateEngine != null)
                {
                    pUpdateEngine.SetProgress(100);
                }

                // ???UI??????
                UpdateUIManager.Instance.Release();

                GResources.UnloadUnusedAssets();

                UI.UpdateWidget UpdateWidget = UpdateUIManager.Instance.GetUpdateWidget();
                if (UpdateWidget != null)
                {
                    UpdateWidget.gameObject.SetActive(false);
                    Destroy(UpdateWidget.gameObject);
                }

                //Destroy(this);
            }
#endif
        }
        private void CreatePlatform()
        {
            switch (GHelp.GetSystemPlatform())
            {
                case SystemPlatform.PC:
                    GSpawnPool.Instance.GetItemAsyncToParent(SystemPlatformPrefabPath.PC, transform, (int key, int reqID, string path1, GameObject asset) =>
                    {
                        asset.SetActive(true);
                    });
                    break;
                case SystemPlatform.Pico:
                    GSpawnPool.Instance.GetItemAsyncToParent(SystemPlatformPrefabPath.Pico, transform, (int key, int reqID, string path1, GameObject asset) =>
                    {
                        asset.SetActive(true);
                    });
                    break;
                case SystemPlatform.Quest:
                    GSpawnPool.Instance.GetItemAsyncToParent(SystemPlatformPrefabPath.Quest, transform, (int key, int reqID, string path1, GameObject asset) =>
                    {
                        asset.SetActive(true);
                    });
                    break;
            }
        }

    }
}
