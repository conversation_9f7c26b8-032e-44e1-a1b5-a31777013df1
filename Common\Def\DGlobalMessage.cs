﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
    public class DGlobalMessage
    {
        public const int MSG_PROP_CREATEENTITY = 1;
        public const int MSG_PROP_DESTROYENTITY = 2;
        public const int MSG_PROP_BATCHCREATEENTITY = 3;
        public const int MSG_ACTION_ADDBUFF = 5;
        public const int MSG_ACTION_REMOVEBUFF = 6;
        public const int MSG_ACTION_DIE = 7;
        public const int MSG_ACTION_USE_SKILL = 8;//客户端通知服务器使用技能
        public const int MSG_ACTION_BREAK_SKILL = 9;//服务器或客户端通知自己中止施法
        /// 服务器通知客户端蓄气完毕(ZC)
		/// 上下文：无
		public const int MSG_ACTION_PREPARE_SKILL_OK = 10;
        /// 服务器通知客户端技能使用失败(ZC)
		/// 上下文：(SkillId sid, UID uidOp, UseSkillResult result)
		public const int MSG_ACTION_SKILL_FAILED = 11;
        // 3D移动消息
        public const int MSG_ACTION_3D_MOVE = 100;
        public const int MSG_ACTION_PREP3DMOVE = 101;	//客户端3D路径移动

        // 最大消息码
        public const int MSG_ACTION_MAXID = 200;
    }

    public struct SMsgActionAddBuff_SC
    {
        public UInt32 dwIndex;      // 序号
        public UInt32 dwBuffID;     // BUFF ID
        public UInt32 dwLevel;      // BUFF 等级
        public UInt32 dwTime;           // 时间长
        public byte bRandEffect;    // 是否有随机效果
        public UInt32 dwNerveFlashID;   // 经脉修改技能buff光效
    };

    public struct SMsgActionRemoveBuff_SC
    {
        public UInt32 dwIndex;      // 序号
    };
}
