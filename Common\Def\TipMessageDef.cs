﻿/// <summary>
/// TipMessageDef
/// </summary>
/// <remarks>
/// 2021/8/23 12:10:39: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    /// <summary>
    /// 提示信息基础类
    /// </summary>
    public class BaseElementDef
    {
        /// <summary>
        /// Id
        /// </summary>
        public string RecycleKeyID;
        /// <summary>
        ///提示内容
        /// </summary>
        public string ContentTxt;
        /// <summary>
        /// 字体颜色
        /// </summary>
        public Color TxtColor;
        /// <summary>
        /// 提示回调
        /// </summary>
        public Action OverBack;

    }
    /// <summary>
    /// 向下提示类
    /// </summary>
    public class NotificationElementDef : BaseElementDef
    {
        /// <summary>
        /// 持续时间
        /// </summary>
        public float duration;
        /// <summary>
        /// 释放时间
        /// </summary>
        public float displayTime;
        /// <summary>
        /// 淡出时间
        /// </summary>
        public float fadeoutTime;
    }
    public class NotificationStopTopElementDef : BaseElementDef
    {
        public EMFInfoIcon Icon = EMFInfoIcon.None;
    }
    /// <summary>
    /// 当前提示信息
    /// </summary>
    public class NowTipsInfo
    {
        /// <summary>
        /// 提示类型
        /// </summary>
        public uint eMInfoPos;
        /// <summary>
        /// 提示内容
        /// </summary>
        public string tipsText;
        /// <summary>
        /// 提示时间
        /// </summary>
        public DateTime tipDateTime;
    }
}
