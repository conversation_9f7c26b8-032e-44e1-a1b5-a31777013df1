﻿/// <summary>
/// CHandPoseCenter
/// </summary>
/// <remarks>
/// 2022.12.9: 创建. 谌安 <br/>
/// 手资势中心<br/>
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CHandPoseCenter : ISchemeNode, IHandPoseCenter
    {
        private const string HAPTIC_INFO = "HandPose";
        /// <summary>
        /// 主事件从ID存储
        /// </summary>
        private Dictionary<int, HandPoseDef> m_HandPoseByID;

        private Dictionary<string, HandPoseDef> m_HandPoseByModelName;

        public string SchemeName { get; set; }

        public EMSchemeState SchemeLoadState { get; set; }

        private float m_progress = 0.0f;
        public float Progress
        {
            get { return m_progress; }
            set { m_progress = value; }
        }

        public CHandPoseCenter()
        {
            m_HandPoseByID = new Dictionary<int, HandPoseDef>();
            m_HandPoseByModelName = new Dictionary<string, HandPoseDef>();
        }

        ~CHandPoseCenter()
        {
        }

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public bool LoadScheme()
        {
            string strPath = HAPTIC_INFO;
            Progress = 0.0f;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();

            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                HandPoseDef map = new HandPoseDef();

                map.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.matchingModelName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.leftPoseAnimation = pCSVReader.GetString(nRow, tmp_col++, "");
                String tmpInfo = "";

                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.leftPosition = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.leftPosition = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }
                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.leftRoatate = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.leftRoatate = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }


                map.rightPoseAnimation = pCSVReader.GetString(nRow, tmp_col++, "");

                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.rightPosition = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.rightPosition = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }
                tmpInfo = pCSVReader.GetString(nRow, tmp_col++, "");
                map.rightRoatate = Vector3.zero;
                if (!string.IsNullOrEmpty(tmpInfo))
                {
                    string[] tmp = tmpInfo.Split(';');
                    map.rightRoatate = new Vector3(float.Parse(tmp[0]), float.Parse(tmp[1]), float.Parse(tmp[2]));
                }
                m_HandPoseByID.Add(map.ID, map);

                if (!string.IsNullOrEmpty(map.matchingModelName))
                {
                    if (!m_HandPoseByModelName.ContainsKey(map.matchingModelName))
                    {
                        m_HandPoseByModelName.Add(map.matchingModelName, map);
                    }
                    else
                    {
                        TRACE.ErrorLn("手姿势 配置重复的匹配模型");
                    }
                }
            }
            Progress = 1.0f;
            return true;
        }

        public void Release()
        {
            m_HandPoseByID.Clear();
            m_HandPoseByID = null;
        }

        public HandPoseDef GetHandPoseByID(int toolID)
        {
            HandPoseDef info = null;

            m_HandPoseByID.TryGetValue(toolID, out info);

            return info;
        }

        public HandPoseDef GetHandPoseByModelName(string matchingModelName)
        {
            HandPoseDef info = null;

            m_HandPoseByModelName.TryGetValue(matchingModelName, out info);

            return info;
        }
    }
}
