﻿/// <summary>
/// PersonTankPart
/// </summary>
/// <remarks>
/// 2021.4.2: 创建. 谌安 <br/>
/// 人物实体载具功能控制部件<br/>
/// </remarks>

//#define OpenDebugInfo_PersonTankPart
using System;
using System.Runtime.InteropServices;
using GLib;
using GLib.Common;
using UID = System.Int64;
using UnityEngine;
using System.Collections.Generic;
using Google.Protobuf;
using Game.Entity;
using game.proto;

namespace GLib.Client
{
	public class CPersonTankPart : IPersonTankPart, IMessageExecuteSink, IMessageVoteSink
	{
		// 所属人物
		IPerson m_pMaster;

		// 所属载具实体
		ITank m_pTank;

		// 载具UID
		Int64 m_uidTank;
		/// <summary>
		/// 上次遥感移动的位置
		/// </summary>
		Vector3 m_LastJoystickPos = Vector3.zero;

		// 显示层的命令(true表示此部件处理,其他不处理,false交由其他部件继续处理)
		public bool onCommand(UInt32 cmdid, int nParam, string strParam, object ptrParam)
		{
			return false;
		}
		/** 
		@param   
		@param   
		@return  
		*/
		public CPersonTankPart()
		{
			// 所属人物
			m_pMaster = null;

			// 所属载具实体
			m_pTank = null;

			// 载具UID
			m_uidTank = 0;
		}

		/** 
		@param   
		@param   
		@return  
		*/
		~CPersonTankPart()
		{
			m_pMaster = null;

			// 所属载具实体
			m_pTank = null;

		}

		/** 释放,会释放内存
		@param   
		@param   
		@return  
		*/
		public void Release()
		{
			if (m_pMaster != null)
			{
				//取消订阅
				//m_pMaster.UnSubscibe(DGlobalMessage.MSG_ACTION_TANKPART_EVENT, (IMessageExecuteSink)(this));

				PutDownTank();

				m_pMaster = null;
			}

			// 所属载具实体
			m_pTank = null;

			// 载具UID
			m_uidTank = 0;
		}

        /// <summary>
        /// 还原,不释放对象，只将状态还原到创建时状态
        /// </summary>
        public void Restore()
        {
			Release();
        }

		/** 创建，重新启用，也会调此接口
		@param   
		@param   
		@return  
		*/
		public bool Create(IEntity pMaster, object pszData)
		{
			if (pMaster == null || !pMaster.GetEntityClass().IsPerson())
			{
				return false;
			}
			m_pMaster = (IPerson)pMaster;

#if OpenDebugInfo_PersonTankPart
			string szBuf = Api._NGT("CPersonTankPart->Create() 创建:玩家=") + m_pMaster.GetName();
			TRACE.TraceLn(szBuf);
#endif

			//订阅消息
			//m_pMaster.Subscibe(DGlobalMessage.MSG_ACTION_TANKPART_EVENT, (IMessageExecuteSink)(this), "CPersonTankPart->Create");


			return true;
		}

		/** 取是部件ID
		@param   
		@param   
		@return  
		*/
		public UInt32 GetPartID()
		{
			return (UInt32)EMENTITYPART.ENTITYPART_PERSON_TANK;
		}

		/** 消息
		@param   
		@param   
		@return  
		*/
		public int OnMessage(UInt32 dwMsgID, CPacketRecv pszMsg)
		{
			return 0;
		}

		/** 取是主人
		@param   
		@param   
		@return  
		*/
		public IEntity GetMaster()
		{
			return m_pMaster;
		}

		/** 激活部件
		@param   
		@param   
		@return  
		*/
		public bool Active(object pszContext)
		{
			return true;
		}

		/** 冻结部件
		@param   比如当生物转到坐下状态，就需冻结移动部件
		@param   
		@return  
		*/
		public bool Freeze(object pszContext)
		{
			return true;
		}

		/** 导入初始化数据
		@param   
		@param   
		@return  
		*/
		public bool ImportBuildContext(IMessage pszContext, int nLen)
		{
			return true;
		}

		/** 
		@param   dwMsgID ：消息ID
		@param   pszMsg ：消息结构
		@param   nLen ：消息长度
		@return  
		*/
		public void OnExecute(UInt32 dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			switch (dwMsgID)
			{
				// 发送人物载具事件到客户端
				/*case (UInt32)DGlobalMessage.MSG_ACTION_TANKPART_EVENT:
					{
						OnMsgGetEventData(dwMsgID, pGameMsgHead, pszMsg);
					}
					break;*/
				default:
					{
						TRACE.ErrorLn("人物实体载具功能控制部件尚有一个消息订阅了但未处理，msgid = " + dwMsgID);
					}
					break;
			}
		}


		/**
		@param   dwMsgID ：消息ID
		@param   pszMsg ：消息结构
		@param   nLen ：消息长度
		@return  返回false，表示截断
		*/
		public bool OnVote(UInt32 dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{
			bool bResult = true;
			switch (dwMsgID)
			{
				//移动
				case (UInt32)DGlobalMessage.MSG_ACTION_PREP3DMOVE:
					{
						bResult = OnVoteTankMove(dwMsgID, pGameMsgHead, pszMsg, 0);
					}
					break;
				default:
					{
						TRACE.ErrorLn("人物实体载具功能控制部件尚有一个投票消息订阅了但未处理，msgid = " + dwMsgID);
					}
					break;
			}

			return bResult;
		}

		/** 发送人物载具事件到客户端, 数据消息
		@param   dwMsgID ：消息ID
		@param   pszMsg ：消息结构
		@param   nLen ：消息长度
		@return  CPacketRecv
		*/
		public void OnMsgGetEventData(UInt32 dwMsgID, SGameMsgHead pGameMsgHead, CPacketRecv pszMsg)
		{

			if (pszMsg == null) // || nLen < sizeof(SMsgActionSendTankPartEvent_SC)
			{
				return;
			}

			switch (1)//pMsg.nEventType
			{
				case (byte)EMTankPartEventType.TANKPART_EVENT_UPDATE:	// 更新载具数据,人物创建时下发数据
					{

						// 载具UID
						m_uidTank = 0;// pMsg.uidTank;

						// 设定uid载具对象
						//SetTankEntity(m_uidTank);

						// 进入载具
						if (IsOnTank())
						{
							if (m_pTank != null)
							{
								
							}

						}
					}
					break;
				case (byte)EMTankPartEventType.TANKPART_EVENT_ENTER:	// 进入载具
					{

						// 载具UID
						//m_uidTank = 0;
						// 设定uid载具对象
						//SetTankEntity(m_uidTank);
					}
					break;
				case (byte)EMTankPartEventType.TANKPART_EVENT_EXIT:	// 载具出来
					{

#if OpenDebugInfo_PersonTankPart
						szBuf = string.Format(Api._NGT("事件:载具出来,载具UID={0},位置={1},载具名={2}"),
							m_uidTank, m_dwTankIndex, ((m_pTank != null) ? m_pTank.GetName() : "null"));
						TRACE.TraceLn(szBuf);
#endif
						// 设定uid载具对象
						//SetTankEntity(m_uidTank);

						// 载具出来
						//if (IsOnTank() && m_pTank != null)
						{
						}

						// 载具UID
						//m_uidTank = 0;
						// 所属载具实体
					}
					break;
				case (byte)EMTankPartEventType.TANKPART_EVENT_DRIVER:	// 驾驶事件
					{

#if OpenDebugInfo_PersonTankPart
						szBuf = string.Format(Api._NGT("事件:位置改变,载具UID={0},位置={1},载具名={2},新位置={3}"),
							m_uidTank, m_dwTankIndex, ((m_pTank != null) ? m_pTank.GetName() : "null"), pMsg.dwSpare);
						TRACE.TraceLn(szBuf);
#endif

						// 是主角
						//if (IsHero())
						{
							// TankControlClient 接收到数据,通知界面刷新载具乘员控制界面,同时刷新技能
							// TankToolsClient 接收到数据,通知界面刷新载具乘员快捷工具条

							/* 待翻译成c# 先注释原c++代码  已经翻译成下面的代码
							// 直接调用Lua函数
							CLuaParam param[2];
							param[0] = m_uidTank;			// 载具uid
							param[1] = m_dwTankIndex;		// 坐在载具的位置

							// 直接调用Lua函数
							gGlobalClient->getLuaEngine()->RunLuaFunction("TankForm_OnDriverTank", &param[0], sizeof(param) / sizeof(CLuaParam), null, 0);
							 */
							/*gamelogic_TankEnterInfo_C info = new gamelogic_TankEnterInfo_C();
							info.uidTank = m_uidTank;				// 载具uid
							info.dwTankIndex = m_dwTankIndex;		// 坐在载具的位置
							GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_TANK_DRIVERTANK, 0, "", info);*/

						}
						

					}
					break;
				default:
					{
					}
					break;
			}
		}

		/** 载具行走
		@param  POINT vPathList :目标点
		@return  成功否
		*/
		public bool DriverTankMove(List<Vector3> vPathList,byte bFlag,bool bClientMoveFlag = true)
		{

#if OpenDebugInfo_PersonTankPart
			string szBuf = string.Format(Api._NGT("DriverTankMove() 叫载具行走:在载具中={0},司机状态={1},目标点({2},{3})"), IsOnTank(), IsOnDriver(), ptTarget.x, ptTarget.y);
			TRACE.TraceLn(szBuf);
#endif

			bool bRet = false;
			int nPathLen = vPathList.Count;
			// 在载具中
			if (IsOnTank() && m_pTank != null)
			{
				// 司机状态
				if (IsOnDriver())
				{
					if (nPathLen < 1)
					{
						TRACE.ErrorLn("DriverTankMove() 叫载具行走失败， 原因：obuf.good()=false");
						return false;
					}
					
					{
						

						
						/*SGameMsgHead head = default(SGameMsgHead);
						head.SrcEndPoint = (byte)ENDPOINT.Appclient;
						head.DestEndPoint = (byte)ENDPOINT.Scene;
						head.wKeyModule = (ushort)MSG_MODULEID.Entity;
						head.wKeyAction = (ushort)DGlobalMessage.MSG_ACTION_PREP3DMOVE;

						CPacketSend packet = new CPacketSend();
						for (int i = 0; i < vPathList.Count; i++)
						{
							packet.Push<Vector3>(vPathList[i]);
						}
						byte[] dataByte = packet.GetByte();
						CPacketRecv recvPackage = new CPacketRecv();
						recvPackage.Init(dataByte);
						m_pTank.OnMessage((uint)DGlobalMessage.MSG_ACTION_PREP3DMOVE, head, recvPackage);*/
						if (bClientMoveFlag)
						{
							cmd_MovePos data = new cmd_MovePos();
							//data.nCount = (int)dwPathLen;
							data.bFlag = bFlag;
							data.fSendCmdTime = Time.realtimeSinceStartup;
							data.listPath = vPathList;

							GHelp.sendEntityCommand(m_pTank.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_MOVE_POS, 0, "", data);
						}


                        bRet = true;

					}
				}
			}

			return bRet;
		}

		
		/** 移动
		@param   dwMsgID ：消息ID
		@param   pszMsg ：消息结构
		@param   nLen ：消息长度
		@return  返回false，表示截断
		*/
		public bool OnVoteTankMove(UInt32 dwMsgID, SGameMsgHead pGameMsgHead, object pszMsg, int nLen)
		{
			return true;
		}

		/** 设定uid载具对象	
		@param	LONGLONG uidTank	: 载具UID
		@return	
		*/
		public void SetTankEntity(Int64 uidTank,ITank tank)
		{

			m_uidTank = uidTank;
			// 所属载具实体
			m_pTank = tank;
		}

		//////////////////////////////////////////////////////////////////


		/** 取得乘坐的载具实体,没有坐上为null
		@return	 ITank * pTank  :进入的载具
		*/
		public ITank GetTank()
		{
			return m_pTank;
		}

		/** 取得乘坐的载具UID
		@return	 LONGLONG :载具UID
		*/
		public Int64 GetTankUID()
		{
			return m_uidTank;
		}

		/** 是否乘坐状态
		@return	  bool 返回状态
		*/
		public bool IsOnTank()
		{
			if (m_uidTank == 0)
			{
				return false;
			}
			return true;
		}

		/** 是否司机状态
		@return	  bool 返回状态
		*/
		public bool IsOnDriver()
		{
			return true;

		}


		/** 是否是主角
		@return	  bool 返回状态
		*/
		public bool IsHero()
		{
			if (m_pMaster == GHelp.GetHero())
			{
				return true;
			}
			return false;
		}

		/** 退出当前载具
		@return	  bool 成功否
		*/
		public bool PutDownTank()
		{
			// 载具出来
			if (IsOnTank() && m_pTank != null)
			{

				// 载具UID
				m_uidTank = 0;
				// 所属载具实体
				m_pTank = null;

				return true;

			}
			return false;
		}

		/** 请求进入指定载具
		@param   LONGLONG uidTank: 载具UID
		@return	  bool 成功否
		*/
		public bool EnterTank(ICreature person, ITank tank)
		{


			/*STankSchemeInfo tankSchemeInfo = null;
			IEntity enity = GlobalGame.Instance.EntityClient.Get(uidTank);
			if (enity != null && enity.GetEntityClass().IsTank())
			{
				ITank tank = enity as ITank;
				tankSchemeInfo = tank.GetTankSchemeInfo();
			}
			bool bShowTip = false;
			if (tankSchemeInfo != null && (tankSchemeInfo.nOpinionFlag & (int)ETankOpinionFlag.TANK_OPINION_SHOW_TIPS) > 0)
			{
				bShowTip = true;
			}*/

			SetTankEntity(tank.GetUID(), tank);

			EnterTankContext context = GHelp.GetObjectItem<EnterTankContext>();
			context.TankID = (uint)tank.GetUID();
			context.EnterID = person.GetEntityViewID();
			GHelp.sendEntityCommand(tank.GetEntityViewID(), (int)EntityLogicDef.ENTITY_TOVIEW_ENTER_TANK, 0, "", context);

			return true;
		}

		/** 请求退出当前载具
		@return	  bool 成功否
		*/
		public bool ExitTank()
		{

#if OpenDebugInfo_PersonTankPart
			// 调式代码
			string szBuf = string.Format(Api._NGT("ExitTank() 请求退出当前载具: 载具UID={0},序号={1},驾驶={2}"), m_uidTank, m_dwTankIndex, m_bOnDriver);
			TRACE.TraceLn(szBuf);
#endif

			/*if (!IsOnTank())
			{
                GlobalGame.Instance.ChatClient.addSystemTips((uint)EMTipType.TipType_Operate, (uint)EMInfoPos.InfoPos_ActorUnder, (Api._GT("没在上面")));
				return false;
			}

			// 请求退出当前载具
			SendEventToSelf((byte)EMTankPartClientEventType.TANKPART_CLIENT_EXIT, 0, 0, 0);*/

			return true;
		}

		/// <summary>
		/// 遥感控制载具移动
		/// </summary>
		/// <param name="cmd">遥感命令</param>
		/// <returns></returns>
		public bool DriverTankMoveJoystick(SMsgActionJoystick3DMove_CS msgJoystickCmd)
		{

			bool bRet = false;
			if (IsOnTank() && m_pTank != null)
			{
				// 司机状态
				if (IsOnDriver())
				{
					if (msgJoystickCmd.bMoveCmd == (byte)EMJoystickCmd.Joystick_Cmd_MOVE || msgJoystickCmd.bMoveCmd == (byte)EMJoystickCmd.Joystick_Cmd_STOP_MOVE)
					{
						List<Vector3> pathList = new List<Vector3>();
						pathList.Add(m_LastJoystickPos);
						pathList.Add(m_pTank.GetPosition());
						byte bFlag = (byte)EM3DMoveFlag.EM3DMoveFlag_ServerFrameSyn;
						DriverTankMove(pathList, bFlag, false);
						SendNavMeshPathMessage(pathList, bFlag);
					}

					m_LastJoystickPos = m_pTank.GetPosition();
					bRet = true;
				}
			}

			return bRet;
		}

		/// <summary>
		/// 发送导航数据消息
		/// </summary>
		/// <param name="pathList"></param>
		private bool SendNavMeshPathMessage(List<Vector3> pathList, byte bFlag)
		{
			//首先判断pathList的大小,如果超过阈值，则分段发送
			if (pathList.Count > 200)
			{
				//todo
				TRACE.ErrorLn("发送移路径节点给服务器 nPathNode >" + 200 + ",发送失败");
				return false;
			}
			else
			{
				MCSyncPos_CS cs = new MCSyncPos_CS();

				string syncPos = string.Format("{0}:{1}:{2}", m_pTank.GetPosition().x, m_pTank.GetPosition().y, m_pTank.GetPosition().z);
				cs.Properties.Add("SyncPosition", syncPos);
				cs.TargetId = m_pTank.GetStrGUID();
				//回调的消息头
				SGameMsgHead head = new SGameMsgHead();
				head.SerialNumber = GHelp.GenSerialNumber();
				head.SrcEndPoint = (int)ENDPOINT.Appclient;
				head.DestEndPoint = (int)ENDPOINT.Scene;
				head.wKeyModule = (int)MSG_MODULEID.Entity;
				head.wKeyAction = (int)MessageCodesOfEntity.CodeMcsyncPosCs;

				//把消息头压进发送栈
				CPacketSend packet = new CPacketSend();
				packet.Push(head);
				//回调的协议

				//把协议压进发送栈
				packet.PushPB<MCSyncPos_CS>(cs);
				//发送
				GlobalGame.Instance.NetManager.SendMessage(packet);
				return true;
			}
		}

		public void OnDie(Entity_Die_SC pszMsg)
        {
        }
    }
}