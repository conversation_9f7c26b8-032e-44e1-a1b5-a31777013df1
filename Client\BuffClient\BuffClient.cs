﻿/// <summary>
/// CBuffClient
/// </summary>
/// <remarks>
/// 2021.4.22: 创建. 谌安 <br/>
/// BUFF客户端<br/>
/// </remarks>
using game.schemes;
using GLib.Common;

namespace GLib.Client
{
    public class CBuffClient : IBuffClient
    {
        // buff配置
	    CSchemeBuff				m_SchemeBuff;

        /// <summary>
        /// 模块名称(模块实现者不用设置)
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 异步模块的加载状态(注释:异步模块专用)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 异步模块加载的进度,范围(0.0f,1.0f)(注释:异步模块专用)
        /// </summary>
        public float Progress { get; set; }

        /** 
        @param   
        @param   
        @return  
        */
        public CBuffClient() //: m_FlashEnabled(true)
        {
            m_SchemeBuff = new CSchemeBuff();
        }

        /** 
        @param   
        @param   
        @return  
        */
        ~CBuffClient()
        {

        }

        /** 释放
        @param   
        @param   
        @param   
        @return  
        @note     释放所有资源，并且销毁此对像
        @warning 
        @retval buffer 
        */
        public void Release()
        {
	        // buff配置
	        m_SchemeBuff.Release();
        }

        /** 
        @param   
        @param   
        @return  
        */
        public bool Create()
        {
	        if(!m_SchemeBuff.Create())
	        {
		        return false;
	        }
	
	        return true;
        }

        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {

        }

        /// <summary>
        /// 每逻辑帧
        /// </summary>
        public void FixedUpdate()
        {

        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {

        }

        /** 取得BUFF配置信息
        @param   
        @param   
        @return  
        */
        public Buff.Types.Item GetBuffSchemeInfo(uint dwBuffID, uint dwLevel = 1)
        {
			return m_SchemeBuff.GetBuffSchemeInfo(dwBuffID, dwLevel);
        }

        /** 取得BUFF配置信息
        @param   
        @param   
        @return  
        */
        public Buff.Types.Item GetBuffSchemeInfo(uint dwBuffKey)
        {
            return m_SchemeBuff.GetBuffSchemeInfo(dwBuffKey);
        }

        //LNID liaoxc
        /** 取得buff信息中效果ID
        @param   
        @param   
        @return  
        */
        public uint GetBuffSchemeInfoEffectId(int nBuffID, int nLevel)
        {
            Buff.Types.Item pSchemeInfo = GetBuffSchemeInfo((uint)nBuffID, (uint)nLevel);
	        if(pSchemeInfo == null)
	        {
				TRACE.ErrorLn("BuffClient::GetBuffSchemeInfoEffectId 获取效果ID失败 nBuffID=" + nBuffID);
		        return 0;
	        }

            

           /* foreach (SBuffSchemeInfo.SLevelInfo info in pSchemeInfo.listLevelInfo)
            {
				if (info.dwLevel == nLevel && info.listEffect != null && info.listEffect.Count > 0)
                {
                    return info.listEffect[0];
                }
            }*/

	        return 0;

        }

        /** 给实体创建buff部件，重新new的部件才调此方法。如果是回收的，则不需要
        @param   
        @param   
        @param   
        @return   成功: 非NULL,失败: NULL
        @note     
        @warning 
        @retval buffer 
        */
        public IBuffPart CreatePart()
        {
	        CBuffPart pBuffPart = new CBuffPart();

	        return pBuffPart;
        }

        /** 取得某个buff的描述
        @param   
        @param   
        @return  
        */
        public string GetBuffDesc(int nBuffID, int nLevel)
        {
            Buff.Types.Item pSchemeInfo = GetBuffSchemeInfo((uint)nBuffID, (uint)nLevel);
	        if(pSchemeInfo == null)
	        {
				TRACE.ErrorLn("BuffClient::GetBuffDesc 获取效果ID失败 nBuffID=" + nBuffID);
		        return null;
	        }

	        return pSchemeInfo.Description;
        }

        /** 取得buff名字
        @param   
        @param   
        @return  
        */
        public string GetBuffName(uint dwBuffID)
        {
            Buff.Types.Item pSchemeInfo = GetBuffSchemeInfo(dwBuffID);
	        if(pSchemeInfo == null)
	        {
				TRACE.ErrorLn("BuffClient::GetBuffDesc 获取效果ID失败 nBuffID=" + dwBuffID);
		        return null;
	        }

	        return pSchemeInfo.Name;
        }
    };
}
