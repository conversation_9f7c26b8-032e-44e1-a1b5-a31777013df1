﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandStateChange : IHandleCommand
    {
                                          // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private int m_stateKind = 0;
        private int m_iconID;
        private List<IHandleCommand> m_others;
        public COpHandleCommandStateChange(SOpHandleCommand_StateChange data)
        {
            m_stateKind = data.isLeftState;
            m_iconID = data.iconID;
            m_isPlay = false;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpMove;
        }

        public void OnPause()
        {
            
        }

        public void release()
        {
            m_isPlay = false;
        }
        
        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }
            //0：右手 1:左手     2:左手默认状态  3：右手默认状态  4：带手套状态  5：不带手套状态
            switch (m_stateKind)
            {
                case 0:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_TOOL_RIGHTHAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, m_iconID);
                    }
                    break;
                case 1:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_TOOL_LEFTHAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, m_iconID);
                    }
                    break;
                case 2:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_NONE_RIGHTHAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
                    }
                    break;
                case 3:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_NONE_LEFTHAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
                    }
                    break;
                case 4:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.Event_GLOVE_HAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, 20017);
                    }
                    break;
                case 5:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.EVENT_DEFAULT_HAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, null);
                    }
                    break;
                case 6:
                    {
                        GlobalGame.Instance.EventEngine.FireExecute((ushort)ViewLogicDef.Event_GLOVE_HAND, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, m_iconID);
                    }
                    break;
            }
            return true;
        }

        public void update()
        {
        }
    }
}
