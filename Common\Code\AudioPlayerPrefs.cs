﻿/// <summary>
/// ConfigControlDef
/// </summary>
/// <remarks>
/// 2019.7.31: 创建. 谌安 <br/>
/// 音效数据保存类 <br/>
/// </remarks>

using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace GLib.Common
{
    //控制全局数据的设置...
    public static class AudioPlayerPrefs
    {
        private static bool _musicEnabled = true;
        private static bool _soundEnabled = true;
        private static float _soundVolume = 1;
        private static float _musicVolume=1;

        public static float fadeSoundMultiplay = 1;
        public static float fadeMusicMultiply = 1;
        //这些估计是下次使用使用时调用的数据...

        public static bool soundEnabled {
            get {
                //return true;
                return _soundEnabled;
            }
            set {
                _soundEnabled = value;
                int iWrite = value ? 1 : 0;
                PlayerPrefs.SetInt(ConfigControlDef.ConfigOptionBool.SoundOn.ToString(), iWrite);
            }
        }
        
        public static bool musicEnabled
        {
            get
            {
                //return true;
                return _musicEnabled;
            }
            set
            {
                _musicEnabled = value;
                int iWrite = value ? 1 : 0;
                PlayerPrefs.SetInt(ConfigControlDef.ConfigOptionBool.MusicOn.ToString(), iWrite);
            }
        }
        public static float musicVolume
        {
            get
            {
                return _musicVolume;
            }
            set
            {
                _musicVolume = Mathf.Clamp01(value);
            }
        }
        //设置音量
        public static float soundVolume
        {
            get
            {
                return _soundVolume;
            }
            set
            {
                _soundVolume = Mathf.Clamp01(value);
            }
        }
       
    }
}
