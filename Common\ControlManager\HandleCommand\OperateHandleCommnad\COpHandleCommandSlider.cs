﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandSlider : IHandleCommand, IEventExecuteSink
    {
        // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        bool m_isPlay = false;

        private int taskId;
        private float orginValue;
        private float targetValue;
        private float offsetValue = 0.1f;
        private string text;
        private GameObject m_target;
        private SOpHandle_RunInstance runInstance;
        private Vector3 vectorOffset;
        private Vector3 uiPos;
        private bool m_isOver;


        private List<IHandleCommand> m_others;
        public COpHandleCommandSlider(SOpHandleCommand_Slider data)
        {
            m_target = data.target;
            runInstance = data.runInstance;
            vectorOffset = data.vectorOffset;
            taskId = data.taskId;
            orginValue = data.originValue;
            targetValue = data.targetValue;
            offsetValue = data.offsetValue;
            text = data.text;
            m_isPlay = false;
            m_isOver = false;
            m_others = data.otherCommand;
            uiPos = data.uiPos;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpSlider;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            switch (wEventID)
            {
                case (ushort)ViewLogicDef.EVENT_SLIDERWINDOW_OVERBTNCLICK:
                    {
                        float value = (float)pContext;
                        if(Math.Abs(value - targetValue) < offsetValue)
                        {
                            OperateEndStatus(true);
                        }
                        else
                        {
                            OperateEndStatus(false);
                        }
                        GlobalGame.Instance.EventEngine.UnSubscibe(this, (ushort)ViewLogicDef.EVENT_SLIDERWINDOW_OVERBTNCLICK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0);
                        m_isOver = true;
                    }
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 根据目标值判断是否成功
        /// </summary>
        /// <param name="isSuccess"></param>
        private void OperateEndStatus(bool isSuccess)
        {
            OverOperateHandle overOperate = new OverOperateHandle();
            overOperate.taskId = taskId;
            overOperate.isSuccess = isSuccess;
            GHelp.FireExecute((ushort)ViewLogicDef.EVENT_OVER_OPERATEENDSTATUS, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, overOperate);
        }

        public void OnPause()
        {

        }

        public void release()
        {
            m_isPlay = false;
            m_isOver = false;
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                GlobalGame.Instance.EventEngine.Subscibe(this, (ushort)ViewLogicDef.EVENT_SLIDERWINDOW_OVERBTNCLICK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
                cmd_SliderWindow cmd_Help = new cmd_SliderWindow();
                cmd_Help.wModel = WindowModel.SliderWindow;
                cmd_Help.aCommandState = AsyncCommandState.CreateCommmand;
                cmd_Help.originValue = orginValue;
                cmd_Help.targetValue = targetValue;
                cmd_Help.text = text;
                cmd_Help.target = m_target;
                cmd_Help.vectorOffset = vectorOffset;
                cmd_Help.uiPos = uiPos;
                GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CREATE_SLIDERWINDOW, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, cmd_Help);
                m_isPlay = true;
            }

            if (m_isOver)
            {
                return true;
            }
            return false;
        }

        public void update()
        {
        }
    }
}
