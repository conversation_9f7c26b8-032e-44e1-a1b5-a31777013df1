﻿/// <summary>
/// CScoreCondition
/// </summary>
/// <remarks>
/// 2023/2/21 9:33:27: 创建. 但传红 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CScoreCondition : ISchemeNode, IScoreCondition
    {
        public const string Task_Info = "ScoreCondition";
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }

        private Dictionary<int, Dictionary<int, ScoreConditionInfo>> m_ScoreInfoByID;
        private Action<Dictionary<int, ScoreConditionInfo>> m_curLoadFinishCallback;
        private int m_CourseId;

        public CScoreCondition()
        {
            m_ScoreInfoByID = new Dictionary<int, Dictionary<int, ScoreConditionInfo>>();
        }
        public bool Create()
        {
            return true;
        }
        public bool LoadScheme(int courseID, Action<Dictionary<int, ScoreConditionInfo>> action)
        {
            m_curLoadFinishCallback = action;
            m_CourseId = courseID;
            if (!m_ScoreInfoByID.ContainsKey(courseID))
            {
                m_ScoreInfoByID.Add(courseID, null);
                string strPath = "CourseCsv/" + courseID + "/" + Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, courseID);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<int, ScoreConditionInfo> datas = new Dictionary<int, ScoreConditionInfo>();
                m_ScoreInfoByID.TryGetValue(courseID, out datas);
                m_curLoadFinishCallback?.Invoke(datas);
            }
            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, ScoreConditionInfo> datas = null;
            m_ScoreInfoByID.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, ScoreConditionInfo>();
                m_ScoreInfoByID[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                ScoreConditionInfo scoreCondition = new ScoreConditionInfo();

                scoreCondition.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                scoreCondition.ConditionType = pCSVReader.GetInt(nRow, tmp_col++, 0);
                scoreCondition.ConditionDate = pCSVReader.GetString(nRow, tmp_col++, "");

                datas.Add(scoreCondition.ID, scoreCondition);
            }

            m_curLoadFinishCallback?.Invoke(datas);
            return true;
        }
        public void Release()
        {

        }
    }
}