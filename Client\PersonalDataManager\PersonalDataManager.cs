﻿/// <summary>
/// PersonalDataManager 个人中心管理类
/// </summary>
/// <remarks>
/// 2023/6/12 15:02:48: 创建. ysh <br/>
///  <br/>
/// </remarks>
using System;
using System.Collections.Generic;
using System.Text;
using GLib.Common;
using UnityEngine;
using GLib.LitJson;
using Object = UnityEngine.Object;

namespace GLib.Client
{
    public class PersonalDataManager : IPersonalDataMgr, HTTP_Response_Handler, IEventExecuteSink
    {
        /// <summary>
        /// 请求个人中心token
        /// </summary>
        private string requestToken;

        private PersonalInfo myPersonlInfo = null;
        private Dictionary<string, List<int>> dicAllTrainCount;                 // 根据年份保存训练数据
        private readonly string DownloadPageUrl = "http://www.yiqicmr.com";     // 下载地址

        private List<S2C_NoticeItem> listNotice;                                // 公告列表

        private readonly string LAST_LOGIN_TIME_KEY = "LastLoginTime";
        private string lastLoginTime;                                           // 上次登录时间

        #region 处理token过期时重新请求字段
        bool isNeedReRequestPersonalInfo = false;       // 获取到token后重新请求个人信息
        bool isNeedReRequestTrainCount = false;         // 获取到token后重新请求训练次数
        string curYear;                     // 请求训练年份s
        int reRequestCnt = 0;               // 重新请求当前次数
        int maxReCnt = 3;                   // 重新请求最大次数
        Texture2D headIconTexture2D; // 头像纹理
        #endregion

        #region IModule接口实现
        public string ModuleName { get; set; }
        public EMModuleLoadState ModuleLoadState { get; set; }
        public float Progress { get; set; }

        public bool Create()
        {
            dicAllTrainCount = new Dictionary<string, List<int>>();
            listNotice = new List<S2C_NoticeItem>();
            lastLoginTime = PlayerPrefs.GetString(LAST_LOGIN_TIME_KEY, DateTime.Now.ToString());

            return true;
        }

        public void Release()
        {
            myPersonlInfo = null;
            dicAllTrainCount.Clear();
            listNotice.Clear();
            requestToken = null;
            reRequestCnt = 0;
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {

        }

        public void Update()
        {

        }

        public void FixedUpdate()
        {

        }

        public void LateUpdate()
        {

        }
        #endregion

        #region HTTP_Response_Handler 接口实现
        public void OnResponse(byte[] pContent, uint dwLen, string url)
        {
        }

        public void OnError(uint dwError, string url)
        {
        }

        public void OnLocation(string new_url, string url)
        {
        }

        public bool OnDataStream(byte[] pData, uint dwLen, float fProgress, string url)
        {
            if (pData == null)
                return true;
            var data = Encoding.UTF8.GetString(pData);
            // token
            if (url.Contains(WebURL.GetToken))
            {
                TRACE.TraceLn("个人中心 请求Token回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_GetPersonalToken>(data);
                requestToken = info.data.token;
                SendGetNotice();
                GHelp.FireExecute((ushort)ViewLogicDef.EVENT_GET_PERSONAL_TOKEN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                if (isNeedReRequestPersonalInfo || isNeedReRequestTrainCount)
                    DoReRequest();
            }
            // 年份训练次数
            else if (url.Contains(WebURL.GetTrainCountByYear))
            {
                TRACE.TraceLn("个人中心 请求训练数据回复 data = " + data);
                var trainInfo = JsonMapper.ToObject<S2C_GetTrainData>(data);
                // 成功获取训练数据
                if (trainInfo.status == "success")
                {
                    reRequestCnt = 0;
                    var listTrainCount = SetTrainCount(trainInfo.data);
                    GHelp.FireExecute((ushort)ViewLogicDef.EVENT_PERSONALCENTER_LINECHARTDATA, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, listTrainCount);
                }
                else if (trainInfo.status == "failure")
                {
                    // 暂时处理，防止可能存在的无限请求token
                    if (reRequestCnt > maxReCnt)
                        return false;
                    SetReRequest(PersonalSendType.GetTrainByYear, true);
                    reRequestCnt++;
                    SendGetToken();
                }
            }
            // 修改密码
            else if (url.Contains(WebURL.ChangePassword))
            {
                TRACE.TraceLn("个人中心 请求修改密码回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_GetPersonalToken>(data);
                if (info.status == "success")
                {
                    GHelp.addSystemTipsWithIcon(Api.TR("修改密码成功"));
                    GHelp.FireExecute((ushort)ViewLogicDef.EVENT_CHANGE_PASSWARD_CB, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");

                }
            }
            // 意见反馈
            else if (url.Contains(WebURL.FeedbackUrl))
            {
                TRACE.TraceLn("个人中心 意见反馈回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_GetPersonalToken>(data);
                if (info.status == "success")
                {
                    GHelp.addSystemTipsWithIcon(Api.TR("意见反馈提交成功"));
                    GHelp.FireExecute((ushort)ViewLogicDef.EVENT_RECV_OPINIONFEEDBACK, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
                }
            }
            // 获取排行
            else if (url.Contains(WebURL.GetMockRank))
            {
                TRACE.TraceLn("个人中心 获取排行回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_GetMockRank>(data);
                OnRecvMockRank(info);
            }
            // 获取公告
            else if (url.Contains(WebURL.GetNotice))
            {
                TRACE.TraceLn("个人中心 获取公告回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_GetNotice>(data);
                OnRecvNotice(info);
            }
            else if (url.Contains(WebURL.GetPersonalInfo))
            {
                TRACE.TraceLn("个人中心 获取个人信息回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_GetPersonalInfo>(data);
                OnRecvPersonalInfo(info);
            }
            // 修改个人资料
            else if (CheckUrl(url, 0))
            {
                TRACE.TraceLn("个人中心 修改个人资料回复 data = " + data);
                var info = JsonMapper.ToObject<S2C_ChangeUserInfos>(data);
                if (info.status == "success")
                {
                    OnRecvChangePersonalInfoSuccess(info);
                }
            }
            // 获取头像
            else if (CheckUrl(url, 1))
            {
                TRACE.TraceLn("个人中心 获取头像回复 data = " + data);
                OnRecvGetHeadIcon(data, pData);
            }
            return true;
        }

        public EMHTTP_METHOD GetCurrentHttpMethod()
        {
            return EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST;
        }
        #endregion

        #region 后台交互
        /// <summary>
        /// 获取个人中心token
        /// </summary>
        public void SendGetToken()
        {
            var account = GHelp.AccountToJson(GlobalGame.Instance.LoginModule.UserName, GlobalGame.Instance.LoginModule.Password);
            byte[] databyte = Encoding.UTF8.GetBytes(account.ToString());
            TRACE.TraceLn("个人中心 请求个人中心token url = " + WebURL.GetToken);
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, WebURL.GetToken, this, null, "", databyte);
        }

        /// <summary>
        /// 获取头像
        /// </summary>
        public void SendGetHeadIcon()
        {
            var id = GlobalGame.Instance.GameSDK.GetUserInfo().userID;
            // /api/vr/student/avatar/{id}
            string webPath = string.Format("{0}/{1}", WebURL.GetHeadIcon, id);
            TRACE.TraceLn("个人中心 请求头像 webPath = " + webPath);
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this);
        }
        void OnRecvGetHeadIcon(string data, byte[] data1)
        {
            // 这里只保存byteData数据，不保存Texture2D数据到个人信息中

            // if (info.TexHeadIcon != null)
            // {
            //     GameObject.DestroyImmediate(info.TexHeadIcon, true);
            //     info.TexHeadIcon = null;
            // }
            // if (info.TexHeadIcon == null)
            //     info.TexHeadIcon = new Texture2D(2, 2);
            
            // 去除文件头 data:image/png;base64,
            byte[] byteData = null;
            if (data.StartsWith("data:"))
            {
                data = data.Substring(22);
                byteData = Convert.FromBase64String(data);
            }
            else
            {
                byteData = data1;
            }
            // info.TexHeadIcon = GHelp.ImageConversionLoadImage(info.TexHeadIcon, byteData);
            myPersonlInfo.HeadIconData = byteData;
            GHelp.FireExecute((ushort)ViewLogicDef.EVETN_UPDATE_PERSONAL_HEAD_ICON, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
        }

        /// <summary>
        /// 根据年份获取训练次数
        /// </summary>
        /// <param name="year"></param>
        public void SendGetTrainCountByYear(string year)
        {
            curYear = year;
            if (string.IsNullOrEmpty(requestToken))
            {
                SetReRequest(PersonalSendType.GetTrainByYear, true);
                return;
            }
            string webPath = string.Format("{0}/{1}/{2}", WebURL.GetTrainCountByYear, ResUtil.GetCurrentPlatformName(), year);
            TRACE.TraceLn(string.Format("个人中心 请求训练数据 webPath = {0}, token = {1}", webPath, requestToken));
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this, null, requestToken);
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        public void SendChangePassword(string pwd)
        {
            // 需要发送 userID, password, oldPassword
            StringBuilder sb = new StringBuilder();
            JsonWriter jsonWriter = new JsonWriter(sb);
            jsonWriter.WriteObjectStart();
            jsonWriter.WritePropertyName("userID");
            jsonWriter.Write(JLSDKDev.loginReturn.data.id);
            jsonWriter.WritePropertyName("password");
            jsonWriter.Write(pwd);
            jsonWriter.WritePropertyName("oldPassword");
            jsonWriter.Write(GlobalGame.Instance.LoginModule.Password);
            jsonWriter.WriteObjectEnd();
            byte[] databyte = Encoding.UTF8.GetBytes(sb.ToString());
            TRACE.TraceLn(string.Format("个人中心 修改密码数据 new = {0}, old = {1}, userId = {2}", pwd, GlobalGame.Instance.LoginModule.Password, JLSDKDev.loginReturn.data.id));
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, WebURL.ChangePassword, this, null, requestToken, databyte);
        }

        /// <summary>
        /// 修改个人信息
        /// </summary>
        /// <param name="headIcon"></param>
        /// <param name="gendar"></param>
        /// <param name="birthDay"></param>
        /// <param name="phone"></param>
        /// <param name="email"></param>
        public void SendChangePersonalInfo(byte[] headIcon, string gendar, string birthDay, string phone, string email)
        {
            saveBirthDay = birthDay;
            TRACE.TraceLn("个人中心 修改个人信息 头像 = " + headIcon + " 性别=" + gendar + " 生日=" + birthDay + " 手机=" + phone + " 邮箱=" + email);
            ProfileInfo info = new ProfileInfo();
            info.gender = gendar;
            info.birthday = birthDay;       // "2005-1-1";
            info.id = JLSDKDev.loginReturn.data.id;         // 学生id
            info.phoneNumber = phone;                       // 电话
            info.email = email;                             // 邮箱
            string e = JsonUtility.ToJson(info);
            string boundary = "";
            byte[] r = GHelp.GenHttpBoundary(headIcon, e, ref boundary, "avatar", "profile", "png", "HeadIcon");
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, WebURL.ChangeUserInfos, this, null, "", r, boundary);
        }
        string saveBirthDay;
        void OnRecvChangePersonalInfoSuccess(S2C_ChangeUserInfos data)
        {
            var info = GetPersonalInfo();
            info.BirthDay = saveBirthDay;
            var changeInfo = data.data;
            info.BirthDay = changeInfo.birthday;
            info.Gendar = changeInfo.gender;
            info.Phone = changeInfo.phoneNumber;
            info.Email = changeInfo.email;
            // 修改个人信息成功之后，头像需要重新请求一下以获取最新头像
            SendGetHeadIcon();
            GHelp.FireExecute((ushort)ViewLogicDef.EVETN_UPDATE_PERSONAL_DATA, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
        }

        /// <summary>
        /// 发送意见反馈
        /// </summary>
        /// <param name="suggestion">意见文本</param>
        /// <param name="listPhoto">上传的照片</param>
        public void SendFeedBack(string suggestion, List<byte[]> listPhoto)
        {
            TRACE.TraceLn(string.Format("个人中心 提交意见 = {0}，照片数量 = {1}", suggestion, listPhoto.Count));

            FeedBackInfo info = new FeedBackInfo();
            var userInfo = GlobalGame.Instance.GameSDK.GetUserInfo();
            info.nickname = userInfo.username;
            info.phoneNumber = userInfo.phoneNumber;
            //info.email = userInfo.email;
            info.feedback = suggestion;
            //info.filename = "";
            string e = JsonUtility.ToJson(info);
            string boundary = "";
            // 暂时只传一张图片
            var pic = listPhoto[0];
            byte[] r = GHelp.GenHttpBoundary(pic, e, ref boundary, "pic", "newFeed");
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB_POST, WebURL.FeedbackUrl, this, null, requestToken, r, boundary);
        }

        public void SendGetMockRank()
        {
            var info = GetPersonalInfo();
            var platform = ResUtil.GetCurrentPlatformName();
            platform = platform == "Android" || platform == "IOS" ? "Android" : "PC";
            string webPath = string.Format("{0}/{1}/{2}", WebURL.GetMockRank, info.StudentId, platform);
            TRACE.TraceLn(string.Format("个人中心 获得班级近7天的模考排行 webPath = {0}", webPath));
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this, null, requestToken);
        }
        void OnRecvMockRank(S2C_GetMockRank data)
        {
            if (data.status == "success")
            {
                var listData = new List<S2C_MockRankItem>();
                if (data.data != null && data.data.Count > 0)
                {
                    listData = data.data;
                    int rank = 1;
                    foreach (var item in listData)
                    {
                        item.rank = rank;
                        // 科目名称去掉“-移动版”这几个文字
                        item.projectName = item.projectName.Replace("-移动版", string.Empty);
                        rank++;
                    }
                }
                GHelp.FireExecute((ushort)ViewLogicDef.EVENT_GET_MOCKRANK_DATA, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, listData);
            }
        }

        public void SendGetNotice()
        {
            // 发送获取公告
            TRACE.TraceLn(string.Format("个人中心 获取公告 webPath = {0}, token = {1}", WebURL.GetNotice, requestToken));
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, WebURL.GetNotice, this, null, requestToken);
        }
        void OnRecvNotice(S2C_GetNotice data)
        {
            if (data.status == "success")
            {
                listNotice = data.data;
                GHelp.FireExecute((ushort)ViewLogicDef.EVENT_GET_NOTICE_DATA, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
            }
        }

        public void SendGetPersonalInfo()
        {
            if (string.IsNullOrEmpty(requestToken))
            {
                SetReRequest(PersonalSendType.GetPersonalInfo, true);
                return;
            }
            var info = GetPersonalInfo();
            string webPath = string.Format("{0}/{1}", WebURL.GetPersonalInfo, info.StudentId);
            TRACE.TraceLn(string.Format("个人中心 获取个人信息 webPath = {0}, token = {1}", webPath, requestToken));
            CHttp.Instance.Request(EMHTTP_METHOD.EHHTTP_METHOD_UNITYWEB, webPath, this, null, requestToken);
        }
        void OnRecvPersonalInfo(S2C_GetPersonalInfo data)
        {
            if (data.status == "success")
            {
                SetPersonalInfo(data.data);
            }
            GHelp.FireExecute((ushort)ViewLogicDef.EVETN_UPDATE_PERSONAL_DATA, (byte)EMSOURCE_TYPE.SOURCE_TYPE_UI, 0, "");
        }
        #endregion

        #region 共有方法
        public void InitPersonalInfo()
        {
            SaveLastLoginTime(DateTime.Now.ToString());
            InitMyPersonalInfo();
            SendGetToken();
            SendGetHeadIcon();
            SendGetPersonalInfo();
        }

        public List<int> GetTrainCountByYear(string year)
        {
            List<int> result;
            if (dicAllTrainCount.TryGetValue(year, out result))
            {
                return result;
            }
            return null;
        }

        public PersonalInfo GetPersonalInfo()
        {
            return myPersonlInfo;
        }

        public Texture2D GetHeadIconTexture2D()
        {
            var info = GetPersonalInfo();
            if (info?.HeadIconData == null)
            {
                return GetDefaultHeadIconTexture();
            }
            if(headIconTexture2D == null)
            {
                headIconTexture2D = new Texture2D(2, 2);
                headIconTexture2D.name = "PlayerHeadIcon";
            }
            return GHelp.ImageConversionLoadImage(headIconTexture2D, info?.HeadIconData);
        }

        public void UnloadHeadIcon()
        {
            if (headIconTexture2D != null)
            {
                Object.Destroy(headIconTexture2D);
                headIconTexture2D = null;
            }
        }

        public String GetRequestToken()
        {
            return requestToken;
        }

        /// <summary>
        /// 通过地址判断是否显示个人中心界面
        /// </summary>
        public bool IsShowPersonalCenterPage()
        {
            string url = DownloadPageUrl.Replace("http://", "").Replace("https://", "");
            return !WebURL.GetToken.Contains(url);
        }

        public string GetDownloadPageUrl()
        {
            return DownloadPageUrl;
        }

        public List<S2C_NoticeItem> GetNoticeList()
        {
            return listNotice;
        }

        public string GetLastLoginTime()
        {
            return lastLoginTime;
        }

        public void LogOut()
        {
            Release();
        }
        #endregion

        #region 私有方法

        void InitMyPersonalInfo()
        {
            var userInfo = GlobalGame.Instance.GameSDK.GetUserInfo();
            myPersonlInfo = new PersonalInfo();
            myPersonlInfo.StudentName = userInfo.username;
            myPersonlInfo.StudentId = userInfo.userID;
            myPersonlInfo.Phone = userInfo.phoneNumber;
            myPersonlInfo.HeadIconData = null;
        }
        void SetPersonalInfo(S2C_PersonalInfo_Detail detail)
        {
            var info = GetPersonalInfo();
            info.SchoolName = detail.school;
            info.Email = detail.email;
            info.Major = detail.profession;
            info.City = detail.city;
            info.BirthDay = detail.birthday;
            info.Gendar = detail.gender;
            info.Class = detail.dept.name;
            info.Grade = detail.sclass;
        }

        void SetReRequest(PersonalSendType type, bool value)
        {
            switch (type)
            {
                case PersonalSendType.All:
                    isNeedReRequestTrainCount = value;
                    isNeedReRequestPersonalInfo = value;
                    break;
                case PersonalSendType.GetTrainByYear:
                    isNeedReRequestTrainCount = value;
                    break;
                case PersonalSendType.GetPersonalInfo:
                    isNeedReRequestPersonalInfo = value;
                    break;
            }
        }

        void DoReRequest()
        {
            if (isNeedReRequestPersonalInfo)
            {
                SendGetPersonalInfo();
                SetReRequest(PersonalSendType.GetPersonalInfo, false);
            }

            if (isNeedReRequestTrainCount)
            {
                SendGetTrainCountByYear(curYear);
                SetReRequest(PersonalSendType.GetTrainByYear, false);
            }
        }

        List<int> SetTrainCount(List<List<object>> data)
        {
            List<int> listCnt = new List<int>();
            int monthCnt = 12;
            if (data.Count == 0)
            {
                for (int i = 0; i < monthCnt; i++)
                {
                    listCnt.Add(0);
                }
                return listCnt;
            }

            if(dicAllTrainCount.ContainsKey(curYear))
                dicAllTrainCount.Remove(curYear);

            // 限定当前年份最大月数为当前月数
            string nowYear = GHelp.GetTimeIntDataByTimeStamp(GHelp.GetCurTimeStamp()).year.ToString();
            if (nowYear == curYear)
            {
                var lastMonth = (string)data[data.Count - 1][0];
                monthCnt = int.Parse(lastMonth.Split('-')[1]);
            }
            for (int i = 0; i < monthCnt; i++)
            {
                listCnt.Add(0);
            }
            foreach (List<object> item in data)
            {
                string timeStr = (string)item[0];
                int month = int.Parse(timeStr.Split('-')[1]);
                int cnt = (int)item[1];
                listCnt[month - 1] = cnt;
            }
            dicAllTrainCount.Add(curYear, listCnt);
            return listCnt;
        }

        /// <summary>
        /// 主要用于检测string.Contain区分不出的情况
        /// </summary>
        /// <param name="url"></param>
        /// <param name="type">0:修改个人资料 1:获取头像</param>
        /// <returns></returns>
        bool CheckUrl(string url, int type)
        {
            if(type == 0)
            {
                return string.Equals(url, WebURL.ChangeUserInfos);
            }
            else if(type == 1)
            {
                var info = GetPersonalInfo();
                string targetUrl = string.Format("{0}/{1}", WebURL.GetHeadIcon, info.StudentId);
                return string.Equals(url, targetUrl);
            }
            return false;
        }

        Texture2D GetDefaultHeadIconTexture()
        {
            // var pInfo = GetPersonalInfo();
            // pInfo.TexHeadIcon = GResources.Load<Sprite>("UI/Common/Icon_Round_YoungMale").texture;
            return GResources.Load<Sprite>("UI/Common/Icon_Round_YoungMale").texture;
        }

        void SaveLastLoginTime(string time)
        {
            PlayerPrefs.SetString(LAST_LOGIN_TIME_KEY, time);
            PlayerPrefs.Save();
        }
        #endregion
    }
    [System.Serializable]
    public class ProfileInfo
    {
        public int id;
        public string birthday;
        public string email;
        public string gender;
        public string phoneNumber;
    }

    [System.Serializable]
    public class FeedBackInfo
    {
        public string nickname;
        public string phoneNumber;
        public string email;
        public string feedback;
        public string filename;
    }
}
