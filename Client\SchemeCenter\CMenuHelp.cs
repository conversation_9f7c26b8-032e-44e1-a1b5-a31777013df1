﻿/// <summary>
/// CMenuHelp
/// </summary>
/// <remarks>
/// 2023/3/17 : 创建. 吴俊宇 <br/>
///  <br/>
/// </remarks>

using GLib.Common;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Client
{
    public class CMenuHelp : ISchemeNode, IMenuHelp
    {
        public const string Task_Info = "MenuHelp";
        private Dictionary<int, Dictionary<int, MenuHelp>> m_TaskInfoById;
        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }
        public float Progress { get; set; }
        private Action<Dictionary<int, MenuHelp>> m_action;
        private int m_courseId;
        public CMenuHelp()
        {
            m_TaskInfoById = new Dictionary<int, Dictionary<int, MenuHelp>>();
        }
        ~CMenuHelp()
        {
        }
        public bool Create()
        {
            //if (!LoadScheme())
            //{
            //    return false;
            //}
            return true;
        }
        public bool LoadScheme(int CourseId, Action<Dictionary<int, MenuHelp>> action = null)
        {
            m_action = action;
            m_courseId = CourseId;
            if (!m_TaskInfoById.ContainsKey(CourseId))
            {
                string strPath = Task_Info;
                bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent, CourseId);
                if (!bResult)
                {
                    TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                    return false;
                }
            }
            else
            {
                Dictionary<int, MenuHelp> datas = new Dictionary<int, MenuHelp>();
                m_TaskInfoById.TryGetValue(CourseId, out datas);
                m_action?.Invoke(datas);
            }
            return true;
        }
        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }

            Int32 nRecordCount = pCSVReader.GetRecordCount();
            Dictionary<int, MenuHelp> datas = null;
            m_TaskInfoById.TryGetValue((int)pData, out datas);
            if (datas == null)
            {
                datas = new Dictionary<int, MenuHelp>();
                m_TaskInfoById[(int)pData] = datas;
            }
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                MenuHelp menuHelp = new MenuHelp();
                menuHelp.ID = pCSVReader.GetInt(nRow, tmp_col++, 0);
                menuHelp.MenuName = pCSVReader.GetString(nRow, tmp_col++, "");
                menuHelp.Content = pCSVReader.GetString(nRow, tmp_col++, "");               
                datas.Add(menuHelp.ID, menuHelp);
            }
            m_action?.Invoke(datas);
            return true;
        }
        public void Release()
        {
            m_TaskInfoById.Clear();
            m_TaskInfoById = null;
        }

        public MenuHelp GetMenuHelpByID(int tID)
        {
            MenuHelp info = null;
            Dictionary<int, MenuHelp> datas = new Dictionary<int, MenuHelp>();
            m_TaskInfoById.TryGetValue(m_courseId, out datas);
            datas.TryGetValue(tID, out info);
            return info;
        }
    }
}