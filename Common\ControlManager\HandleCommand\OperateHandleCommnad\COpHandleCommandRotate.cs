﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandRotate : IHandleCommand
    {
        private GameObject m_target;
        private Vector3 m_orgRotate;
        private Vector3 m_targetRotate;
        private Quaternion m_targetQua;
        private float rotateSpeed = 5.0f;
        private bool m_isLocal;

        private float rotateLimit = 0.01f;

        bool m_isEnd = false; // 是否不正确的执行完指令
        bool m_isPlay = false;

        private SOpHandle_RunInstance runInstance;
        private List<IHandleCommand> m_others;

        public COpHandleCommandRotate(SOpHandleCommand_Rotate data)
        {
            m_target = data.target;
            m_orgRotate = data.orgRotate;
            m_targetRotate = data.targetRotate;
            m_targetQua = Quaternion.Euler(data.targetRotate);
            rotateSpeed = data.speed;
            m_isLocal = data.isLocal;
            runInstance = data.runInstance;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpRotate;
        }

        public void OnPause()
        {

        }

        public void release()
        {

        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_target == null)
                {
                    Component t = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
            }
            if (m_target == null)
            {
                m_isEnd = true;
                return false;
            }
            if (!m_isPlay)
            {
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
                if (m_orgRotate.x > -9999)
                {
                    m_target.transform.localEulerAngles = m_orgRotate;
                }
            }
            var curRot = m_isLocal ? m_target.transform.localRotation : m_target.transform.rotation;
            float angle = Quaternion.Angle(curRot, m_targetQua);
            if (angle > rotateLimit)
            {
                if (m_isLocal)
                    m_target.transform.localRotation = Quaternion.Slerp(m_target.transform.localRotation, m_targetQua, Time.deltaTime * rotateSpeed);
                else
                    m_target.transform.rotation = Quaternion.Slerp(m_target.transform.rotation, m_targetQua, Time.deltaTime * rotateSpeed);
                return false;
            }
            return true;
        }

        public void update()
        {
        }
    }
}
