﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    public class COpHandleCommandShowAndHide : IHandleCommand
    {
                                          // 最小距离
        const float MOVE_STATE_MIN_DISTANCE = 0.001f;

        bool m_isEnd = false; // 是否不正确的执行完指令

        private GameObject m_target;
        private int m_isShow;
        private SOpHandle_RunInstance runInstance;
        private bool m_isPlay;
        private List<IHandleCommand> m_others;
        public COpHandleCommandShowAndHide(SOpHandleCommand_ShowAndHide data)
        {
            m_target = data.target;
            m_isShow = data.isShow;
            runInstance = data.runInstance;
            m_others = data.otherCommand;
        }

        public bool finish()
        {
            return m_isEnd;
        }

        public CommandsType GetCommandType()
        {
            return CommandsType.OperateProperty;
        }

        public EHandleCommandType GetTypeEX()
        {
            return EHandleCommandType.OpMove;
        }

        public void OnPause()
        {
            
        }

        public void release()
        {
        }

        public bool run()
        {
            if (!m_isPlay)
            {
                if (m_target == null)
                {
                    Component t  = OperateHandleCommandHelp.FindTarget<Component>(runInstance);
                    if (t != null)
                    {
                        m_target = t.gameObject;
                    }
                }
                OperateHandleCommandHelp.AppendCommand(m_others);
                m_isPlay = true;
            }
            if (m_target == null)
            {
                TRACE.ErrorLn("error target = null");
                m_isEnd = true;
                return false;
            }

            m_target.SetActive(m_isShow == 1 ? true : false);

            return true;
        }

        public void update()
        {
        }
    }
}
