﻿/// <summary>
/// CNetManager
/// </summary>
/// <remarks>
/// 2020.8.17: 创建. 谌安 <br/>
/// 网络管理<br/>
/// </remarks>
//#define OpenDebugInfo_Net

using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using System.Text;
using System.Runtime.InteropServices;
using GLib.Common;
using game.proto;
using Game.Messages;

namespace GLib.Client
{
    /// <summary>
    /// 网络模块引擎
    /// </summary>
    public class CNetManager : INetManager, INetStatuCallBack, IMessageHandler, ITimerHandler, IEventExecuteSink
    {
        //最大断线续完次数
        const Int32 MAX_RECONNECT_TIMES = 10;
        // 服务器超时时间 15s太短，有时候初始加载场景的时候会达到15s，导致进入场景就重连
        const Int32 PING_TIMEOUT_SECONDS = 30;
        // 断线续完超时时间
        const float RE_CONNECT_TIMEOUT_SEC = 60f;
        // 连接超时时间
        const float CONNECT_TIMEOUT_SEC = 16f;


        // 断线续完重连间隔 毫秒
        const int RE_CONNECT_INTERVAL = 3000;


        //超时重连标志
        bool m_bTimeOutReconnectFlag = false;
        //允许重联
        bool m_bAllowReconnect = true;
        //是否正在进行断线重连操作
        bool m_bReConnecting = false;
        //允许发送心跳包
        bool m_AllowHeartbeat = false;

        //短线重连操作时间 只针对已经进入到connected后的
        float m_fReConnectTime = 0.0f;
        // 网络延时
        UInt32 m_dwLatency = 0;
        // 网关主动断开连接的原因
        Int32 m_nLastCloseConReason = -1;
        // 回应Ping包
        bool m_bResponsePing = true;

        // 最后一次收到ping包的时间
        float m_fLastRecvPingTime = 0.0f;

        // 重试ping包
        // 1208外网版本测试发现，大部分断线续玩都是客户端检测到Ping超时主动断开的连接，所以现在新增一个ping补发机制.
        // 目的是尽量减少客户端主动断开连接的概率。
        bool m_bTryRePing = true;

        // 通知连接断开
        bool m_bDisconnectNotifyed = false;

        //断线重连判断
        bool m_bStartConnect = false;
        /// <summary>
        /// 连接状态
        /// </summary>
        enum ConnectState
        {
            Idle = 0,               //空闲
            Connect,                //连接网关
            ConnectTimeOut,         //连接超时
            Connected,              //连接成功
            ConnectedErr,           //连接出错
            Close,                  //关闭连接
            End,
            Max,
        }

        /// <summary>
        /// 发包状态
        /// </summary>
        enum SendState
        {
            Check,              //检测网络和发送队列
            Send,               //发包
            SendTimeOut,        //发包超时
            End,                //结束
            Max,
        }

        public static IConnectionManager NET_MANAGER = CConnectionManager.Create();

        // 连接器
        private CServerConnection m_ServerConnection = null;
        //连接状态
        private ConnectState m_connectState = ConnectState.Idle;
        //状态的进入次数
        //在进入idle状态时清零，idle状态不需要统计次数
        private int[] m_nConnectStateEnterNum = new int[(int)ConnectState.Max];
        //连接状态的进入时间
        private float[] m_fConnectStateTime = new float[(int)ConnectState.Max];
        //发包状态进入的时间
        private float[] m_fSendStateTime = new float[(int)SendState.Max];
        //发包状态
        private SendState m_sendState = SendState.Check;
        // 发送队列
        private List<CPacketSend> m_SendList = new List<CPacketSend>();
        // 消息处理器字典
        private Dictionary<MSG_MODULEID, IMessageHandler> m_MessageHandlerDic = new Dictionary<MSG_MODULEID, IMessageHandler>();
        //连接出错或主动断开标识
        private bool m_disConnectSelf = false;
        /// <summary>
        /// 开始连接网关
        /// </summary>
        private bool m_StartConnect = false;

        /// <summary>
        /// 正在连接标志
        /// </summary>
        private bool m_Connecting = false;

        /// <summary>
        /// 网关地址
        /// </summary>
        /// 
        private string m_strIp = "";
        /// <summary>
        /// 网关端口号
        /// </summary>
        private int m_nPort = 0;
        /// <summary>
        /// Sorketf标识，主要是用于和服务器起到一个项目关联对应，需要与服务端相同。
        /// </summary>
        private string m_MYAppName = "JLFrame";
        //最后发包的时间
        private float m_fLastSendDataTime = 0.0f;

        //连接成功标志
        private bool m_bConnectOK = false;

        // 断线续玩成功
        private bool m_bTryReConnectOK = false;
        //握手相关变量
        UInt16 m_nUDPPort = 0;

        //续完唯一编号
        UInt32 m_dwUCode = 0;

        //网络类型
        UInt32 m_dwNetType = 0;

        //可重连
        bool m_bCanReconnect = false;

        //重连次数
        Int32 m_dwReconnectTimes = (Int32)MAX_RECONNECT_TIMES;

        Int32 m_curReconnectCount = 0;
        // 发送缓存
        byte[] m_SendBuff = null;
        MemoryStream m_sendStream = null;
        //正在发送的消息队列
        List<CPacketSend> m_SendingPacket = new List<CPacketSend>();

        private bool m_bSwitchArea = false;             // 是否开始跨区
        //private SMsgLoginServer_AreaSwitchRes m_areaSwitchRes;
        int m_nSwitchGatewayNum;        // 切换网关次数

        bool m_bNotifyErr = false;

        // 延时重连定时器
        bool m_bDelayReConnectTimerFlag = false;
        //tcp连接成功标志
        float m_bServerConnectOKTime = 0.0f;

        // 最后错误代码
        private EMNetErrorCode m_nLastErrorCode = EMNetErrorCode.NetErrorCode_None;
        // 最后错误描述
        private string m_strLastError = "";
        //是否显示了重联UI
        private bool m_isShowReconnectWait = false;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 模块异步加载状态(异步模块)
        /// </summary>
        public EMModuleLoadState ModuleLoadState { get; set; }

        /// <summary>
        /// 模块异步加载的进度,范围(0.0f,1.0f)
        /// </summary>
        public float Progress { get; set; }

        // 最后错误代码
        public EMNetErrorCode LastErrorCode { get { return m_nLastErrorCode; } }

        // 最后错误描述
        public string LastErrorString { get { return m_strLastError; } }

        /// <summary>
        /// 网络连接是否成功
        /// </summary>
        public bool Connected
        {
            get
            {
                return m_connectState == ConnectState.Connected && m_ServerConnection != null && m_ServerConnection.IsConnected();
            }
        }

        /// <summary>
        /// 连接错误
        /// </summary>
        public bool ConnectErr
        {
            get
            {
                return m_connectState == ConnectState.ConnectedErr;
            }
        }

        /// <summary>
        /// 是否正在连接服务器
        /// </summary>
        public bool Connecting
        {
            get
            {
                return m_Connecting;
            }
        }

        public bool IsReConnect
        {
            get 
            { 
                return m_bStartConnect;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public CNetManager()
        {
            m_fLastSendDataTime = Time.realtimeSinceStartup;
            m_SendBuff = new byte[SimplePacker.SEND_BUFF_MAX_SIZE];
            m_sendStream = new MemoryStream(m_SendBuff);
        }

        /// <summary>
        /// 模块初始化
        /// </summary>
        public bool Create()
        {
            //订阅fixedupdate事件
            //GlobalGame.Instance.RegisterModuleEvent(this, (UInt32)EMModuleEvent.FixedUpdate);
            //注册网关模块消息
            RegisterMessageHandler(MSG_MODULEID.Gateway, (IMessageHandler)this);
            //采用https方式连接
            //CHttp.Instance.Request(EMHTTP_METHOD.EMHTTP_METHOD_WWW, "https://kmax-arvr.com/ipconfig.php?AppName=" + m_MYAppName, this);
            //IP进行连接
            //GHelp.GetNetManager().Connect("************", 5555);
            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_CHANGE_ROLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "NetManaer::Create");
                pEventEngine.Subscibe(this, (ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, "NetManaer::Create");
            }
            return true;
        }

        /// <summary>
        /// 模块析构
        /// </summary>
        public void Release()
        {
            //GlobalGame.Instance.RoomClient.ExitRoom();
            Close();
            //注销网关模块消息
            UnRegisterMessageHandler(MSG_MODULEID.Gateway);
            //注销fixedupdate事件
            //GlobalGame.Instance.UnRegisterModuleEvent(this);

            IEventEngine pEventEngine = GHelp.GetEventEngine();
            if (pEventEngine != null)
            {
                pEventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_CHANGE_ROLE, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
                pEventEngine.UnSubscibe(this, (ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0);
            }
        }

        /// <summary>
        /// 连接服务器,该方法返回true，并不表示连接成功，只表示启动连接服务器成功
        /// 最终需要获取Connected属性判断
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        public bool Connect(string ip, int port, bool reConnect = false)
        {
            //可以校验一哈地址和参数
            if (ip == null || ip == string.Empty)
            {
                TRACE.ErrorLn("NetManager::Connect 连接参数不合法 ip为空");
                m_bStartConnect = false;
                return false;
            }

            //如果已经是连接状态,则断开
            if (Connected)
            {
                GHelp.HideWait();
                TRACE.ErrorLn("当前连接未断开，请求新连接失败");
                m_bStartConnect = false;
                return false;
            }

            //如果正在连接中，暂时不要连接。
            if (m_Connecting)
            {
                TRACE.WarningLn("当前重连中");
                return false;
            }

            m_strIp = ip;
            m_nPort = port;
            m_StartConnect = true;
            //非重连
            if (!reConnect) //不是重连的话 这里要把重连需要用到的code和重连次数都设置一下
            {
                //重置断线重连变量
                m_bReConnecting = false;
                m_bCanReconnect = false;
                m_dwUCode = 0;
                m_dwReconnectTimes = MAX_RECONNECT_TIMES;
                TRACE.TraceLn("===连接服务器,非重连");
                m_bAllowReconnect = false;
            }
            else
            {
                m_bAllowReconnect = true;
                //TRACE.WarningLn("NetManager::Connect开始第"+(MAX_RECONNECT_TIMES - m_dwReconnectTimes)+"次重连网关服务器...");
            }

            //是否需要强转到idle状态?
            SetConnectState(ConnectState.Idle);
            return true;
        }

        /// <summary>
        /// 重连服务器标志
        /// </summary>
        public void SetRetryCon(bool bcon)
        {
            m_StartConnect = bcon;
        }


        /// <summary>
        /// 获取网关断开连接的Reason
        /// </summary>
        /// <returns></returns>
        public int GetCloseConReason()
        {
            return m_nLastCloseConReason;
        }

        /// <summary>
        /// 获取平均网络延时
        /// </summary>
        /// <returns></returns>
        public uint GetLatency()
        {
            return m_dwLatency;
        }
        /// <summary>
        /// 设置连接状态
        /// </summary>
        /// <param name="newState"></param>
        private void SetConnectState(ConnectState newState)
        {
            // 旧的流程
            ConnectState nOldState = m_connectState;

            //记录进入该状态的时间
            m_fConnectStateTime[(int)newState] = Time.realtimeSinceStartup;

            //记录一次进入该状态的次数
            m_nConnectStateEnterNum[(int)newState]++;

            // 当游戏流程退出
            OnConnectStateExit(nOldState, newState);

            //修改当前状态
            m_connectState = newState;

            // 当流程进入
            OnConnectStateEnter(newState, nOldState);

#if OpenDebugInfo_Net
			TRACE.TraceLn("NetManager.SetConnectState():" + nOldState.ToString() + "->" + newState.ToString());
#endif
        }

        private void OnConnectStateEnter(ConnectState state, ConnectState oldState)
        {
            switch (state)
            {
                case ConnectState.Idle:
                    {
                        m_disConnectSelf = false;
                        for (int i = 0; i < m_nConnectStateEnterNum.Length; i++)
                        {
                            m_nConnectStateEnterNum[i] = 0;
                        }
                        break;
                    }
                case ConnectState.Connect:
                    {
                        m_bDisconnectNotifyed = false;
                        m_bServerConnectOKTime = 0.0f;
                        m_disConnectSelf = false;
                        m_bConnectOK = false;
                        m_bTryReConnectOK = false;
                        m_nLastCloseConReason = -1;
                        m_bTryRePing = true;
                        if (m_ServerConnection != null)
                        {
                            Close();
                        }
                        //todo
                        m_ServerConnection = new CServerConnection(this);
#if OpenDebugInfo_Net
						TRACE.TraceLn("NetManager::OnConnectStateEnter 连接服务器 IP="+m_strIp+",port="+m_nPort);
#endif
                        if (!m_ServerConnection.Connect(m_strIp, m_nPort))
                        {
                            TRACE.ErrorLn("NetManager::OnConnectStateEnter 连接服务器失败!!! IP=" + m_strIp + ",port=" + m_nPort);
                            m_disConnectSelf = true;
                            m_Connecting = false;
                        }
                        // 断线续完就不要提示了
                        if (!m_bReConnecting)
                            TRACE.WarningLn("NetManager::开始连接网关 IP=" + m_strIp + ",port=" + m_nPort);
                    }
                    break;
                case ConnectState.ConnectTimeOut:
#if OpenDebugInfo_Net
					TRACE.TraceLn("NetManager::OnConnectStateEnter 连接超时....");
#endif
                    break;
                case ConnectState.Connected:
                    m_Connecting = false;
                    m_disConnectSelf = false;
                    m_fLastRecvPingTime = 0.0f;
                    break;
                case ConnectState.ConnectedErr:
                    m_bNotifyErr = true;
                    break;
                case ConnectState.Close:
                    {
                        Close();
                    }
                    break;
                case ConnectState.End:
                    break;
                default:
                    break;
            }
        }

        private void OnConnectedStateUpdate()
        {
            switch (m_connectState)
            {
                case ConnectState.Idle:
                    if (Connected)
                    {
                        SetConnectState(ConnectState.Connected);
                        break;
                    }
                    //开始进入连接状态
                    if (m_StartConnect) //connect设置为true了
                    {
                        m_Connecting = true;//正在连接中
                        SetConnectState(ConnectState.Connect);//进入连接状态
                        m_StartConnect = false;
                    }

                    break;
                case ConnectState.Connect:
                    //等待连接成功过程中,连接出错或客户端请求中断
                    if (m_disConnectSelf)
                    {
                        m_disConnectSelf = false;
                        m_Connecting = false;
                        TRACE.WarningLn("NetManager::OnConnectedStateUpdate 等待连接过程中，网络被中断");
                        SetConnectState(ConnectState.Close);
                        break;
                    }

                    if (m_ServerConnection == null)
                    {
                        TRACE.WarningLn("NetManager::OnConnectedStateUpdate 等待连接过程中，网络被中断 m_ServerConnection == null");
                        m_Connecting = false;
                        SetConnectState(ConnectState.Close);
                        break;
                    }

                    if (m_ServerConnection.IsConnected())
                    {
                        if (m_bServerConnectOKTime < 0.0001f)
                        {
                            m_bServerConnectOKTime = Time.realtimeSinceStartup;
                        }

                        // 如果Tcp已经连接成功后n秒都还没有断线续完成功，就关闭当前连接
                        if (m_bReConnecting && (Time.realtimeSinceStartup - m_bServerConnectOKTime) > 10f)
                        {
                            m_bReConnecting = false;
                            m_bCanReconnect = false;
                            SetConnectState(ConnectState.Close);
                            break;
                        }
                        // 断线续完成功
                        if (m_bTryReConnectOK)
                        {
                            m_bConnectOK = true;
                            SetConnectState(ConnectState.Connected);
                            break;
                        }

                        //licc 网关不支持短线重连 则这里直接下一步
                        //如果网关不支持断线续完，不会主动发送握手会用给客户端,连接成功3s超时之后就认为连接已经成功
                        if (!m_bReConnecting && (m_bConnectOK || (Time.realtimeSinceStartup - m_fConnectStateTime[(int)ConnectState.Connect]) > 3.0f))
                        {
                            m_bConnectOK = true;
                            SetConnectState(ConnectState.Connected);
                            break;
                        }
                    }

                    //连接超时 超时时间CONNECT_TIMEOUT_SEC
                    if ((Time.realtimeSinceStartup - m_fConnectStateTime[(int)ConnectState.Connect]) > CONNECT_TIMEOUT_SEC)
                    {
                        SetConnectState(ConnectState.ConnectTimeOut);
                        break;
                    }
                    break;

                case ConnectState.ConnectTimeOut:
                    {
                        //超时重连
                        if (m_bTimeOutReconnectFlag && m_nConnectStateEnterNum[(int)ConnectState.Connect] < m_dwReconnectTimes) //尝试超时重连  但是现在没有地方把m_bTimeOutReconnectFlag
                        {
                            SetConnectState(ConnectState.Connect);
                            break;
                        }
                        // m_Connecting = false;
                        //连接3次之后还未连接成功
                        //SetConnectState(ConnectState.Close);
                    }
                    break;
                case ConnectState.Connected:
                    // 服务器有启用ping功能
                    if (m_fLastRecvPingTime > 0.0001f)
                    {
                        // 如果是在加载场景期间，不需要判断
                        if (!m_bResponsePing)
                        {
                            m_fLastRecvPingTime = Time.realtimeSinceStartup;
                        }
                        // 没有收到ping包超时了
                        if (Time.realtimeSinceStartup - m_fLastRecvPingTime > PING_TIMEOUT_SECONDS)
                        {
                            if (m_bTryRePing)
                            {
                                TRACE.ErrorLn("NetManager::Ping超时,客户端补发一次ping包");
                                m_fLastRecvPingTime = Time.realtimeSinceStartup;
                                m_bTryRePing = false;
                            }
                            else
                            {
                                TRACE.ErrorLn("NetManager::Ping超时,主动断开本地连接!!!");
                                SetConnectState(ConnectState.Close);
                            }

                            break;
                        }
                    }

                    if (m_disConnectSelf)
                    {
                        m_disConnectSelf = false;
                        SetConnectState(ConnectState.Close);
                        break;
                    }
                    if (m_ServerConnection == null || !m_ServerConnection.IsConnected())
                    {
                        SetConnectState(ConnectState.ConnectedErr);
                        break;
                    }

                    break;
                case ConnectState.ConnectedErr:
                    {
                        if (m_bSwitchArea)
                        {
                            break;
                        }
                        // 检测到和服务器的连接断开导致进入ContectedErr状态。
                        if (m_bNotifyErr)
                        {
                            m_bNotifyErr = false;
                            OnConnectedErr();
                        }

                    }
                    break;
                case ConnectState.Close:
                    {
                        SetConnectState(ConnectState.Idle);
                    }
                    break;
                case ConnectState.End:
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 通知断开连接
        /// </summary>
        private void NotifyDisconnected()
        {
            /*(if (m_bDisconnectNotifyed)
                return;
            m_bDisconnectNotifyed = true;*/
            Close();
            m_Connecting = false;
            m_bDelayReConnectTimerFlag = false;
            // 通知连接断开
            //GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_NET_DISCONNECT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
            // 结束断线重连
            // GameHelp.FireExecute((UInt16)DGlobalEvent.EVENT_RECONNECT_END, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
        }

        private void OnConnectStateExit(ConnectState oldState, ConnectState State)
        {
            switch (oldState)
            {
                case ConnectState.Idle:
                    break;
                case ConnectState.Connect:
                    break;
                case ConnectState.ConnectTimeOut:
                    break;
                case ConnectState.Connected:
                    //断线续完
                    m_bReConnecting = false;
                    break;
                case ConnectState.ConnectedErr:
                    break;
                case ConnectState.Close:
                    break;
                case ConnectState.End:
                    break;
                default:
                    break;
            }
        }


        private void SetSendState(SendState newState)
        {
            // 旧的流程
            SendState nOldState = m_sendState;

            //记录进入该状态的时间
            m_fSendStateTime[(int)newState] = Time.realtimeSinceStartup;

            // 当游戏流程退出
            OnSendStateExit(nOldState, newState);

            m_sendState = newState;

            // 当游戏流程进入
            OnSendStateEnter(newState, nOldState);

#if OpenDebugInfo_Net
			TRACE.TraceLn("NetManager.SetConnectState():" + nOldState.ToString() + "->" + newState.ToString());
#endif
        }

        private void OnSendStateEnter(SendState state, SendState oldState)
        {
            switch (state)
            {
                case SendState.Check:
                    {
                    }
                    break;
                case SendState.Send:
                    {
                        m_sendPackSucc = false;

                        //把消息打包一次性发送给服务器，目的是提高网络吞吐量
                        //和降低消息排队发送造成的延时.
                        m_sendStream.Seek(0, SeekOrigin.Begin);
                        m_SendingPacket.Clear();

                        int nCurLen = m_ServerConnection.PackData(ref m_SendList, ref m_sendStream, ref m_SendingPacket);
                        //int nCurLen = 0;
                        //for(int i = 0; i < m_SendList.Count; i++)
                        //{
                        //    if (m_SendList[i] == null)
                        //        continue;
                        //    byte[] data = m_SendList[i].GetByte();
                        //    if (data == null)
                        //        continue;
                        //    //发送缓存满了
                        //    if ((nCurLen + 2 + data.Length) > SEND_BUFF_MAX_SIZE)
                        //    {
                        //        if (nCurLen == 0)
                        //        {
                        //            TRACE.ErrorLn("NetManager::Send 网络包太大，不能拷贝入发送队列 发送队列大小=" + SEND_BUFF_MAX_SIZE + ",当前包大小=" + data.Length);
                        //        }
                        //        break;
                        //    }

                        //    Int16 wLen = (Int16)data.Length;
                        //    //写入数据长度
                        //    byte[] lenData = BitConverter.GetBytes(wLen);
                        //    m_sendStream.Write(lenData, 0, lenData.Length);
                        //    nCurLen += 2;
                        //    //写入数据
                        //    m_sendStream.Write(data, 0, data.Length);
                        //    nCurLen += data.Length;
                        //    //加入到正在发送的包队列
                        //    m_SendingPacket.Add(m_SendList[i]);						
                        //}


                        if (nCurLen > 0)
                        {
                            //TRACE.ErrorLn("当前打包发送 包数=" + m_SendingPacket.Count + ",size=" + nCurLen);
                            //最后发包时间
                            m_fLastSendDataTime = Time.realtimeSinceStartup;
                            //TRACE.ErrorLn("发送网络包 moduleID=" + head.wKeyModule + ",actionID=" + head.wKeyAction + "time=" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.ffff"));
                            //发包错误
                            if (!InterSendData(m_SendBuff, nCurLen))
                            {
                                TRACE.WarningLn("NetManager::OnSendStateEnter SendData失败,请求关闭当前连接");
                                Close();
                            }
                        }
                    }
                    break;
                case SendState.SendTimeOut:
                    {
                    }
                    break;
                case SendState.End:
                    break;
            }
        }


        private void OnSendStateUpdate()
        {
            switch (m_sendState)
            {
                case SendState.Check:
                    //检查网络,游戏服登陆状态和发送队列不为空
                    if (m_SendList.Count > 0 && (m_ServerConnection != null && m_ServerConnection.IsConnected()))
                    {
                        SetSendState(SendState.Send);
                        break;
                    }
                    break;
                case SendState.Send:
                    {
                        if (m_ServerConnection == null || !m_ServerConnection.IsConnected())
                        {
                            SetSendState(SendState.Check);
                            break;
                        }

                        //判断当前包发送完毕，继续发送下一个包
                        if (m_sendPackSucc)
                        {
                            //移除已经发送的包
                            for (int i = 0; i < m_SendingPacket.Count; i++)
                            {
                                CPacketSend send = m_SendingPacket[i];
                                if (send == null)
                                    continue;
                                m_SendList.Remove(send);
                            }
                            m_SendingPacket.Clear();
                            SetSendState(SendState.Check);
                            break;
                        }
                        ////超时5s,没有收到回应
                        //if ((Time.realtimeSinceStartup - m_fSendStateTime[(int)SendState.Send]) > 10.0f)
                        //{
                        //	SetSendState(SendState.SendTimeOut);
                        //	break;
                        //}
                    }
                    break;
                case SendState.SendTimeOut:
                    {
                        //发送超时？？？,怎么处理
                        TRACE.ErrorLn("NetMamager::发送包超时!!!!!");
                        SetSendState(SendState.Check);
                    }
                    break;
                case SendState.End:
                    break;
            }
        }

        private void OnSendStateExit(SendState oldState, SendState State)
        {
            switch (oldState)
            {
                case SendState.Check:
                    break;
                case SendState.Send:
                    break;
                case SendState.SendTimeOut:
                    break;
                case SendState.End:
                    break;
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="packet">内部带流水号的包</param>
        /// <param name="isImport">是否重要包</param>
        /// <returns></returns>
        public bool SendMessage(CPacketSend packet)
        {
            AnalysisSendMessage(packet.GetByte());

            if (m_ServerConnection == null || !m_ServerConnection.IsConnected())
            {
                TRACE.WarningLn("NetManager::SendMessage 检测到网络连接已断开，请求重新连接网络");
                //SetRetryCon(true);
                return false;
            }

            m_SendList.Add(packet);
            return true;
        }
        /// <summary>
        /// 解析发送的消息数据
        /// </summary>
        /// <param name="b"></param>
        private static void AnalysisSendMessage(byte[] b)
        {
            try
            {
                if (b.Length > 0)
                {
                    CPacketRecv packetIn = new CPacketRecv();
                    //解析消息头部
                    packetIn.Init(b);
                    SGameMsgHead head = new SGameMsgHead();
                    packetIn.Pop(ref head);
                    if (head.wKeyAction != 0)
                    {
                        //TRACE.TraceLn(string.Format("Mess_获取场景服API接口，SerialNumber:{0},SrcEndPoint:{1},DestEndPoint:{2},wKeyModule:{3},wKeyAction:{4}",
                        //    head.SerialNumber,
                        //    head.SrcEndPoint,
                        //    head.DestEndPoint,
                        //    head.wKeyModule,
                        //    head.wKeyAction));
                    }
                }
            }
            catch (Exception ex)
            {
                TRACE.TraceLn(string.Format("Mess_解析出错：{0}", ex.Message));
            }
        }

        private bool InterSendData(byte[] data, int nLen)
        {
            if (data == null || nLen == 0)
                return true;
            if (null != m_ServerConnection)
            {
                return m_ServerConnection.SendData(data, (ushort)nLen);
            }
            return false;
        }

        /// <summary>
        /// 关闭连接
        /// </summary>
        public void Close()
        {
            //清空发送队列？连接断开暂时清空
            m_SendList.Clear();
            m_dwLatency = 0;
            if (m_ServerConnection != null)
            {
                //断线续完就不要提示了
                if (!m_bReConnecting)
                    TRACE.WarningLn("NetManager::Close 客户端强制关闭当前网络连接");
                m_ServerConnection.Close();
                m_ServerConnection = null;
            }
        }

        /// <summary>
        /// 网络连接事件
        /// 三次握手 连接成功
        /// 成功之后应该立即发送网关握手
        /// </summary>
        public void OnConnected()
        {
            if (m_bSwitchArea)
            {
                // 重新登陆新区
                m_dwUCode = 0;
                // 重新登陆新区
                LoginToNewArea();
                m_bSwitchArea = false;
            }
            else
            {
            }
            m_curReconnectCount = 0;
            if (m_isShowReconnectWait)
            {
                m_isShowReconnectWait = false;
                GHelp.HideWait();
            }
            SetConnectState(ConnectState.Connected);
        }

        /// <summary>
        /// 网络连接被服务器正常断开事件
        /// 连接被服务器断开,此时不需要通知玩家。
        /// </summary>
        public void OnDisConnected()
        {
            TRACE.WarningLn("NetManager::OnDisConnected 网络连接已断开");
            //通知关闭本地网络连接
            SetAllowHeartbeat(false);
            m_disConnectSelf = true;

            if (m_bSwitchArea)
            {
                m_dwUCode = 0;
                return;
            }
            else
            {

                //如果是服务器主动断开连接，表示不希望客户端继续断线续完了
                // test
                m_bCanReconnect = false;
            }
        }

        // 网络错误
        public void OnError(EMNetErrorCode nErrorCode, string strErr)
        {
            m_nLastErrorCode = nErrorCode;
            m_strLastError = strErr;
            switch (nErrorCode)
            {
                case EMNetErrorCode.NetErrorCode_ConnectError:
                    {
                        OnConnectedErr();
                    }
                    break;
                case EMNetErrorCode.NetErrorCode_Recv:
                    {
                        TRACE.WarningLn("NetManager::OnError 网络Recv错误 erroNO=" + nErrorCode.ToString());

                        //通知关闭本地网络连接
                        //m_disConnectSelf = true;
                        //请求玩家确认重连
                        NotifyDisconnected(); 
                        NotifyReconnection();
                    }
                    break;
                case EMNetErrorCode.NetErrorCode_Send:
                    {
                        TRACE.WarningLn("NetManager::OnError 网络Send错误 erroNO=" + nErrorCode.ToString());
                        //通知关闭本地网络连接
                        //m_disConnectSelf = true;
                        //请求玩家确认重连
                        NotifyDisconnected();
                        NotifyReconnection();
                    }
                    break;
                case EMNetErrorCode.NetErrorCode_UnKnown:
                    {
                        TRACE.WarningLn("NetManager::OnError 网络未知错误，描述=" + strErr);
                    }
                    break;

            }
        }

        /// <summary>
        /// 网络连接错误事件
        /// 连接过程中出错,需要立即提示玩家
        /// </summary>
        public void OnConnectedErr()
        {
            TRACE.WarningLn("NetManager::OnConnectedErr 网络连接错误 ，描述=" + LastErrorString);
            //断线续完就不要提示了
            if (!m_bReConnecting)
                TRACE.WarningLn("NetManager::OnConnectedErr 网络连接错误 ，描述=" + LastErrorString);
            //通知关闭本地网络连接
            m_Connecting = false;
            GlobalGame.Instance.EventEngine.FireExecute((ushort)DGlobalEvent.EVENT_NET_CONNERCT, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, false);
            DelayReConnect();
            if (m_bSwitchArea)
            {
                return;
            }
            else
            {
                /*if (GlobalGame.Instance.GameClient.GetState() == GameState.Game)
                {
                    //如果可以进行断线续完操作
                    if (m_bCanReconnect && m_bReConnecting == false)
                    {
                        m_bReConnecting = true;
                        m_fReConnectTime = Time.realtimeSinceStartup;
                        Connect(m_strIp, m_nPort, true);
                        TRACE.WarningLn("断线续玩::重连网关");
                        // 开始断线重连
                       // GHelp.FireExecute((UInt16)DGlobalEvent.EVENT_RECONNECT_BEGIN, (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM, 0, null);
                        return;
                    }

                    //断线续完重试
                    if (m_bReConnecting && m_bCanReconnect && Time.realtimeSinceStartup - m_fReConnectTime < RE_CONNECT_TIMEOUT_SEC)
                    {
                        DelayReConnect();
                        return;
                    }
                }

                //提示玩家连接断开了,手动重连
                NotifyDisconnected();

                //通知关闭本地网络连接
                m_disConnectSelf = true;*/
            }
        }

        /// <summary>
        /// 通知重联，例：从后台返回前台
        /// </summary>
        public void NotifyReconnection()
        {
            DelayReConnect();
        }

        public void SetAllowReconnectBool(bool acitve)
        {
            m_bAllowReconnect = acitve;
        }

        /// <summary>
        /// 延时发送断线续请求
        /// </summary>
        private void DelayReConnect()
        {
            if (!m_bAllowReconnect)
            {
                TRACE.TraceLn("===不允许断线重连");
                return;
            }
            if (m_bDelayReConnectTimerFlag)
            {
                TRACE.TraceLn("===延时重连定时器");
                return;
            }
            if (Connected) 
            {
                TRACE.TraceLn("===网络连接成功");
                return;//联接中
            }
            if (ProductGlobal.Instance.GetProductGlobal() != ProductModel.MineModule
                && ProductGlobal.Instance.GetProductGlobal() != ProductModel.HomePageModule
                && ProductGlobal.Instance.GetProductGlobal() != ProductModel.CourseModule
                && ProductGlobal.Instance.GetProductGlobal() != ProductModel.LinkModule)
            {
                GHelp.addWait(Api.NTR("重连中"));
            }
            //GHelp.addWait(Api.NTR("重连中"));

            m_isShowReconnectWait = true;
            m_curReconnectCount++;
            if (m_curReconnectCount > m_dwReconnectTimes)
            {
                //超过了重联时间
                TRACE.ErrorLn("超过断线重连次数");
                GHelp.HideWait();
                GHelp.SetAbnormalLoginOut("网络异常,请稍候重试!");
                return;
            }
            GlobalGame.Instance.TimerManager.AddTimer(this, 0, RE_CONNECT_INTERVAL, 1, "Reconnect");
            m_bDelayReConnectTimerFlag = true;
        }

        public void OnTimer(TimerInfo ti)
        {
            m_bDelayReConnectTimerFlag = false;
            if (!m_bAllowReconnect)
            {
                TRACE.TraceLn("===不允许断线重连2");
                return;
            }
            //断线续玩重连网关
            //if (m_bReConnecting)
            {
                m_bStartConnect = true;
                Connect(m_strIp, m_nPort, true);
                TRACE.WarningLn("断线续玩::重连网关");
            }
        }



        /// <summary>
        /// 接受消息 
        /// </summary>
        public void FixedUpdate()
        {
            //更新连接状态
            OnConnectedStateUpdate();
            //更新发送状态
            OnSendStateUpdate();


            if (null != NET_MANAGER)
            {
                //心跳包，每秒发送一次
                HeartbeatMessage();
                NET_MANAGER.DispatchNetwork();
            }
        }
        /// <summary>
        /// 每渲染帧
        /// </summary>
        public void Update()
        {
        }

        /// <summary>
        /// LateUpdate更新
        /// </summary>
        public void LateUpdate()
        {
        }

        /// <summary>
        /// 注册消息处理者
        /// 一个模块只能注册一类消息
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <param name="handler">消息处理者实例</param>
        /// <returns>是否成功</returns>
        public bool RegisterMessageHandler(MSG_MODULEID moduleId, IMessageHandler handler)
        {
            if (m_MessageHandlerDic.ContainsKey(moduleId))
            {
                TRACE.ErrorLn("NetManager::RegisterMessageHandler 重复注册IMessageHandler，moduleId = " + moduleId.ToString());
                return false;
            }
            else
            {
                m_MessageHandlerDic.Add(moduleId, handler);
                return true;
            }

        }

        /// <summary>
        /// 注销消息处理者
        /// </summary>
        /// <param name="moduleId">模块ID</param>
        /// <returns>是否成功</returns>
        public bool UnRegisterMessageHandler(MSG_MODULEID moduleId)
        {
            return m_MessageHandlerDic.Remove(moduleId);
        }


        private bool m_sendPackSucc = false;

        /// <summary>
        /// 数据发送成功回调
        /// </summary>
        /// <param name="nLen">数据长度</param>
        /// <returns></returns>
        public bool OnSendFinish(int nLen)
        {
            //需要判断nLen和实际包的长度是否一致吗?todo
            m_sendPackSucc = true;
            return true;
        }

        /// <summary>
        /// 派发消息给注册的模块处理
        /// </summary>
        /// <param name="message">消息字节数组</param>
        /// <returns>true:消息被处理   false：未处理</returns>
        public bool OnRecv(byte[] message)
        {
            if (message == null)
            {
                TRACE.ErrorLn("NetManager::DispatchMessage error message == null");
                return false;
            }

            if (message.Length < Marshal.SizeOf(typeof(SGameMsgHead)))
            {
                TRACE.ErrorLn("NetManager::DispatchMessage 无效的message len=" + message.Length);
                return false;
            }
            //客户端Tick数(毫秒)
            CPacketRecv packetIn = new CPacketRecv();
            //解析消息头部
            packetIn.Init(message);
            SGameMsgHead head = new SGameMsgHead();
            packetIn.Pop(ref head);
            MSG_MODULEID moduleID = (MSG_MODULEID)head.wKeyModule;

            IMessageHandler handler = null;
            if (m_MessageHandlerDic.TryGetValue(moduleID, out handler))
            {
                if (handler != null)
                {
                    handler.OnMessage(head, packetIn);
                    return true;
                }
            }


            TRACE.TraceLn("Mess_NetManager::DispatchMessage 消息没有被分发 nSrcEndPoint=" + head.SrcEndPoint + ",nDestEndPoint=" + head.DestEndPoint + ",wKeyModule=" + head.wKeyModule + ",wKeyAction=" + head.wKeyAction);
            return false;
        }

        public bool ClientMessageHandler(CPacketRecv packetRecv)
        {
            SGameMsgHead head = new SGameMsgHead();
            packetRecv.Pop(ref head);
            MSG_MODULEID moduleID = (MSG_MODULEID)head.wKeyModule;
            IMessageHandler handler = null;
            if (m_MessageHandlerDic.TryGetValue(moduleID, out handler))
            {
                if (handler != null)
                {
                    handler.OnMessage(head, packetRecv);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 模块处理消息接口
        /// </summary>
        /// <param name="head">消息头</param>
        /// <param name="package">消息体</param>
        public void OnMessage(SGameMsgHead head, CPacketRecv package)
        {
            switch (head.wKeyAction)
            {
                case (int)GatewayMessageCodes.ServerInfoResponse:         //	握手回应  //licc 只有开启短线重连机制才会回发
                    {
                        m_bAllowReconnect = true;
                        GlobalGame.Instance.LoginModule.OnMessage(head, package);
                        TRACE.WarningLn("网关握手连接成功");
                    }
                    break;
                case (int)GatewayMessageCodes.AuthenticateResponse:
                    {
                        SetAllowHeartbeat(true);
                        GlobalGame.Instance.LoginModule.OnMessage(head, package);
                        TRACE.WarningLn("授权回应包返回成功!");
                        //SendSceneNodeName();
                    }
                    break;
                case (int)GatewayMessageCodes.SessionPropertiesResponse:
                    {
                        TRACE.WarningLn("设置会话特性回调包!");
                        package.Pop(out int nLen);
                        package.ReadByteBuffer(out byte[] szValue, nLen);
                        SessionPropertiesResponse response = SessionPropertiesResponse.Parser.ParseFrom(szValue);
                        if (response.Properties != null)
                        {
                            if (response.Properties.ContainsKey("serverNodeName"))
                            {
                                string serverName = response.Properties["serverNodeName"];
                                if (!string.IsNullOrEmpty(serverName))
                                {
                                    TRACE.TraceLn("===存在场景服："+ serverName);
                                    GlobalGame.Instance.SceneClient.OpenSceneServerReady();
                                }
                                else
                                {
                                    TRACE.TraceLn("===不存在场景服");
                                    GlobalGame.Instance.SceneClient.CloseSceneServerReady();
                                }
                            }
                            else
                            {
                                TRACE.TraceLn("===并不存在场景服");
                                GlobalGame.Instance.SceneClient.CloseSceneServerReady();
                            }
                        }
                    }
                    break;
                case (int)GatewayMessageCodes.KickOutNotification:
                    {//取消重联逻辑
                        TRACE.TraceLn("===取消重联逻辑");
                        m_bAllowReconnect = false;
                        //跨区断线就不要提示了
                        if (!m_bSwitchArea)
                        {
                            TRACE.ErrorLn("服务器通知停机维护,不要断线续完。");
                        }
                        //开启异常登陆提醒
                        GHelp.SetAbnormalLoginOut();
                        m_bCanReconnect = false;
                        SetConnectState(ConnectState.Close);
                    }
                    break;
                case (int)GatewayMessageCodes.PongResponse:
                    { }
                    break;
                    // 服务器离线
                case (int)GatewayMessageCodes.ServerOffline:
                    {
                        ShowMessageBoxData showMessageBoxData = new ShowMessageBoxData();
                        showMessageBoxData.messageBoxID = MessageBoxID.COMMON;
                        showMessageBoxData.msgBoxModule = MsgBoxModule.LOGIN_OUT;
                        showMessageBoxData.msgBoxAction = MsgBoxAction.ANOMALYLOGINOUT;
                        showMessageBoxData.title = Api.TR("退出提醒");
                        showMessageBoxData.content = Api.TR("网络波动异常，请重新登录!");
                        showMessageBoxData.dicTemp = new Dictionary<int, string>();
                        showMessageBoxData.dicTemp.Add(0, Api.TR("确定"));
                        GHelp.InformShowMessageBox(showMessageBoxData, true);
                    }
                    break;
                default:
                    {
                        TRACE.WarningLn("未处理的网关消息 actionID=" + head.wKeyAction);
                    }
                    break;
            }
        }

        public void OnExecute(ushort wEventID, byte bSrcType, uint dwSrcID, object pContext)
        {
            /*if (wEventID == (ushort)DGlobalEvent.EVENT_SYSTEM_BUILDZONE && bSrcType == (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM)
            {
                // 加载场景时，不响应ping包
                m_bResponsePing = false;
            }
            else if (wEventID == (ushort)DGlobalEvent.EVENT_SCENE_LOAD_FINISH_EX && bSrcType == (byte)EMSOURCE_TYPE.SOURCE_TYPE_SYSTEM)
            {
                m_bResponsePing = true;
            }*/
            switch (wEventID)
            {
                case (ushort)DGlobalEvent.EVENT_CHANGE_ROLE:
                    {

                        break;
                    }
                    break;
                case (ushort)DGlobalEvent.EVENT_NET_CONNERCT:
                    {
                        m_bStartConnect = false;
                    }
                    break;
                default: break;
            }
        }


        /// <summary>
		/// 重置重连次数
		/// </summary>
	    public	void ResetReconnectCount()
        {
            m_curReconnectCount = 0;
        }


        /// <summary>
        /// 重新登录新区
        /// </summary>
        private void LoginToNewArea()
        {
        }

        #region 重联重新发送机器人id给服务器

        private void SendSceneNodeName()
        {
            //if (string.IsNullOrEmpty(GlobalGame.Instance.GameSDK.GetUserInfo().robotTargetID))
            {
                //获取机器人targetID
                SGameMsgHead head;
                head.SerialNumber = GHelp.GenSerialNumber();
                head.SrcEndPoint = (int)ENDPOINT.Appclient;
                head.DestEndPoint = (int)ENDPOINT.Gateway;
                head.wKeyModule = (int)MSG_MODULEID.Gateway;
                head.wKeyAction = (int)GatewayMessageCodes.SetSessionPropertiesRequest;

                CPacketSend packet = new CPacketSend();

                packet.Push<SGameMsgHead>(head);

                SetSessionPropertiesRequest requset = new SetSessionPropertiesRequest();

                if (!string.IsNullOrEmpty(GHelp.GetServerSceneNode()))
                {
                    requset.Properties["serverNodeName"] = GHelp.GetServerSceneNode();
                }
                if (!string.IsNullOrEmpty(GHelp.GetVMServerSceneNode()))
                {
                    requset.Properties["vmServerNodeName"] = GHelp.GetVMServerSceneNode();
                }
                packet.PushPB<SetSessionPropertiesRequest>(requset);

                GlobalGame.Instance.NetManager.SendMessage(packet);
            }
        }

        #endregion


        #region 心跳包
        public void SetAllowHeartbeat(bool _AllowHeartbeat)
        {
            m_AllowHeartbeat = _AllowHeartbeat;
        }
        private float m_oldTick = 0;
        private void HeartbeatMessage()
        {
            if (!m_AllowHeartbeat) return;
            if (m_ServerConnection == null || !m_ServerConnection.IsConnected()) return;
            float curTick = Time.realtimeSinceStartup;
            if (curTick - m_oldTick > 1)
            {
                m_oldTick = curTick;
                SGameMsgHead head;
                head.SerialNumber = GHelp.GenSerialNumber();
                head.SrcEndPoint = (int)ENDPOINT.Appclient;
                head.DestEndPoint = (int)ENDPOINT.Gateway;
                head.wKeyModule = (int)MSG_MODULEID.Gateway;
                head.wKeyAction = (int)GatewayMessageCodes.PingRequest;

                CPacketSend packet = new CPacketSend();

                packet.Push<SGameMsgHead>(head);

                SendMessage(packet);
            }
        }
        #endregion
    }
}