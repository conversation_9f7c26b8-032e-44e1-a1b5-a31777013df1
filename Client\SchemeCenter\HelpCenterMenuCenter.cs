﻿/// <summary>
/// HelpCenterMenuCenter
/// </summary>
/// <remarks>
/// 2023.7.12: 创建. YSH <br/>
/// 帮助中心功能入口<br/>
/// </remarks>
using game.common;
using game.schemes;
using GLib.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace GLib.Client
{
    public class HelpCenterMenuCenter : ISchemeNode, IHelpCenterMenuCenter
    {
        private const string HELPCENTERMENU_INFO = "HelpCenterMenu";

        public string SchemeName { get; set; }
        public EMSchemeState SchemeLoadState { get; set; }

        private float m_progress = 0.0f;
        public float Progress
        {
            get { return m_progress; }
            set { m_progress = value; }
        }

        private Dictionary<int, HelpCenterMenuDef> m_HelpCenterMenuByID;
        private Dictionary<int, List<HelpCenterMenuDef>> m_HelpCenterMenuByTab;

        public bool Create()
        {
            if (!LoadScheme())
            {
                return false;
            }
            return true;
        }

        public void Release()
        {
            m_HelpCenterMenuByID.Clear();
            m_HelpCenterMenuByID = null;
            m_HelpCenterMenuByTab.Clear();
            m_HelpCenterMenuByTab = null;
        }

        public HelpCenterMenuCenter()
        {
            m_HelpCenterMenuByID = new Dictionary<int, HelpCenterMenuDef>();
            m_HelpCenterMenuByTab = new Dictionary<int, List<HelpCenterMenuDef>>();
        }

        public bool LoadScheme()
        {
            string strPath = HELPCENTERMENU_INFO;
            Progress = 0.0f;
            bool bResult = ScpReader.LoadScheme(strPath, OnSchemeLoad_MainEvent);
            if (!bResult)
            {
                TRACE.ErrorLn(("加载配置文件失败。文件名 = ") + strPath);
                return false;
            }

            return true;
        }

        public bool OnSchemeLoad_MainEvent(string szFileName, ScpReader pCSVReader, object pData)
        {
            if (pCSVReader == null)
            {
                return false;
            }
            Int32 nRecordCount = pCSVReader.GetRecordCount();
            for (Int32 nRow = 0; nRow < nRecordCount; nRow++)
            {
                int tmp_col = 0;
                HelpCenterMenuDef map = new HelpCenterMenuDef();
                map.Id = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.Tab = pCSVReader.GetInt(nRow, tmp_col++, 0);
                map.TabName = pCSVReader.GetString(nRow, tmp_col++, "");
                map.Name = pCSVReader.GetString(nRow, tmp_col++, "");
                map.WebUrl = pCSVReader.GetString(nRow, tmp_col++, "");
                map.WebUrlParam = pCSVReader.GetString(nRow, tmp_col++, "");
                m_HelpCenterMenuByID.Add(map.Id, map);
                if (!m_HelpCenterMenuByTab.ContainsKey(map.Tab))
                {
                    List<HelpCenterMenuDef> list = new List<HelpCenterMenuDef>();
                    m_HelpCenterMenuByTab.Add(map.Tab, list);
                }
                var dList = m_HelpCenterMenuByTab[map.Tab];
                dList.Add(map);
            }
            Progress = 1.0f;
            return true;
        }

        /// <summary>
        /// 获取所有表格数据
        /// </summary>
        public Dictionary<int, HelpCenterMenuDef> GetAllMenu()
        {
            return m_HelpCenterMenuByID;
        }

        public Dictionary<int, List<HelpCenterMenuDef>> GetAllMenuWithTab()
        {
            return m_HelpCenterMenuByTab;
        }
    }
}
