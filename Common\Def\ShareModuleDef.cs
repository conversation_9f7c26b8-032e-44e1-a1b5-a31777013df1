﻿/// <summary>
/// ShareModuleDef
/// </summary>
/// <remarks>
/// 2021/10/15 16:41:04: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GLib.Common
{
	public class ContentType
	{
		public const int Auto = 0;              //自动(iOS为自动，安卓仅为Text)
		public const int Text = 1;              //文字分享
		public const int Image = 2;             //图文分享
		public const int Webpage = 4;           //链接分享
		public const int Music = 5;             //音乐分享 
		public const int Video = 6;             //视频分享 
		public const int App = 7;               //应用分享
		public const int File = 8;              //附件分享
		public const int Emoji = 9;             //表情分享
		public const int MiniProgram = 10;      //微信小程序 v3.6.3
		public const int Message = 11;          //快手-分享到私信
		public const int OpenMiniProgram = 12;  //打开微信小程序 v3.6.3
		public const int QQ_MINI_PROGRAM = 15;  //qq小程序

	}
	public class ShareContentDef
	{
		public string m_ShareTitle;
		/// <summary>
		/// Android Only
		/// </summary>
		public string m_ShareTitleUrl;
		public string m_ShareText;
		public string m_ShareUrl;
		/// <summary>
		/// 图片本地路径
		/// </summary>
		public string m_ShareImagePath;
		/// <summary>
		/// 图片网络路径
		/// </summary>
		public string m_ShareImageUrl;
		/// <summary>
		///从 ContentType中获取
		/// </summary>
		public int m_ContentType;
		/// <summary>
		/// 内容
		/// </summary>
		public string ShareComment;
		/// <summary>
		/// 文件路径
		/// </summary>
		public string ShareFilePath;
		/// <summary>
		/// 音频文件路径
		/// </summary>
		public string ShareMusicUrl;

	}
}
