﻿/// <summary>
/// ModuleDispatch
/// </summary>
/// <remarks>
/// 2019.6.25: 创建. 谌安 <br/>
/// 模块基础事件驱动器<br/>
/// </remarks>
//#define OpenDebugInfo_Module

using System;
using System.Collections.Generic;

namespace GLib.Common
{
	/// <summary>
	/// 模块事件驱动器
	/// </summary>
	public class ModuleDispatch
	{
		//每逻辑帧FixedUpdate更新列表
		private List<IModule> m_fixedUpdateList = new List<IModule>();
		//每LateUpdate更新列表
		private List<IModule> m_lateUpdateList = new List<IModule>();
		//每渲染帧Update更新列表
		private List<IModule> m_updateList = new List<IModule>();

		/// <summary>
		/// 分发器创建
		/// </summary>
		/// <returns></returns>
		public bool Create()
		{
			return true;
		}

		/// <summary>
		/// 分发器释放
		/// </summary>
		public void Release()
		{
			if (m_updateList != null)
				m_updateList.Clear();

			if (m_lateUpdateList != null)
				m_lateUpdateList.Clear();

			if (m_fixedUpdateList != null)
				m_fixedUpdateList.Clear();
		}

		/// <summary>
		/// 模块注册驱动事件
		/// </summary>
		/// <param name="module">IModule模块引用</param>
		/// <param name="eventFlag">事件Flag,多个EMModuleEvent类型的|,默认全部注册</param>
		public void RegisterModuleEvent(IModule module, uint eventFlag = (uint)EMModuleEvent.All)
		{
			if (module == null)
				return;
			if ((eventFlag & (UInt32)EMModuleEvent.Update) > 0)
			{
				if (!m_updateList.Contains(module))
				{
					m_updateList.Add(module);
				}
				else
				{
					TRACE.WarningLn("ModuleDispatch::RegisterModuleEvent 重复注册Update事件");
				}
			}

			if ((eventFlag & (UInt32)EMModuleEvent.FixedUpdate) > 0)
			{
				if (!m_fixedUpdateList.Contains(module))
				{
					m_fixedUpdateList.Add(module);
				}
				else
				{
					TRACE.WarningLn("ModuleDispatch::RegisterModuleEvent 重复注册FixedUpdate事件");
				}
			}

			if ((eventFlag & (UInt32)EMModuleEvent.LateUpdate) > 0)
			{
				if (!m_lateUpdateList.Contains(module))
				{
					m_lateUpdateList.Add(module);
				}
				else
				{
					TRACE.WarningLn("ModuleDispatch::RegisterModuleEvent 重复注册LateUpdate事件");
				}
			}
		}

		/// <summary>
		/// 模块取消驱动
		/// </summary>
		/// <param name="module">IModule模块引用</param>
		public void UnRegisterModuleEvent(IModule module)
		{
			if (module == null)
				return;
			m_updateList.Remove(module);
			m_lateUpdateList.Remove(module);
			m_fixedUpdateList.Remove(module);
		}

		/// <summary>
		/// 分发渲染帧
		/// </summary>
		public void DispatchUpdate()
		{
			try
			{
#if OpenDebugInfo_Module
				int startTick = Api.GetTickCount();
#endif
                for (int i = m_updateList.Count - 1; i >= 0; i--)
				{
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_START(String.Format("{0}.Update()",m_updateList[i].ModuleName.ToString()) );
#endif
					m_updateList[i].Update();
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                }
#if OpenDebugInfo_Module
                int costick = Api.GetTickCount() -startTick;

				if (costick > 50)
				{
					TRACE.WarningLn("渲染超时，tick = " + costick.ToString());
				}
#endif
			}
			catch (Exception e)
			{
				TRACE.ErrorLn("Game Update 异常:" + e.Message);
				TRACE.ErrorLn("Game Update 堆栈:" + e.StackTrace);
			}
		}

		/// <summary>
		/// 分发逻辑帧
		/// </summary>
		public void DispatchFixedUpdate()
		{
			try
			{
#if OpenDebugInfo_Module
				int startTick = Api.GetTickCount();
#endif
				for (int i = m_fixedUpdateList.Count - 1; i >= 0; i--)
				{
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_START(String.Format("{0}.FixedUpdate()",m_fixedUpdateList[i].ModuleName.ToString()) );
#endif
                    m_fixedUpdateList[i].FixedUpdate();
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                }

#if OpenDebugInfo_Module
                int costick = Api.GetTickCount() - startTick;

				if (costick > 50)
				{
					TRACE.WarningLn("FixedUpdate超时，tick = " + costick.ToString());
				}
#endif
			}
			catch (Exception e)
			{
				TRACE.ErrorLn("Game FixedUpdate 异常:" + e.Message);
				TRACE.ErrorLn("Game FixedUpdate 堆栈:" + e.StackTrace);
			}
		}

		/// <summary>
		/// 分发LateUpdate帧
		/// </summary>
		public void DispatchLateUpdate()
		{
			try
			{
#if OpenDebugInfo_Module
				int startTick = Api.GetTickCount();
#endif
				for (int i = m_lateUpdateList.Count - 1; i >= 0; i--)
				{
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_START(String.Format("{0}.LateUpdate()",m_lateUpdateList[i].ModuleName));
#endif
                    m_lateUpdateList[i].LateUpdate();
#if OpenDebugInfo_Profiler
                    Api.PP_BY_NAME_STOP();
#endif
                }
#if OpenDebugInfo_Module
                int costick = Api.GetTickCount() - startTick;

				if (costick > 50)
				{
					TRACE.WarningLn("LateUpdate超时，tick = " + costick.ToString());
				}
#endif
			}
			catch (Exception e)
			{
				TRACE.ErrorLn("Game LateUpdate 异常:" + e.Message);
				TRACE.ErrorLn("Game LateUpdate 堆栈:" + e.StackTrace);
			}
		}
	}
}
