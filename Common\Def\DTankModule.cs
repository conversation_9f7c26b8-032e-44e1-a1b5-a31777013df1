﻿/// <summary>
/// DTankModule
/// </summary>
/// <remarks>
/// 2021.4.2: 创建. 谌安 <br/>
/// 载具定义<br/>
/// </remarks>
using System;
using System.Collections.Generic;

namespace GLib.Common
{
    public class DTankModule
    {
    }

	public class STankClientNodeData
	{
		public UInt32 dwIndex;          // 序号
		public UInt32 dwPlayerID;       // 玩家ID
		public string szPlayerName;     // 人物名

		public void clear()
		{
			dwPlayerID = 0;             // 玩家ID
		}

	};

	// 乘客载具事件类型
	public enum EMTankPartEventType
	{
		// 乘客载具事件类型ID定义规则（TANKPART_EVENT + 功能涵义）
		TANKPART_EVENT_INVALID = 0,     // 无效方式ID
		TANKPART_EVENT_UPDATE,          // 更新载具数据
		TANKPART_EVENT_ENTER,           // 进入载具
		TANKPART_EVENT_EXIT,            // 载具出来
		TANKPART_EVENT_DRIVER,          // 驾驶事件
		TANKPART_EVENT_INDEXCHANGE,     // 位置改变
		TANKPART_EVENT_ENABLED,         // 是否有效
		TANKPART_EVENT_MAXID,           // 最大方式ID
	};
}
