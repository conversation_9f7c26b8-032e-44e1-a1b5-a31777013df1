﻿/// <summary>
/// ButtonControl
/// </summary>
/// <remarks>
/// 2021/12/31 18:13:05: 创建. 王正勇 <br/>
/// 
/// </remarks>
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace GLib.Common
{
    /// <summary>
    /// ButtonControl
    /// </summary>
    public class CButtonControl : IButtonControl
    {
        private Dictionary<ModuleType, Dictionary<ButtonTriggerMode, GameObject>> m_dicButtonModule;



        public string ModuleName { get; set; }
        public EMModuleLoadState ModuleLoadState { get; set; }
        public float Progress { get; set; }
        /// <summary>
        /// 增加按钮数据
        /// </summary>
        /// <param name="moduleType"></param>
        /// <param name="triggerMode"></param>
        /// <param name="gameButton"></param>
        public void AddButton(ModuleType moduleType, ButtonTriggerMode triggerMode, GameObject gameButton)
        {
            if (m_dicButtonModule.ContainsKey(moduleType))
            {
                if (!m_dicButtonModule[moduleType].ContainsKey(triggerMode))
                {
                    m_dicButtonModule[moduleType].Add(triggerMode, gameButton);
                }
            }
            else
            {
                Dictionary<ButtonTriggerMode, GameObject> keyValuePairs = new Dictionary<ButtonTriggerMode, GameObject>();
                keyValuePairs.Add(triggerMode, gameButton);
                m_dicButtonModule.Add(moduleType, keyValuePairs);
            }
        }

        public bool Create()
        {
            m_dicButtonModule = new Dictionary<ModuleType, Dictionary<ButtonTriggerMode, GameObject>>();
            return true;
        }

        public void FixedUpdate()
        {
        }

        /// <summary>
        /// 获取按钮
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <param name="triggerMode">按钮类型</param>
        /// <returns></returns>
        public GameObject GetButtonGame(ModuleType moduleType, ButtonTriggerMode triggerMode)
        {
            if (m_dicButtonModule.ContainsKey(moduleType))
            {
                if (m_dicButtonModule[moduleType].ContainsKey(triggerMode))
                {
                    return m_dicButtonModule[moduleType][triggerMode];
                }
            }
            return null;
        }

        public void LateUpdate()
        {
        }

        public void Release()
        {
        }

        /// <summary>
        /// 移除按钮数据
        /// </summary>
        /// <param name="moduleType"></param>
        /// <param name="triggerMode"></param>
        public void RemoveButton(ModuleType moduleType, ButtonTriggerMode triggerMode)
        {
            if (m_dicButtonModule.ContainsKey(moduleType))
            {
                if (m_dicButtonModule[moduleType].ContainsKey(triggerMode))
                {
                    m_dicButtonModule[moduleType].Remove(triggerMode);
                }
            }
        }

        public void Update()
        {
            throw new NotImplementedException();
        }
    }
}
