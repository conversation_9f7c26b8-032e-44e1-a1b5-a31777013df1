﻿/// <summary>
/// CommonApi
/// </summary>
/// <remarks>
/// 2021.4.7: 创建. 谌安 <br/>
/// 命令api管理<br/>
/// </remarks>
using ENTITY_ID = System.UInt32;
using game.common;
using game.proto;
using game.scene;
using game.schemes;
using Game.Entity;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace GLib.Common
{
    public class CommonApi
    {
        /// <summary>
        /// 准备移动实体通知服务器
        /// </summary>
        /// <param name="_x"></param>
        /// <param name="_y"></param>
        /// <param name="_z"></param>
        /// <returns></returns>
        public static bool RequestEntityMove(float _x,float _y,float _z)
        {
            Vector3 targetPos = new Vector3(_x, _y, _z);
            string entityGuid = "";
            //查找导航网格
            List<Vector3> paths = GHelp.FindPathByHero(targetPos,ref entityGuid);

            if (paths.Count > 0)
            {
                SGameMsgHead head;
                head.SerialNumber = GHelp.GenSerialNumber();
                head.SrcEndPoint = (int)ENDPOINT.Appclient;
                head.DestEndPoint = (int)ENDPOINT.Scene;
                head.wKeyModule = (int)MSG_MODULEID.Entity;
                head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityHeroMove;

                CPacketSend packet = new CPacketSend();

                packet.Push<SGameMsgHead>(head);

                Entity_Move requset = new Entity_Move();

                paths[paths.Count - 1] = new Vector3(_x, _y, _z);

                foreach (Vector3 p in paths)
                {
                    requset.Positions.Add(new PBVec3() { X = p.x, Y = p.y ,Z=p.z}) ;
                }
                requset.EntityGuid = entityGuid;
                packet.PushPB<Entity_Move>(requset);

                GlobalGame.Instance.NetManager.SendMessage(packet);
            }
            return true;
        }

        /// <summary>
        /// 准备移动实体通知服务器，带路径长度返回
        /// </summary>
        /// <param name="_x"></param>
        /// <param name="_y"></param>
        /// <param name="_z"></param>
        /// <returns></returns>
        public static float RequestEntityMoveToLength(float _x, float _y, float _z)
        {
            Vector3 targetPos = new Vector3(_x, _y, _z);
            string entityGuid = "";
            //查找导航网格
            List<Vector3> paths = GHelp.FindPathByHero(targetPos, ref entityGuid);

            if (paths.Count > 0)
            {
                SGameMsgHead head;
                head.SerialNumber = GHelp.GenSerialNumber();
                head.SrcEndPoint = (int)ENDPOINT.Appclient;
                head.DestEndPoint = (int)ENDPOINT.Scene;
                head.wKeyModule = (int)MSG_MODULEID.Entity;
                head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityHeroMove;

                CPacketSend packet = new CPacketSend();

                packet.Push<SGameMsgHead>(head);

                Entity_Move requset = new Entity_Move();

                paths[paths.Count - 1] = new Vector3(_x, _y, _z);

                foreach (Vector3 p in paths)
                {
                    requset.Positions.Add(new PBVec3() { X = p.x, Y = p.y, Z = p.z });
                }
                requset.EntityGuid = entityGuid;
                packet.PushPB<Entity_Move>(requset);

                GlobalGame.Instance.NetManager.SendMessage(packet);

                if (paths.Count > 1)
                {
                    float allLength = 0;
                    for (int i = 1; i < paths.Count; i++)
                    {
                        allLength += Vector3.Distance(paths[i - 1], paths[i]);
                    }
                    return allLength;
                }
            }
            return 0;
        }

        /// <summary>
        /// 客户端触发去打开宝箱
        /// </summary>
        /// <param name="playerGuid"></param>
        /// <param name="BoxGuid"></param>
        /// <returns></returns>
        public static bool StartOpenTreasureBox(string playerGuid,string BoxGuid)
        {
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Entity;
            head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityOpenTreasureBox;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            Entity_OpenTreasureBox_CS requset = new Entity_OpenTreasureBox_CS();

            requset.Playerguid = playerGuid;
            requset.Targetguid = BoxGuid;

            packet.PushPB<Entity_OpenTreasureBox_CS>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);
            return true;
        }

        //拾取发送到服务器
        public static bool PickUp_ToService(string playerGuid, string tragetGuid)
        {
            //发送拾取命令给到服务器
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Entity;
            head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityPickUp;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            Entity_PickUp_CS requset = new Entity_PickUp_CS();

            requset.Playerguid = playerGuid;
            requset.Targetguid = tragetGuid;

            packet.PushPB<Entity_PickUp_CS>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);

            return true;
        }
        //拾取
        public static bool PickUp(Int64 uidEntity)
        {
            IEntity pEntity = GlobalGame.Instance.EntityClient.Get(uidEntity);
            if (pEntity == null)
            {
                return true;
            }

            bool isCreate =  GHelp.GetEntityFactory().EntityCreateIsFinish(pEntity.GetEntityViewID());
            if (!isCreate)
                return false;

            IPerson person = GHelp.GetHero();
            if (person != null)
            {
                int nEffectID = 3001;
                LightingEffectContext context = GHelp.GetObjectItem<LightingEffectContext>();
                context.id = (uint)nEffectID;  // 效果Id;
                context.ptCenter = pEntity.GetPosition();
                context.src = pEntity.GetEntityViewID();
                context.target = person.GetEntityViewID();      // 技能效果发起者
                GHelp.sendControllerCommand((int)ViewLogicDef.GVIEWCMD_ADD_LIGHTING, nEffectID, "", context);

                //发送拾取命令给到服务器
                SGameMsgHead head;
                head.SerialNumber = GHelp.GenSerialNumber();
                head.SrcEndPoint = (int)ENDPOINT.Appclient;
                head.DestEndPoint = (int)ENDPOINT.Scene;
                head.wKeyModule = (int)MSG_MODULEID.Entity;
                head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityPickUp;

                CPacketSend packet = new CPacketSend();

                packet.Push<SGameMsgHead>(head);

                Entity_PickUp_CS requset = new Entity_PickUp_CS();

                requset.Playerguid = GHelp.GetHero().GetStrGUID();
                requset.Targetguid = pEntity.GetStrGUID();

                packet.PushPB<Entity_PickUp_CS>(requset);

                GlobalGame.Instance.NetManager.SendMessage(packet);

                pEntity.Release();
            }

            return true;
        }

        /// <summary>
        /// 客户端移动到传送门
        /// </summary>
        /// <param name="playerGuid"></param>
        /// <param name="BoxGuid"></param>
        /// <returns></returns>
        public static bool SendMoveTo_Request(string playerGuid, string PortalGuid)
        {
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Entity;
            head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityMoveTo;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            Entity_MoveTo_CS requset = new Entity_MoveTo_CS();

            requset.Playerguid = playerGuid;
            requset.TargetGuid = PortalGuid;

            packet.PushPB<Entity_MoveTo_CS>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);
            return true;
        }

        /// <summary>
        /// 客户端触发传送门
        /// </summary>
        /// <param name="playerGuid"></param>
        /// <param name="BoxGuid"></param>
        /// <returns></returns>
        public static bool TriggerPortal(string PortalGuid)
        {
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Entity;
            head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityTelesport;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            Entity_Telesport_CS requset = new Entity_Telesport_CS();

            requset.Playerguid = GHelp.GetHero().GetStrGUID();
            requset.Portalguid = PortalGuid;

            packet.PushPB<Entity_Telesport_CS>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);
            return true;
        }

        /// <summary>
        /// 客户端请求伤害信息
        /// </summary>
        /// <param name="attackGuid"></param>
        /// <param name="hitEntity"></param>
        /// <param name="skillid"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public static bool SendDamage_Requset(string attackGuid,List<string> hitEntity,int skillid,int count)
        {
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Entity;
            head.wKeyAction = (int)MessageCodesOfEntity.CodeEntityDamage;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            Entity_Damage_CS requset = new Entity_Damage_CS();

            requset.Playerguid = attackGuid;
            for (int i = 0; i < hitEntity.Count; i++)
            {
                Entity_Damage_CS.Types.EntityInfo e = new Entity_Damage_CS.Types.EntityInfo();
                e.Guid = hitEntity[i];
                requset.Items.Add(e);
            }
            requset.SkillId = skillid;
            requset.Count = count;

            packet.PushPB<Entity_Damage_CS>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);
            return true;
        }
        /// <summary>
        /// 发送技能完成事件
        /// </summary>
        /// <param name="attackGuid"></param>
        /// <param name="skillID"></param>
        /// <returns></returns>
        public static bool SendSkillFinish(string attackGuid,int skillID)
        {
            SGameMsgHead head;
            head.SerialNumber = GHelp.GenSerialNumber();
            head.SrcEndPoint = (int)ENDPOINT.Appclient;
            head.DestEndPoint = (int)ENDPOINT.Scene;
            head.wKeyModule = (int)MSG_MODULEID.Entity;
            head.wKeyAction = (int)SkillAction.SkillFinish;

            CPacketSend packet = new CPacketSend();

            packet.Push<SGameMsgHead>(head);

            Skill_SkillFinish requset = new Skill_SkillFinish();

            requset.AttackGuid = attackGuid;
            requset.SkillId = skillID;

            packet.PushPB<Skill_SkillFinish>(requset);

            GlobalGame.Instance.NetManager.SendMessage(packet);
            return true;
        }
        public static bool SendPlayVideo(string strVideoPath, string taskId)
        {
            return true;
        }

        /// <summary>
        /// 获取下一步该跳跃的目标点
        /// </summary>
        /// By --王康阳
        /// <param name="eUID"></param>
        /// <param name="Distance"></param>
        /// <param name="IsPortal"></param>
        /// <returns></returns>
        public static Vector3 GetNextJumpPos(Vector3 pos, Vector3 angle,Vector3 sourcePos,string Distance,bool IsPortal)
        {
            // 如果跳跃的距离是0，则原地跳，无需计算下一步定位器在哪
            if (float.Parse(Distance) == 0)
            {

            }
            else
            {
                // 是否是寻定位器跳跃
                if (IsPortal)
                {
                    pos.x += float.Parse(Distance) * angle.x;
                    pos.z += float.Parse(Distance) * angle.z;
                    // 根据落脚点，选择最近的定位器，此定位器不能包含自身脚下定位器
                    List<IEntity> targetList = new List<IEntity>();
                    GlobalGame.Instance.EntityClient.GetEntityWorld().GetEntityByType(EMEntityType.typePortal, ref targetList);

                    IEntity target = null;
                    float minDistance = 9999f;
                    // 计算当前落脚点与机器人之间的角度
                    double tempAngle = Math.Atan2((pos.z - sourcePos.z), (pos.x - sourcePos.x));  //弧度  0.6435011087932844
                    double theta = tempAngle * (180 / Math.PI);  //角度  36.86989764584402
                    foreach (var item in targetList)
                    {
                        // 然后再计算定位器与机器人的角度
                        double tempAngle2 = Math.Atan2(item.GetPosition().z - sourcePos.z, item.GetPosition().x - sourcePos.x);
                        double theta2 = tempAngle2 * (180 / Math.PI);
                        bool isSameLine = false;
                        // 如果两者间角度相差无几，说明该定位器与机器人处在同一朝向上
                        if (Mathf.Abs((float)(theta2 - theta)) <= 5)
                        {
                            isSameLine = true;
                        }
                        if (isSameLine)
                        {
                            // 接着对这些同一朝向的定位器取最近的定位器，大于0.5是为了不取当前脚下的定位器
                            float tmpTargetDistance = Vector3.Distance(item.GetPosition(), pos);
                            float tmpSelfDistance = Vector3.Distance(item.GetPosition(), sourcePos);
                            if (tmpTargetDistance <= minDistance && tmpSelfDistance > 0.5f)
                            {
                                target = item;
                                minDistance = tmpTargetDistance;
                            }
                        }
                    }

                    if (target != null)
                    {
                        pos = target.GetPosition();
                    }
                }
                // 不是则直接根据当前朝向取整后跳跃
                else
                {
                    Vector3 tmpAngle = new Vector3(0, 0, 1);
                    Vector3 changeAngle = tmpAngle;
                    float dis = Vector3.Distance(angle, tmpAngle);
                    tmpAngle.x = 1;
                    tmpAngle.z = 0;
                    if (Vector3.Distance(angle, tmpAngle) < dis)
                    {
                        dis = Vector3.Distance(angle, tmpAngle);
                        changeAngle = tmpAngle;
                    }
                    tmpAngle.x = 0;
                    tmpAngle.z = -1;
                    if (Vector3.Distance(angle, tmpAngle) < dis)
                    {
                        dis = Vector3.Distance(angle, tmpAngle);
                        changeAngle = tmpAngle;
                    }
                    tmpAngle.x = -1;
                    tmpAngle.z = 0;
                    if (Vector3.Distance(angle, tmpAngle) < dis)
                    {
                        dis = Vector3.Distance(angle, tmpAngle);
                        changeAngle = tmpAngle;
                    }

                    pos.x += float.Parse(Distance) * changeAngle.x;
                    pos.z += float.Parse(Distance) * changeAngle.z;
                }
            }
            return pos;
        }
    }
}
